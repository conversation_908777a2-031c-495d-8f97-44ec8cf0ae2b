# ChewyAI Staging Environment Configuration
# Optimized for cost-efficient testing

# Server Configuration
NODE_ENV=staging
PORT=3001
FRONTEND_URL=https://304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev

# Supabase Configuration (same as production for data consistency)
SUPABASE_URL=https://jpvbtrzvbpyzgtpvltss.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxMzUzMCwiZXhwIjoyMDY2NDg5NTMwfQ.Kor-OkR26YK5D8TJ4T9EUVUpyjBEf_CeHcOwpOP6Xx4

# Stripe Configuration (Test Mode)
STRIPE_SECRET_KEY=sk_test_51RXsOB7lNlVY1bMupuZPxacaVrGoaNj6PNiwi2zQCNtCx2HTZo5c8FMFTrhgZysHluJ2I6ZdOyXms5MtpzVQIHtk00dCTcOrGp
STRIPE_WEBHOOK_SECRET=whsec_staging_webhook_secret

# OpenRouter AI Configuration (same key, lower usage)
OPENROUTER_API_KEY=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

# JWT Configuration
JWT_SECRET=Glootie_Staging_2025

# File Upload Configuration (reduced for staging)
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=pdf,docx,txt

# Cost Optimization Settings
ENABLE_CACHING=true
LOG_LEVEL=warn
ENABLE_COMPRESSION=true

