# Phase 4: Authentication System
**Priority**: HIGH - Required before any user-specific features
**Dependencies**: Phase 1 (Foundation), Phase 2 (Database Schema), Phase 3 (Document Management Backend)
**Estimated Time**: 3-4 hours

## Overview
Implement complete authentication system using Supa<PERSON> Auth with backend middleware and frontend auth components.

## Tasks

### 3.1 Backend Authentication Service
**File**: `backend/src/services/supabaseService.ts`

```typescript
import { createClient } from '@supabase/supabase-js';
import { UserProfile, AuthResult } from '../../../shared/types';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!;

// Service role client for backend operations
export const supabase = createClient(supabaseUrl, supabaseServiceKey);

export class SupabaseService {
  // Create user profile after Supabase auth signup
  async createUserProfile(userId: string, email: string, name?: string): Promise<UserProfile> {
    const { data, error } = await supabase
      .from('users')
      .insert({
        id: userId,
        email,
        name,
        subscription_tier: 'Free',
        credits_remaining: 10,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create user profile: ${error.message}`);
    }

    return data;
  }

  // Get user profile by ID
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to get user profile: ${error.message}`);
    }

    return data;
  }

  // Update user profile
  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update user profile: ${error.message}`);
    }

    return data;
  }

  // Verify JWT token and get user
  async verifyToken(token: string): Promise<{ user: any; error: any }> {
    return await supabase.auth.getUser(token);
  }
}

export const supabaseService = new SupabaseService();
```

### 3.2 Authentication Middleware
**File**: `backend/src/middleware/auth.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { supabaseService } from '../services/supabaseService';

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        profile?: any;
      };
    }
  }
}

export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      });
    }

    // Verify token with Supabase
    const { data: { user }, error } = await supabaseService.verifyToken(token);

    if (error || !user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    // Get user profile
    const profile = await supabaseService.getUserProfile(user.id);
    
    if (!profile || !profile.is_active) {
      return res.status(401).json({
        success: false,
        error: 'User account not found or inactive'
      });
    }

    // Attach user to request
    req.user = {
      id: user.id,
      email: user.email!,
      profile
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication service error'
    });
  }
};

// Optional authentication (for public endpoints that can benefit from user context)
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const { data: { user }, error } = await supabaseService.verifyToken(token);
      
      if (!error && user) {
        const profile = await supabaseService.getUserProfile(user.id);
        if (profile && profile.is_active) {
          req.user = {
            id: user.id,
            email: user.email!,
            profile
          };
        }
      }
    }

    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};
```

### 3.3 Authentication Routes
**File**: `backend/src/routes/auth.ts`

```typescript
import { Router, Request, Response } from 'express';
import { supabase, supabaseService } from '../services/supabaseService';
import { authenticateToken } from '../middleware/auth';
import { AuthResult } from '../../../shared/types';

const router = Router();

// Sign up new user
router.post('/signup', async (req: Request, res: Response) => {
  try {
    const { email, password, name } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Create auth user with Supabase
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: { name }
      }
    });

    if (authError) {
      return res.status(400).json({
        success: false,
        error: authError.message
      });
    }

    if (!authData.user) {
      return res.status(400).json({
        success: false,
        error: 'Failed to create user account'
      });
    }

    // Create user profile
    const profile = await supabaseService.createUserProfile(
      authData.user.id,
      email,
      name
    );

    const result: AuthResult = {
      success: true,
      user: profile,
      token: authData.session?.access_token
    };

    res.status(201).json(result);
  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Sign in user
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Authenticate with Supabase
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      return res.status(401).json({
        success: false,
        error: authError.message
      });
    }

    if (!authData.user || !authData.session) {
      return res.status(401).json({
        success: false,
        error: 'Authentication failed'
      });
    }

    // Get user profile
    const profile = await supabaseService.getUserProfile(authData.user.id);
    
    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'User profile not found'
      });
    }

    // Update last login
    await supabaseService.updateUserProfile(authData.user.id, {
      last_login: new Date().toISOString()
    });

    const result: AuthResult = {
      success: true,
      user: profile,
      token: authData.session.access_token
    };

    res.json(result);
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Get current user profile
router.get('/user', authenticateToken, async (req: Request, res: Response) => {
  try {
    const profile = await supabaseService.getUserProfile(req.user!.id);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile'
    });
  }
});

// Sign out user
router.post('/logout', authenticateToken, async (req: Request, res: Response) => {
  try {
    // Supabase handles token invalidation on client side
    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Logout failed'
    });
  }
});

export default router;
```

### 3.4 Frontend Authentication Service
**File**: `frontend/src/services/auth.ts`

```typescript
import { createClient } from '@supabase/supabase-js';
import { UserProfile, AuthResult } from '../../../shared/types';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export class AuthService {
  // Sign up new user
  async signUp(email: string, password: string, name?: string): Promise<AuthResult> {
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, name }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: 'Network error during signup'
      };
    }
  }

  // Sign in user
  async signIn(email: string, password: string): Promise<AuthResult> {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const result = await response.json();
      
      if (result.success && result.token) {
        localStorage.setItem('auth_token', result.token);
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        error: 'Network error during login'
      };
    }
  }

  // Sign out user
  async signOut(): Promise<void> {
    try {
      const token = localStorage.getItem('auth_token');
      
      if (token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('auth_token');
    }
  }

  // Get current user
  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const token = localStorage.getItem('auth_token');
      
      if (!token) return null;

      const response = await fetch('/api/auth/user', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('auth_token');
        }
        return null;
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Get stored token
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

export const authService = new AuthService();
```

## Acceptance Criteria
- [ ] Supabase authentication service implemented
- [ ] Authentication middleware working correctly
- [ ] Auth routes handle signup, login, logout, and user profile
- [ ] Frontend auth service communicates with backend
- [ ] JWT tokens stored and managed properly
- [ ] User profiles created automatically on signup
- [ ] Row Level Security working with authenticated users
- [ ] Error handling for invalid credentials and network issues
- [ ] Token expiration handled gracefully

## Next Phase Dependencies
- Phase 4 (Frontend Auth Components) requires this auth service
- Phase 5 (Document Management) requires authentication middleware
- All subsequent user-specific features depend on authentication
