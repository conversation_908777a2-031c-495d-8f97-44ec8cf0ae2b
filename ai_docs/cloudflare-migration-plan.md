# ChewyAI Cloudflare Migration Plan

**Migration Target**: Railway → Cloudflare Workers + Vercel → Cloudflare Pages  
**Database**: Keep Supabase (no change)  
**Timeline**: 1-2 weeks  
**Cost Impact**: Similar base cost, significant performance improvement

## 🎯 Migration Overview

### Current Architecture

```
User → Vercel (Frontend) → Railway (Express.js Backend) → Supabase DB
                         ↘ OpenRouter AI
                         ↘ Stripe
```

### Target Architecture

```
User → Cloudflare Pages → Cloudflare Workers → Supabase DB
                       ↘ OpenRouter AI
                       ↘ Stripe
```

## 📊 Cost & Performance Analysis

### Current Monthly Costs

| Service   | Current Plan | Cost            | Features                            |
| --------- | ------------ | --------------- | ----------------------------------- |
| Vercel    | Free         | $0              | Frontend hosting, edge network      |
| Railway   | Hobby        | $5-15           | Backend API, 512MB RAM, cold starts |
| **Total** |              | **$5-15/month** |                                     |

### Cloudflare Migration Costs

| Service            | Plan | Cost         | Features                                   |
| ------------------ | ---- | ------------ | ------------------------------------------ |
| Cloudflare Pages   | Free | $0           | Frontend hosting, global edge              |
| Cloudflare Workers | Paid | $5/month     | 10M requests, 128MB RAM, ~10ms cold starts |
| **Total**          |      | **$5/month** |                                            |

### Performance Improvements

| Metric      | Railway         | Cloudflare Workers | Improvement      |
| ----------- | --------------- | ------------------ | ---------------- |
| Cold Start  | ~30 seconds     | ~10ms              | **3000x faster** |
| Global Edge | Single region   | 275+ locations     | **Massive**      |
| Scaling     | Manual/limited  | Instant auto-scale | **Superior**     |
| Bandwidth   | Limited by plan | Unlimited          | **No limits**    |

## 🔧 Required Cloudflare API Keys

### 1. Account Setup

```bash
# Get these from Cloudflare Dashboard
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_API_TOKEN=your_api_token_with_workers_edit_permissions
```

### 2. Domain Configuration (Optional)

```bash
CLOUDFLARE_ZONE_ID=your_domain_zone_id
```

### 3. Workers Environment Variables

```bash
# These go into Workers dashboard or wrangler.toml
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
OPENROUTER_API_KEY=your_openrouter_api_key
STRIPE_SECRET_KEY=your_stripe_secret_key
JWT_SECRET=your_jwt_signing_secret
NODE_ENV=production
```

### 4. Optional Storage (R2)

```bash
# Only if using R2 for file storage instead of Supabase Storage
R2_ACCESS_KEY_ID=your_r2_access_key
R2_SECRET_ACCESS_KEY=your_r2_secret_key
```

## 📋 Work Breakdown Structure

### Phase 1: Environment Setup (1 day)

**Dependencies**: None  
**Deliverables**:

- [ ] Cloudflare account setup
- [ ] Workers and Pages services enabled
- [ ] Wrangler CLI installed and configured
- [ ] API keys gathered and documented

**Tasks**:

1. Create Cloudflare account (if not exists)
2. Install Wrangler CLI: `npm install -g wrangler`
3. Login to Wrangler: `wrangler login`
4. Create Workers project: `wrangler init chewyai-api`
5. Enable Pages service
6. Generate API tokens with Workers:edit permissions

### Phase 2: Backend Migration (3-4 days)

**Dependencies**: Phase 1  
**Deliverables**:

- [ ] Express.js routes converted to Workers handlers
- [ ] File upload handling adapted for Workers
- [ ] Environment variables configured
- [ ] Local testing completed

**Tasks**:

1. **Route Migration** (2 days)

   - Convert Express.js routes to Workers fetch handlers
   - Adapt middleware to Workers environment
   - Update authentication middleware
   - Convert document upload to use Workers

2. **Environment Setup** (1 day)

   - Configure `wrangler.toml` with environment variables
   - Set up Workers secrets
   - Configure KV storage if needed

3. **Testing** (1 day)
   - Local development testing with `wrangler dev`
   - API endpoint verification
   - Database connection testing

### Phase 3: Frontend Migration (2 days)

**Dependencies**: Phase 2  
**Deliverables**:

- [ ] React app configured for Cloudflare Pages
- [ ] API endpoints updated to Workers URLs
- [ ] Build process optimized
- [ ] Local testing completed

**Tasks**:

1. **Configuration Update** (1 day)

   - Update API base URLs
   - Configure build process for Pages
   - Update environment variables

2. **Testing & Optimization** (1 day)
   - Local build testing
   - Performance optimization
   - Responsive design verification

### Phase 4: Deployment & Testing (2-3 days)

**Dependencies**: Phase 2, 3  
**Deliverables**:

- [ ] Workers backend deployed
- [ ] Pages frontend deployed
- [ ] End-to-end testing completed
- [ ] Performance monitoring setup

**Tasks**:

1. **Workers Deployment** (1 day)

   - Deploy backend: `wrangler deploy`
   - Configure custom domain (optional)
   - Set up environment variables

2. **Pages Deployment** (1 day)

   - Deploy frontend: `wrangler pages deploy`
   - Configure custom domain
   - Set up automatic deployments

3. **Integration Testing** (1 day)
   - End-to-end functionality testing
   - Performance testing
   - Error handling verification

### Phase 5: Cutover & Monitoring (1 day)

**Dependencies**: Phase 4  
**Deliverables**:

- [ ] DNS switched to Cloudflare
- [ ] Railway service decommissioned
- [ ] Vercel service decommissioned
- [ ] Monitoring dashboard setup

**Tasks**:

1. **DNS Cutover**

   - Update DNS records
   - Verify SSL certificates
   - Test all functionality

2. **Service Cleanup**

   - Disable Railway deployment
   - Disable Vercel deployment
   - Cancel unused services

3. **Monitoring Setup**
   - Configure Workers analytics
   - Set up error tracking
   - Performance monitoring

## 🔧 Technical Implementation Details

### Workers API Structure

```typescript
// src/worker.ts
export default {
  async fetch(
    request: Request,
    env: Env,
    ctx: ExecutionContext
  ): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    // Route handling
    if (path.startsWith("/api/auth")) {
      return handleAuth(request, env);
    }
    if (path.startsWith("/api/documents")) {
      return handleDocuments(request, env);
    }
    // ... other routes

    return new Response("Not Found", { status: 404 });
  },
};
```

### File Upload Adaptation

```typescript
// Convert Express.js multer to Workers FormData
async function handleFileUpload(request: Request): Promise<Response> {
  const formData = await request.formData();
  const file = formData.get("document") as File;

  if (!file) {
    return new Response(JSON.stringify({ error: "No file uploaded" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  const buffer = await file.arrayBuffer();
  const uint8Array = new Uint8Array(buffer);

  // Process file and upload to Supabase Storage
  // ... processing logic
}
```

### Wrangler Configuration

```toml
# wrangler.toml
name = "chewyai-api"
main = "src/worker.ts"
compatibility_date = "2024-01-01"

[env.production]
vars = { NODE_ENV = "production" }

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
```

## 🚨 Migration Risks & Mitigation

### Risk 1: Memory Limitations

**Risk**: Workers limited to 128MB RAM  
**Mitigation**:

- Optimize file processing to stream data
- Use chunked processing for large documents
- Implement file size limits (already 10MB)

### Risk 2: Execution Time Limits

**Risk**: Workers timeout at 30 seconds  
**Mitigation**:

- AI generation typically takes 5-15 seconds
- Implement progress tracking for long operations
- Use async patterns for heavy processing

### Risk 3: API Compatibility

**Risk**: Express.js patterns may not work in Workers  
**Mitigation**:

- Thorough testing of all endpoints
- Gradual migration with parallel testing
- Rollback plan to Railway if needed

## 📈 Success Metrics

### Performance Goals

- [ ] Cold start time < 100ms (vs current ~30s)
- [ ] API response time < 500ms globally
- [ ] 99.9% uptime (vs current ~99.5%)
- [ ] Global latency < 100ms

### Cost Goals

- [ ] Maintain similar monthly costs ($5-15)
- [ ] Eliminate overage charges
- [ ] Predictable scaling costs

### User Experience Goals

- [ ] Faster initial page loads
- [ ] No more "app sleeping" issues
- [ ] Consistent global performance

## 🔄 Rollback Plan

If issues arise during migration:

1. **Immediate Rollback** (< 1 hour)

   - Revert DNS to original services
   - Re-enable Railway/Vercel deployments

2. **Data Consistency**

   - Database remains unchanged (Supabase)
   - No data migration required

3. **Service Restoration**
   - Railway backend: automatic restart
   - Vercel frontend: automatic deployment

## 🎯 Post-Migration Optimization

### Week 1: Performance Tuning

- Monitor Workers performance metrics
- Optimize cold start performance
- Fine-tune caching strategies

### Week 2: Cost Optimization

- Analyze actual usage patterns
- Optimize Workers execution time
- Consider KV storage for caching

### Month 1: Advanced Features

- Implement Workers KV for session caching
- Add Cloudflare Analytics integration
- Optimize global edge performance

## 📝 Documentation Updates Required

### Files to Update:

1. `README.md` - Deployment instructions
2. `package.json` - Build scripts
3. `DEPLOYMENT.md` - New deployment guide
4. `ai_docs/current-state.md` - Architecture update
5. All PRD files - Technology stack updates

### New Files to Create:

1. `wrangler.toml` - Workers configuration
2. `src/worker.ts` - Main Workers entry point
3. `scripts/deploy-cloudflare.js` - Deployment automation
4. `docs/cloudflare-setup.md` - Setup guide

---

**Estimated Total Effort**: 8-10 days  
**Risk Level**: Medium (well-tested migration path)  
**ROI**: High (significant performance improvement)  
**Recommended Start**: After current feature freeze
