# ChewyAI - AI-Powered Study Materials Platform

Transform your documents into interactive flashcards and quizzes using advanced AI technology.

## 🚀 Features

- **Document Upload**: Support for PDF, DOCX, TXT, and PPTX files
- **AI Generation**: Create flashcards and quizzes from your documents
- **Interactive Study**: Keyboard navigation, progress tracking, and flagging
- **Credit System**: Flexible pricing with Stripe integration
- **Dark Theme**: Modern, accessible interface design

## 🏗️ Project Structure

```
chewy-ai/
├── frontend/          # React + TypeScript + Vite
├── backend/           # Express.js + TypeScript
├── shared/            # Shared type definitions
├── ai_docs/           # Project documentation
└── package.json       # Workspace configuration
```

## 🛠️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **TailwindCSS** for styling with dark theme
- **Zustand** for state management
- **React Query** for server state
- **Framer Motion** for animations
- **React Router** for navigation

### Backend
- **Express.js** with TypeScript
- **Supabase** for database and authentication
- **Stripe** for payment processing
- **OpenRouter** for AI integration
- **Multer** for file uploads

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ and npm 8+
- Supabase account (for database)
- Stripe account (for payments)
- OpenRouter account (for AI)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Chewy42/chewy-ai-one-shot.git
   cd chewy-ai-one-shot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your configuration
   ```

4. **Start development servers**
   ```bash
   npm run dev
   ```

   This starts both frontend (http://localhost:3000) and backend (http://localhost:4000)

### Development Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both frontend and backend for production
- `npm run dev:frontend` - Start only frontend development server
- `npm run dev:backend` - Start only backend development server
- `npm run lint` - Run linting for both frontend and backend
- `npm run test` - Run tests for both frontend and backend

## 📋 Development Phases

- [x] **Phase 1**: Foundation Setup ✅
- [x] **Phase 2**: Database Schema & Security ✅
- [x] **Phase 3**: Authentication System ✅
- [ ] **Phase 4**: Document Processing System
- [ ] **Phase 5**: AI Integration & Content Generation
- [ ] **Phase 6**: Study Interface & Flashcards
- [ ] **Phase 7**: Credit System & Stripe Integration
- [ ] **Phase 8**: Advanced Features & Polish

## 🔧 Configuration

### Frontend Build
The frontend builds into `backend/public/` for seamless deployment as a single application.

### API Proxy
Development server proxies `/api/*` requests to the backend server.

### Environment Variables
See `backend/.env.example` for required environment variables.

## 📚 Documentation

- [Project Requirements](ai_docs/chewyai_docs/ChewyAI_Complete_PRD_v3.md)
- [Functional Specification](ai_docs/chewyai_docs/ChewyAI_Functional_Spec.md)
- [Development Workflow](ai_docs/prime-context-prompt.md)
- [Task Specifications](ai_docs/chewyai_spec/)

## 🤝 Contributing

1. Follow the Git workflow defined in `ai_docs/prime-context-prompt.md`
2. Create feature branches from `staging`
3. Ensure all tests pass before creating pull requests
4. Follow TypeScript and ESLint configurations

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For questions or issues, please refer to the documentation in the `ai_docs/` directory or create an issue in the repository.
