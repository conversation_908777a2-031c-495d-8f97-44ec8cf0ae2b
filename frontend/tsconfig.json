{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es6", "es2017"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noUnusedLocals": true, "noUnusedParameters": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/shared/*": ["./src/shared/*"]}}, "include": ["src"]}