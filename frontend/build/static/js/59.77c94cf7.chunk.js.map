{"version": 3, "file": "static/js/59.77c94cf7.chunk.js", "mappings": "8LAoBA,MAAMA,EAAqB,CACzB,CACEC,GAAI,IACJC,SAAU,kBACVC,SAAU,qCACVC,OAAQ,gMAEV,CACEH,GAAI,IACJC,SAAU,YACVC,SAAU,mCACVC,OAAQ,uIAEV,CACEH,GAAI,IACJC,SAAU,aACVC,SAAU,yCACVC,OAAQ,sMAEV,CACEH,GAAI,IACJC,SAAU,UACVC,SAAU,kDACVC,OAAQ,sJAEV,CACEH,GAAI,IACJC,SAAU,UACVC,SAAU,mCACVC,OAAQ,uLAEV,CACEH,GAAI,IACJC,SAAU,kBACVC,SAAU,4CACVC,OAAQ,gKAINC,EAAa,CACjB,CAAEJ,GAAI,kBAAmBK,MAAO,kBAAmBC,KAAMC,EAAAA,KACzD,CAAEP,GAAI,YAAaK,MAAO,YAAaC,KAAME,EAAAA,KAC7C,CAAER,GAAI,aAAcK,MAAO,aAAcC,KAAMG,EAAAA,KAC/C,CAAET,GAAI,UAAWK,MAAO,UAAWC,KAAMI,EAAAA,KACzC,CAAEV,GAAI,UAAWK,MAAO,oBAAqBC,KAAMK,EAAAA,MAGxCC,EAAqBA,KAAO,IAADC,EAAAC,EACtC,MAAOC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAiB,oBAC1DC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAwB,MAExDG,EAAerB,EAAQsB,OAAOC,GAAOA,EAAIrB,WAAac,GAM5D,OACEQ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAAMT,SAAA,EAE9BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qCAAoCC,SAAC,oBACnDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0CAAyCC,SAAC,qFAM3DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,gBACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBrB,EAAW+B,IAAKlC,IACf,MAAMmC,EAAOnC,EAASK,KAChB+B,EAAWtB,IAAqBd,EAASD,GAE/C,OACE0B,EAAAA,EAAAA,MAAA,UAEEY,QAASA,IAAMtB,EAAoBf,EAASD,IAC5CwB,UAAS,6KAAAe,OAGLF,EACE,kEACA,8DAA6D,4BAEjEZ,SAAA,EAEFF,EAAAA,EAAAA,KAACa,EAAI,CAACZ,UAAU,aAChBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAExB,EAASI,UAZnCJ,EAASD,YAoBxB0B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2EAA0EC,SAAA,EACvFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,qBACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,mEAG1CC,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0IAAyIC,SAAA,EACzJF,EAAAA,EAAAA,KAACZ,EAAAA,IAAM,CAACa,UAAU,aAClBD,EAAAA,EAAAA,KAAA,QAAAE,SAAM,qBACNF,EAAAA,EAAAA,KAACiB,EAAAA,IAAc,CAAChB,UAAU,sBAMhCD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGW,EAAG,IAC1BT,QAAS,CAAEF,QAAS,EAAGW,EAAG,GAC1BR,WAAY,CAAEC,SAAU,IACxBV,UAAU,kEAAiEC,SAAA,EAE3EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qCAAoCC,SAAA,EACjDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gCAA+BC,SAAA,CACS,QADTZ,EAC1CT,EAAWsC,KAAKC,GAAOA,EAAI3C,KAAOe,UAAiB,IAAAF,OAAA,EAAnDA,EAAqDR,MAAM,WAE9DqB,EAAAA,EAAAA,MAAA,KAAGF,UAAU,qBAAoBC,SAAA,CAAC,oCACqD,QAApDX,EAACV,EAAWsC,KAAKC,GAAOA,EAAI3C,KAAOe,UAAiB,IAAAD,OAAA,EAAnDA,EAAqDT,MAAMuC,qBAIjGlB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBL,EAAae,IAAI,CAACb,EAAKuB,KACtBnB,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKY,MAAe,GAARD,GACpCrB,UAAU,0DAAyDC,SAAA,EAEnEC,EAAAA,EAAAA,MAAA,UACEY,QAASA,KAAMS,OA/FlB/C,EA+F4BsB,EAAItB,QA9FjDmB,EAAeD,IAAgBlB,EAAK,KAAOA,GAD1BA,OAgGGwB,UAAU,wGAAuGC,SAAA,EAEjHF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,8BAA6BC,SAAEH,EAAIpB,WAClDgB,IAAgBI,EAAItB,IACnBuB,EAAAA,EAAAA,KAACyB,EAAAA,IAAa,CAACxB,UAAU,yCAEzBD,EAAAA,EAAAA,KAAC0B,EAAAA,IAAc,CAACzB,UAAU,4CAI9BD,EAAAA,EAAAA,KAACI,EAAAA,EAAOC,IAAG,CACTC,SAAS,EACTG,QAAS,CACPkB,OAAQhC,IAAgBI,EAAItB,GAAK,OAAS,EAC1C8B,QAASZ,IAAgBI,EAAItB,GAAK,EAAI,GAExCiC,WAAY,CAAEC,SAAU,IACxBV,UAAU,kBAAiBC,UAE3BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0CAAyCC,UACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gCAA+BC,SAAEH,EAAInB,eA5BjDmB,EAAItB,OAmCU,IAAxBoB,EAAa+B,SACZzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAACb,EAAAA,IAAoB,CAACc,UAAU,0CAChCD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yCAAwCC,SAAC,uBACvDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,4EA1D9BV,SAqEXW,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFC,SAAA,EAC9FF,EAAAA,EAAAA,KAACd,EAAAA,IAAU,CAACe,UAAU,6CACtBD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,uBACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,wDAG1CF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,sDAAqDC,SAAC,2BAK1EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFC,SAAA,EAC9FF,EAAAA,EAAAA,KAACb,EAAAA,IAAoB,CAACc,UAAU,6CAChCD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,oBACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,4DAG1CF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,sDAAqDC,SAAC,2BAK1EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFC,SAAA,EAC9FF,EAAAA,EAAAA,KAACf,EAAAA,IAAc,CAACgB,UAAU,6CAC1BD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,sBACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,4CAG1CF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,sDAAqDC,SAAC,kC", "sources": ["pages/HelpPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { \r\n  HiQuestionMarkCircle, \r\n  HiSparkles, \r\n  HiDocumentText, \r\n  HiAcademicCap,\r\n  HiChevronDown,\r\n  HiChevronRight,\r\n  HiMail,\r\n  HiExternalLink\r\n} from 'react-icons/hi';\r\n\r\ninterface FAQItem {\r\n  id: string;\r\n  question: string;\r\n  answer: string;\r\n  category: 'getting-started' | 'flashcards' | 'quizzes' | 'documents' | 'billing';\r\n}\r\n\r\nconst faqData: FAQItem[] = [\r\n  {\r\n    id: '1',\r\n    category: 'getting-started',\r\n    question: 'How do I get started with ChewyAI?',\r\n    answer: 'Start by uploading your documents in the Documents section, then use our AI-powered tools to generate flashcards or quizzes from your content. You can also create study materials manually.'\r\n  },\r\n  {\r\n    id: '2',\r\n    category: 'documents',\r\n    question: 'What file formats are supported?',\r\n    answer: 'ChewyAI supports PDF, DOCX, TXT, and PPTX files. Make sure your documents contain readable text for the best AI generation results.'\r\n  },\r\n  {\r\n    id: '3',\r\n    category: 'flashcards',\r\n    question: 'How does AI flashcard generation work?',\r\n    answer: 'Our AI analyzes your uploaded documents and creates relevant flashcards with questions and answers. You can specify the number of cards to generate and provide custom prompts for better results.'\r\n  },\r\n  {\r\n    id: '4',\r\n    category: 'quizzes',\r\n    question: 'Can I create different types of quiz questions?',\r\n    answer: 'Yes! ChewyAI supports multiple choice, true/false, and short answer questions. You can specify the question types when generating quizzes with AI.'\r\n  },\r\n  {\r\n    id: '5',\r\n    category: 'billing',\r\n    question: 'How does the credit system work?',\r\n    answer: 'Credits are used for AI-powered features like generating flashcards and quizzes. Manual creation is always free. You can purchase credit packages or subscribe for unlimited usage.'\r\n  },\r\n  {\r\n    id: '6',\r\n    category: 'getting-started',\r\n    question: 'How do I study with my created materials?',\r\n    answer: 'Navigate to your study sets from the dashboard and choose your preferred study mode. Use keyboard shortcuts for efficient navigation during study sessions.'\r\n  }\r\n];\r\n\r\nconst categories = [\r\n  { id: 'getting-started', label: 'Getting Started', icon: HiAcademicCap },\r\n  { id: 'documents', label: 'Documents', icon: HiDocumentText },\r\n  { id: 'flashcards', label: 'Flashcards', icon: HiSparkles },\r\n  { id: 'quizzes', label: 'Quizzes', icon: HiQuestionMarkCircle },\r\n  { id: 'billing', label: 'Billing & Credits', icon: HiMail }\r\n];\r\n\r\nexport const HelpPage: React.FC = () => {\r\n  const [selectedCategory, setSelectedCategory] = useState<string>('getting-started');\r\n  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);\r\n\r\n  const filteredFAQs = faqData.filter(faq => faq.category === selectedCategory);\r\n\r\n  const toggleFAQ = (id: string) => {\r\n    setExpandedFAQ(expandedFAQ === id ? null : id);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary text-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-12\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n          >\r\n            <h1 className=\"text-4xl font-bold text-white mb-4\">Help & Support</h1>\r\n            <p className=\"text-xl text-gray-400 max-w-2xl mx-auto\">\r\n              Find answers to common questions and learn how to make the most of ChewyAI\r\n            </p>\r\n          </motion.div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          {/* Category Sidebar */}\r\n          <div className=\"lg:col-span-1\">\r\n            <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n              <h2 className=\"text-lg font-semibold text-white mb-4\">Categories</h2>\r\n              <nav className=\"space-y-2\">\r\n                {categories.map((category) => {\r\n                  const Icon = category.icon;\r\n                  const isActive = selectedCategory === category.id;\r\n                  \r\n                  return (\r\n                    <button\r\n                      key={category.id}\r\n                      onClick={() => setSelectedCategory(category.id)}\r\n                      className={`\r\n                        w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left\r\n                        transition-all duration-200\r\n                        ${isActive \r\n                          ? 'bg-primary-500/20 text-primary-400 border border-primary-500/30' \r\n                          : 'text-gray-300 hover:bg-background-tertiary hover:text-white'\r\n                        }\r\n                      `}\r\n                    >\r\n                      <Icon className=\"w-5 h-5\" />\r\n                      <span className=\"font-medium\">{category.label}</span>\r\n                    </button>\r\n                  );\r\n                })}\r\n              </nav>\r\n            </div>\r\n\r\n            {/* Contact Support */}\r\n            <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary mt-6\">\r\n              <h3 className=\"text-lg font-semibold text-white mb-3\">Need More Help?</h3>\r\n              <p className=\"text-gray-400 text-sm mb-4\">\r\n                Can't find what you're looking for? Contact our support team.\r\n              </p>\r\n              <button className=\"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors\">\r\n                <HiMail className=\"w-4 h-4\" />\r\n                <span>Contact Support</span>\r\n                <HiExternalLink className=\"w-4 h-4\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* FAQ Content */}\r\n          <div className=\"lg:col-span-3\">\r\n            <motion.div\r\n              key={selectedCategory}\r\n              initial={{ opacity: 0, x: 20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.3 }}\r\n              className=\"bg-background-secondary rounded-lg border border-border-primary\"\r\n            >\r\n              <div className=\"p-6 border-b border-border-primary\">\r\n                <h2 className=\"text-2xl font-bold text-white\">\r\n                  {categories.find(cat => cat.id === selectedCategory)?.label} FAQ\r\n                </h2>\r\n                <p className=\"text-gray-400 mt-2\">\r\n                  Frequently asked questions about {categories.find(cat => cat.id === selectedCategory)?.label.toLowerCase()}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"p-6\">\r\n                <div className=\"space-y-4\">\r\n                  {filteredFAQs.map((faq, index) => (\r\n                    <motion.div\r\n                      key={faq.id}\r\n                      initial={{ opacity: 0, y: 10 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ duration: 0.3, delay: index * 0.1 }}\r\n                      className=\"border border-border-primary rounded-lg overflow-hidden\"\r\n                    >\r\n                      <button\r\n                        onClick={() => toggleFAQ(faq.id)}\r\n                        className=\"w-full flex items-center justify-between p-4 text-left hover:bg-background-tertiary transition-colors\"\r\n                      >\r\n                        <span className=\"font-medium text-white pr-4\">{faq.question}</span>\r\n                        {expandedFAQ === faq.id ? (\r\n                          <HiChevronDown className=\"w-5 h-5 text-gray-400 flex-shrink-0\" />\r\n                        ) : (\r\n                          <HiChevronRight className=\"w-5 h-5 text-gray-400 flex-shrink-0\" />\r\n                        )}\r\n                      </button>\r\n                      \r\n                      <motion.div\r\n                        initial={false}\r\n                        animate={{\r\n                          height: expandedFAQ === faq.id ? 'auto' : 0,\r\n                          opacity: expandedFAQ === faq.id ? 1 : 0\r\n                        }}\r\n                        transition={{ duration: 0.3 }}\r\n                        className=\"overflow-hidden\"\r\n                      >\r\n                        <div className=\"p-4 pt-0 border-t border-border-primary\">\r\n                          <p className=\"text-gray-300 leading-relaxed\">{faq.answer}</p>\r\n                        </div>\r\n                      </motion.div>\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n\r\n                {filteredFAQs.length === 0 && (\r\n                  <div className=\"text-center py-12\">\r\n                    <HiQuestionMarkCircle className=\"w-16 h-16 text-gray-500 mx-auto mb-4\" />\r\n                    <h3 className=\"text-lg font-medium text-gray-400 mb-2\">No FAQs Available</h3>\r\n                    <p className=\"text-gray-500\">\r\n                      We're working on adding more helpful content for this category.\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quick Links */}\r\n        <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n          <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary text-center\">\r\n            <HiSparkles className=\"w-12 h-12 text-primary-400 mx-auto mb-4\" />\r\n            <h3 className=\"text-lg font-semibold text-white mb-2\">Create Flashcards</h3>\r\n            <p className=\"text-gray-400 text-sm mb-4\">\r\n              Generate AI-powered flashcards from your documents\r\n            </p>\r\n            <button className=\"text-primary-400 hover:text-primary-300 font-medium\">\r\n              Get Started →\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary text-center\">\r\n            <HiQuestionMarkCircle className=\"w-12 h-12 text-primary-400 mx-auto mb-4\" />\r\n            <h3 className=\"text-lg font-semibold text-white mb-2\">Create Quizzes</h3>\r\n            <p className=\"text-gray-400 text-sm mb-4\">\r\n              Build interactive quizzes with multiple question types\r\n            </p>\r\n            <button className=\"text-primary-400 hover:text-primary-300 font-medium\">\r\n              Get Started →\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary text-center\">\r\n            <HiDocumentText className=\"w-12 h-12 text-primary-400 mx-auto mb-4\" />\r\n            <h3 className=\"text-lg font-semibold text-white mb-2\">Upload Documents</h3>\r\n            <p className=\"text-gray-400 text-sm mb-4\">\r\n              Upload and manage your study documents\r\n            </p>\r\n            <button className=\"text-primary-400 hover:text-primary-300 font-medium\">\r\n              Get Started →\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["faqData", "id", "category", "question", "answer", "categories", "label", "icon", "HiAcademicCap", "HiDocumentText", "HiSparkles", "HiQuestionMarkCircle", "HiMail", "HelpPage", "_categories$find", "_categories$find2", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "useState", "expandedFAQ", "setExpandedFAQ", "filteredFAQs", "filter", "faq", "_jsx", "className", "children", "_jsxs", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "map", "Icon", "isActive", "onClick", "concat", "HiExternalLink", "x", "find", "cat", "toLowerCase", "index", "delay", "toggleFAQ", "HiChevronDown", "HiChevronRight", "height", "length"], "sourceRoot": ""}