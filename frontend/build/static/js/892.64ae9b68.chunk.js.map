{"version": 3, "file": "static/js/892.64ae9b68.chunk.js", "mappings": "oJAqBO,MAAMA,GAAmBC,E,QAAAA,IAAuBC,IAAG,CACxDC,UAAW,GACXC,kBAAmB,IAAIC,IACvBC,WAAW,EACXC,eAAgB,CAAC,EAEjBC,eAAgBC,UACdP,EAAI,CAAEI,WAAW,IAEjB,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,iBAAkB,CAC7CC,QAAS,CACPC,cAAc,UAADC,OAAYP,MAI7B,IAAKG,EAASK,GACZ,MAAM,IAAIC,MAAM,6BAGlB,MAAMC,QAAeP,EAASQ,OAE9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOG,OAFvBrB,EAAI,CAAEC,UAAWiB,EAAOI,KAAMlB,WAAW,GAI7C,CAAE,MAAOiB,GAGP,MAFAE,QAAQF,MAAM,yBAA0BA,GACxCrB,EAAI,CAAEI,WAAW,IACXiB,CACR,GAGFG,eAAgBjB,UACd,MAAMkB,EAAW,IAAIC,SACrBD,EAASE,OAAO,WAAYC,GAE5B,IACE,MAAMpB,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,wBAAyB,CACpDiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAADC,OAAYP,IAE3BsB,KAAML,IAGR,IAAKd,EAASK,GAAI,CAChB,MAAMe,QAAoBpB,EAASQ,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,gBACvC,CAEA,MAAMH,QAAeP,EAASQ,OAE9B,GAAID,EAAOE,QAOT,OALApB,EAAKgC,IAAK,CACR/B,UAAW,CAACiB,EAAOI,QAASU,EAAM/B,WAClCI,gBAAc4B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAM3B,gBAAc,IAAE,CAACuB,EAAKM,MAAO,SAGnDhB,EAAOI,KAEd,MAAM,IAAIL,MAAMC,EAAOG,MAE3B,CAAE,MAAOA,GAEP,MADAE,QAAQF,MAAM,yBAA0BA,GAClCA,CACR,GAGFc,eAAgB5B,UACd,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADG,OAAmBqB,GAAM,CACnDP,OAAQ,SACRhB,QAAS,CACPC,cAAc,UAADC,OAAYP,MAI7B,IAAKG,EAASK,GAAI,CAChB,MAAMe,QAAoBpB,EAASQ,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,gBACvC,CAGArB,EAAKgC,IAAK,CACR/B,UAAW+B,EAAM/B,UAAUoC,OAAQC,GAAQA,EAAIF,KAAOA,GACtDlC,kBAAmB,IAAIC,IACrB,IAAI6B,EAAM9B,mBAAmBmC,OAAQE,GAAUA,IAAUH,MAG/D,CAAE,MAAOf,GAEP,MADAE,QAAQF,MAAM,yBAA0BA,GAClCA,CACR,GAGFmB,gBAAiBjC,UACf,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAADG,OACC0B,mBAAmBC,IAC9C,CACE7B,QAAS,CACPC,cAAc,UAADC,OAAYP,MAK/B,IAAKG,EAASK,GACZ,MAAM,IAAIC,MAAM,iBAGlB,MAAMC,QAAeP,EAASQ,OAC9B,OAAOD,EAAOE,QAAUF,EAAOI,KAAO,EACxC,CAAE,MAAOD,GAEP,OADAE,QAAQF,MAAM,0BAA2BA,GAClC,EACT,GAGFsB,YAAapC,UACX,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADG,OAAmBqB,GAAM,CACnDvB,QAAS,CACPC,cAAc,UAADC,OAAYP,MAI7B,IAAKG,EAASK,GACZ,OAAO,KAGT,MAAME,QAAeP,EAASQ,OAC9B,OAAOD,EAAOE,QAAUF,EAAOI,KAAO,IACxC,CAAE,MAAOD,GAEP,OADAE,QAAQF,MAAM,sBAAuBA,GAC9B,IACT,GAGFuB,wBAA0BR,IACxBpC,EAAKgC,IACH,MAAMa,EAAe,IAAI1C,IAAI6B,EAAM9B,mBAMnC,OALI2C,EAAaC,IAAIV,GACnBS,EAAaE,OAAOX,GAEpBS,EAAaG,IAAIZ,GAEZ,CAAElC,kBAAmB2C,MAIhCI,eAAgBA,KACdjD,EAAI,CAAEE,kBAAmB,IAAIC,OAG/B+C,UAAWA,KACTlD,EAAKgC,IAAK,CACR9B,kBAAmB,IAAIC,IAAI6B,EAAM/B,UAAUkD,IAAKb,GAAQA,EAAIF,SAIhEgB,kBAAmBA,CAACC,EAAkBC,KACpCtD,EAAKgC,IAAK,CACR3B,gBAAc4B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAM3B,gBAAc,IAAE,CAACgD,GAAWC,U,2HC1LtD,MAAMC,EAA4BA,KACvC,MAAOC,EAAaC,IAAkBC,EAAAA,EAAAA,WAAS,IACxCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAmB,KACpDG,EAAeC,IAAoBJ,EAAAA,EAAAA,UAAmB,KACvD,eAAElC,EAAc,kBAAE4B,IAAsBtD,EAAAA,EAAAA,KACxCiE,GAAWC,EAAAA,EAAAA,MAEXC,GAASC,EAAAA,EAAAA,aAAY3D,UACzBkD,GAAe,GACfG,EAAgB,IAChBE,EAAiB,IAEjB,MAAMK,EAAmB,GACnBC,EAAsB,GAE5B,IAAK,MAAMxC,KAAQyC,EACjB,IAEE,GAAIzC,EAAK0C,KAAO,SAAkB,CAChCH,EAAOI,KAAK,GAADxD,OAAIa,EAAKM,KAAI,mCACxB,QACF,CAUA,IAPqB,CACnB,kBACA,0EACA,aACA,6EAGgBsC,SAAS5C,EAAK6C,MAAO,CACrCN,EAAOI,KAAK,GAADxD,OAAIa,EAAKM,KAAI,0EACxB,QACF,CAGAkB,EAAkBxB,EAAKM,KAAM,SAGvBV,EAAeI,GAGrBwB,EAAkBxB,EAAKM,KAAM,KAC7BkC,EAAUG,KAAK3C,EAAKM,KACtB,CAAE,MAAOb,GACP8C,EAAOI,KAAK,GAADxD,OAAIa,EAAKM,KAAI,MAAAnB,OAAKM,aAAiBJ,MAAQI,EAAMqD,QAAU,iBACxE,CAGFd,EAAgBO,GAChBL,EAAiBM,GACjBX,GAAe,GAGXW,EAAUO,OAAS,GACrBC,WAAW,IAAMd,EAAiB,IAAK,MAExC,CAACtC,EAAgB4B,KAEd,aAAEyB,EAAY,cAAEC,EAAa,aAAEC,IAAiBC,EAAAA,EAAAA,IAAY,CAChEf,SACAgB,OAAQ,CACN,kBAAmB,CAAC,QACpB,0EAA2E,CAAC,SAC5E,aAAc,CAAC,QACf,4EAA6E,CAAC,UAEhFC,UAAU,EACVC,SAAU,GACVC,SAAU5B,IAGZ,OACE6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EAEnBF,EAAAA,EAAAA,MAAA,OAAApD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACM4C,KAAc,IAClBS,UAAS,wHAAAvE,OAELgE,EACE,oDACA,kEAAiE,gBAAAhE,OAEnEyC,EAAc,gCAAkC,GAAE,oDAEpD+B,SAAA,EAEFC,EAAAA,EAAAA,KAAA,SAAAvD,EAAAA,EAAAA,GAAA,GAAW6C,OAEXO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBC,UAClCC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,qEAAAvE,OAEVgE,EAAe,oBAAsB,iBAAgB,kBACvDQ,UACAC,EAAAA,EAAAA,KAAA,OACEF,UAAS,+BAAAvE,OACPgE,EAAe,mBAAqB,iBAEtCU,OAAO,eACPC,KAAK,OACLC,QAAQ,YAAWJ,UAEnBC,EAAAA,EAAAA,KAAA,QACEI,EAAE,yLACFC,YAAa,EACbC,cAAc,QACdC,eAAe,gBAOtBhB,GACCM,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8CAA6CC,SAAC,8BAG5DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gBAAeC,SAAC,qCAK/BF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qCAAoCC,SAAC,2BAGnDF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BC,SAAA,CAAC,6BACb,KAC3BC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,0EAAyEC,SAAC,yBAI5FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAC,4DAOvCR,IAAiBvB,IACjB6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mEAAkEC,SAAA,EAC/EC,EAAAA,EAAAA,KAAA,UACEQ,QAAUC,IACRA,EAAEC,kBACFnC,EAAS,eAEXuB,UAAU,yFAAwFC,SACnG,4CAGDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BC,SAAC,YAChDC,EAAAA,EAAAA,KAAA,UACEQ,QAAUC,IACRA,EAAEC,kBACFnC,EAAS,sBAEXuB,UAAU,yFAAwFC,SACnG,4CASR/B,IACC6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qEAAoEC,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qEACfE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCAAmCC,SAAC,2BAEnDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,UAClDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAAgDa,MAAO,CAAEC,MAAO,cAMpFvC,EAAcc,OAAS,IACtBU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAyBI,KAAK,eAAeC,QAAQ,YAAWJ,UAC7EC,EAAAA,EAAAA,KAAA,QAAMa,SAAS,UAAUT,EAAE,wIAAwIU,SAAS,eAE9Kd,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6BAA4BC,SAAC,+BAE7CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mCAAkCC,SAC7C1B,EAAcV,IAAI,CAACoD,EAAUC,KAC5BnB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAgB,UAAGgB,IAAVC,SAOhB7C,EAAagB,OAAS,IACrBU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0DAAyDC,SAAA,EACtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAuBI,KAAK,eAAeC,QAAQ,YAAWJ,UAC3EC,EAAAA,EAAAA,KAAA,QAAMa,SAAS,UAAUT,EAAE,0NAA0NU,SAAS,eAEhQd,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2BAA0BC,SAAC,uBAE3CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iCAAgCC,SAC3C5B,EAAaR,IAAI,CAAC9B,EAAOmF,KACxBnB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAgB,UAAGlE,IAAVmF,aCjNVC,GAAsBC,EAAAA,EAAAA,MAAK,KACtC,MAAM3C,GAAWC,EAAAA,EAAAA,OACV2C,EAAWC,IAAgBlD,EAAAA,EAAAA,UAAqB,KAChDtD,EAAWyG,IAAgBnD,EAAAA,EAAAA,WAAS,IACpCrC,EAAOyF,IAAYpD,EAAAA,EAAAA,UAAwB,OAElDqD,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAAiBzG,UACrB,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAAmB,CAC9CC,QAAS,CACP,cAAgB,UAADE,OAAYP,MAI/B,IAAKG,EAASK,GACZ,MAAM,IAAIC,MAAM,8BAGlB,MAAMC,QAAeP,EAASQ,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOG,OAAS,8BAFhCuF,EAAa1F,EAAOI,KAIxB,CAAE,MAAOD,GACPyF,EAASzF,EAAMqD,QACjB,CAAC,QACCmC,GAAa,EACf,GAGII,EAAcC,GACX,IAAIC,KAAKD,GAAYE,qBAG9B,OACE5B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gCAA+BC,SAAC,qBAC9CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAAC,kEAGpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACLrB,QAASA,IAAMjC,EAAS,qBACxBuD,QAAQ,UAAS/B,SAClB,2BAOLC,EAAAA,EAAAA,KAACjC,EAAe,KAGhB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wCAAuCC,SAAC,oBAErDnF,GACCiF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qEACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qBAAoBC,SAAC,6BAErClE,GACFgE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,CAAC,UAAQlE,MAC3CmE,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CAACrB,QAASgB,EAAgBM,QAAQ,YAAW/B,SAAC,iBAIhC,IAArBoB,EAAUhC,QACZU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAAoBC,SAAC,yBACpCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAAC,8DAClCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBC,UAClCC,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACLrB,QAASA,IAAMjC,EAAS,qBACxBuD,QAAQ,UAAS/B,SAClB,2BAMLC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEoB,EAAUxD,IAAKoE,IACdlC,EAAAA,EAAAA,MAAA,OAEEC,UAAU,sFACVU,QAASA,IAAMjC,EAAS,eAADhD,OAAgBwG,EAASnF,KAAMmD,SAAA,EAEtDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0CAAyCC,SACpDgC,EAASrF,QAEZsD,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+DAA8DC,SAC3EgC,EAAS9C,WAIdY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,YACNC,EAAAA,EAAAA,KAAA,QAAAD,SAAyB,eAAlBgC,EAAS9C,KAAwB8C,EAASC,iBAAmB,EAAID,EAASE,qBAAuB,QAG1GpC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,cACNC,EAAAA,EAAAA,KAAA,QAAAD,SAAO0B,EAAWM,EAASG,iBAG5BH,EAASI,kBACRtC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,mBACNC,EAAAA,EAAAA,KAAA,QAAAD,SAAO0B,EAAWM,EAASI,sBAI9BJ,EAASK,kBACRvC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAC,kBAC1BC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,wBAKZC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,UACjDC,EAAAA,EAAAA,KAAA,OACEQ,QAAUC,IACRA,EAAEC,kBACFnC,EAAS,eAADhD,OAAgBwG,EAASnF,MACjCmD,UAEFC,EAAAA,EAAAA,KAAC6B,EAAAA,EAAM,CACLC,QAAQ,YACRhD,KAAK,KACLgB,UAAU,SAAQC,SACnB,yBAlDAgC,EAASnF,gB", "sources": ["stores/documentStore.ts", "components/dashboard/DashboardUpload.tsx", "components/dashboard/Dashboard.tsx"], "sourcesContent": ["import { create } from \"zustand\";\nimport { DocumentMetadata, DocumentWithContent } from \"../shared/types\";\n\ninterface DocumentState {\n  documents: DocumentMetadata[];\n  selectedDocuments: Set<string>;\n  isLoading: boolean;\n  uploadProgress: { [key: string]: number };\n\n  // Actions\n  fetchDocuments: () => Promise<void>;\n  uploadDocument: (file: File) => Promise<DocumentMetadata>;\n  deleteDocument: (id: string) => Promise<void>;\n  searchDocuments: (query: string) => Promise<DocumentMetadata[]>;\n  getDocument: (id: string) => Promise<DocumentWithContent | null>;\n  toggleDocumentSelection: (id: string) => void;\n  clearSelection: () => void;\n  selectAll: () => void;\n  setUploadProgress: (fileName: string, progress: number) => void;\n}\n\nexport const useDocumentStore = create<DocumentState>((set) => ({\n  documents: [],\n  selectedDocuments: new Set(),\n  isLoading: false,\n  uploadProgress: {},\n\n  fetchDocuments: async () => {\n    set({ isLoading: true });\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/documents\", {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch documents\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        set({ documents: result.data, isLoading: false });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error(\"Fetch documents error:\", error);\n      set({ isLoading: false });\n      throw error;\n    }\n  },\n\n  uploadDocument: async (file: File) => {\n    const formData = new FormData();\n    formData.append(\"document\", file);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/documents/upload\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Upload failed\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        // Add new document to the list\n        set((state) => ({\n          documents: [result.data, ...state.documents],\n          uploadProgress: { ...state.uploadProgress, [file.name]: 100 },\n        }));\n\n        return result.data;\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error(\"Upload document error:\", error);\n      throw error;\n    }\n  },\n\n  deleteDocument: async (id: string) => {\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(`/api/documents/${id}`, {\n        method: \"DELETE\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Delete failed\");\n      }\n\n      // Remove document from the list\n      set((state) => ({\n        documents: state.documents.filter((doc) => doc.id !== id),\n        selectedDocuments: new Set(\n          [...state.selectedDocuments].filter((docId) => docId !== id)\n        ),\n      }));\n    } catch (error) {\n      console.error(\"Delete document error:\", error);\n      throw error;\n    }\n  },\n\n  searchDocuments: async (query: string) => {\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\n        `/api/documents/search?q=${encodeURIComponent(query)}`,\n        {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(\"Search failed\");\n      }\n\n      const result = await response.json();\n      return result.success ? result.data : [];\n    } catch (error) {\n      console.error(\"Search documents error:\", error);\n      return [];\n    }\n  },\n\n  getDocument: async (id: string) => {\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(`/api/documents/${id}`, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        return null;\n      }\n\n      const result = await response.json();\n      return result.success ? result.data : null;\n    } catch (error) {\n      console.error(\"Get document error:\", error);\n      return null;\n    }\n  },\n\n  toggleDocumentSelection: (id: string) => {\n    set((state) => {\n      const newSelection = new Set(state.selectedDocuments);\n      if (newSelection.has(id)) {\n        newSelection.delete(id);\n      } else {\n        newSelection.add(id);\n      }\n      return { selectedDocuments: newSelection };\n    });\n  },\n\n  clearSelection: () => {\n    set({ selectedDocuments: new Set() });\n  },\n\n  selectAll: () => {\n    set((state) => ({\n      selectedDocuments: new Set(state.documents.map((doc) => doc.id)),\n    }));\n  },\n\n  setUploadProgress: (fileName: string, progress: number) => {\n    set((state) => ({\n      uploadProgress: { ...state.uploadProgress, [fileName]: progress },\n    }));\n  },\n}));\n", "import React, { useState, useCallback } from 'react';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport { useDocumentStore } from '../../stores/documentStore';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nexport const DashboardUpload: React.FC = () => {\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [uploadErrors, setUploadErrors] = useState<string[]>([]);\r\n  const [uploadSuccess, setUploadSuccess] = useState<string[]>([]);\r\n  const { uploadDocument, setUploadProgress } = useDocumentStore();\r\n  const navigate = useNavigate();\r\n\r\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\r\n    setIsUploading(true);\r\n    setUploadErrors([]);\r\n    setUploadSuccess([]);\r\n\r\n    const errors: string[] = [];\r\n    const successes: string[] = [];\r\n\r\n    for (const file of acceptedFiles) {\r\n      try {\r\n        // Validate file size (50MB limit)\r\n        if (file.size > 50 * 1024 * 1024) {\r\n          errors.push(`${file.name}: File size exceeds 50MB limit`);\r\n          continue;\r\n        }\r\n\r\n        // Validate file type\r\n        const allowedTypes = [\r\n          'application/pdf',\r\n          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n          'text/plain',\r\n          'application/vnd.openxmlformats-officedocument.presentationml.presentation'\r\n        ];\r\n\r\n        if (!allowedTypes.includes(file.type)) {\r\n          errors.push(`${file.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);\r\n          continue;\r\n        }\r\n\r\n        // Set initial progress\r\n        setUploadProgress(file.name, 0);\r\n\r\n        // Upload file\r\n        await uploadDocument(file);\r\n        \r\n        // Set completion progress\r\n        setUploadProgress(file.name, 100);\r\n        successes.push(file.name);\r\n      } catch (error) {\r\n        errors.push(`${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      }\r\n    }\r\n\r\n    setUploadErrors(errors);\r\n    setUploadSuccess(successes);\r\n    setIsUploading(false);\r\n\r\n    // Auto-clear success messages after 3 seconds\r\n    if (successes.length > 0) {\r\n      setTimeout(() => setUploadSuccess([]), 3000);\r\n    }\r\n  }, [uploadDocument, setUploadProgress]);\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'application/pdf': ['.pdf'],\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\r\n      'text/plain': ['.txt'],\r\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']\r\n    },\r\n    multiple: true,\r\n    maxFiles: 10,\r\n    disabled: isUploading\r\n  });\r\n\r\n  return (\r\n    <div className=\"mb-8\">\r\n      {/* Upload Area */}\r\n      <div\r\n        {...getRootProps()}\r\n        className={`\r\n          border-2 border-dashed rounded-xl p-12 text-center cursor-pointer transition-all duration-200\r\n          ${isDragActive \r\n            ? 'border-primary-500 bg-primary-500/10 scale-[1.02]' \r\n            : 'border-gray-600 hover:border-primary-500 hover:bg-primary-500/5'\r\n          }\r\n          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}\r\n          bg-background-secondary/50\r\n        `}\r\n      >\r\n        <input {...getInputProps()} />\r\n        \r\n        <div className=\"space-y-4\">\r\n          {/* Upload Icon */}\r\n          <div className=\"flex justify-center\">\r\n            <div className={`\r\n              p-4 rounded-full transition-colors\r\n              ${isDragActive ? 'bg-primary-500/20' : 'bg-gray-800/50'}\r\n            `}>\r\n              <svg\r\n                className={`h-16 w-16 transition-colors ${\r\n                  isDragActive ? 'text-primary-400' : 'text-gray-400'\r\n                }`}\r\n                stroke=\"currentColor\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 48 48\"\r\n              >\r\n                <path\r\n                  d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\r\n                  strokeWidth={2}\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Upload Text */}\r\n          {isDragActive ? (\r\n            <div>\r\n              <h3 className=\"text-xl font-semibold text-primary-400 mb-2\">\r\n                Drop your documents here\r\n              </h3>\r\n              <p className=\"text-gray-300\">\r\n                Release to upload your files\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div>\r\n              <h3 className=\"text-2xl font-bold text-white mb-2\">\r\n                Upload Your Documents\r\n              </h3>\r\n              <p className=\"text-lg text-gray-300 mb-2\">\r\n                Drag & drop files here, or{' '}\r\n                <span className=\"text-primary-500 font-semibold hover:text-primary-400 transition-colors\">\r\n                  browse to select\r\n                </span>\r\n              </p>\r\n              <p className=\"text-sm text-gray-500\">\r\n                Supports PDF, DOCX, TXT, PPTX files (max 50MB each)\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Quick Actions */}\r\n          {!isDragActive && !isUploading && (\r\n            <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 pt-4\">\r\n              <button\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  navigate('/documents');\r\n                }}\r\n                className=\"text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2\"\r\n              >\r\n                📄 Manage existing documents\r\n              </button>\r\n              <span className=\"hidden sm:block text-gray-600\">•</span>\r\n              <button\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  navigate('/create-study-set');\r\n                }}\r\n                className=\"text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2\"\r\n              >\r\n                📚 Create study set\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Upload Progress */}\r\n      {isUploading && (\r\n        <div className=\"mt-4 bg-background-secondary rounded-lg p-4 border border-gray-700\">\r\n          <div className=\"flex items-center gap-3 mb-2\">\r\n            <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500\"></div>\r\n            <p className=\"text-sm font-medium text-gray-300\">Uploading files...</p>\r\n          </div>\r\n          <div className=\"w-full bg-gray-700 rounded-full h-2\">\r\n            <div className=\"bg-primary-500 h-2 rounded-full animate-pulse\" style={{ width: '60%' }}></div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Success Messages */}\r\n      {uploadSuccess.length > 0 && (\r\n        <div className=\"mt-4 bg-green-900/20 border border-green-700 rounded-lg p-4\">\r\n          <div className=\"flex items-center gap-2 mb-2\">\r\n            <svg className=\"h-5 w-5 text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n            <h4 className=\"text-green-400 font-medium\">Successfully uploaded:</h4>\r\n          </div>\r\n          <ul className=\"text-sm text-green-300 space-y-1\">\r\n            {uploadSuccess.map((filename, index) => (\r\n              <li key={index}>• {filename}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error Messages */}\r\n      {uploadErrors.length > 0 && (\r\n        <div className=\"mt-4 bg-red-900/20 border border-red-700 rounded-lg p-4\">\r\n          <div className=\"flex items-center gap-2 mb-2\">\r\n            <svg className=\"h-5 w-5 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n            <h4 className=\"text-red-400 font-medium\">Upload errors:</h4>\r\n          </div>\r\n          <ul className=\"text-sm text-red-300 space-y-1\">\r\n            {uploadErrors.map((error, index) => (\r\n              <li key={index}>• {error}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect, useState, memo } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { StudySet } from \"../../../../shared/types\";\r\nimport { Button } from \"../common/Button\";\r\nimport { DashboardUpload } from \"./DashboardUpload\";\r\n\r\nexport const Dashboard: React.FC = memo(() => {\r\n  const navigate = useNavigate();\r\n  const [studySets, setStudySets] = useState<StudySet[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    fetchStudySets();\r\n  }, []);\r\n\r\n  const fetchStudySets = async () => {\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/study-sets', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch study sets');\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setStudySets(result.data);\r\n      } else {\r\n        throw new Error(result.error || 'Failed to fetch study sets');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString();\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary text-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between mb-8\">\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-white\">Your Study Sets</h1>\r\n            <p className=\"text-gray-400 mt-2\">Create, manage, and study your flashcard sets and quizzes</p>\r\n          </div>\r\n\r\n          <div className=\"flex space-x-4\">\r\n            <Button\r\n              onClick={() => navigate('/create-study-set')}\r\n              variant=\"primary\"\r\n            >\r\n              Create Study Set\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Document Upload Section */}\r\n        <DashboardUpload />\r\n\r\n        {/* Study Sets Section */}\r\n        <div className=\"mb-8\">\r\n          <h2 className=\"text-xl font-semibold text-white mb-4\">Your Study Sets</h2>\r\n\r\n          {isLoading ? (\r\n            <div className=\"flex items-center justify-center py-12\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500\"></div>\r\n              <span className=\"ml-3 text-gray-400\">Loading study sets...</span>\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"text-red-400 mb-4\">Error: {error}</div>\r\n              <Button onClick={fetchStudySets} variant=\"secondary\">\r\n                Try Again\r\n              </Button>\r\n            </div>\r\n          ) : studySets.length === 0 ? (\r\n            <div className=\"text-center py-12 bg-gray-800/50 rounded-lg\">\r\n              <div className=\"text-gray-400 mb-4\">No study sets found</div>\r\n              <p className=\"text-gray-500 mb-6\">Create your first study set to get started with studying</p>\r\n              <div className=\"flex justify-center\">\r\n                <Button\r\n                  onClick={() => navigate('/create-study-set')}\r\n                  variant=\"primary\"\r\n                >\r\n                  Create Study Set\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {studySets.map((studySet) => (\r\n                <div\r\n                  key={studySet.id}\r\n                  className=\"bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer\"\r\n                  onClick={() => navigate(`/study-sets/${studySet.id}`)}\r\n                >\r\n                  <div className=\"flex items-start justify-between mb-4\">\r\n                    <h3 className=\"text-lg font-medium text-white truncate\">\r\n                      {studySet.name}\r\n                    </h3>\r\n                    <span className=\"text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded\">\r\n                      {studySet.type}\r\n                    </span>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-2 text-sm text-gray-400\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span>Items:</span>\r\n                      <span>{studySet.type === 'flashcards' ? studySet.flashcard_count || 0 : studySet.quiz_question_count || 0}</span>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span>Created:</span>\r\n                      <span>{formatDate(studySet.created_at)}</span>\r\n                    </div>\r\n\r\n                    {studySet.last_studied_at && (\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <span>Last studied:</span>\r\n                        <span>{formatDate(studySet.last_studied_at)}</span>\r\n                      </div>\r\n                    )}\r\n\r\n                    {studySet.is_ai_generated && (\r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <span className=\"text-xs\">🤖</span>\r\n                        <span>AI Generated</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 pt-4 border-t border-gray-700\">\r\n                    <div\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        navigate(`/study-sets/${studySet.id}`);\r\n                      }}\r\n                    >\r\n                      <Button\r\n                        variant=\"secondary\"\r\n                        size=\"sm\"\r\n                        className=\"w-full\"\r\n                      >\r\n                        Start Studying\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n"], "names": ["useDocumentStore", "create", "set", "documents", "selectedDocuments", "Set", "isLoading", "uploadProgress", "fetchDocuments", "async", "token", "localStorage", "getItem", "response", "fetch", "headers", "Authorization", "concat", "ok", "Error", "result", "json", "success", "error", "data", "console", "uploadDocument", "formData", "FormData", "append", "file", "method", "body", "errorResult", "state", "_objectSpread", "name", "deleteDocument", "id", "filter", "doc", "docId", "searchDocuments", "encodeURIComponent", "query", "getDocument", "toggleDocumentSelection", "newSelection", "has", "delete", "add", "clearSelection", "selectAll", "map", "setUploadProgress", "fileName", "progress", "DashboardUpload", "isUploading", "setIsUploading", "useState", "uploadErrors", "setUploadErrors", "uploadSuccess", "setUploadSuccess", "navigate", "useNavigate", "onDrop", "useCallback", "errors", "successes", "acceptedFiles", "size", "push", "includes", "type", "message", "length", "setTimeout", "getRootProps", "getInputProps", "isDragActive", "useDropzone", "accept", "multiple", "maxFiles", "disabled", "_jsxs", "className", "children", "_jsx", "stroke", "fill", "viewBox", "d", "strokeWidth", "strokeLinecap", "strokeLinejoin", "onClick", "e", "stopPropagation", "style", "width", "fillRule", "clipRule", "filename", "index", "Dashboard", "memo", "studySets", "setStudySets", "setIsLoading", "setError", "useEffect", "fetchStudySets", "formatDate", "dateString", "Date", "toLocaleDateString", "<PERSON><PERSON>", "variant", "studySet", "flashcard_count", "quiz_question_count", "created_at", "last_studied_at", "is_ai_generated"], "sourceRoot": ""}