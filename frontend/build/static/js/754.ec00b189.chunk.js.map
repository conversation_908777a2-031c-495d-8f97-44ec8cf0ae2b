{"version": 3, "file": "static/js/754.ec00b189.chunk.js", "mappings": "qNAQO,MAAMA,EAAsEC,IAI5E,IAJ6E,QAClFC,EAAO,SACPC,EAAW,SAAQ,WACnBC,EAAa,KACdH,EACC,MAAOI,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,IAgBrD,OAdAC,EAAAA,EAAAA,WAAU,KACR,GAAIN,IACFI,EAAkBJ,GAEdE,EAAa,GAAG,CAClB,MAAMK,EAAQC,WAAW,KACvBJ,EAAkB,KACjBF,GAEH,MAAO,IAAMO,aAAaF,EAC5B,GAED,CAACP,EAASE,KAGXQ,EAAAA,EAAAA,KAAA,OACE,YAAWT,EACX,cAAY,OACZU,UAAU,UACVC,KAAK,SAAQC,SAEZV,K,cC7BA,MAAMW,GAA+BC,EAAAA,EAAAA,MAAK,KAAO,IAADC,EACrD,MAAMC,GAAWC,EAAAA,EAAAA,OACX,MAAEC,IAAUC,EAAAA,EAAAA,MACZ,eACJC,EAAc,gBACdC,EAAe,SACfC,EAAQ,aACRC,EAAY,WACZC,EAAU,aACVC,EAAY,gBACZC,EAAe,gBACfC,EAAe,KACfC,EAAI,KACJC,EAAI,QACJC,EAAO,QACPC,IACEC,EAAAA,EAAAA,kBAEGC,EAAWC,IAAgB9B,EAAAA,EAAAA,WAAS,IACpC+B,EAAWC,IAAgBhC,EAAAA,EAAAA,UAASiC,KAAKC,QAC1C,SAAEC,EAAQ,sBAAEC,GDewBC,MAC1C,MAAOC,EAAcC,IAAmBvC,EAAAA,EAAAA,UAAS,KAC1CJ,EAAU4C,IAAexC,EAAAA,EAAAA,UAAiC,UAcjE,MAAO,CAAEmC,SAZQ,SAACxC,GAChB6C,EAD6EC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,UAEhFF,EAAgB5C,EAClB,EASmByC,sBAPWA,KAC5B/B,EAAAA,EAAAA,KAACZ,EAAyB,CACxBE,QAAS2C,EACT1C,SAAUA,MC3B8ByC,GA6E5C,IA3EApC,EAAAA,EAAAA,WAAU,KACR,MAAM2C,EAAWC,YAAY,KAC3BtB,EAAgB,IACf,KAEH,MAAO,IAAMuB,cAAcF,IAC1B,CAACrB,KAEJtB,EAAAA,EAAAA,WAAU,KAER6B,GAAa,GACbE,EAAaC,KAAKC,QACjB,CAAe,OAAdlB,QAAc,IAAdA,OAAc,EAAdA,EAAgB+B,gBAGpB9C,EAAAA,EAAAA,WAAU,KACR,MAAM+C,EAAkBC,IAAsB,IAADC,EAE3C,KACED,EAAEE,kBAAkBC,kBACpBH,EAAEE,kBAAkBE,qBACpBJ,EAAEE,kBAAkBG,mBAC2B,UAAtC,QAATJ,EAACD,EAAEE,cAAM,IAAAD,OAAA,EAATA,EAA2BK,kBAK7B,GAAc,cAAVN,EAAEO,KAGJ,GAFAP,EAAEQ,iBAEEzC,EAAgB,CAClBG,IAEA,MAAMuC,EAA2C,IAAhC1C,EAAe+B,aAC5B/B,EAAe2C,WACf3C,EAAe+B,aACnBZ,EAAS,QAADyB,OAASF,EAAQ,QAAAE,OAAO5C,EAAe2C,YACjD,OACK,GAAc,eAAVV,EAAEO,KAGX,GAFAP,EAAEQ,iBAEEzC,EAAgB,CAClBE,IAEA,MAAMwC,EAAW1C,EAAe+B,eAAiB/B,EAAe2C,WAAa,EACzE,EACA3C,EAAe+B,aAAe,EAClCZ,EAAS,QAADyB,OAASF,EAAQ,QAAAE,OAAO5C,EAAe2C,YACjD,OACK,GAAc,MAAVV,EAAEO,KAAyB,YAAVP,EAAEO,KAA+B,cAAVP,EAAEO,IACnDP,EAAEQ,iBACF3B,GAAcD,QACT,GAAc,MAAVoB,EAAEO,KAAyB,MAAVP,EAAEO,KAE5B,GADAP,EAAEQ,iBACEzC,GAAiC,OAAfC,QAAe,IAAfA,GAAAA,EAAiB4C,WAAY,CACjD,MAAMC,EAAmB7C,EAAgB4C,WAAW7C,EAAe+B,cACnE,GAAIe,EAAkB,CACpB1C,EAAW0C,EAAiBC,IAC5B,MAAMC,EAAYhD,EAAeiD,aAAaC,SAASJ,EAAiBC,IACxE5B,EAAS6B,EAAY,iBAAmB,0BAC1C,CACF,OACUf,EAAEkB,UAAWlB,EAAEmB,SAAsB,MAAVnB,EAAEO,KAAgBP,EAAEoB,UAG/CpB,EAAEkB,SAAWlB,EAAEmB,WAAuB,MAAVnB,EAAEO,KAA0B,MAAVP,EAAEO,KAAeP,EAAEoB,YAC3EpB,EAAEQ,iBACE9B,GAASF,MAJbwB,EAAEQ,iBACE/B,GAASF,MAQjB,OADA8C,SAASC,iBAAiB,UAAWvB,GAC9B,IAAMsB,SAASE,oBAAoB,UAAWxB,IACpD,CAAChC,EAAgBC,EAAiBY,EAAWH,EAASC,EAASR,EAAcD,EAAUE,EAAYI,EAAMC,EAAMU,KAE7GnB,GAAkC,OAAfC,QAAe,IAAfA,IAAAA,EAAiB4C,WACvC,OACExD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CE,UAC1DiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,oBAAmBE,SAAA,EAChCH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAoBE,SAAC,gCACpCH,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CAACC,QAASA,IAAM/D,EAAS,cAAegE,QAAQ,YAAWpE,SAAC,4BAQ3E,MACMsD,EADa7C,EAAgB4C,WACC7C,EAAe+B,cAC7C8B,GAAa7D,EAAe+B,aAAe,GAAK/B,EAAe2C,WAAc,IAC7EmB,EAA8C,IAAhC9D,EAAe+B,aAC7BgC,EAAa/D,EAAe+B,eAAiB/B,EAAe2C,WAAa,EACzEK,EAAYhD,EAAeiD,aAAaC,SAASJ,EAAiBC,IA4BlEiB,EAAaA,KACjBlD,GAAcD,GACdM,EAASN,EAAY,wBAA0B,yBAqBjD,OACE4C,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8CAA6CE,SAAA,EAE1DiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,KAAA,UACEsE,QAASA,IAAM/D,EAAS,eAADgD,OAAgB5C,EAAeiE,aACtD3E,UAAU,mDAAkDE,SAC7D,8BAIDiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,cAAaE,SAAA,EAC1BH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCE,SACrB,QADqBG,EAC7CM,EAAgBiE,gBAAQ,IAAAvE,OAAA,EAAxBA,EAA0BwE,QAE7BV,EAAAA,EAAAA,MAAA,KAAGnE,UAAU,wBAAuBE,SAAA,CAAC,QAC7BQ,EAAe+B,aAAe,EAAE,OAAK/B,EAAe2C,kBAI9DtD,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QAvCkBS,UACxB,MAAMC,EAAYC,KAAKC,OAAOtD,KAAKC,MAAQH,GAAa,KAClDyD,EAAgBxE,EAAeyE,cAAc/C,OAC7CgD,EAAe1E,EAAeiD,aAAavB,OAEjDpB,UAGMR,EAAM,CACV6E,MAAO,0BACPhG,QAAQ,aAADiE,OAAe4B,EAAa,KAAA5B,OAAI5C,EAAe2C,WAAU,qBAAAC,OAAoB8B,EAAY,wBAAA9B,OAAuB0B,KAAKC,MAAMF,EAAY,IAAG,MAAAzB,OAAKyB,EAAY,GAAE,KACpKT,QAAS,UACTgB,YAAa,aAGfhF,EAAS,eAADgD,OAAgB5C,EAAeiE,cAyBjCL,QAAQ,YACRiB,KAAK,KAAIrF,SACV,eAMHiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,OAAME,SAAA,EACnBH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sCAAqCE,UAClDH,EAAAA,EAAAA,KAAA,OACEC,UAAU,8DACVwF,MAAO,CAAEC,MAAM,GAADnC,OAAKiB,EAAQ,WAG/BJ,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,kDAAiDE,SAAA,EAC9DiE,EAAAA,EAAAA,MAAA,QAAAjE,SAAA,CAAM,aAAW8E,KAAKU,MAAMnB,GAAU,QACtCJ,EAAAA,EAAAA,MAAA,QAAAjE,SAAA,CAAM,SAAO8E,KAAKC,MAAMvE,EAAeqE,UAAY,IAAI,KAAGrE,EAAeqE,UAAY,IAAIY,WAAWC,SAAS,EAAG,eAKpH7F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2BAA0BE,UACvCiE,EAAAA,EAAAA,MAAA,OACEnE,UAAS,uJAAAsD,OAEL/B,EAAY,eAAiB,GAAE,gBAEnC8C,QAASK,EACTzE,KAAK,SACL4F,SAAU,EACV,0BAAAvC,OAAyB5C,EAAe+B,aAAe,EAAC,QAAAa,OAAO5C,EAAe2C,WAAU,MAAAC,OAAK/B,EAAY,eAAiB,gBAAe,mCACzIuE,UAAYnD,IACI,MAAVA,EAAEO,KAAyB,UAAVP,EAAEO,MACrBP,EAAEQ,iBACFuB,MAEFxE,SAAA,EAGFH,EAAAA,EAAAA,KAAA,OAAKC,UAAS,mOAIZE,UACAiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CE,SAAC,WAC1DH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oHAAmHE,UAChIH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYE,SACxBsD,EAAiBuC,WAGpBxE,IACAxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CE,SAAC,iCAQhEH,EAAAA,EAAAA,KAAA,OAAKC,UAAS,gPAIZE,UACAiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CE,SAAC,UAC7DH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oHAAmHE,UAChIH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYE,SACxBsD,EAAiBwC,SAGrBzE,IACCxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CE,SAAC,kCAUpEiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QAjJe4B,KAErBpF,IAEA,MAAMuC,EAAWoB,EAAc9D,EAAe2C,WAAa3C,EAAe+B,aAC1EZ,EAAS,QAADyB,OAASF,EAAQ,QAAAE,OAAO5C,EAAe2C,cA6IzCiB,QAAQ,YAAWpE,SACpB,qBAIDiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8BAA6BE,SAAA,EAC1CH,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QAjJe6B,KACvBpF,EAAW0C,EAAiBC,IAE5B5B,EAAS,QAADyB,OADWI,EAAY,YAAc,aAgJrCY,QAASZ,EAAY,UAAY,YACjC6B,KAAK,KAAIrF,SAERwD,EAAY,uBAAe,6BAG9B3D,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QAASA,IAAM7C,GAAcD,GAC7B+C,QAAQ,YAAWpE,SAElBqB,EAAY,aAAe,kBAIhCxB,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QArLW8B,KACb5E,GACFR,EAAayC,EAAiBC,IAIhC7C,IAEA,MAAMwC,EAAWqB,EAAa,EAAI/D,EAAe+B,aAAe,EAChEZ,EAAS,QAADyB,OAASF,EAAQ,QAAAE,OAAO5C,EAAe2C,cA6KzCiB,QAAQ,UAASpE,SAClB,oBAMHH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCE,UACrDH,EAAAA,EAAAA,KAAA,KAAAG,SAAG,wFAILH,EAAAA,EAAAA,KAAC+B,EAAqB,SCtTfsE,EAA0BA,KAAO,IAAD/F,EAC3C,MAAMC,GAAWC,EAAAA,EAAAA,OACX,MAAEC,IAAUC,EAAAA,EAAAA,MACZ,eACJC,EAAc,gBACdC,EAAe,SACfC,EAAQ,iBACRyF,EAAgB,gBAChBrF,EAAe,gBACfC,IACEK,EAAAA,EAAAA,kBAEGgF,EAAiBC,IAAsB7G,EAAAA,EAAAA,UAAmB,KAC1D8G,EAAaC,IAAkB/G,EAAAA,EAAAA,WAAS,IACxCgH,EAAiBC,IAAsBjH,EAAAA,EAAAA,WAAS,GAoBvD,IAhBAC,EAAAA,EAAAA,WAAU,KACR,MAAM2C,EAAWC,YAAY,KAC3BtB,EAAgB,IACf,KAEH,MAAO,IAAMuB,cAAcF,IAC1B,CAACrB,KAEJtB,EAAAA,EAAAA,WAAU,KAER4G,EAAmB,IACnBE,GAAe,GACfE,GAAmB,IAElB,CAAe,OAAdjG,QAAc,IAAdA,OAAc,EAAdA,EAAgB+B,gBAEf/B,GAAkC,OAAfC,QAAe,IAAfA,IAAAA,EAAiBiG,UACvC,OACE7G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CE,UAC1DiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,oBAAmBE,SAAA,EAChCH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAoBE,SAAC,2BACpCH,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CAACC,QAASA,IAAM/D,EAAS,cAAegE,QAAQ,YAAWpE,SAAC,2BAQ3E,MACM2G,EADYlG,EAAgBiG,UACAlG,EAAe+B,cAC3C8B,GAAa7D,EAAe+B,aAAe,GAAK/B,EAAe2C,WAAc,IAC7EyD,EAAiBpG,EAAe+B,eAAiB/B,EAAe2C,WAAa,EAE7E0D,EAAsBC,IACtBR,IAEkC,oBAAlCK,EAAgBI,eAAyE,eAAlCJ,EAAgBI,cACzEV,EAAmB,CAACS,IACuB,eAAlCH,EAAgBI,eACzBV,EAAmBW,GACjBA,EAAKtD,SAASoD,GACVE,EAAKC,OAAOC,GAAKA,IAAMJ,GACvB,IAAIE,EAAMF,MAoBdK,EAAcA,KAClB,MAAMC,EAAiBT,EAAgBU,gBAEvC,GAAsC,iBAAlCV,EAAgBI,cAAkC,CAAC,IAADO,EAEpD,MAAMC,GAA+B,QAAlBD,EAAAlB,EAAgB,UAAE,IAAAkB,OAAA,EAAlBA,EAAoBE,cAAcC,SAAU,GAC/D,OAAOL,EAAeM,KAAKC,GACzBJ,EAAW7D,SAASiE,EAAQH,cAAcC,SAC1CE,EAAQH,cAAcC,OAAO/D,SAAS6D,GAE1C,CAEE,OAAOnB,EAAgBlE,SAAWkF,EAAelF,QAC1CkE,EAAgBwB,MAAMd,GAAUM,EAAe1D,SAASoD,KAY7De,EAAmBjD,UACvB,MAAMkD,EAAiBtH,EAAe2C,WAChCiE,EAAiB5G,EAAe4G,gBAAkB,EAClDW,EAAajD,KAAKU,MAAO4B,EAAiBU,EAAkB,KAC5DjD,EAAYrE,EAAeqE,UAEjC/D,UAGMR,EAAM,CACV6E,MAAO,iBACPhG,QAAQ,UAADiE,OAAYgE,EAAc,KAAAhE,OAAI0E,EAAc,MAAA1E,OAAK2E,EAAU,oBAAA3E,OAAmB0B,KAAKC,MAAMF,EAAY,IAAG,MAAAzB,OAAKyB,EAAY,GAAE,KAClIT,QAAS,UACTgB,YAAa,aAGfhF,EAAS,eAADgD,OAAgB5C,EAAeiE,cAgHzC,OACER,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8CAA6CE,SAAA,EAE1DiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,KAAA,UACEsE,QAASA,IAAM/D,EAAS,eAADgD,OAAgB5C,EAAeiE,aACtD3E,UAAU,mDAAkDE,SAC7D,8BAIDiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,cAAaE,SAAA,EAC1BH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCE,SACrB,QADqBG,EAC7CM,EAAgBiE,gBAAQ,IAAAvE,OAAA,EAAxBA,EAA0BwE,QAE7BV,EAAAA,EAAAA,MAAA,KAAGnE,UAAU,wBAAuBE,SAAA,CAAC,YACzBQ,EAAe+B,aAAe,EAAE,OAAK/B,EAAe2C,kBAIlEtD,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QAAS0D,EACTzD,QAAQ,YACRiB,KAAK,KAAIrF,SACV,oBAMHiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,OAAME,SAAA,EACnBH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sCAAqCE,UAClDH,EAAAA,EAAAA,KAAA,OACEC,UAAU,8DACVwF,MAAO,CAAEC,MAAM,GAADnC,OAAKiB,EAAQ,WAG/BJ,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,kDAAiDE,SAAA,EAC9DiE,EAAAA,EAAAA,MAAA,QAAAjE,SAAA,CAAM,aAAW8E,KAAKU,MAAMnB,GAAU,QACtCJ,EAAAA,EAAAA,MAAA,QAAAjE,SAAA,CAAM,UAAQQ,EAAe4G,gBAAkB,EAAE,IAAE5G,EAAe+B,cAAgB+D,EAAc,EAAI,OACpGrC,EAAAA,EAAAA,MAAA,QAAAjE,SAAA,CAAM,SAAO8E,KAAKC,MAAMvE,EAAeqE,UAAY,IAAI,KAAGrE,EAAeqE,UAAY,IAAIY,WAAWC,SAAS,EAAG,eAKpHzB,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAME,UACnBH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CE,SAC5D2G,EAAgBI,cAAciB,QAAQ,IAAK,UAIhDnI,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0CAAyCE,SACpD2G,EAAgBsB,iBAInBpI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWE,SACW,iBAAlC2G,EAAgBI,eACflH,EAAAA,EAAAA,KAAA,YACEqI,MAAO9B,EAAgB,IAAM,GAC7B+B,SAAW1F,IAAM2F,OArOIF,EAqOoBzF,EAAEE,OAAOuF,WApOxD5B,GACJD,EAAmB,CAAC6B,KAFWA,OAsOrBG,SAAU/B,EACVgC,YAAY,2BACZC,KAAM,EACNzI,UAAU,wLAEwB,eAAlC6G,EAAgBI,cAlHV,CAAC,OAAQ,SAEVyB,IAAKC,IAClB,MAAMC,EAAatC,EAAgB1C,SAAS+E,GACtCE,EAAYhC,EAAgBU,gBAAgB3D,SAAS+E,GAE3D,IAAIG,EAAc,yDAkBlB,OAdIA,GAFAtC,EACEqC,EACa,kDACND,IAAeC,EACT,4CAEA,wDAGbD,EACa,wDAEA,4EAKjB7I,EAAAA,EAAAA,KAAA,UAEEsE,QAASA,IAAM0C,EAAmB4B,GAClCJ,SAAU/B,EACVxG,UAAW8I,EAAY5I,UAEvBiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8BAA6BE,SAAA,EAC1CH,EAAAA,EAAAA,KAAA,OAAKC,UAAS,iGAAAsD,OAEVsF,EAAa,iBAAmB,kBAAiB,kBACnD1I,SACC0I,IACC7I,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAGnBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBE,SAAEyI,QAdpCA,KAvFN9B,EAAgBkC,QAEdlC,EAAgBkC,QAAQL,IAAI,CAACC,EAAQK,KAC1C,MAAMJ,EAAatC,EAAgB1C,SAAS+E,GACtCE,EAAYhC,EAAgBU,gBAAgB3D,SAAS+E,GAE3D,IAAIG,EAAc,yDAkBlB,OAdIA,GAFAtC,EACEqC,EACa,kDACND,IAAeC,EACT,4CAEA,wDAGbD,EACa,wDAEA,4EAKjB7I,EAAAA,EAAAA,KAAA,UAEEsE,QAASA,IAAM0C,EAAmB4B,GAClCJ,SAAU/B,EACVxG,UAAW8I,EAAY5I,UAEvBiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,8BAA6BE,SAAA,CACP,oBAAlC2G,EAAgBI,eAEflH,EAAAA,EAAAA,KAAA,OAAKC,UAAS,qGAAAsD,OAEVsF,EAAa,iBAAmB,kBAAiB,oBACnD1I,SACC0I,IACC7I,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAKnBD,EAAAA,EAAAA,KAAA,OAAKC,UAAS,gGAAAsD,OAEVsF,EAAa,iBAAmB,kBAAiB,oBACnD1I,SACC0I,IACC7I,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kCAIrBD,EAAAA,EAAAA,KAAA,QAAAG,SAAOyI,QA3BJK,KA1B0B,QAuL/BxC,IACAzG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAME,UACnBH,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QAjPe4E,KACzB,GAAIzC,GAA0C,IAA3BF,EAAgBlE,OAAc,OAEjD,MAAMyG,EAAYxB,IAElBhB,EAAiBQ,EAAgBpD,GAAI6C,EAAiBuC,GACtDpC,GAAe,GACfE,GAAmB,IA2OT4B,SAAqC,IAA3BjC,EAAgBlE,OAC1BpC,UAAU,SAAQE,SACnB,oBAOJsG,GAAeE,GAAmBG,EAAgBqC,cACjD/E,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,+DAA8DE,SAAA,EAC3EH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCE,SAAC,iBAC/CH,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeE,SAAE2G,EAAgBqC,oBAMnD1C,IACCzG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBE,UAClCH,EAAAA,EAAAA,KAACqE,EAAAA,EAAM,CACLC,QA5OS8B,KACbW,EACFiB,IAEAnH,KAyOM0D,QAAQ,UACRiB,KAAK,KAAIrF,SAER4G,EAAiB,cAAgB,+BCnVjCqC,EAAsBA,KACjC,MAAM,GAAE1F,EAAE,KAAE2F,IAASC,EAAAA,EAAAA,KACf/I,GAAWC,EAAAA,EAAAA,OACX,eAAEG,EAAc,kBAAE4I,IAAsBhI,EAAAA,EAAAA,kBACtCiI,SAAUC,IAAiBC,EAAAA,EAAAA,KAqBnC,OAnBA9J,EAAAA,EAAAA,WAAU,KAER,IAAKe,GACDA,EAAeiE,aAAelB,GAC9B/C,EAAegJ,OAASN,EAE1B,GAAI3F,GAAM2F,IAAkB,eAATA,GAAkC,SAATA,GAAkB,CAC5D,MAAMO,GAA6B,OAAZH,QAAY,IAAZA,OAAY,EAAZA,EAAcI,sBAAsB,EAC3DN,EAAkB7F,EAAI2F,EAAMO,GAAgBE,MAAOC,IACjDC,QAAQD,MAAM,iCAAkCA,GAChDtJ,MAAMsJ,EAAMzK,SAAW,iCACvBiB,EAAS,eAADgD,OAAgBG,KAE5B,MACEnD,EAAS,eAGZ,CAACmD,EAAI2F,EAAM1I,EAAgB4I,EAAmBhJ,EAAUkJ,IAEtD9I,EAWQ,eAAT0I,GACKrJ,EAAAA,EAAAA,KAACI,EAAkB,IACR,SAATiJ,GACFrJ,EAAAA,EAAAA,KAACqG,EAAa,KAErB9F,EAAS,cACF,OAfLP,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CE,UAC1DiE,EAAAA,EAAAA,MAAA,OAAKnE,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qEACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qBAAoBE,SAAC,mC,gDC5BxC,MAAMuJ,EAAkBA,KAC7B,MAAOF,EAAUS,IAAetK,EAAAA,EAAAA,UAA8B,OACvDuK,EAASC,IAAcxK,EAAAA,EAAAA,WAAS,IAChCoK,EAAOK,IAAYzK,EAAAA,EAAAA,UAAwB,MAE5C0K,GAAgBC,EAAAA,EAAAA,aAAYvF,UAChC,IACEoF,GAAW,GACXC,EAAS,MAET,MAAMG,EAAQC,aAAaC,QAAQ,eAAiBC,eAAeD,QAAQ,cAC3E,IAAKF,EACH,MAAM,IAAII,MAAM,iCAGlB,MAAMC,QAAiBC,MAAM,qBAAsB,CACjDC,QAAS,CACP,cAAgB,UAADvH,OAAYgH,GAC3B,eAAgB,sBAIpB,IAAKK,EAASG,GAAI,CAChB,MAAMC,QAAkBJ,EAASK,OACjC,MAAM,IAAIN,MAAMK,EAAUjB,OAAS,gCACrC,CAEA,MAAMmB,QAAaN,EAASK,OAC5BhB,EAAYiB,EAAKA,KACnB,CAAE,MAAOC,GACPf,EAASe,EAAI7L,SACb0K,QAAQD,MAAM,gCAAiCoB,EACjD,CAAC,QACChB,GAAW,EACb,GACC,IAEGiB,GAAiBd,EAAAA,EAAAA,aAAYvF,UACjC,IACEqF,EAAS,MAET,MAAMG,EAAQC,aAAaC,QAAQ,eAAiBC,eAAeD,QAAQ,cAC3E,IAAKF,EACH,MAAM,IAAII,MAAM,iCAGlB,MAAMC,QAAiBC,MAAM,qBAAsB,CACjDQ,OAAQ,QACRP,QAAS,CACP,cAAgB,UAADvH,OAAYgH,GAC3B,eAAgB,oBAElBe,KAAMC,KAAKC,UAAUC,KAGvB,IAAKb,EAASG,GAAI,CAChB,MAAMC,QAAkBJ,EAASK,OACjC,MAAM,IAAIN,MAAMK,EAAUjB,OAAS,iCACrC,CAEA,MAAMmB,QAAaN,EAASK,OAC5BhB,EAAYiB,EAAKA,KACnB,CAAE,MAAOC,GAGP,MAFAf,EAASe,EAAI7L,SACb0K,QAAQD,MAAM,gCAAiCoB,GACzCA,CACR,GACC,IAEGO,GAAUpB,EAAAA,EAAAA,aAAYvF,gBACpBsF,KACL,CAACA,IAMJ,OAJAzK,EAAAA,EAAAA,WAAU,KACRyK,KACC,CAACA,IAEG,CACLb,WACAU,UACAH,QACAqB,iBACAM,W,sECvFJ,MAAMC,EAAmBC,IACvB,MAAMC,EAAW,IAAID,GACrB,IAAK,IAAIE,EAAID,EAASxJ,OAAS,EAAGyJ,EAAI,EAAGA,IAAK,CAC5C,MAAMC,EAAI9G,KAAKC,MAAMD,KAAK+G,UAAYF,EAAI,KACzCD,EAASC,GAAID,EAASE,IAAM,CAACF,EAASE,GAAIF,EAASC,GACtD,CACA,OAAOD,GAmGItK,GAAgB0K,EAAAA,EAAAA,IAAmB,CAACC,EAAKC,KAAG,CACvDxL,eAAgB,KAChBC,gBAAiB,KACjBwL,UAAW,GACXC,SAAU,GACVC,WAAW,EACXvC,MAAO,KAGPwC,cAAe,GACfC,oBAAqB,EACrBnL,SAAS,EACTC,SAAS,EAETmL,qBAAsB1H,eAAOH,GAA8C,IAADtE,EAAA,IAAzBoM,EAAYtK,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAE3D,MAAM,gBAAExB,GAAoBuL,IAC5B,GACEO,IACC9L,IACuB,QAAxBN,EAAAM,EAAgBiE,gBAAQ,IAAAvE,OAAA,EAAxBA,EAA0BoD,MAAOkB,EACjC,CACAsH,EAAI,CAAEI,WAAW,EAAMvC,MAAO,OAE9B,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAE7BG,QAAiBC,MAAM,mBAADtH,OAAoBqB,EAAU,YAAY,CACpEkG,QAAS,CACP6B,cAAc,UAADpJ,OAAYgH,MAI7B,IAAKK,EAASG,GAAI,CAChB,MAAM6B,QAAoBhC,EAASK,OACnC,MAAM,IAAIN,MACRiC,EAAY7C,OAAS,oCAEzB,CAEA,MAAM8C,QAAejC,EAASK,OAE9B,IAAI4B,EAAOC,QAUT,MAAM,IAAInC,MAAMkC,EAAO9C,OATvBmC,EAAI,CACFtL,gBAAiB,CACfiE,SAAUgI,EAAO3B,KAAKrG,SACtBrB,WAAYqJ,EAAO3B,KAAK1H,YAAc,GACtCqD,UAAWgG,EAAO3B,KAAKrE,WAAa,IAEtCyF,WAAW,GAKjB,CAAE,MAAOvC,GAKP,MAJAmC,EAAI,CACFnC,MAAOA,EAAMzK,SAAW,oCACxBgN,WAAW,IAEPvC,CACR,CACF,CACF,EAEAR,kBAAmBxE,eACjBH,EACA+E,GAEI,IAADoD,EAAAC,EAAAC,EAAA,IADHC,EAAO9K,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAEP,MAAM,gBAAExB,EAAe,qBAAE6L,GAAyBN,IAG7CvL,IAA2C,QAAxBmM,EAAAnM,EAAgBiE,gBAAQ,IAAAkI,OAAA,EAAxBA,EAA0BrJ,MAAOkB,SACjD6H,EAAqB7H,GAG7B,MAAMuI,EAAUhB,IAAMvL,gBACtB,IAAKuM,EACH,MAAM,IAAIxC,MAAM,oCAGlB,MAAMrH,EACK,eAATqG,GACsB,QAAlBqD,EAAAG,EAAQ3J,kBAAU,IAAAwJ,OAAA,EAAlBA,EAAoB3K,SAAU,GACb,QAAjB4K,EAAAE,EAAQtG,iBAAS,IAAAoG,OAAA,EAAjBA,EAAmB5K,SAAU,EAEnC,GAAmB,IAAfiB,EACF,MAAM,IAAIqH,MAAM,wCAIlB,IAAIyC,EACJ,GAAIF,EAKF,GAHAE,EAAgBC,MAAMC,KAAK,CAAEjL,OAAQiB,GAAc,CAACiK,EAAGzB,IAAMA,GAGhD,eAATnC,GAAyBwD,EAAQ3J,WAAY,CAC/C,MAAMgK,EAAqB7B,EAAawB,EAAQ3J,YAChD0I,EAAKuB,IAAK,CACR7M,iBAAe8M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVD,EAAM7M,iBAAe,IACxB4C,WAAYgK,MAGlB,MAAO,GAAa,SAAT7D,GAAmBwD,EAAQtG,UAAW,CAC/C,MAAM8G,EAAoBhC,EAAawB,EAAQtG,WAC/CqF,EAAKuB,IAAK,CACR7M,iBAAe8M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVD,EAAM7M,iBAAe,IACxBiG,UAAW8G,MAGjB,CAGFzB,EAAI,CACFvL,eAAgB,CACdiE,aACA+E,OACAjI,UAAW,IAAIE,KACfc,aAAc,EACdY,aACA8B,cAAe,GACfxB,aAAc,GACd2D,eAAyB,SAAToC,EAAkB,OAAIrH,EACtC0C,UAAW,EACX4I,WAAYV,EACZE,kBAGN,EAEAnM,gBAAiBA,KACfiL,EAAI,CAAEvL,eAAgB,QAGxBE,SAAUA,KACR,MAAM,eAAEF,EAAc,aAAEkN,GAAiB1B,IACzC,IAAKxL,EAAgB,OAGrB,MAAMmN,EACJnN,EAAe+B,eAAiB/B,EAAe2C,WAAa,EACxD,EACA3C,EAAe+B,aAAe,EAGpCmL,EAAa,CACXlE,KAAM,YACNoE,QAAS,CAAEC,UAAWrN,EAAe+B,aAAcuL,QAASH,GAC5DI,cAAe,CAAExL,aAAc/B,EAAe+B,cAC9CyL,UAAWvM,KAAKC,QAGlBqK,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GAAc,IACjB+B,aAAcoL,OAKpBhN,aAAcA,KACZ,MAAM,eAAEH,EAAc,aAAEkN,GAAiB1B,IACzC,IAAKxL,EAAgB,OAGrB,MAAMyN,EAC4B,IAAhCzN,EAAe+B,aACX/B,EAAe2C,WAAa,EAC5B3C,EAAe+B,aAAe,EAGpCmL,EAAa,CACXlE,KAAM,gBACNoE,QAAS,CAAEC,UAAWrN,EAAe+B,aAAcuL,QAASG,GAC5DF,cAAe,CAAExL,aAAc/B,EAAe+B,cAC9CyL,UAAWvM,KAAKC,QAGlBqK,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GAAc,IACjB+B,aAAc0L,OAKpBC,SAAWpF,IACT,MAAM,eAAEtI,GAAmBwL,IAC3B,IAAKxL,EAAgB,OAErB,MAAM2N,EAAerJ,KAAKsJ,IACxB,EACAtJ,KAAKuJ,IAAIvF,EAAOtI,EAAe2C,WAAa,IAE9C4I,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GAAc,IACjB+B,aAAc4L,OAKpBvN,WAAa0N,IACX,MAAM,eAAE9N,EAAc,aAAEkN,GAAiB1B,IACzC,IAAKxL,EAAgB,OAErB,MAAM+N,EAAa/N,EAAeiD,aAAaC,SAAS4K,GAClD7K,EAAe8K,EACjB/N,EAAeiD,aAAawD,OAAQ1D,GAAOA,IAAO+K,GAClD,IAAI9N,EAAeiD,aAAc6K,GAGrCZ,EAAa,CACXlE,KAAM,cACNoE,QAAS,CAAEU,SAAQC,cACnBR,cAAe,CAAEtK,aAAcjD,EAAeiD,cAC9CuK,UAAWvM,KAAKC,QAGlBqK,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GAAc,IACjBiD,oBAKN5C,aAAe2N,IACb,MAAM,eAAEhO,GAAmBwL,IACtBxL,IAEAA,EAAeyE,cAAcvB,SAASlD,EAAe+B,eACxDwJ,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GAAc,IACjByE,cAAe,IACVzE,EAAeyE,cAClBzE,EAAe+B,oBAOzB4D,iBAAkBA,CAChBsI,EACAC,EACA/F,KAEA,MAAM,eAAEnI,EAAc,aAAEK,GAAiBmL,IACpCxL,GAA0C,SAAxBA,EAAegJ,OAEtC3I,EAAa4N,GAET9F,GACFoD,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GAAc,IACjB4G,gBAAiB5G,EAAe4G,gBAAkB,GAAK,QAM/DrG,gBAAkB4N,IAChB,MAAM,eAAEnO,GAAmBwL,IACtBxL,GAELuL,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GAAc,IACjBqE,UAAWrE,EAAeqE,UAAY8J,OAM5CjB,aAAekB,IACb,MAAM,cAAExC,EAAa,mBAAEC,GAAuBL,IAGxC6C,EAAazC,EAAc0C,MAAM,EAAGzC,EAAqB,GAC/DwC,EAAWE,KAAKH,GAGhB,MAAMI,EAAiBH,EAAWC,OAAO,IAEzC/C,EAAI,CACFK,cAAe4C,EACf3C,mBAAoB2C,EAAe9M,OAAS,EAC5ChB,QAAS8N,EAAe9M,OAAS,EACjCf,SAAS,KAIbH,KAAMA,KACJ,MAAM,cAAEoL,EAAa,mBAAEC,EAAkB,eAAE7L,GAAmBwL,IAE9D,GAAIK,EAAqB,IAAM7L,EAAgB,OAE/C,MAAMoO,EAASxC,EAAcC,GAG7BN,EAAI,CACFvL,gBAAc+M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT/M,GACAoO,EAAOb,eAEZ1B,mBAAoBA,EAAqB,EACzCnL,QAASmL,EAAqB,EAC9BlL,SAAS,KAIbF,KAAMA,KACJ,MAAM,cAAEmL,EAAa,mBAAEC,EAAkB,eAAE7L,GAAmBwL,IAE9D,GAAIK,GAAsBD,EAAclK,OAAS,IAAM1B,EACrD,OAEF,MAAMyO,EAAkB5C,EAAqB,EACvCuC,EAASxC,EAAc6C,GAG7B,OAAQL,EAAOpF,MACb,IAAK,YACHwC,IAAMtL,WACN,MACF,IAAK,gBACHsL,IAAMrL,eACN,MACF,IAAK,cACHqL,IAAMpL,WAAWgO,EAAOhB,QAAQU,QAChC,MACF,IAAK,gBACHtC,IAAMnL,aAAa+N,EAAOhB,QAAQU,QAItCvC,EAAI,CACFM,mBAAoB4C,EACpB/N,SAAS,EACTC,QAAS8N,EAAkB7C,EAAclK,OAAS,KAItDgN,aAAcA,KACZnD,EAAI,CACFK,cAAe,GACfC,oBAAqB,EACrBnL,SAAS,EACTC,SAAS,KAIbgO,eAAgBvK,UACdmH,EAAI,CAAEI,WAAW,EAAMvC,MAAO,OAE9B,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAE7BG,QAAiBC,MAAM,kBAAmB,CAC9CC,QAAS,CACP6B,cAAc,UAADpJ,OAAYgH,MAI7B,IAAKK,EAASG,GAAI,CAChB,MAAM6B,QAAoBhC,EAASK,OACnC,MAAM,IAAIN,MAAMiC,EAAY7C,OAAS,6BACvC,CAEA,MAAM8C,QAAejC,EAASK,OAE9B,IAAI4B,EAAOC,QAMT,MAAM,IAAInC,MAAMkC,EAAO9C,OALvBmC,EAAI,CACFE,UAAWS,EAAO3B,KAClBoB,WAAW,GAKjB,CAAE,MAAOvC,GAKP,MAJAmC,EAAI,CACFnC,MAAOA,EAAMzK,SAAW,6BACxBgN,WAAW,IAEPvC,CACR,GAGFwF,mBAAoBxK,iBAA8B,IAAvByK,EAASpN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,MACrC8J,EAAI,CAAEI,WAAW,EAAMvC,MAAO,OAE9B,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAE7BG,QAAiBC,MAAM,iCAADtH,OACOiM,GACjC,CACE1E,QAAS,CACP6B,cAAc,UAADpJ,OAAYgH,MAK/B,IAAKK,EAASG,GAAI,CAChB,MAAM6B,QAAoBhC,EAASK,OACnC,MAAM,IAAIN,MAAMiC,EAAY7C,OAAS,iCACvC,CAEA,MAAM8C,QAAejC,EAASK,OAE9B,IAAI4B,EAAOC,QAaT,MAAM,IAAInC,MAAMkC,EAAO9C,OAbL,CAElB,MAAMsC,EAAWQ,EAAO3B,KAAKvC,IAAK8G,IAAY/B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACzC+B,GAAO,IACV/N,UAAW,IAAIE,KAAK6N,EAAQ/N,WAC5BgO,QAASD,EAAQC,QAAU,IAAI9N,KAAK6N,EAAQC,cAAWpN,KAGzD4J,EAAI,CACFG,WACAC,WAAW,GAEf,CAGF,CAAE,MAAOvC,GAKP,MAJAmC,EAAI,CACFnC,MAAOA,EAAMzK,SAAW,iCACxBgN,WAAW,IAEPvC,CACR,CACF,EAGA4F,0BAA4B/K,IAC1B,MAAM,gBAAEhE,GAAoBuL,IAEX,IAADyD,EAAZhL,GAEiB,OAAfhE,QAAe,IAAfA,GAAyB,QAAVgP,EAAfhP,EAAiBiE,gBAAQ,IAAA+K,OAAV,EAAfA,EAA2BlM,MAAOkB,GACpCsH,EAAI,CAAEtL,gBAAiB,OAIzBsL,EAAI,CAAEtL,gBAAiB,QAI3BiP,uBAAwB9K,gBAEhBoH,IAAMM,qBAAqB7H,GAAY,IAG/CkL,oBAAqBA,KAEnB5D,EAAI,CAAEE,UAAW,Q", "sources": ["components/accessibility/ScreenReaderAnnouncements.tsx", "components/study/FlashcardInterface.tsx", "components/study/QuizInterface.tsx", "pages/StudyPage.tsx", "hooks/useUserSettings.ts", "stores/studyStore.ts"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\n\r\ninterface ScreenReaderAnnouncementsProps {\r\n  message: string;\r\n  priority?: 'polite' | 'assertive';\r\n  clearAfter?: number; // milliseconds\r\n}\r\n\r\nexport const ScreenReaderAnnouncements: React.FC<ScreenReaderAnnouncementsProps> = ({\r\n  message,\r\n  priority = 'polite',\r\n  clearAfter = 3000\r\n}) => {\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n\r\n  useEffect(() => {\r\n    if (message) {\r\n      setCurrentMessage(message);\r\n      \r\n      if (clearAfter > 0) {\r\n        const timer = setTimeout(() => {\r\n          setCurrentMessage('');\r\n        }, clearAfter);\r\n        \r\n        return () => clearTimeout(timer);\r\n      }\r\n    }\r\n  }, [message, clearAfter]);\r\n\r\n  return (\r\n    <div\r\n      aria-live={priority}\r\n      aria-atomic=\"true\"\r\n      className=\"sr-only\"\r\n      role=\"status\"\r\n    >\r\n      {currentMessage}\r\n    </div>\r\n  );\r\n};\r\n\r\n// Hook for managing screen reader announcements\r\nexport const useScreenReaderAnnouncements = () => {\r\n  const [announcement, setAnnouncement] = useState('');\r\n  const [priority, setPriority] = useState<'polite' | 'assertive'>('polite');\r\n\r\n  const announce = (message: string, announcementPriority: 'polite' | 'assertive' = 'polite') => {\r\n    setPriority(announcementPriority);\r\n    setAnnouncement(message);\r\n  };\r\n\r\n  const AnnouncementComponent = () => (\r\n    <ScreenReaderAnnouncements \r\n      message={announcement} \r\n      priority={priority}\r\n    />\r\n  );\r\n\r\n  return { announce, AnnouncementComponent };\r\n};\r\n", "import React, { useState, useEffect, memo } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useStudyStore } from '../../stores/studyStore';\r\nimport { Button } from '../common/Button';\r\nimport { useScreenReaderAnnouncements } from '../accessibility/ScreenReaderAnnouncements';\r\nimport { useDialog } from '../../contexts/DialogContext';\r\n\r\nexport const FlashcardInterface: React.FC = memo(() => {\r\n  const navigate = useNavigate();\r\n  const { alert } = useDialog();\r\n  const {\r\n    currentSession,\r\n    studySetContent,\r\n    nextItem,\r\n    previousItem,\r\n    toggleFlag,\r\n    markReviewed,\r\n    endStudySession,\r\n    updateTimeSpent,\r\n    undo,\r\n    redo,\r\n    canUndo,\r\n    canRedo\r\n  } = useStudyStore();\r\n\r\n  const [isFlipped, setIsFlipped] = useState(false);\r\n  const [startTime, setStartTime] = useState(Date.now());\r\n  const { announce, AnnouncementComponent } = useScreenReaderAnnouncements();\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      updateTimeSpent(1);\r\n    }, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [updateTimeSpent]);\r\n\r\n  useEffect(() => {\r\n    // Reset flip state when moving to new card\r\n    setIsFlipped(false);\r\n    setStartTime(Date.now());\r\n  }, [currentSession?.currentIndex]);\r\n\r\n  // Keyboard navigation\r\n  useEffect(() => {\r\n    const handleKeyPress = (e: KeyboardEvent) => {\r\n      // Don't trigger shortcuts when typing in inputs\r\n      if (\r\n        e.target instanceof HTMLInputElement ||\r\n        e.target instanceof HTMLTextAreaElement ||\r\n        e.target instanceof HTMLSelectElement ||\r\n        (e.target as HTMLElement)?.contentEditable === 'true'\r\n      ) {\r\n        return;\r\n      }\r\n\r\n      if (e.key === 'ArrowLeft') {\r\n        e.preventDefault();\r\n        // Call store function directly to avoid stale closure - now supports circular navigation\r\n        if (currentSession) {\r\n          previousItem();\r\n          // Calculate the new index for announcement (with wraparound)\r\n          const newIndex = currentSession.currentIndex === 0\r\n            ? currentSession.totalItems\r\n            : currentSession.currentIndex;\r\n          announce(`Card ${newIndex} of ${currentSession.totalItems}`);\r\n        }\r\n      } else if (e.key === 'ArrowRight') {\r\n        e.preventDefault();\r\n        // Call store function directly to avoid stale closure - now supports circular navigation\r\n        if (currentSession) {\r\n          nextItem();\r\n          // Calculate the new index for announcement (with wraparound)\r\n          const newIndex = currentSession.currentIndex === currentSession.totalItems - 1\r\n            ? 1\r\n            : currentSession.currentIndex + 2;\r\n          announce(`Card ${newIndex} of ${currentSession.totalItems}`);\r\n        }\r\n      } else if (e.key === ' ' || e.key === 'ArrowUp' || e.key === 'ArrowDown') {\r\n        e.preventDefault();\r\n        setIsFlipped(!isFlipped);\r\n      } else if (e.key === 'f' || e.key === 'F') {\r\n        e.preventDefault();\r\n        if (currentSession && studySetContent?.flashcards) {\r\n          const currentFlashcard = studySetContent.flashcards[currentSession.currentIndex];\r\n          if (currentFlashcard) {\r\n            toggleFlag(currentFlashcard.id);\r\n            const isFlagged = currentSession.flaggedItems.includes(currentFlashcard.id);\r\n            announce(isFlagged ? 'Card unflagged' : 'Card flagged for review');\r\n          }\r\n        }\r\n      } else if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {\r\n        e.preventDefault();\r\n        if (canUndo) undo();\r\n      } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {\r\n        e.preventDefault();\r\n        if (canRedo) redo();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyPress);\r\n    return () => document.removeEventListener('keydown', handleKeyPress);\r\n  }, [currentSession, studySetContent, isFlipped, canUndo, canRedo, previousItem, nextItem, toggleFlag, undo, redo, announce]);\r\n\r\n  if (!currentSession || !studySetContent?.flashcards) {\r\n    return (\r\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"text-center py-12\">\r\n          <div className=\"text-gray-400 mb-4\">No flashcard session found</div>\r\n          <Button onClick={() => navigate('/dashboard')} variant=\"secondary\">\r\n            Back to Study Sets\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const flashcards = studySetContent.flashcards;\r\n  const currentFlashcard = flashcards[currentSession.currentIndex];\r\n  const progress = ((currentSession.currentIndex + 1) / currentSession.totalItems) * 100;\r\n  const isFirstCard = currentSession.currentIndex === 0;\r\n  const isLastCard = currentSession.currentIndex === currentSession.totalItems - 1;\r\n  const isFlagged = currentSession.flaggedItems.includes(currentFlashcard.id);\r\n\r\n  const handleNext = () => {\r\n    if (isFlipped) {\r\n      markReviewed(currentFlashcard.id);\r\n    }\r\n\r\n    // With circular navigation, we always move to next item (wraps around)\r\n    nextItem();\r\n    // Calculate the new index for announcement (with wraparound)\r\n    const newIndex = isLastCard ? 1 : currentSession.currentIndex + 2;\r\n    announce(`Card ${newIndex} of ${currentSession.totalItems}`);\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    // With circular navigation, we always move to previous item (wraps around)\r\n    previousItem();\r\n    // Calculate the new index for announcement (with wraparound)\r\n    const newIndex = isFirstCard ? currentSession.totalItems : currentSession.currentIndex;\r\n    announce(`Card ${newIndex} of ${currentSession.totalItems}`);\r\n  };\r\n\r\n  const handleToggleFlag = () => {\r\n    toggleFlag(currentFlashcard.id);\r\n    const flagStatus = isFlagged ? 'unflagged' : 'flagged';\r\n    announce(`Card ${flagStatus}`);\r\n  };\r\n\r\n  const handleFlip = () => {\r\n    setIsFlipped(!isFlipped);\r\n    announce(isFlipped ? 'Showing front of card' : 'Showing back of card');\r\n  };\r\n\r\n  const handleFinishStudy = async () => {\r\n    const timeSpent = Math.floor((Date.now() - startTime) / 1000);\r\n    const reviewedCount = currentSession.reviewedItems.length;\r\n    const flaggedCount = currentSession.flaggedItems.length;\r\n\r\n    endStudySession();\r\n\r\n    // Show completion modal or navigate with results\r\n    await alert({\r\n      title: 'Study Session Complete!',\r\n      message: `Reviewed: ${reviewedCount}/${currentSession.totalItems} cards\\nFlagged: ${flaggedCount} cards\\nTime spent: ${Math.floor(timeSpent / 60)}m ${timeSpent % 60}s`,\r\n      variant: 'success',\r\n      confirmText: 'Continue'\r\n    });\r\n\r\n    navigate(`/study-sets/${currentSession.studySetId}`);\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <button\r\n          onClick={() => navigate(`/study-sets/${currentSession.studySetId}`)}\r\n          className=\"text-gray-400 hover:text-white flex items-center\"\r\n        >\r\n          ← Back to Study Set\r\n        </button>\r\n        \r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-xl font-semibold text-white\">\r\n            {studySetContent.studySet?.name}\r\n          </h1>\r\n          <p className=\"text-sm text-gray-400\">\r\n            Card {currentSession.currentIndex + 1} of {currentSession.totalItems}\r\n          </p>\r\n        </div>\r\n\r\n        <Button\r\n          onClick={handleFinishStudy}\r\n          variant=\"secondary\"\r\n          size=\"sm\"\r\n        >\r\n          Finish\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Progress Bar */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"w-full bg-gray-700 rounded-full h-2\">\r\n          <div \r\n            className=\"bg-primary-500 h-2 rounded-full transition-all duration-300\"\r\n            style={{ width: `${progress}%` }}\r\n          ></div>\r\n        </div>\r\n        <div className=\"flex justify-between text-xs text-gray-400 mt-1\">\r\n          <span>Progress: {Math.round(progress)}%</span>\r\n          <span>Time: {Math.floor(currentSession.timeSpent / 60)}:{(currentSession.timeSpent % 60).toString().padStart(2, '0')}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Flashcard */}\r\n      <div className=\"mb-8 flashcard-container\">\r\n        <div\r\n          className={`\r\n            relative w-full min-h-[24rem] max-h-[32rem] cursor-pointer transition-transform duration-500 transform-style-preserve-3d\r\n            ${isFlipped ? 'rotate-y-180' : ''}\r\n          `}\r\n          onClick={handleFlip}\r\n          role=\"button\"\r\n          tabIndex={0}\r\n          aria-label={`Flashcard ${currentSession.currentIndex + 1} of ${currentSession.totalItems}. ${isFlipped ? 'Showing back' : 'Showing front'}. Click or press space to flip.`}\r\n          onKeyDown={(e) => {\r\n            if (e.key === ' ' || e.key === 'Enter') {\r\n              e.preventDefault();\r\n              handleFlip();\r\n            }\r\n          }}\r\n        >\r\n          {/* Front of card */}\r\n          <div className={`\r\n            absolute inset-0 w-full h-full backface-hidden\r\n            bg-background-secondary border border-gray-600 rounded-lg p-6 sm:p-8\r\n            flex flex-col justify-center text-center overflow-y-auto\r\n          `}>\r\n            <div className=\"flex-1 flex flex-col justify-center min-h-0\">\r\n              <div className=\"text-sm text-gray-400 mb-4 flex-shrink-0\">FRONT</div>\r\n              <div className=\"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center\">\r\n                <div className=\"max-w-full\">\r\n                  {currentFlashcard.front}\r\n                </div>\r\n              </div>\r\n              {!isFlipped && (\r\n                <div className=\"text-sm text-gray-500 mt-6 flex-shrink-0\">\r\n                  Click to reveal answer\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Back of card */}\r\n          <div className={`\r\n            absolute inset-0 w-full h-full backface-hidden rotate-y-180\r\n            bg-primary-500/10 border border-primary-500/30 rounded-lg p-6 sm:p-8\r\n            flex flex-col justify-center text-center overflow-y-auto\r\n          `}>\r\n            <div className=\"flex-1 flex flex-col justify-center min-h-0\">\r\n              <div className=\"text-sm text-primary-400 mb-4 flex-shrink-0\">BACK</div>\r\n              <div className=\"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center\">\r\n                <div className=\"max-w-full\">\r\n                  {currentFlashcard.back}\r\n                </div>\r\n              </div>\r\n              {isFlipped && (\r\n                <div className=\"text-sm text-gray-500 mt-6 flex-shrink-0\">\r\n                  Click to flip back\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Controls */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <Button\r\n          onClick={handlePrevious}\r\n          variant=\"secondary\"\r\n        >\r\n          ← Previous\r\n        </Button>\r\n\r\n        <div className=\"flex items-center space-x-4\">\r\n          <Button\r\n            onClick={handleToggleFlag}\r\n            variant={isFlagged ? \"primary\" : \"secondary\"}\r\n            size=\"sm\"\r\n          >\r\n            {isFlagged ? '🚩 Flagged' : '🏳️ Flag'}\r\n          </Button>\r\n\r\n          <Button\r\n            onClick={() => setIsFlipped(!isFlipped)}\r\n            variant=\"secondary\"\r\n          >\r\n            {isFlipped ? 'Show Front' : 'Show Back'}\r\n          </Button>\r\n        </div>\r\n\r\n        <Button\r\n          onClick={handleNext}\r\n          variant=\"primary\"\r\n        >\r\n          Next →\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Keyboard shortcuts */}\r\n      <div className=\"mt-8 text-center text-sm text-gray-500\">\r\n        <p>Keyboard shortcuts: ← → (navigate) • Space (flip) • F (flag)</p>\r\n      </div>\r\n\r\n      {/* Screen reader announcements */}\r\n      <AnnouncementComponent />\r\n    </div>\r\n  );\r\n});\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useStudyStore } from '../../stores/studyStore';\r\nimport { useDialog } from '../../contexts/DialogContext';\r\nimport { Button } from '../common/Button';\r\n\r\n\r\n\r\nexport const QuizInterface: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const { alert } = useDialog();\r\n  const {\r\n    currentSession,\r\n    studySetContent,\r\n    nextItem,\r\n    submitQuizAnswer,\r\n    endStudySession,\r\n    updateTimeSpent\r\n  } = useStudyStore();\r\n\r\n  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);\r\n  const [hasAnswered, setHasAnswered] = useState(false);\r\n  const [showExplanation, setShowExplanation] = useState(false);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      updateTimeSpent(1);\r\n    }, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [updateTimeSpent]);\r\n\r\n  useEffect(() => {\r\n    // Reset state when moving to new question\r\n    setSelectedAnswers([]);\r\n    setHasAnswered(false);\r\n    setShowExplanation(false);\r\n\r\n  }, [currentSession?.currentIndex]);\r\n\r\n  if (!currentSession || !studySetContent?.questions) {\r\n    return (\r\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"text-center py-12\">\r\n          <div className=\"text-gray-400 mb-4\">No quiz session found</div>\r\n          <Button onClick={() => navigate('/dashboard')} variant=\"secondary\">\r\n            Back to Dashboard\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const questions = studySetContent.questions;\r\n  const currentQuestion = questions[currentSession.currentIndex];\r\n  const progress = ((currentSession.currentIndex + 1) / currentSession.totalItems) * 100;\r\n  const isLastQuestion = currentSession.currentIndex === currentSession.totalItems - 1;\r\n\r\n  const handleAnswerSelect = (answer: string) => {\r\n    if (hasAnswered) return;\r\n\r\n    if (currentQuestion.question_type === 'multiple_choice' || currentQuestion.question_type === 'true_false') {\r\n      setSelectedAnswers([answer]);\r\n    } else if (currentQuestion.question_type === 'select_all') {\r\n      setSelectedAnswers(prev => \r\n        prev.includes(answer) \r\n          ? prev.filter(a => a !== answer)\r\n          : [...prev, answer]\r\n      );\r\n    }\r\n  };\r\n\r\n  const handleShortAnswerChange = (value: string) => {\r\n    if (hasAnswered) return;\r\n    setSelectedAnswers([value]);\r\n  };\r\n\r\n  const handleSubmitAnswer = () => {\r\n    if (hasAnswered || selectedAnswers.length === 0) return;\r\n\r\n    const isCorrect = checkAnswer();\r\n\r\n    submitQuizAnswer(currentQuestion.id, selectedAnswers, isCorrect);\r\n    setHasAnswered(true);\r\n    setShowExplanation(true);\r\n  };\r\n\r\n  const checkAnswer = (): boolean => {\r\n    const correctAnswers = currentQuestion.correct_answers;\r\n    \r\n    if (currentQuestion.question_type === 'short_answer') {\r\n      // For short answer, check if any correct answer is contained in the user's answer\r\n      const userAnswer = selectedAnswers[0]?.toLowerCase().trim() || '';\r\n      return correctAnswers.some(correct => \r\n        userAnswer.includes(correct.toLowerCase().trim()) ||\r\n        correct.toLowerCase().trim().includes(userAnswer)\r\n      );\r\n    } else {\r\n      // For other types, exact match required\r\n      return selectedAnswers.length === correctAnswers.length &&\r\n             selectedAnswers.every(answer => correctAnswers.includes(answer));\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (isLastQuestion) {\r\n      handleFinishQuiz();\r\n    } else {\r\n      nextItem();\r\n    }\r\n  };\r\n\r\n  const handleFinishQuiz = async () => {\r\n    const totalQuestions = currentSession.totalItems;\r\n    const correctAnswers = currentSession.correctAnswers || 0;\r\n    const percentage = Math.round((correctAnswers / totalQuestions) * 100);\r\n    const timeSpent = currentSession.timeSpent;\r\n\r\n    endStudySession();\r\n\r\n    // Show results modal or navigate with results\r\n    await alert({\r\n      title: 'Quiz Complete!',\r\n      message: `Score: ${correctAnswers}/${totalQuestions} (${percentage}%)\\nTime spent: ${Math.floor(timeSpent / 60)}m ${timeSpent % 60}s`,\r\n      variant: 'success',\r\n      confirmText: 'Continue'\r\n    });\r\n\r\n    navigate(`/study-sets/${currentSession.studySetId}`);\r\n  };\r\n\r\n  const renderQuestionOptions = () => {\r\n    if (!currentQuestion.options) return null;\r\n\r\n    return currentQuestion.options.map((option, index) => {\r\n      const isSelected = selectedAnswers.includes(option);\r\n      const isCorrect = currentQuestion.correct_answers.includes(option);\r\n      \r\n      let buttonClass = 'w-full text-left p-4 rounded-lg border transition-all ';\r\n      \r\n      if (hasAnswered) {\r\n        if (isCorrect) {\r\n          buttonClass += 'border-green-500 bg-green-500/20 text-green-400';\r\n        } else if (isSelected && !isCorrect) {\r\n          buttonClass += 'border-red-500 bg-red-500/20 text-red-400';\r\n        } else {\r\n          buttonClass += 'border-gray-600 bg-background-secondary text-gray-400';\r\n        }\r\n      } else {\r\n        if (isSelected) {\r\n          buttonClass += 'border-primary-500 bg-primary-500/20 text-primary-400';\r\n        } else {\r\n          buttonClass += 'border-gray-600 bg-background-secondary text-white hover:border-gray-500';\r\n        }\r\n      }\r\n\r\n      return (\r\n        <button\r\n          key={index}\r\n          onClick={() => handleAnswerSelect(option)}\r\n          disabled={hasAnswered}\r\n          className={buttonClass}\r\n        >\r\n          <div className=\"flex items-center space-x-3\">\r\n            {currentQuestion.question_type === 'multiple_choice' ? (\r\n              // Radio button for multiple choice\r\n              <div className={`\r\n                w-5 h-5 rounded-full border-2 flex items-center justify-center\r\n                ${isSelected ? 'border-current' : 'border-gray-500'}\r\n              `}>\r\n                {isSelected && (\r\n                  <div className=\"w-3 h-3 rounded-full bg-current\"></div>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              // Checkbox for select all\r\n              <div className={`\r\n                w-5 h-5 rounded border-2 flex items-center justify-center\r\n                ${isSelected ? 'border-current' : 'border-gray-500'}\r\n              `}>\r\n                {isSelected && (\r\n                  <div className=\"w-2 h-2 rounded bg-current\"></div>\r\n                )}\r\n              </div>\r\n            )}\r\n            <span>{option}</span>\r\n          </div>\r\n        </button>\r\n      );\r\n    });\r\n  };\r\n\r\n  const renderTrueFalseOptions = () => {\r\n    const options = ['True', 'False'];\r\n\r\n    return options.map((option) => {\r\n      const isSelected = selectedAnswers.includes(option);\r\n      const isCorrect = currentQuestion.correct_answers.includes(option);\r\n\r\n      let buttonClass = 'w-full text-left p-4 rounded-lg border transition-all ';\r\n\r\n      if (hasAnswered) {\r\n        if (isCorrect) {\r\n          buttonClass += 'border-green-500 bg-green-500/20 text-green-400';\r\n        } else if (isSelected && !isCorrect) {\r\n          buttonClass += 'border-red-500 bg-red-500/20 text-red-400';\r\n        } else {\r\n          buttonClass += 'border-gray-600 bg-background-secondary text-gray-400';\r\n        }\r\n      } else {\r\n        if (isSelected) {\r\n          buttonClass += 'border-primary-500 bg-primary-500/20 text-primary-400';\r\n        } else {\r\n          buttonClass += 'border-gray-600 bg-background-secondary text-white hover:border-gray-500';\r\n        }\r\n      }\r\n\r\n      return (\r\n        <button\r\n          key={option}\r\n          onClick={() => handleAnswerSelect(option)}\r\n          disabled={hasAnswered}\r\n          className={buttonClass}\r\n        >\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className={`\r\n              w-5 h-5 rounded-full border-2 flex items-center justify-center\r\n              ${isSelected ? 'border-current' : 'border-gray-500'}\r\n            `}>\r\n              {isSelected && (\r\n                <div className=\"w-3 h-3 rounded-full bg-current\"></div>\r\n              )}\r\n            </div>\r\n            <span className=\"text-lg font-medium\">{option}</span>\r\n          </div>\r\n        </button>\r\n      );\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <button\r\n          onClick={() => navigate(`/study-sets/${currentSession.studySetId}`)}\r\n          className=\"text-gray-400 hover:text-white flex items-center\"\r\n        >\r\n          ← Back to Study Set\r\n        </button>\r\n        \r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-xl font-semibold text-white\">\r\n            {studySetContent.studySet?.name}\r\n          </h1>\r\n          <p className=\"text-sm text-gray-400\">\r\n            Question {currentSession.currentIndex + 1} of {currentSession.totalItems}\r\n          </p>\r\n        </div>\r\n\r\n        <Button\r\n          onClick={handleFinishQuiz}\r\n          variant=\"secondary\"\r\n          size=\"sm\"\r\n        >\r\n          Finish Quiz\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Progress Bar */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"w-full bg-gray-700 rounded-full h-2\">\r\n          <div \r\n            className=\"bg-primary-500 h-2 rounded-full transition-all duration-300\"\r\n            style={{ width: `${progress}%` }}\r\n          ></div>\r\n        </div>\r\n        <div className=\"flex justify-between text-xs text-gray-400 mt-1\">\r\n          <span>Progress: {Math.round(progress)}%</span>\r\n          <span>Score: {currentSession.correctAnswers || 0}/{currentSession.currentIndex + (hasAnswered ? 1 : 0)}</span>\r\n          <span>Time: {Math.floor(currentSession.timeSpent / 60)}:{(currentSession.timeSpent % 60).toString().padStart(2, '0')}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Question */}\r\n      <div className=\"bg-background-secondary rounded-lg p-6 mb-6\">\r\n        <div className=\"mb-4\">\r\n          <span className=\"text-sm text-gray-400 uppercase tracking-wide\">\r\n            {currentQuestion.question_type.replace('_', ' ')}\r\n          </span>\r\n        </div>\r\n        \r\n        <h2 className=\"text-xl text-white mb-6 leading-relaxed\">\r\n          {currentQuestion.question_text}\r\n        </h2>\r\n\r\n        {/* Answer Options */}\r\n        <div className=\"space-y-3\">\r\n          {currentQuestion.question_type === 'short_answer' ? (\r\n            <textarea\r\n              value={selectedAnswers[0] || ''}\r\n              onChange={(e) => handleShortAnswerChange(e.target.value)}\r\n              disabled={hasAnswered}\r\n              placeholder=\"Type your answer here...\"\r\n              rows={3}\r\n              className=\"w-full px-4 py-3 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50\"\r\n            />\r\n          ) : currentQuestion.question_type === 'true_false' ? (\r\n            renderTrueFalseOptions()\r\n          ) : (\r\n            renderQuestionOptions()\r\n          )}\r\n        </div>\r\n\r\n        {/* Submit Button */}\r\n        {!hasAnswered && (\r\n          <div className=\"mt-6\">\r\n            <Button\r\n              onClick={handleSubmitAnswer}\r\n              disabled={selectedAnswers.length === 0}\r\n              className=\"w-full\"\r\n            >\r\n              Submit Answer\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        {/* Explanation */}\r\n        {hasAnswered && showExplanation && currentQuestion.explanation && (\r\n          <div className=\"mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg\">\r\n            <h4 className=\"text-blue-400 font-medium mb-2\">Explanation</h4>\r\n            <p className=\"text-gray-300\">{currentQuestion.explanation}</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      {hasAnswered && (\r\n        <div className=\"flex justify-center\">\r\n          <Button\r\n            onClick={handleNext}\r\n            variant=\"primary\"\r\n            size=\"lg\"\r\n          >\r\n            {isLastQuestion ? 'Finish Quiz' : 'Next Question →'}\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { useStudyStore } from '../stores/studyStore';\r\nimport { useUserSettings } from '../hooks/useUserSettings';\r\nimport { FlashcardInterface } from '../components/study/FlashcardInterface';\r\nimport { QuizInterface } from '../components/study/QuizInterface';\r\n\r\nexport const StudyPage: React.FC = () => {\r\n  const { id, mode } = useParams<{ id: string; mode: 'flashcards' | 'quiz' }>();\r\n  const navigate = useNavigate();\r\n  const { currentSession, startStudySession } = useStudyStore();\r\n  const { settings: userSettings } = useUserSettings();\r\n\r\n  useEffect(() => {\r\n    // If no active session or session doesn't match URL, start new session\r\n    if (!currentSession || \r\n        currentSession.studySetId !== id || \r\n        currentSession.type !== mode) {\r\n      \r\n      if (id && mode && (mode === 'flashcards' || mode === 'quiz')) {\r\n        const shuffleEnabled = userSettings?.shuffle_flashcards || false;\r\n        startStudySession(id, mode, shuffleEnabled).catch((error) => {\r\n          console.error('Failed to start study session:', error);\r\n          alert(error.message || 'Failed to start study session');\r\n          navigate(`/study-sets/${id}`);\r\n        });\r\n      } else {\r\n        navigate('/dashboard');\r\n      }\r\n    }\r\n  }, [id, mode, currentSession, startStudySession, navigate, userSettings]);\r\n\r\n  if (!currentSession) {\r\n    return (\r\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"flex items-center justify-center py-12\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500\"></div>\r\n          <span className=\"ml-3 text-gray-400\">Starting study session...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (mode === 'flashcards') {\r\n    return <FlashcardInterface />;\r\n  } else if (mode === 'quiz') {\r\n    return <QuizInterface />;\r\n  } else {\r\n    navigate('/dashboard');\r\n    return null;\r\n  }\r\n};\r\n", "import { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  UserSettings,\r\n  UserSettingsUpdate,\r\n  UseUserSettingsReturn\r\n} from '../types/userSettings';\r\n\r\n\r\n\r\nexport const useUserSettings = (): UseUserSettingsReturn => {\r\n  const [settings, setSettings] = useState<UserSettings | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchSettings = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await fetch('/api/user/settings', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || 'Failed to fetch user settings');\r\n      }\r\n\r\n      const data = await response.json();\r\n      setSettings(data.data);\r\n    } catch (err: any) {\r\n      setError(err.message);\r\n      console.error('Error fetching user settings:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateSettings = useCallback(async (updates: UserSettingsUpdate) => {\r\n    try {\r\n      setError(null);\r\n\r\n      const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await fetch('/api/user/settings', {\r\n        method: 'PATCH',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify(updates)\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || 'Failed to update user settings');\r\n      }\r\n\r\n      const data = await response.json();\r\n      setSettings(data.data);\r\n    } catch (err: any) {\r\n      setError(err.message);\r\n      console.error('Error updating user settings:', err);\r\n      throw err; // Re-throw to allow caller to handle\r\n    }\r\n  }, []);\r\n\r\n  const refetch = useCallback(async () => {\r\n    await fetchSettings();\r\n  }, [fetchSettings]);\r\n\r\n  useEffect(() => {\r\n    fetchSettings();\r\n  }, [fetchSettings]);\r\n\r\n  return {\r\n    settings,\r\n    loading,\r\n    error,\r\n    updateSettings,\r\n    refetch\r\n  };\r\n};\r\n", "import { create } from \"zustand\";\nimport { StudySet, Flashcard, QuizQuestion } from \"../shared/types\";\n\n// Utility function for shuffling arrays using Fisher-Yates algorithm\nconst shuffleArray = <T>(array: T[]): T[] => {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n};\n\ninterface StudyAction {\n  type:\n    | \"NEXT_ITEM\"\n    | \"PREVIOUS_ITEM\"\n    | \"TOGGLE_FLAG\"\n    | \"MARK_REVIEWED\"\n    | \"SUBMIT_ANSWER\";\n  payload: any;\n  previousState: Partial<StudySession>;\n  timestamp: number;\n}\n\ninterface StudySession {\n  studySetId: string;\n  type: \"flashcards\" | \"quiz\";\n  startTime: Date;\n  currentIndex: number;\n  totalItems: number;\n  reviewedItems: number[];\n  flaggedItems: string[];\n  correctAnswers?: number; // For quizzes\n  timeSpent: number; // in seconds\n  isShuffled: boolean; // Whether the session uses shuffled order\n  originalOrder?: number[]; // Original indices for unshuffling if needed\n}\n\ninterface StudySessionData {\n  id: string;\n  studySetId: string;\n  type: \"flashcards\" | \"quiz\";\n  startTime: Date;\n  endTime?: Date;\n  totalItems: number;\n  reviewedItems: number;\n  flaggedItems: number;\n  correctAnswers?: number;\n  timeSpent: number;\n}\n\ninterface StudyState {\n  currentSession: StudySession | null;\n  studySetContent: {\n    studySet?: StudySet;\n    flashcards?: Flashcard[];\n    questions?: QuizQuestion[];\n  } | null;\n  studySets: StudySet[];\n  sessions: StudySessionData[];\n  isLoading: boolean;\n  error: string | null;\n\n  // Undo/Redo functionality\n  actionHistory: StudyAction[];\n  currentActionIndex: number;\n  canUndo: boolean;\n  canRedo: boolean;\n\n  // Actions\n  startStudySession: (\n    studySetId: string,\n    type: \"flashcards\" | \"quiz\",\n    shuffle?: boolean\n  ) => Promise<void>;\n  endStudySession: () => void;\n  nextItem: () => void;\n  previousItem: () => void;\n  goToItem: (index: number) => void;\n  toggleFlag: (itemId: string) => void;\n  markReviewed: (itemId: string) => void;\n  submitQuizAnswer: (\n    questionId: string,\n    answer: string[],\n    isCorrect: boolean\n  ) => void;\n  updateTimeSpent: (seconds: number) => void;\n  fetchStudySetContent: (\n    studySetId: string,\n    forceRefresh?: boolean\n  ) => Promise<void>;\n  fetchStudySets: () => Promise<void>;\n  fetchStudySessions: (\n    timeRange?: \"7d\" | \"30d\" | \"90d\" | \"all\"\n  ) => Promise<void>;\n\n  // Cache management methods\n  invalidateStudySetContent: (studySetId?: string) => void;\n  refreshStudySetContent: (studySetId: string) => Promise<void>;\n  invalidateStudySets: () => void;\n\n  // Undo/Redo actions\n  undo: () => void;\n  redo: () => void;\n  clearHistory: () => void;\n  addToHistory: (action: StudyAction) => void;\n}\n\nexport const useStudyStore = create<StudyState>((set, get) => ({\n  currentSession: null,\n  studySetContent: null,\n  studySets: [],\n  sessions: [],\n  isLoading: false,\n  error: null,\n\n  // Undo/Redo state\n  actionHistory: [],\n  currentActionIndex: -1,\n  canUndo: false,\n  canRedo: false,\n\n  fetchStudySetContent: async (studySetId: string, forceRefresh = false) => {\n    // If forceRefresh is true or content doesn't exist, refetch\n    const { studySetContent } = get();\n    if (\n      forceRefresh ||\n      !studySetContent ||\n      studySetContent.studySet?.id !== studySetId\n    ) {\n      set({ isLoading: true, error: null });\n\n      try {\n        const token = localStorage.getItem(\"auth_token\");\n\n        const response = await fetch(`/api/study-sets/${studySetId}/content`, {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (!response.ok) {\n          const errorResult = await response.json();\n          throw new Error(\n            errorResult.error || \"Failed to fetch study set content\"\n          );\n        }\n\n        const result = await response.json();\n\n        if (result.success) {\n          set({\n            studySetContent: {\n              studySet: result.data.studySet,\n              flashcards: result.data.flashcards || [],\n              questions: result.data.questions || [],\n            },\n            isLoading: false,\n          });\n        } else {\n          throw new Error(result.error);\n        }\n      } catch (error: any) {\n        set({\n          error: error.message || \"Failed to fetch study set content\",\n          isLoading: false,\n        });\n        throw error;\n      }\n    }\n  },\n\n  startStudySession: async (\n    studySetId: string,\n    type: \"flashcards\" | \"quiz\",\n    shuffle = false\n  ) => {\n    const { studySetContent, fetchStudySetContent } = get();\n\n    // Fetch content if not already loaded\n    if (!studySetContent || studySetContent.studySet?.id !== studySetId) {\n      await fetchStudySetContent(studySetId);\n    }\n\n    const content = get().studySetContent;\n    if (!content) {\n      throw new Error(\"Failed to load study set content\");\n    }\n\n    const totalItems =\n      type === \"flashcards\"\n        ? content.flashcards?.length || 0\n        : content.questions?.length || 0;\n\n    if (totalItems === 0) {\n      throw new Error(\"No study materials found in this set\");\n    }\n\n    // Handle shuffling\n    let originalOrder: number[] | undefined;\n    if (shuffle) {\n      // Create array of original indices\n      originalOrder = Array.from({ length: totalItems }, (_, i) => i);\n\n      // Shuffle the content arrays\n      if (type === \"flashcards\" && content.flashcards) {\n        const shuffledFlashcards = shuffleArray(content.flashcards);\n        set((state) => ({\n          studySetContent: {\n            ...state.studySetContent!,\n            flashcards: shuffledFlashcards,\n          },\n        }));\n      } else if (type === \"quiz\" && content.questions) {\n        const shuffledQuestions = shuffleArray(content.questions);\n        set((state) => ({\n          studySetContent: {\n            ...state.studySetContent!,\n            questions: shuffledQuestions,\n          },\n        }));\n      }\n    }\n\n    set({\n      currentSession: {\n        studySetId,\n        type,\n        startTime: new Date(),\n        currentIndex: 0,\n        totalItems,\n        reviewedItems: [],\n        flaggedItems: [],\n        correctAnswers: type === \"quiz\" ? 0 : undefined,\n        timeSpent: 0,\n        isShuffled: shuffle,\n        originalOrder,\n      },\n    });\n  },\n\n  endStudySession: () => {\n    set({ currentSession: null });\n  },\n\n  nextItem: () => {\n    const { currentSession, addToHistory } = get();\n    if (!currentSession) return;\n\n    // Implement circular navigation: if at last item, wrap to first item\n    const nextIndex =\n      currentSession.currentIndex === currentSession.totalItems - 1\n        ? 0\n        : currentSession.currentIndex + 1;\n\n    // Record action in history\n    addToHistory({\n      type: \"NEXT_ITEM\",\n      payload: { fromIndex: currentSession.currentIndex, toIndex: nextIndex },\n      previousState: { currentIndex: currentSession.currentIndex },\n      timestamp: Date.now(),\n    });\n\n    set({\n      currentSession: {\n        ...currentSession,\n        currentIndex: nextIndex,\n      },\n    });\n  },\n\n  previousItem: () => {\n    const { currentSession, addToHistory } = get();\n    if (!currentSession) return;\n\n    // Implement circular navigation: if at first item, wrap to last item\n    const prevIndex =\n      currentSession.currentIndex === 0\n        ? currentSession.totalItems - 1\n        : currentSession.currentIndex - 1;\n\n    // Record action in history\n    addToHistory({\n      type: \"PREVIOUS_ITEM\",\n      payload: { fromIndex: currentSession.currentIndex, toIndex: prevIndex },\n      previousState: { currentIndex: currentSession.currentIndex },\n      timestamp: Date.now(),\n    });\n\n    set({\n      currentSession: {\n        ...currentSession,\n        currentIndex: prevIndex,\n      },\n    });\n  },\n\n  goToItem: (index: number) => {\n    const { currentSession } = get();\n    if (!currentSession) return;\n\n    const clampedIndex = Math.max(\n      0,\n      Math.min(index, currentSession.totalItems - 1)\n    );\n    set({\n      currentSession: {\n        ...currentSession,\n        currentIndex: clampedIndex,\n      },\n    });\n  },\n\n  toggleFlag: (itemId: string) => {\n    const { currentSession, addToHistory } = get();\n    if (!currentSession) return;\n\n    const wasFlagged = currentSession.flaggedItems.includes(itemId);\n    const flaggedItems = wasFlagged\n      ? currentSession.flaggedItems.filter((id) => id !== itemId)\n      : [...currentSession.flaggedItems, itemId];\n\n    // Record action in history\n    addToHistory({\n      type: \"TOGGLE_FLAG\",\n      payload: { itemId, wasFlagged },\n      previousState: { flaggedItems: currentSession.flaggedItems },\n      timestamp: Date.now(),\n    });\n\n    set({\n      currentSession: {\n        ...currentSession,\n        flaggedItems,\n      },\n    });\n  },\n\n  markReviewed: (_itemId: string) => {\n    const { currentSession } = get();\n    if (!currentSession) return;\n\n    if (!currentSession.reviewedItems.includes(currentSession.currentIndex)) {\n      set({\n        currentSession: {\n          ...currentSession,\n          reviewedItems: [\n            ...currentSession.reviewedItems,\n            currentSession.currentIndex,\n          ],\n        },\n      });\n    }\n  },\n\n  submitQuizAnswer: (\n    questionId: string,\n    _answer: string[],\n    isCorrect: boolean\n  ) => {\n    const { currentSession, markReviewed } = get();\n    if (!currentSession || currentSession.type !== \"quiz\") return;\n\n    markReviewed(questionId);\n\n    if (isCorrect) {\n      set({\n        currentSession: {\n          ...currentSession,\n          correctAnswers: (currentSession.correctAnswers || 0) + 1,\n        },\n      });\n    }\n  },\n\n  updateTimeSpent: (seconds: number) => {\n    const { currentSession } = get();\n    if (!currentSession) return;\n\n    set({\n      currentSession: {\n        ...currentSession,\n        timeSpent: currentSession.timeSpent + seconds,\n      },\n    });\n  },\n\n  // Helper function to add action to history\n  addToHistory: (action: StudyAction) => {\n    const { actionHistory, currentActionIndex } = get();\n\n    // Remove any actions after current index (when undoing then doing new action)\n    const newHistory = actionHistory.slice(0, currentActionIndex + 1);\n    newHistory.push(action);\n\n    // Limit history to last 50 actions for performance\n    const limitedHistory = newHistory.slice(-50);\n\n    set({\n      actionHistory: limitedHistory,\n      currentActionIndex: limitedHistory.length - 1,\n      canUndo: limitedHistory.length > 0,\n      canRedo: false,\n    });\n  },\n\n  undo: () => {\n    const { actionHistory, currentActionIndex, currentSession } = get();\n\n    if (currentActionIndex < 0 || !currentSession) return;\n\n    const action = actionHistory[currentActionIndex];\n\n    // Restore previous state\n    set({\n      currentSession: {\n        ...currentSession,\n        ...action.previousState,\n      },\n      currentActionIndex: currentActionIndex - 1,\n      canUndo: currentActionIndex > 0,\n      canRedo: true,\n    });\n  },\n\n  redo: () => {\n    const { actionHistory, currentActionIndex, currentSession } = get();\n\n    if (currentActionIndex >= actionHistory.length - 1 || !currentSession)\n      return;\n\n    const nextActionIndex = currentActionIndex + 1;\n    const action = actionHistory[nextActionIndex];\n\n    // Re-apply the action\n    switch (action.type) {\n      case \"NEXT_ITEM\":\n        get().nextItem();\n        break;\n      case \"PREVIOUS_ITEM\":\n        get().previousItem();\n        break;\n      case \"TOGGLE_FLAG\":\n        get().toggleFlag(action.payload.itemId);\n        break;\n      case \"MARK_REVIEWED\":\n        get().markReviewed(action.payload.itemId);\n        break;\n    }\n\n    set({\n      currentActionIndex: nextActionIndex,\n      canUndo: true,\n      canRedo: nextActionIndex < actionHistory.length - 1,\n    });\n  },\n\n  clearHistory: () => {\n    set({\n      actionHistory: [],\n      currentActionIndex: -1,\n      canUndo: false,\n      canRedo: false,\n    });\n  },\n\n  fetchStudySets: async () => {\n    set({ isLoading: true, error: null });\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n\n      const response = await fetch(\"/api/study-sets\", {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Failed to fetch study sets\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        set({\n          studySets: result.data,\n          isLoading: false,\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error: any) {\n      set({\n        error: error.message || \"Failed to fetch study sets\",\n        isLoading: false,\n      });\n      throw error;\n    }\n  },\n\n  fetchStudySessions: async (timeRange = \"30d\") => {\n    set({ isLoading: true, error: null });\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n\n      const response = await fetch(\n        `/api/study-sessions?timeRange=${timeRange}`,\n        {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Failed to fetch study sessions\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        // Convert date strings to Date objects\n        const sessions = result.data.map((session: any) => ({\n          ...session,\n          startTime: new Date(session.startTime),\n          endTime: session.endTime ? new Date(session.endTime) : undefined,\n        }));\n\n        set({\n          sessions,\n          isLoading: false,\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error: any) {\n      set({\n        error: error.message || \"Failed to fetch study sessions\",\n        isLoading: false,\n      });\n      throw error;\n    }\n  },\n\n  // Cache management methods\n  invalidateStudySetContent: (studySetId?: string) => {\n    const { studySetContent } = get();\n\n    if (studySetId) {\n      // Clear cache for specific study set only if it matches\n      if (studySetContent?.studySet?.id === studySetId) {\n        set({ studySetContent: null });\n      }\n    } else {\n      // Clear all cached study set content\n      set({ studySetContent: null });\n    }\n  },\n\n  refreshStudySetContent: async (studySetId: string) => {\n    // Force a fresh fetch of study set content\n    await get().fetchStudySetContent(studySetId, true);\n  },\n\n  invalidateStudySets: () => {\n    // Clear the study sets list to force refetch\n    set({ studySets: [] });\n  },\n}));\n"], "names": ["ScreenReaderAnnouncements", "_ref", "message", "priority", "clearAfter", "currentMessage", "setCurrentMessage", "useState", "useEffect", "timer", "setTimeout", "clearTimeout", "_jsx", "className", "role", "children", "FlashcardInterface", "memo", "_studySetContent$stud", "navigate", "useNavigate", "alert", "useDialog", "currentSession", "studySetContent", "nextItem", "previousItem", "toggleFlag", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endStudySession", "updateTimeSpent", "undo", "redo", "canUndo", "canRedo", "useStudyStore", "isFlipped", "setIsFlipped", "startTime", "setStartTime", "Date", "now", "announce", "AnnouncementComponent", "useScreenReaderAnnouncements", "announcement", "setAnnouncement", "setPriority", "arguments", "length", "undefined", "interval", "setInterval", "clearInterval", "currentIndex", "handleKeyPress", "e", "_e$target", "target", "HTMLInputElement", "HTMLTextAreaElement", "HTMLSelectElement", "contentEditable", "key", "preventDefault", "newIndex", "totalItems", "concat", "flashcards", "currentFlashcard", "id", "isFlagged", "flaggedItems", "includes", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "document", "addEventListener", "removeEventListener", "_jsxs", "<PERSON><PERSON>", "onClick", "variant", "progress", "isFirstCard", "isLastCard", "handleFlip", "studySetId", "studySet", "name", "async", "timeSpent", "Math", "floor", "reviewedCount", "reviewedItems", "flaggedCount", "title", "confirmText", "size", "style", "width", "round", "toString", "padStart", "tabIndex", "onKeyDown", "front", "back", "handlePrevious", "handleToggleFlag", "handleNext", "QuizInterface", "submitQuizAnswer", "selectedAnswer<PERSON>", "setSelectedAnswers", "hasAnswered", "setHasAnswered", "showExplanation", "setShowExplanation", "questions", "currentQuestion", "isLastQuestion", "handleAnswerSelect", "answer", "question_type", "prev", "filter", "a", "checkAnswer", "correctAnswers", "correct_answers", "_selectedAnswers$", "userAnswer", "toLowerCase", "trim", "some", "correct", "every", "handleFinishQuiz", "totalQuestions", "percentage", "replace", "question_text", "value", "onChange", "handleShortAnswerChange", "disabled", "placeholder", "rows", "map", "option", "isSelected", "isCorrect", "buttonClass", "options", "index", "handleSubmitAnswer", "explanation", "StudyPage", "mode", "useParams", "startStudySession", "settings", "userSettings", "useUserSettings", "type", "shuffleEnabled", "shuffle_flashcards", "catch", "error", "console", "setSettings", "loading", "setLoading", "setError", "fetchSettings", "useCallback", "token", "localStorage", "getItem", "sessionStorage", "Error", "response", "fetch", "headers", "ok", "errorData", "json", "data", "err", "updateSettings", "method", "body", "JSON", "stringify", "updates", "refetch", "shuffle<PERSON><PERSON><PERSON>", "array", "shuffled", "i", "j", "random", "create", "set", "get", "studySets", "sessions", "isLoading", "actionHistory", "currentActionIndex", "fetchStudySetContent", "forceRefresh", "Authorization", "errorResult", "result", "success", "_studySetContent$stud2", "_content$flashcards", "_content$questions", "shuffle", "content", "originalOrder", "Array", "from", "_", "shuffledFlashcards", "state", "_objectSpread", "shuffledQuestions", "isShuffled", "addToHistory", "nextIndex", "payload", "fromIndex", "toIndex", "previousState", "timestamp", "prevIndex", "goToItem", "clampedIndex", "max", "min", "itemId", "wasFlagged", "_itemId", "questionId", "_answer", "seconds", "action", "newHistory", "slice", "push", "limitedHistory", "nextActionIndex", "clearHistory", "fetchStudySets", "fetchStudySessions", "timeRange", "session", "endTime", "invalidateStudySetContent", "_studySetContent$stud3", "refreshStudySetContent", "invalidateStudySets"], "sourceRoot": ""}