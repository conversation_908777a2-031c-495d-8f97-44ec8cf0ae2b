{"version": 3, "file": "static/js/447.4ea8605c.chunk.js", "mappings": "6NAMO,MAAMA,EAA+BA,KAC1C,MAAMC,GAAWC,EAAAA,EAAAA,OACX,MAAEC,IAAUC,EAAAA,EAAAA,MAEXC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,KAC1CC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAgC,eACjEG,EAAQC,IAAaJ,EAAAA,EAAAA,UAAiC,CAAC,IACvDK,EAAWC,IAAgBN,EAAAA,EAAAA,WAAS,GA+D3C,OACEO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qCAAoCC,SAAC,0BACnDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,0FAM/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFC,EAAAA,EAAAA,MAAA,QAAMC,SAtCOC,UAGnB,GAFAC,EAAEC,iBAZiBC,MACnB,MAAMC,EAAoC,CAAC,EAO3C,OALKlB,EAAamB,SAChBD,EAAUE,KAAO,8BAGnBd,EAAUY,GAC+B,IAAlCG,OAAOC,KAAKJ,GAAWK,QAMzBN,GAAL,CAEAT,GAAa,GACb,IACE,MAAMgB,OA3CaV,WACrB,MAAMW,EAAQC,aAAaC,QAAQ,cAE7BC,QAAiBC,MAAM,kBAAmB,CAC9CC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADC,OAAYP,IAE7BQ,KAAMC,KAAKC,UAAUC,KAGvB,IAAKR,EAASS,GAAI,CAChB,MAAMC,QAAoBV,EAASW,OACnC,MAAM,IAAIC,MAAMF,EAAYG,OAAS,6BACvC,CAEA,MAAMC,QAAed,EAASW,OAC9B,GAAIG,EAAOC,QACT,OAAOD,EAAON,KAEd,MAAM,IAAII,MAAME,EAAOD,QAsBAG,CAAe,CACpCxB,KAAMpB,EAAamB,OACnB0B,KAAM1C,IAIRP,EAAS,eAADoC,OAAgBR,EAASsB,IACnC,CAAE,MAAOL,SACD3C,EAAM,CACViD,MAAO,QACPC,QAASP,EAAMO,SAAW,6BAC1BC,QAAS,SAEb,CAAC,QACCzC,GAAa,EACf,CAnB2B,GAmCSE,UAAU,YAAWC,SAAA,EAEjDF,EAAAA,EAAAA,KAACyC,EAAAA,EAAK,CACJC,MAAM,iBACNC,MAAOpD,EACPqD,SAAUpD,EACVqD,YAAY,gDACZb,MAAOpC,EAAOe,KACdmC,UAAQ,EACR7C,UAAU,YAIZE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,0BAGhEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OACEC,UAAS,oGAAAsB,OAEY,eAAjB7B,EACE,uCACA,wCAAuC,wBAG7CqD,QAASA,IAAMpD,EAAgB,cAAcO,UAE7CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,8FAAAsB,OAEO,eAAjB7B,EACE,oCACA,kBAAiB,0BAErBQ,SACkB,eAAjBR,IACCM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAGnBE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAC,gBACvCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,wDAO3CF,EAAAA,EAAAA,KAAA,OACEC,UAAS,oGAAAsB,OAEY,SAAjB7B,EACE,uCACA,wCAAuC,wBAG7CqD,QAASA,IAAMpD,EAAgB,QAAQO,UAEvCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,8FAAAsB,OAEO,SAAjB7B,EACE,oCACA,kBAAiB,0BAErBQ,SACkB,SAAjBR,IACCM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAGnBE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAC,oBACvCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,2EAU/CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6CAA4CC,SAAC,kBAG5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAAgCC,SAAC,wBAC9CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,4JASnCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACgD,EAAAA,EAAM,CACLZ,KAAK,SACLI,QAAQ,YACRO,QAASA,IAAM5D,EAAS,cACxBc,UAAU,SAAQC,SACnB,YAGDF,EAAAA,EAAAA,KAACgD,EAAAA,EAAM,CACLZ,KAAK,SACLI,QAAQ,UACR1C,UAAWA,EACXG,UAAU,SAAQC,SACnB,kC", "sources": ["pages/CreateStudySetPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { Button } from '../components/common/Button';\r\nimport { Input } from '../components/common/Input';\r\nimport { useDialog } from '../contexts/DialogContext';\r\n\r\nexport const CreateStudySetPage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const { alert } = useDialog();\r\n  \r\n  const [studySetName, setStudySetName] = useState('');\r\n  const [studySetType, setStudySetType] = useState<'flashcards' | 'quiz'>('flashcards');\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const createStudySet = async (data: { name: string; type: 'flashcards' | 'quiz' }) => {\r\n    const token = localStorage.getItem('auth_token');\r\n    \r\n    const response = await fetch('/api/study-sets', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorResult = await response.json();\r\n      throw new Error(errorResult.error || 'Failed to create study set');\r\n    }\r\n\r\n    const result = await response.json();\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!studySetName.trim()) {\r\n      newErrors.name = 'Study set name is required';\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!validateForm()) return;\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      const studySet = await createStudySet({\r\n        name: studySetName.trim(),\r\n        type: studySetType\r\n      });\r\n\r\n      // Navigate to the newly created study set for editing\r\n      navigate(`/study-sets/${studySet.id}`);\r\n    } catch (error: any) {\r\n      await alert({\r\n        title: 'Error',\r\n        message: error.message || 'Failed to create study set',\r\n        variant: 'error'\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary text-white\">\r\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-white mb-2\">Create New Study Set</h1>\r\n          <p className=\"text-gray-400\">\r\n            Create an empty study set that you can populate with flashcards or quiz questions\r\n          </p>\r\n        </div>\r\n\r\n        {/* Form */}\r\n        <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n            {/* Study Set Name */}\r\n            <Input\r\n              label=\"Study Set Name\"\r\n              value={studySetName}\r\n              onChange={setStudySetName}\r\n              placeholder=\"e.g., Biology Chapter 5, History Final Review\"\r\n              error={errors.name}\r\n              required\r\n              className=\"w-full\"\r\n            />\r\n\r\n            {/* Study Set Type */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-300 mb-3\">\r\n                Primary Content Type\r\n              </label>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div\r\n                  className={`\r\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\r\n                    ${studySetType === 'flashcards'\r\n                      ? 'border-primary-500 bg-primary-500/10'\r\n                      : 'border-gray-600 hover:border-gray-500'\r\n                    }\r\n                  `}\r\n                  onClick={() => setStudySetType('flashcards')}\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className={`\r\n                      w-4 h-4 rounded-full border-2 flex-shrink-0\r\n                      ${studySetType === 'flashcards'\r\n                        ? 'border-primary-500 bg-primary-500'\r\n                        : 'border-gray-500'\r\n                      }\r\n                    `}>\r\n                      {studySetType === 'flashcards' && (\r\n                        <div className=\"w-full h-full rounded-full bg-white scale-50\"></div>\r\n                      )}\r\n                    </div>\r\n                    <div>\r\n                      <h3 className=\"font-medium text-white\">Flashcards</h3>\r\n                      <p className=\"text-sm text-gray-400\">\r\n                        Question and answer cards for memorization\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className={`\r\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\r\n                    ${studySetType === 'quiz'\r\n                      ? 'border-primary-500 bg-primary-500/10'\r\n                      : 'border-gray-600 hover:border-gray-500'\r\n                    }\r\n                  `}\r\n                  onClick={() => setStudySetType('quiz')}\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className={`\r\n                      w-4 h-4 rounded-full border-2 flex-shrink-0\r\n                      ${studySetType === 'quiz'\r\n                        ? 'border-primary-500 bg-primary-500'\r\n                        : 'border-gray-500'\r\n                      }\r\n                    `}>\r\n                      {studySetType === 'quiz' && (\r\n                        <div className=\"w-full h-full rounded-full bg-white scale-50\"></div>\r\n                      )}\r\n                    </div>\r\n                    <div>\r\n                      <h3 className=\"font-medium text-white\">Quiz Questions</h3>\r\n                      <p className=\"text-sm text-gray-400\">\r\n                        Multiple choice, true/false, and short answer questions\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Info Box */}\r\n            <div className=\"bg-blue-500/10 border border-blue-500/30 rounded-lg p-4\">\r\n              <div className=\"flex items-start space-x-3\">\r\n                <div className=\"w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5\">\r\n                  💡\r\n                </div>\r\n                <div className=\"text-sm\">\r\n                  <p className=\"text-blue-400 font-medium mb-1\">What happens next?</p>\r\n                  <p className=\"text-blue-300\">\r\n                    After creating your study set, you'll be able to add content manually or \r\n                    use AI to generate flashcards and quiz questions from your documents.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Submit Button */}\r\n            <div className=\"flex space-x-4\">\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"secondary\"\r\n                onClick={() => navigate('/dashboard')}\r\n                className=\"flex-1\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                type=\"submit\"\r\n                variant=\"primary\"\r\n                isLoading={isLoading}\r\n                className=\"flex-1\"\r\n              >\r\n                Create Study Set\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["CreateStudySetPage", "navigate", "useNavigate", "alert", "useDialog", "studySetName", "setStudySetName", "useState", "studySetType", "setStudySetType", "errors", "setErrors", "isLoading", "setIsLoading", "_jsx", "className", "children", "_jsxs", "onSubmit", "async", "e", "preventDefault", "validateForm", "newErrors", "trim", "name", "Object", "keys", "length", "studySet", "token", "localStorage", "getItem", "response", "fetch", "method", "headers", "concat", "body", "JSON", "stringify", "data", "ok", "errorResult", "json", "Error", "error", "result", "success", "createStudySet", "type", "id", "title", "message", "variant", "Input", "label", "value", "onChange", "placeholder", "required", "onClick", "<PERSON><PERSON>"], "sourceRoot": ""}