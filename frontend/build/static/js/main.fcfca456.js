/*! For license information please see main.fcfca456.js.LICENSE.txt */
(()=>{"use strict";var e={226:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,m(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},C={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var a,i={},o=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(o=""+t.key),t)k.call(t,a)&&!C.hasOwnProperty(a)&&(i[a]=t[a]);var l=arguments.length-2;if(1===l)i.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(a in l=e.defaultProps)void 0===i[a]&&(i[a]=l[a]);return{$$typeof:n,type:e,key:o,ref:s,props:i,_owner:S.current}}function P(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var j=/\/+/g;function A(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function T(e,t,a,i,o){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return o=o(l=e),e=""===i?"."+A(l,0):i,w(o)?(a="",null!=e&&(a=e.replace(j,"$&/")+"/"),T(o,t,a,"",function(e){return e})):null!=o&&(P(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(j,"$&/")+"/")+e)),t.push(o)),1;if(l=0,i=""===i?".":i+":",w(e))for(var u=0;u<e.length;u++){var c=i+A(s=e[u],u);l+=T(s,t,a,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=T(s=s.value,t,a,c=i+A(s,u++),o);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function N(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",function(e){return t.call(n,e,a++)}),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var M={current:null},R={transition:null},O={ReactCurrentDispatcher:M,ReactCurrentBatchConfig:R,ReactCurrentOwner:S};function z(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:N,forEach:function(e,t,n){N(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return N(e,function(){t++}),t},toArray:function(e){return N(e,function(e){return e})||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=i,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=O,t.act=z,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),i=e.key,o=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,s=S.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)k.call(t,u)&&!C.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];a.children=l}return{$$typeof:n,type:e.type,key:i,ref:o,props:a,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=R.transition;R.transition={};try{e()}finally{R.transition=t}},t.unstable_act=z,t.useCallback=function(e,t){return M.current.useCallback(e,t)},t.useContext=function(e){return M.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return M.current.useDeferredValue(e)},t.useEffect=function(e,t){return M.current.useEffect(e,t)},t.useId=function(){return M.current.useId()},t.useImperativeHandle=function(e,t,n){return M.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return M.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return M.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return M.current.useMemo(e,t)},t.useReducer=function(e,t,n){return M.current.useReducer(e,t,n)},t.useRef=function(e){return M.current.useRef(e)},t.useState=function(e){return M.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return M.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return M.current.useTransition()},t.version="18.3.1"},306:(e,t,n)=>{var r=n(9643),a=n(1261);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,a,i,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),P=Symbol.for("react.provider"),j=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),L=Symbol.for("react.memo"),M=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var R=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var O=Symbol.iterator;function z(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=O&&e[O]||e["@@iterator"])?e:null}var D,_=Object.assign;function V(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var F=!1;function B(e,t){if(!e||F)return"";F=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),i=r.stack.split("\n"),o=a.length-1,s=i.length-1;1<=o&&0<=s&&a[o]!==i[s];)s--;for(;1<=o&&0<=s;o--,s--)if(a[o]!==i[s]){if(1!==o||1!==s)do{if(o--,0>--s||a[o]!==i[s]){var l="\n"+a[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=o&&0<=s);break}}}finally{F=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?V(e):""}function I(e){switch(e.tag){case 5:return V(e.type);case 16:return V("Lazy");case 13:return V("Suspense");case 19:return V("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function U(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case E:return"Profiler";case C:return"StrictMode";case T:return"Suspense";case N:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case j:return(e.displayName||"Context")+".Consumer";case P:return(e._context.displayName||"Context")+".Provider";case A:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case L:return null!==(t=e.displayName||null)?t:U(e.type)||"Memo";case M:t=e._payload,e=e._init;try{return U(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return U(t);case 8:return t===C?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function G(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function K(e,t){var n=t.checked;return _({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Z(e,t){Y(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return _({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(i(92));if(te(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function ie(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(he).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]})});var ve=_({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(i(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ce=null;function Ee(e){if(e=ba(e)){if("function"!==typeof ke)throw Error(i(280));var t=e.stateNode;t&&(t=wa(t),ke(e.stateNode,e.type,t))}}function Pe(e){Se?Ce?Ce.push(e):Ce=[e]:Se=e}function je(){if(Se){var e=Se,t=Ce;if(Ce=Se=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function Ae(e,t){return e(t)}function Te(){}var Ne=!1;function Le(e,t,n){if(Ne)return e(t,n);Ne=!0;try{return Ae(e,t,n)}finally{Ne=!1,(null!==Se||null!==Ce)&&(Te(),je())}}function Me(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Re=!1;if(c)try{var Oe={};Object.defineProperty(Oe,"passive",{get:function(){Re=!0}}),window.addEventListener("test",Oe,Oe),window.removeEventListener("test",Oe,Oe)}catch(ce){Re=!1}function ze(e,t,n,r,a,i,o,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var De=!1,_e=null,Ve=!1,Fe=null,Be={onError:function(e){De=!0,_e=e}};function Ie(e,t,n,r,a,i,o,s,l){De=!1,_e=null,ze.apply(Be,arguments)}function Ue(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function We(e){if(Ue(e)!==e)throw Error(i(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ue(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return We(a),e;if(o===r)return We(a),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var s=!1,l=a.child;l;){if(l===n){s=!0,n=a,r=o;break}if(l===r){s=!0,r=a,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=a;break}if(l===r){s=!0,r=o,n=a;break}l=l.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(e))?Ge(e):null}function Ge(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ge(e);if(null!==t)return t;e=e.sibling}return null}var qe=a.unstable_scheduleCallback,Qe=a.unstable_cancelCallback,Ke=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ye=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Je=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,it=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,i=e.pingedLanes,o=268435455&n;if(0!==o){var s=o&~a;0!==s?r=dt(s):0!==(i&=o)&&(r=dt(i))}else 0!==(o=n&~a)?r=dt(o):0!==i&&(r=dt(i));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(i=t&-t)||16===a&&0!==(4194240&i)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,kt,St,Ct,Et,Pt=!1,jt=[],At=null,Tt=null,Nt=null,Lt=new Map,Mt=new Map,Rt=[],Ot="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":Lt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mt.delete(t.pointerId)}}function Dt(e,t,n,r,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function _t(e){var t=ya(e.target);if(null!==t){var n=Ue(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Et(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Vt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Ft(e,t,n){Vt(e)&&n.delete(t)}function Bt(){Pt=!1,null!==At&&Vt(At)&&(At=null),null!==Tt&&Vt(Tt)&&(Tt=null),null!==Nt&&Vt(Nt)&&(Nt=null),Lt.forEach(Ft),Mt.forEach(Ft)}function It(e,t){e.blockedOn===t&&(e.blockedOn=null,Pt||(Pt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Bt)))}function Ut(e){function t(t){return It(t,e)}if(0<jt.length){It(jt[0],e);for(var n=1;n<jt.length;n++){var r=jt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==At&&It(At,e),null!==Tt&&It(Tt,e),null!==Nt&&It(Nt,e),Lt.forEach(t),Mt.forEach(t),n=0;n<Rt.length;n++)(r=Rt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Rt.length&&null===(n=Rt[0]).blockedOn;)_t(n),null===n.blockedOn&&Rt.shift()}var Ht=x.ReactCurrentBatchConfig,Wt=!0;function $t(e,t,n,r){var a=bt,i=Ht.transition;Ht.transition=null;try{bt=1,qt(e,t,n,r)}finally{bt=a,Ht.transition=i}}function Gt(e,t,n,r){var a=bt,i=Ht.transition;Ht.transition=null;try{bt=4,qt(e,t,n,r)}finally{bt=a,Ht.transition=i}}function qt(e,t,n,r){if(Wt){var a=Kt(e,t,n,r);if(null===a)Wr(e,t,r,Qt,n),zt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return At=Dt(At,e,t,n,r,a),!0;case"dragenter":return Tt=Dt(Tt,e,t,n,r,a),!0;case"mouseover":return Nt=Dt(Nt,e,t,n,r,a),!0;case"pointerover":var i=a.pointerId;return Lt.set(i,Dt(Lt.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":return i=a.pointerId,Mt.set(i,Dt(Mt.get(i)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(zt(e,r),4&t&&-1<Ot.indexOf(e)){for(;null!==a;){var i=ba(a);if(null!==i&&wt(i),null===(i=Kt(e,t,n,r))&&Wr(e,t,r,Qt,n),i===a)break;a=i}null!==a&&r.stopPropagation()}else Wr(e,t,r,null,n)}}var Qt=null;function Kt(e,t,n,r){if(Qt=null,null!==(e=ya(e=we(r))))if(null===(t=Ue(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,i=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[i-t];t++);return Jt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,i){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return _(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=_({},un,{view:0,detail:0}),fn=an(dn),hn=_({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(on=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=on=0,ln=e),on)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=an(hn),mn=an(_({},hn,{dataTransfer:0})),gn=an(_({},dn,{relatedTarget:0})),vn=an(_({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=_({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),xn=an(_({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function En(){return Cn}var Pn=_({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),jn=an(Pn),An=an(_({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=an(_({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Nn=an(_({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Ln=_({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mn=an(Ln),Rn=[9,13,27,32],On=c&&"CompositionEvent"in window,zn=null;c&&"documentMode"in document&&(zn=document.documentMode);var Dn=c&&"TextEvent"in window&&!zn,_n=c&&(!On||zn&&8<zn&&11>=zn),Vn=String.fromCharCode(32),Fn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==Rn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function In(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function $n(e,t,n,r){Pe(r),0<(t=Gr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Gn=null,qn=null;function Qn(e){Vr(e,0)}function Kn(e){if(q(xa(e)))return e}function Xn(e,t){if("change"===e)return t}var Yn=!1;if(c){var Zn;if(c){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Zn=Jn}else Zn=!1;Yn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Gn&&(Gn.detachEvent("onpropertychange",nr),qn=Gn=null)}function nr(e){if("value"===e.propertyName&&Kn(qn)){var t=[];$n(t,qn,e,we(e)),Le(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,(Gn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Kn(qn)}function ir(e,t){if("click"===e)return Kn(t)}function or(e,t){if("input"===e||"change"===e)return Kn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!sr(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,i=Math.min(r.start,a);r=void 0===r.end?i:Math.min(r.end,a),!e.extend&&i>r&&(a=r,r=i,i=a),a=cr(n,i);var o=cr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==Q(r)||("selectionStart"in(r=gr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&lr(yr,r)||(yr=r,0<(r=Gr(vr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Sr={},Cr={};function Er(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Cr)return Sr[e]=n[t];return e}c&&(Cr=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Pr=Er("animationend"),jr=Er("animationiteration"),Ar=Er("animationstart"),Tr=Er("transitionend"),Nr=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mr(e,t){Nr.set(e,t),l(t,[e])}for(var Rr=0;Rr<Lr.length;Rr++){var Or=Lr[Rr];Mr(Or.toLowerCase(),"on"+(Or[0].toUpperCase()+Or.slice(1)))}Mr(Pr,"onAnimationEnd"),Mr(jr,"onAnimationIteration"),Mr(Ar,"onAnimationStart"),Mr("dblclick","onDoubleClick"),Mr("focusin","onFocus"),Mr("focusout","onBlur"),Mr(Tr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function _r(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,s,l,u){if(Ie.apply(this,arguments),De){if(!De)throw Error(i(198));var c=_e;De=!1,_e=null,Ve||(Ve=!0,Fe=c)}}(r,t,void 0,e),e.currentTarget=null}function Vr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==i&&a.isPropagationStopped())break e;_r(a,s,u),i=l}else for(o=0;o<r.length;o++){if(l=(s=r[o]).instance,u=s.currentTarget,s=s.listener,l!==i&&a.isPropagationStopped())break e;_r(a,s,u),i=l}}}if(Ve)throw e=Fe,Ve=!1,Fe=null,e}function Fr(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Ir="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[Ir]){e[Ir]=!0,o.forEach(function(t){"selectionchange"!==t&&(Dr.has(t)||Br(t,!1,e),Br(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ir]||(t[Ir]=!0,Br("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Xt(t)){case 1:var a=$t;break;case 4:a=Gt;break;default:a=qt}n=a.bind(null,t,n,e),a=void 0,!Re||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Wr(e,t,n,r,a){var i=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===a||8===s.nodeType&&s.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var l=o.tag;if((3===l||4===l)&&((l=o.stateNode.containerInfo)===a||8===l.nodeType&&l.parentNode===a))return;o=o.return}for(;null!==s;){if(null===(o=ya(s)))return;if(5===(l=o.tag)||6===l){r=i=o;continue e}s=s.parentNode}}r=r.return}Le(function(){var r=i,a=we(n),o=[];e:{var s=Nr.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=jn;break;case"focusin":u="focus",l=gn;break;case"focusout":u="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Tn;break;case Pr:case jr:case Ar:l=vn;break;case Tr:l=Nn;break;case"scroll":l=fn;break;case"wheel":l=Mn;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=An}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Me(p,f))&&c.push($r(p,m,h)))),d)break;p=p.return}0<c.length&&(s=new l(s,u,null,n,a),o.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!ya(u)&&!u[pa])&&(l||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?ya(u):null)&&(u!==(d=Ue(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=An,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:xa(l),h=null==u?s:xa(u),(s=new c(m,p+"leave",l,n,a)).target=d,s.relatedTarget=h,m=null,ya(a)===r&&((c=new c(f,p+"enter",u,n,a)).target=h,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,p=0,h=c=l;h;h=qr(h))p++;for(h=0,m=f;m;m=qr(m))h++;for(;0<p-h;)c=qr(c),p--;for(;0<h-p;)f=qr(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=qr(c),f=qr(f)}c=null}else c=null;null!==l&&Qr(o,s,l,c,!1),null!==u&&null!==d&&Qr(o,d,u,c,!0)}if("select"===(l=(s=r?xa(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Xn;else if(Wn(s))if(Yn)g=or;else{g=ar;var v=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=ir);switch(g&&(g=g(e,r))?$n(o,g,n,a):(v&&v(e,s,r),"focusout"===e&&(v=s._wrapperState)&&v.controlled&&"number"===s.type&&ee(s,"number",s.value)),v=r?xa(r):window,e){case"focusin":(Wn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(o,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(o,n,a)}var y;if(On)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Un?Bn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(_n&&"ko"!==n.locale&&(Un||"onCompositionStart"!==b?"onCompositionEnd"===b&&Un&&(y=en()):(Zt="value"in(Yt=a)?Yt.value:Yt.textContent,Un=!0)),0<(v=Gr(r,b)).length&&(b=new xn(b,e,null,n,a),o.push({event:b,listeners:v}),y?b.data=y:null!==(y=In(n))&&(b.data=y))),(y=Dn?function(e,t){switch(e){case"compositionend":return In(t);case"keypress":return 32!==t.which?null:(Fn=!0,Vn);case"textInput":return(e=t.data)===Vn&&Fn?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!On&&Bn(e,t)?(e=en(),Jt=Zt=Yt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _n&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Gr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=y))}Vr(o,t)})}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,i=a.stateNode;5===a.tag&&null!==i&&(a=i,null!=(i=Me(e,n))&&r.unshift($r(e,i,a)),null!=(i=Me(e,t))&&r.push($r(e,i,a))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,a){for(var i=t._reactName,o=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,a?null!=(l=Me(n,i))&&o.unshift($r(n,l,s)):a||null!=(l=Me(n,i))&&o.push($r(n,l,s))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Kr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Kr,"\n").replace(Xr,"")}function Zr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(i(425))}function Jr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,ia="function"===typeof Promise?Promise:void 0,oa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ia?function(e){return ia.resolve(null).then(e).catch(sa)}:ra;function sa(e){setTimeout(function(){throw e})}function la(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Ut(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,ha="__reactProps$"+da,pa="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pa]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[pa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function wa(e){return e[ha]||null}var ka=[],Sa=-1;function Ca(e){return{current:e}}function Ea(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function Pa(e,t){Sa++,ka[Sa]=e.current,e.current=t}var ja={},Aa=Ca(ja),Ta=Ca(!1),Na=ja;function La(e,t){var n=e.type.contextTypes;if(!n)return ja;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,i={};for(a in n)i[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ma(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ra(){Ea(Ta),Ea(Aa)}function Oa(e,t,n){if(Aa.current!==ja)throw Error(i(168));Pa(Aa,t),Pa(Ta,n)}function za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(i(108,H(e)||"Unknown",a));return _({},n,r)}function Da(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ja,Na=Aa.current,Pa(Aa,e),Pa(Ta,Ta.current),!0}function _a(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=za(e,t,Na),r.__reactInternalMemoizedMergedChildContext=e,Ea(Ta),Ea(Aa),Pa(Aa,e)):Ea(Ta),Pa(Ta,n)}var Va=null,Fa=!1,Ba=!1;function Ia(e){null===Va?Va=[e]:Va.push(e)}function Ua(){if(!Ba&&null!==Va){Ba=!0;var e=0,t=bt;try{var n=Va;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Va=null,Fa=!1}catch(a){throw null!==Va&&(Va=Va.slice(e+1)),qe(Je,Ua),a}finally{bt=t,Ba=!1}}return null}var Ha=[],Wa=0,$a=null,Ga=0,qa=[],Qa=0,Ka=null,Xa=1,Ya="";function Za(e,t){Ha[Wa++]=Ga,Ha[Wa++]=$a,$a=e,Ga=t}function Ja(e,t,n){qa[Qa++]=Xa,qa[Qa++]=Ya,qa[Qa++]=Ka,Ka=e;var r=Xa;e=Ya;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var i=32-ot(t)+a;if(30<i){var o=a-a%5;i=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xa=1<<32-ot(t)+a|n<<a|r,Ya=i+e}else Xa=1<<i|n<<a|r,Ya=e}function ei(e){null!==e.return&&(Za(e,1),Ja(e,1,0))}function ti(e){for(;e===$a;)$a=Ha[--Wa],Ha[Wa]=null,Ga=Ha[--Wa],Ha[Wa]=null;for(;e===Ka;)Ka=qa[--Qa],qa[Qa]=null,Ya=qa[--Qa],qa[Qa]=null,Xa=qa[--Qa],qa[Qa]=null}var ni=null,ri=null,ai=!1,ii=null;function oi(e,t){var n=Lu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function si(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ni=e,ri=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ni=e,ri=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ka?{id:Xa,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Lu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ni=e,ri=null,!0);default:return!1}}function li(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ui(e){if(ai){var t=ri;if(t){var n=t;if(!si(e,t)){if(li(e))throw Error(i(418));t=ua(n.nextSibling);var r=ni;t&&si(e,t)?oi(r,n):(e.flags=-4097&e.flags|2,ai=!1,ni=e)}}else{if(li(e))throw Error(i(418));e.flags=-4097&e.flags|2,ai=!1,ni=e}}}function ci(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ni=e}function di(e){if(e!==ni)return!1;if(!ai)return ci(e),ai=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ri)){if(li(e))throw fi(),Error(i(418));for(;t;)oi(e,t),t=ua(t.nextSibling)}if(ci(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ri=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ri=null}}else ri=ni?ua(e.stateNode.nextSibling):null;return!0}function fi(){for(var e=ri;e;)e=ua(e.nextSibling)}function hi(){ri=ni=null,ai=!1}function pi(e){null===ii?ii=[e]:ii.push(e)}var mi=x.ReactCurrentBatchConfig;function gi(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function vi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yi(e){return(0,e._init)(e._payload)}function bi(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Ru(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=_u(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var i=n.type;return i===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===M&&yi(i)===t.type)?((r=a(t,n.props)).ref=gi(e,t,n),r.return=e,r):((r=Ou(n.type,n.key,n.props,null,e.mode,r)).ref=gi(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=zu(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=_u(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Ou(t.type,t.key,t.props,null,e.mode,n)).ref=gi(e,null,t),n.return=e,n;case k:return(t=Vu(t,e.mode,n)).return=e,t;case M:return f(e,(0,t._init)(t._payload),n)}if(te(t)||z(t))return(t=zu(t,e.mode,n,null)).return=e,t;vi(e,t)}return null}function h(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case M:return h(e,t,(a=n._init)(n._payload),r)}if(te(n)||z(n))return null!==a?null:d(e,t,n,r,null);vi(e,n)}return null}function p(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case M:return p(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||z(r))return d(t,e=e.get(n)||null,r,a,null);vi(t,r)}return null}function m(a,i,s,l){for(var u=null,c=null,d=i,m=i=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=h(a,d,s[m],l);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),i=o(v,i,m),null===c?u=v:c.sibling=v,c=v,d=g}if(m===s.length)return n(a,d),ai&&Za(a,m),u;if(null===d){for(;m<s.length;m++)null!==(d=f(a,s[m],l))&&(i=o(d,i,m),null===c?u=d:c.sibling=d,c=d);return ai&&Za(a,m),u}for(d=r(a,d);m<s.length;m++)null!==(g=p(d,a,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),i=o(g,i,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),ai&&Za(a,m),u}function g(a,s,l,u){var c=z(l);if("function"!==typeof c)throw Error(i(150));if(null==(l=c.call(l)))throw Error(i(151));for(var d=c=null,m=s,g=s=0,v=null,y=l.next();null!==m&&!y.done;g++,y=l.next()){m.index>g?(v=m,m=null):v=m.sibling;var b=h(a,m,y.value,u);if(null===b){null===m&&(m=v);break}e&&m&&null===b.alternate&&t(a,m),s=o(b,s,g),null===d?c=b:d.sibling=b,d=b,m=v}if(y.done)return n(a,m),ai&&Za(a,g),c;if(null===m){for(;!y.done;g++,y=l.next())null!==(y=f(a,y.value,u))&&(s=o(y,s,g),null===d?c=y:d.sibling=y,d=y);return ai&&Za(a,g),c}for(m=r(a,m);!y.done;g++,y=l.next())null!==(y=p(m,a,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),s=o(y,s,g),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach(function(e){return t(a,e)}),ai&&Za(a,g),c}return function e(r,i,o,l){if("object"===typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var u=o.key,c=i;null!==c;){if(c.key===u){if((u=o.type)===S){if(7===c.tag){n(r,c.sibling),(i=a(c,o.props.children)).return=r,r=i;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===M&&yi(u)===c.type){n(r,c.sibling),(i=a(c,o.props)).ref=gi(r,c,o),i.return=r,r=i;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===S?((i=zu(o.props.children,r.mode,l,o.key)).return=r,r=i):((l=Ou(o.type,o.key,o.props,null,r.mode,l)).ref=gi(r,i,o),l.return=r,r=l)}return s(r);case k:e:{for(c=o.key;null!==i;){if(i.key===c){if(4===i.tag&&i.stateNode.containerInfo===o.containerInfo&&i.stateNode.implementation===o.implementation){n(r,i.sibling),(i=a(i,o.children||[])).return=r,r=i;break e}n(r,i);break}t(r,i),i=i.sibling}(i=Vu(o,r.mode,l)).return=r,r=i}return s(r);case M:return e(r,i,(c=o._init)(o._payload),l)}if(te(o))return m(r,i,o,l);if(z(o))return g(r,i,o,l);vi(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==i&&6===i.tag?(n(r,i.sibling),(i=a(i,o)).return=r,r=i):(n(r,i),(i=_u(o,r.mode,l)).return=r,r=i),s(r)):n(r,i)}}var xi=bi(!0),wi=bi(!1),ki=Ca(null),Si=null,Ci=null,Ei=null;function Pi(){Ei=Ci=Si=null}function ji(e){var t=ki.current;Ea(ki),e._currentValue=t}function Ai(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ti(e,t){Si=e,Ei=Ci=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function Ni(e){var t=e._currentValue;if(Ei!==e)if(e={context:e,memoizedValue:t,next:null},null===Ci){if(null===Si)throw Error(i(308));Ci=e,Si.dependencies={lanes:0,firstContext:e}}else Ci=Ci.next=e;return t}var Li=null;function Mi(e){null===Li?Li=[e]:Li.push(e)}function Ri(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Mi(t)):(n.next=a.next,a.next=n),t.interleaved=n,Oi(e,r)}function Oi(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var zi=!1;function Di(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _i(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Vi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Fi(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Al)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Oi(e,n)}return null===(a=r.interleaved)?(t.next=t,Mi(r)):(t.next=a.next,a.next=t),r.interleaved=t,Oi(e,n)}function Bi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Ii(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?a=i=o:i=i.next=o,n=n.next}while(null!==n);null===i?a=i=t:i=i.next=t}else a=i=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ui(e,t,n,r){var a=e.updateQueue;zi=!1;var i=a.firstBaseUpdate,o=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,u=l.next;l.next=null,null===o?i=u:o.next=u,o=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==i){var d=a.baseState;for(o=0,c=u=l=null,s=i;;){var f=s.lane,h=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=_({},d,f);break e;case 2:zi=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,o|=f;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(f=s).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(l=d),a.baseState=l,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===i&&(a.shared.lanes=0);Dl|=o,e.lanes=o,e.memoizedState=d}}function Hi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(i(191,a));a.call(r)}}}var Wi={},$i=Ca(Wi),Gi=Ca(Wi),qi=Ca(Wi);function Qi(e){if(e===Wi)throw Error(i(174));return e}function Ki(e,t){switch(Pa(qi,t),Pa(Gi,e),Pa($i,Wi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ea($i),Pa($i,t)}function Xi(){Ea($i),Ea(Gi),Ea(qi)}function Yi(e){Qi(qi.current);var t=Qi($i.current),n=le(t,e.type);t!==n&&(Pa(Gi,e),Pa($i,n))}function Zi(e){Gi.current===e&&(Ea($i),Ea(Gi))}var Ji=Ca(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=x.ReactCurrentDispatcher,ao=x.ReactCurrentBatchConfig,io=0,oo=null,so=null,lo=null,uo=!1,co=!1,fo=0,ho=0;function po(){throw Error(i(321))}function mo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function go(e,t,n,r,a,o){if(io=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Jo:es,e=n(r,a),co){o=0;do{if(co=!1,fo=0,25<=o)throw Error(i(301));o+=1,lo=so=null,t.updateQueue=null,ro.current=ts,e=n(r,a)}while(co)}if(ro.current=Zo,t=null!==so&&null!==so.next,io=0,lo=so=oo=null,uo=!1,t)throw Error(i(300));return e}function vo(){var e=0!==fo;return fo=0,e}function yo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===lo?oo.memoizedState=lo=e:lo=lo.next=e,lo}function bo(){if(null===so){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=so.next;var t=null===lo?oo.memoizedState:lo.next;if(null!==t)lo=t,so=e;else{if(null===e)throw Error(i(310));e={memoizedState:(so=e).memoizedState,baseState:so.baseState,baseQueue:so.baseQueue,queue:so.queue,next:null},null===lo?oo.memoizedState=lo=e:lo=lo.next=e}return lo}function xo(e,t){return"function"===typeof t?t(e):t}function wo(e){var t=bo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=so,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var s=a.next;a.next=o.next,o.next=s}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var l=s=null,u=null,c=o;do{var d=c.lane;if((io&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,oo.lanes|=d,Dl|=d}c=c.next}while(null!==c&&c!==o);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(bs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,Dl|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ko(e){var t=bo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{o=e(o,s.action),s=s.next}while(s!==a);sr(o,t.memoizedState)||(bs=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function So(){}function Co(e,t){var n=oo,r=bo(),a=t(),o=!sr(r.memoizedState,a);if(o&&(r.memoizedState=a,bs=!0),r=r.queue,Do(jo.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==lo&&1&lo.memoizedState.tag){if(n.flags|=2048,Lo(9,Po.bind(null,n,r,a,t),void 0,null),null===Tl)throw Error(i(349));0!==(30&io)||Eo(n,t,a)}return a}function Eo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Po(e,t,n,r){t.value=n,t.getSnapshot=r,Ao(t)&&To(e)}function jo(e,t,n){return n(function(){Ao(t)&&To(e)})}function Ao(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function To(e){var t=Oi(e,1);null!==t&&nu(t,e,1,-1)}function No(e){var t=yo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=Qo.bind(null,oo,e),[t.memoizedState,e]}function Lo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Mo(){return bo().memoizedState}function Ro(e,t,n,r){var a=yo();oo.flags|=e,a.memoizedState=Lo(1|t,n,void 0,void 0===r?null:r)}function Oo(e,t,n,r){var a=bo();r=void 0===r?null:r;var i=void 0;if(null!==so){var o=so.memoizedState;if(i=o.destroy,null!==r&&mo(r,o.deps))return void(a.memoizedState=Lo(t,n,i,r))}oo.flags|=e,a.memoizedState=Lo(1|t,n,i,r)}function zo(e,t){return Ro(8390656,8,e,t)}function Do(e,t){return Oo(2048,8,e,t)}function _o(e,t){return Oo(4,2,e,t)}function Vo(e,t){return Oo(4,4,e,t)}function Fo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Bo(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Oo(4,4,Fo.bind(null,t,e),n)}function Io(){}function Uo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ho(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Wo(e,t,n){return 0===(21&io)?(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),oo.lanes|=n,Dl|=n,e.baseState=!0),t)}function $o(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{bt=n,ao.transition=r}}function Go(){return bo().memoizedState}function qo(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ko(e))Xo(t,n);else if(null!==(n=Ri(e,t,n,r))){nu(n,e,r,eu()),Yo(n,t,r)}}function Qo(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ko(e))Xo(t,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var o=t.lastRenderedState,s=i(o,n);if(a.hasEagerState=!0,a.eagerState=s,sr(s,o)){var l=t.interleaved;return null===l?(a.next=a,Mi(t)):(a.next=l.next,l.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Ri(e,t,a,r))&&(nu(n,e,r,a=eu()),Yo(n,t,r))}}function Ko(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Xo(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yo(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Zo={readContext:Ni,useCallback:po,useContext:po,useEffect:po,useImperativeHandle:po,useInsertionEffect:po,useLayoutEffect:po,useMemo:po,useReducer:po,useRef:po,useState:po,useDebugValue:po,useDeferredValue:po,useTransition:po,useMutableSource:po,useSyncExternalStore:po,useId:po,unstable_isNewReconciler:!1},Jo={readContext:Ni,useCallback:function(e,t){return yo().memoizedState=[e,void 0===t?null:t],e},useContext:Ni,useEffect:zo,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ro(4194308,4,Fo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ro(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ro(4,2,e,t)},useMemo:function(e,t){var n=yo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yo().memoizedState=e},useState:No,useDebugValue:Io,useDeferredValue:function(e){return yo().memoizedState=e},useTransition:function(){var e=No(!1),t=e[0];return e=$o.bind(null,e[1]),yo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=yo();if(ai){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===Tl)throw Error(i(349));0!==(30&io)||Eo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,zo(jo.bind(null,r,o,e),[e]),r.flags|=2048,Lo(9,Po.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=yo(),t=Tl.identifierPrefix;if(ai){var n=Ya;t=":"+t+"R"+(n=(Xa&~(1<<32-ot(Xa)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ho++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Ni,useCallback:Uo,useContext:Ni,useEffect:Do,useImperativeHandle:Bo,useInsertionEffect:_o,useLayoutEffect:Vo,useMemo:Ho,useReducer:wo,useRef:Mo,useState:function(){return wo(xo)},useDebugValue:Io,useDeferredValue:function(e){return Wo(bo(),so.memoizedState,e)},useTransition:function(){return[wo(xo)[0],bo().memoizedState]},useMutableSource:So,useSyncExternalStore:Co,useId:Go,unstable_isNewReconciler:!1},ts={readContext:Ni,useCallback:Uo,useContext:Ni,useEffect:Do,useImperativeHandle:Bo,useInsertionEffect:_o,useLayoutEffect:Vo,useMemo:Ho,useReducer:ko,useRef:Mo,useState:function(){return ko(xo)},useDebugValue:Io,useDeferredValue:function(e){var t=bo();return null===so?t.memoizedState=e:Wo(t,so.memoizedState,e)},useTransition:function(){return[ko(xo)[0],bo().memoizedState]},useMutableSource:So,useSyncExternalStore:Co,useId:Go,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=_({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:_({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return!!(e=e._reactInternals)&&Ue(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),i=Vi(r,a);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Fi(e,i,a))&&(nu(t,e,a,r),Bi(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),i=Vi(r,a);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Fi(e,i,a))&&(nu(t,e,a,r),Bi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Vi(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Fi(e,a,r))&&(nu(t,e,r,n),Bi(t,e,r))}};function is(e,t,n,r,a,i,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,o):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(a,i))}function os(e,t,n){var r=!1,a=ja,i=t.contextType;return"object"===typeof i&&null!==i?i=Ni(i):(a=Ma(t)?Na:Aa.current,i=(r=null!==(r=t.contextTypes)&&void 0!==r)?La(e,a):ja),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Di(e);var i=t.contextType;"object"===typeof i&&null!==i?a.context=Ni(i):(i=Ma(t)?Na:Aa.current,a.context=La(e,i)),a.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(rs(e,t,i,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&as.enqueueReplaceState(a,a.state,null),Ui(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=I(r),r=r.return}while(r);var a=n}catch(i){a="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:a,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fs="function"===typeof WeakMap?WeakMap:Map;function hs(e,t,n){(n=Vi(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Wl||(Wl=!0,$l=r),ds(0,t)},n}function ps(e,t,n){(n=Vi(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ds(0,t)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===Gl?Gl=new Set([this]):Gl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Eu.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vs(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Vi(-1,1)).tag=2,Fi(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var ys=x.ReactCurrentOwner,bs=!1;function xs(e,t,n,r){t.child=null===e?wi(t,null,n,r):xi(t,e.child,n,r)}function ws(e,t,n,r,a){n=n.render;var i=t.ref;return Ti(t,a),r=go(e,t,n,r,i,a),n=vo(),null===e||bs?(ai&&n&&ei(t),t.flags|=1,xs(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function ks(e,t,n,r,a){if(null===e){var i=n.type;return"function"!==typeof i||Mu(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Ou(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Ss(e,t,i,r,a))}if(i=e.child,0===(e.lanes&a)){var o=i.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(o,r)&&e.ref===t.ref)return Ws(e,t,a)}return t.flags|=1,(e=Ru(i,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,a){if(null!==e){var i=e.memoizedProps;if(lr(i,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=i,0===(e.lanes&a))return t.lanes=e.lanes,Ws(e,t,a);0!==(131072&e.flags)&&(bs=!0)}}return Ps(e,t,n,r,a)}function Cs(e,t,n){var r=t.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Pa(Rl,Ml),Ml|=n;else{if(0===(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Pa(Rl,Ml),Ml|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==i?i.baseLanes:n,Pa(Rl,Ml),Ml|=r}else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,Pa(Rl,Ml),Ml|=r;return xs(e,t,a,n),t.child}function Es(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ps(e,t,n,r,a){var i=Ma(n)?Na:Aa.current;return i=La(t,i),Ti(t,a),n=go(e,t,n,r,i,a),r=vo(),null===e||bs?(ai&&r&&ei(t),t.flags|=1,xs(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function js(e,t,n,r,a){if(Ma(n)){var i=!0;Da(t)}else i=!1;if(Ti(t,a),null===t.stateNode)Hs(e,t),os(t,n,r),ls(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,s=t.memoizedProps;o.props=s;var l=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=Ni(u):u=La(t,u=Ma(n)?Na:Aa.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==r||l!==u)&&ss(t,o,r,u),zi=!1;var f=t.memoizedState;o.state=f,Ui(t,r,o,a),l=t.memoizedState,s!==r||f!==l||Ta.current||zi?("function"===typeof c&&(rs(t,n,c,r),l=t.memoizedState),(s=zi||is(t,n,s,r,f,l,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=s):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,_i(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ns(t.type,s),o.props=u,d=t.pendingProps,f=o.context,"object"===typeof(l=n.contextType)&&null!==l?l=Ni(l):l=La(t,l=Ma(n)?Na:Aa.current);var h=n.getDerivedStateFromProps;(c="function"===typeof h||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,o,r,l),zi=!1,f=t.memoizedState,o.state=f,Ui(t,r,o,a);var p=t.memoizedState;s!==d||f!==p||Ta.current||zi?("function"===typeof h&&(rs(t,n,h,r),p=t.memoizedState),(u=zi||is(t,n,u,r,f,p,l)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,p,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),o.props=r,o.state=p,o.context=l,r=u):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return As(e,t,n,r,i,a)}function As(e,t,n,r,a,i){Es(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&_a(t,n,!1),Ws(e,t,i);r=t.stateNode,ys.current=t;var s=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=xi(t,e.child,null,i),t.child=xi(t,null,s,i)):xs(e,t,s,i),t.memoizedState=r.state,a&&_a(t,n,!0),t.child}function Ts(e){var t=e.stateNode;t.pendingContext?Oa(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Oa(0,t.context,!1),Ki(e,t.containerInfo)}function Ns(e,t,n,r,a){return hi(),pi(a),t.flags|=256,xs(e,t,n,r),t.child}var Ls,Ms,Rs,Os,zs={dehydrated:null,treeContext:null,retryLane:0};function Ds(e){return{baseLanes:e,cachePool:null,transitions:null}}function _s(e,t,n){var r,a=t.pendingProps,o=Ji.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Pa(Ji,1&o),null===e)return ui(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=a.children,e=a.fallback,s?(a=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&a)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Du(l,a,0,null),e=zu(e,a,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ds(n),t.memoizedState=zs,e):Vs(t,l));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,s){if(n)return 256&t.flags?(t.flags&=-257,Fs(e,t,s,r=cs(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Du({mode:"visible",children:r.children},a,0,null),(o=zu(o,a,s,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&xi(t,e.child,null,s),t.child.memoizedState=Ds(s),t.memoizedState=zs,o);if(0===(1&t.mode))return Fs(e,t,s,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var l=r.dgst;return r=l,Fs(e,t,s,r=cs(o=Error(i(419)),r,void 0))}if(l=0!==(s&e.childLanes),bs||l){if(null!==(r=Tl)){switch(s&-s){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|s))?0:a)&&a!==o.retryLane&&(o.retryLane=a,Oi(e,a),nu(r,e,a,-1))}return mu(),Fs(e,t,s,r=cs(Error(i(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=ju.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,ri=ua(a.nextSibling),ni=t,ai=!0,ii=null,null!==e&&(qa[Qa++]=Xa,qa[Qa++]=Ya,qa[Qa++]=Ka,Xa=e.id,Ya=e.overflow,Ka=t),t=Vs(t,r.children),t.flags|=4096,t)}(e,t,l,a,r,o,n);if(s){s=a.fallback,l=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&l)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Ru(o,u)).subtreeFlags=14680064&o.subtreeFlags,null!==r?s=Ru(r,s):(s=zu(s,l,n,null)).flags|=2,s.return=t,a.return=t,a.sibling=s,t.child=a,a=s,s=t.child,l=null===(l=e.child.memoizedState)?Ds(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=zs,a}return e=(s=e.child).sibling,a=Ru(s,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Vs(e,t){return(t=Du({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Fs(e,t,n,r){return null!==r&&pi(r),xi(t,e.child,null,n),(e=Vs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ai(e.return,t,n)}function Is(e,t,n,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function Us(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(xs(e,t,r.children,n),0!==(2&(r=Ji.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bs(e,n,t);else if(19===e.tag)Bs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Pa(Ji,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Is(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Is(t,!0,n,null,i);break;case"together":Is(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hs(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ws(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Dl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Ru(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ru(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $s(e,t){if(!ai)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Gs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qs(e,t,n){var r=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Gs(t),null;case 1:case 17:return Ma(t.type)&&Ra(),Gs(t),null;case 3:return r=t.stateNode,Xi(),Ea(Ta),Ea(Aa),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(di(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ii&&(ou(ii),ii=null))),Ms(e,t),Gs(t),null;case 5:Zi(t);var a=Qi(qi.current);if(n=t.type,null!==e&&null!=t.stateNode)Rs(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(i(166));return Gs(t),null}if(e=Qi($i.current),di(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[ha]=o,e=0!==(1&t.mode),n){case"dialog":Fr("cancel",r),Fr("close",r);break;case"iframe":case"object":case"embed":Fr("load",r);break;case"video":case"audio":for(a=0;a<zr.length;a++)Fr(zr[a],r);break;case"source":Fr("error",r);break;case"img":case"image":case"link":Fr("error",r),Fr("load",r);break;case"details":Fr("toggle",r);break;case"input":X(r,o),Fr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Fr("invalid",r);break;case"textarea":ae(r,o),Fr("invalid",r)}for(var l in ye(n,o),a=null,o)if(o.hasOwnProperty(l)){var u=o[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),a=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&Fr("scroll",r)}switch(n){case"input":G(r),J(r,o,!0);break;case"textarea":G(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=Jr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fa]=t,e[ha]=r,Ls(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Fr("cancel",e),Fr("close",e),a=r;break;case"iframe":case"object":case"embed":Fr("load",e),a=r;break;case"video":case"audio":for(a=0;a<zr.length;a++)Fr(zr[a],e);a=r;break;case"source":Fr("error",e),a=r;break;case"img":case"image":case"link":Fr("error",e),Fr("load",e),a=r;break;case"details":Fr("toggle",e),a=r;break;case"input":X(e,r),a=K(e,r),Fr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=_({},r,{value:void 0}),Fr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Fr("invalid",e)}for(o in ye(n,a),u=a)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?ge(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(s.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Fr("scroll",e):null!=c&&b(e,o,c,l))}switch(n){case"input":G(e),J(e,r,!1);break;case"textarea":G(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Gs(t),null;case 6:if(e&&null!=t.stateNode)Os(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(n=Qi(qi.current),Qi($i.current),di(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=ni))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Gs(t),null;case 13:if(Ea(Ji),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ai&&null!==ri&&0!==(1&t.mode)&&0===(128&t.flags))fi(),hi(),t.flags|=98560,o=!1;else if(o=di(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(i(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(i(317));o[fa]=t}else hi(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Gs(t),o=!1}else null!==ii&&(ou(ii),ii=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Ji.current)?0===Ol&&(Ol=3):mu())),null!==t.updateQueue&&(t.flags|=4),Gs(t),null);case 4:return Xi(),Ms(e,t),null===e&&Ur(t.stateNode.containerInfo),Gs(t),null;case 10:return ji(t.type._context),Gs(t),null;case 19:if(Ea(Ji),null===(o=t.memoizedState))return Gs(t),null;if(r=0!==(128&t.flags),null===(l=o.rendering))if(r)$s(o,!1);else{if(0!==Ol||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=eo(e))){for(t.flags|=128,$s(o,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(l=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=l.childLanes,o.lanes=l.lanes,o.child=l.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=l.memoizedProps,o.memoizedState=l.memoizedState,o.updateQueue=l.updateQueue,o.type=l.type,e=l.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Pa(Ji,1&Ji.current|2),t.child}e=e.sibling}null!==o.tail&&Ye()>Ul&&(t.flags|=128,r=!0,$s(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$s(o,!0),null===o.tail&&"hidden"===o.tailMode&&!l.alternate&&!ai)return Gs(t),null}else 2*Ye()-o.renderingStartTime>Ul&&1073741824!==n&&(t.flags|=128,r=!0,$s(o,!1),t.lanes=4194304);o.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=o.last)?n.sibling=l:t.child=l,o.last=l)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ye(),t.sibling=null,n=Ji.current,Pa(Ji,r?1&n|2:1&n),t):(Gs(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ml)&&(Gs(t),6&t.subtreeFlags&&(t.flags|=8192)):Gs(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function Qs(e,t){switch(ti(t),t.tag){case 1:return Ma(t.type)&&Ra(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xi(),Ea(Ta),Ea(Aa),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zi(t),null;case 13:if(Ea(Ji),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));hi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ea(Ji),null;case 4:return Xi(),null;case 10:return ji(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ls=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ms=function(){},Rs=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Qi($i.current);var i,o=null;switch(n){case"input":a=K(e,a),r=K(e,r),o=[];break;case"select":a=_({},a,{value:void 0}),r=_({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Jr)}for(c in ye(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var l=a[c];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(i in l)!l.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in u)u.hasOwnProperty(i)&&l[i]!==u[i]&&(n||(n={}),n[i]=u[i])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Fr("scroll",e),o||l===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},Os=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ks=!1,Xs=!1,Ys="function"===typeof WeakSet?WeakSet:Set,Zs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Cu(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Cu(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&el(t,n,i)}a=a.next}while(a!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function al(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function il(e){var t=e.alternate;null!==t&&(e.alternate=null,il(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[ha],delete t[ma],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ol(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ol(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(it&&"function"===typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(at,n)}catch(s){}switch(n.tag){case 5:Xs||Js(n,t);case 6:var r=cl,a=dl;cl=null,fl(e,t,n),dl=a,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?la(e.parentNode,n):1===e.nodeType&&la(e,n),Ut(e)):la(cl,n.stateNode));break;case 4:r=cl,a=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=a;break;case 0:case 11:case 14:case 15:if(!Xs&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var i=a,o=i.destroy;i=i.tag,void 0!==o&&(0!==(2&i)||0!==(4&i))&&el(n,t,o),a=a.next}while(a!==r)}fl(e,t,n);break;case 1:if(!Xs&&(Js(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Cu(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Xs=(r=Xs)||null!==n.memoizedState,fl(e,t,n),Xs=r):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Ys),t.forEach(function(t){var r=Au.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(i(160));hl(o,s,a),cl=null,dl=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Cu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),vl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(g){Cu(e,e.return,g)}try{nl(5,e,e.return)}catch(g){Cu(e,e.return,g)}}break;case 1:ml(t,e),vl(e),512&r&&null!==n&&Js(n,n.return);break;case 5:if(ml(t,e),vl(e),512&r&&null!==n&&Js(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Cu(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,s=null!==n?n.memoizedProps:o,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===o.type&&null!=o.name&&Y(a,o),be(l,s);var c=be(l,o);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(l){case"input":Z(a,o);break;case"textarea":ie(a,o);break;case"select":var h=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var p=o.value;null!=p?ne(a,!!o.multiple,p,!1):h!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[ha]=o}catch(g){Cu(e,e.return,g)}}break;case 6:if(ml(t,e),vl(e),4&r){if(null===e.stateNode)throw Error(i(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(g){Cu(e,e.return,g)}}break;case 3:if(ml(t,e),vl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(g){Cu(e,e.return,g)}break;case 4:default:ml(t,e),vl(e);break;case 13:ml(t,e),vl(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Il=Ye())),4&r&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xs=(c=Xs)||d,ml(t,e),Xs=c):ml(t,e),vl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Zs=e,d=e.child;null!==d;){for(f=Zs=d;null!==Zs;){switch(p=(h=Zs).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Js(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Cu(r,n,g)}}break;case 5:Js(h,h.return);break;case 22:if(null!==h.memoizedState){wl(f);continue}}null!==p?(p.return=h,Zs=p):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",s))}catch(g){Cu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Cu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),vl(e),4&r&&pl(e);case 21:}}function vl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ol(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),ul(e,sl(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ll(e,sl(e),o);break;default:throw Error(i(161))}}catch(s){Cu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yl(e,t,n){Zs=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Zs;){var a=Zs,i=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Ks;if(!o){var s=a.alternate,l=null!==s&&null!==s.memoizedState||Xs;s=Ks;var u=Xs;if(Ks=o,(Xs=l)&&!u)for(Zs=a;null!==Zs;)l=(o=Zs).child,22===o.tag&&null!==o.memoizedState?kl(a):null!==l?(l.return=o,Zs=l):kl(a);for(;null!==i;)Zs=i,bl(i,t,n),i=i.sibling;Zs=a,Ks=s,Xs=u}xl(e)}else 0!==(8772&a.subtreeFlags)&&null!==i?(i.return=a,Zs=i):xl(e)}}function xl(e){for(;null!==Zs;){var t=Zs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xs||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xs)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Hi(t,o,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Hi(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ut(f)}}}break;default:throw Error(i(163))}Xs||512&t.flags&&al(t)}catch(h){Cu(t,t.return,h)}}if(t===e){Zs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zs=n;break}Zs=t.return}}function wl(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function kl(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Cu(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(l){Cu(t,a,l)}}var i=t.return;try{al(t)}catch(l){Cu(t,i,l)}break;case 5:var o=t.return;try{al(t)}catch(l){Cu(t,o,l)}}}catch(l){Cu(t,t.return,l)}if(t===e){Zs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Zs=s;break}Zs=t.return}}var Sl,Cl=Math.ceil,El=x.ReactCurrentDispatcher,Pl=x.ReactCurrentOwner,jl=x.ReactCurrentBatchConfig,Al=0,Tl=null,Nl=null,Ll=0,Ml=0,Rl=Ca(0),Ol=0,zl=null,Dl=0,_l=0,Vl=0,Fl=null,Bl=null,Il=0,Ul=1/0,Hl=null,Wl=!1,$l=null,Gl=null,ql=!1,Ql=null,Kl=0,Xl=0,Yl=null,Zl=-1,Jl=0;function eu(){return 0!==(6&Al)?Ye():-1!==Zl?Zl:Zl=Ye()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Al)&&0!==Ll?Ll&-Ll:null!==mi.transition?(0===Jl&&(Jl=mt()),Jl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nu(e,t,n,r){if(50<Xl)throw Xl=0,Yl=null,Error(i(185));vt(e,n,r),0!==(2&Al)&&e===Tl||(e===Tl&&(0===(2&Al)&&(_l|=n),4===Ol&&su(e,Ll)),ru(e,r),1===n&&0===Al&&0===(1&t.mode)&&(Ul=Ye()+500,Fa&&Ua()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-ot(i),s=1<<o,l=a[o];-1===l?0!==(s&n)&&0===(s&r)||(a[o]=ht(s,t)):l<=t&&(e.expiredLanes|=s),i&=~s}}(e,t);var r=ft(e,e===Tl?Ll:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Fa=!0,Ia(e)}(lu.bind(null,e)):Ia(lu.bind(null,e)),oa(function(){0===(6&Al)&&Ua()}),n=null;else{switch(xt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Tu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Zl=-1,Jl=0,0!==(6&Al))throw Error(i(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===Tl?Ll:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=Al;Al|=2;var o=pu();for(Tl===e&&Ll===t||(Hl=null,Ul=Ye()+500,fu(e,t));;)try{yu();break}catch(l){hu(e,l)}Pi(),El.current=o,Al=a,null!==Nl?t=0:(Tl=null,Ll=0,t=Ol)}if(0!==t){if(2===t&&(0!==(a=pt(e))&&(r=a,t=iu(e,a))),1===t)throw n=zl,fu(e,0),su(e,r),ru(e,Ye()),n;if(6===t)su(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!sr(i(),a))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gu(e,r))&&(0!==(o=pt(e))&&(r=o,t=iu(e,o))),1===t))throw n=zl,fu(e,0),su(e,r),ru(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:case 5:wu(e,Bl,Hl);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Il+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,Bl,Hl),t);break}wu(e,Bl,Hl);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var s=31-ot(r);o=1<<s,(s=t[s])>a&&(a=s),r&=~o}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Cl(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,Bl,Hl),r);break}wu(e,Bl,Hl);break;default:throw Error(i(329))}}}return ru(e,Ye()),e.callbackNode===n?au.bind(null,e):null}function iu(e,t){var n=Fl;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Bl,Bl=n,null!==t&&ou(t)),e}function ou(e){null===Bl?Bl=e:Bl.push.apply(Bl,e)}function su(e,t){for(t&=~Vl,t&=~_l,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&Al))throw Error(i(327));ku();var t=ft(e,0);if(0===(1&t))return ru(e,Ye()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=iu(e,r))}if(1===n)throw n=zl,fu(e,0),su(e,t),ru(e,Ye()),n;if(6===n)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Bl,Hl),ru(e,Ye()),null}function uu(e,t){var n=Al;Al|=1;try{return e(t)}finally{0===(Al=n)&&(Ul=Ye()+500,Fa&&Ua())}}function cu(e){null!==Ql&&0===Ql.tag&&0===(6&Al)&&ku();var t=Al;Al|=1;var n=jl.transition,r=bt;try{if(jl.transition=null,bt=1,e)return e()}finally{bt=r,jl.transition=n,0===(6&(Al=t))&&Ua()}}function du(){Ml=Rl.current,Ea(Rl)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Nl)for(n=Nl.return;null!==n;){var r=n;switch(ti(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ra();break;case 3:Xi(),Ea(Ta),Ea(Aa),no();break;case 5:Zi(r);break;case 4:Xi();break;case 13:case 19:Ea(Ji);break;case 10:ji(r.type._context);break;case 22:case 23:du()}n=n.return}if(Tl=e,Nl=e=Ru(e.current,null),Ll=Ml=t,Ol=0,zl=null,Vl=_l=Dl=0,Bl=Fl=null,null!==Li){for(t=0;t<Li.length;t++)if(null!==(r=(n=Li[t]).interleaved)){n.interleaved=null;var a=r.next,i=n.pending;if(null!==i){var o=i.next;i.next=a,r.next=o}n.pending=r}Li=null}return e}function hu(e,t){for(;;){var n=Nl;try{if(Pi(),ro.current=Zo,uo){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}uo=!1}if(io=0,lo=so=oo=null,co=!1,fo=0,Pl.current=null,null===n||null===n.return){Ol=1,zl=t,Nl=null;break}e:{var o=e,s=n.return,l=n,u=t;if(t=Ll,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=gs(s);if(null!==p){p.flags&=-257,vs(p,s,l,0,t),1&p.mode&&ms(o,c,t),u=c;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){ms(o,c,t),mu();break e}u=Error(i(426))}else if(ai&&1&l.mode){var v=gs(s);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vs(v,s,l,0,t),pi(us(u,l));break e}}o=u=us(u,l),4!==Ol&&(Ol=2),null===Fl?Fl=[o]:Fl.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Ii(o,hs(0,u,t));break e;case 1:l=u;var y=o.type,b=o.stateNode;if(0===(128&o.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Gl||!Gl.has(b)))){o.flags|=65536,t&=-t,o.lanes|=t,Ii(o,ps(o,l,t));break e}}o=o.return}while(null!==o)}xu(n)}catch(x){t=x,Nl===n&&null!==n&&(Nl=n=n.return);continue}break}}function pu(){var e=El.current;return El.current=Zo,null===e?Zo:e}function mu(){0!==Ol&&3!==Ol&&2!==Ol||(Ol=4),null===Tl||0===(268435455&Dl)&&0===(268435455&_l)||su(Tl,Ll)}function gu(e,t){var n=Al;Al|=2;var r=pu();for(Tl===e&&Ll===t||(Hl=null,fu(e,t));;)try{vu();break}catch(a){hu(e,a)}if(Pi(),Al=n,El.current=r,null!==Nl)throw Error(i(261));return Tl=null,Ll=0,Ol}function vu(){for(;null!==Nl;)bu(Nl)}function yu(){for(;null!==Nl&&!Ke();)bu(Nl)}function bu(e){var t=Sl(e.alternate,e,Ml);e.memoizedProps=e.pendingProps,null===t?xu(e):Nl=t,Pl.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=qs(n,t,Ml)))return void(Nl=n)}else{if(null!==(n=Qs(n,t)))return n.flags&=32767,void(Nl=n);if(null===e)return Ol=6,void(Nl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Nl=t);Nl=t=e}while(null!==t);0===Ol&&(Ol=5)}function wu(e,t,n){var r=bt,a=jl.transition;try{jl.transition=null,bt=1,function(e,t,n,r){do{ku()}while(null!==Ql);if(0!==(6&Al))throw Error(i(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),i=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~i}}(e,o),e===Tl&&(Nl=Tl=null,Ll=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||ql||(ql=!0,Tu(tt,function(){return ku(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=jl.transition,jl.transition=null;var s=bt;bt=1;var l=Al;Al|=4,Pl.current=null,function(e,t){if(ea=Wt,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(l=s+a),f!==o||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===a&&(l=s),h===o&&++d===r&&(u=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Wt=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:ns(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(i(163))}}catch(w){Cu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}m=tl,tl=!1}(e,n),gl(n,e),pr(ta),Wt=!!ea,ta=ea=null,e.current=n,yl(n,e,a),Xe(),Al=l,bt=s,jl.transition=o}else e.current=n;if(ql&&(ql=!1,Ql=e,Kl=a),o=e.pendingLanes,0===o&&(Gl=null),function(e){if(it&&"function"===typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Wl)throw Wl=!1,e=$l,$l=null,e;0!==(1&Kl)&&0!==e.tag&&ku(),o=e.pendingLanes,0!==(1&o)?e===Yl?Xl++:(Xl=0,Yl=e):Xl=0,Ua()}(e,t,n,r)}finally{jl.transition=a,bt=r}return null}function ku(){if(null!==Ql){var e=xt(Kl),t=jl.transition,n=bt;try{if(jl.transition=null,bt=16>e?16:e,null===Ql)var r=!1;else{if(e=Ql,Ql=null,Kl=0,0!==(6&Al))throw Error(i(331));var a=Al;for(Al|=4,Zs=e.current;null!==Zs;){var o=Zs,s=o.child;if(0!==(16&Zs.flags)){var l=o.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Zs=c;null!==Zs;){var d=Zs;switch(d.tag){case 0:case 11:case 15:nl(8,d,o)}var f=d.child;if(null!==f)f.return=d,Zs=f;else for(;null!==Zs;){var h=(d=Zs).sibling,p=d.return;if(il(d),d===c){Zs=null;break}if(null!==h){h.return=p,Zs=h;break}Zs=p}}}var m=o.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Zs=o}}if(0!==(2064&o.subtreeFlags)&&null!==s)s.return=o,Zs=s;else e:for(;null!==Zs;){if(0!==(2048&(o=Zs).flags))switch(o.tag){case 0:case 11:case 15:nl(9,o,o.return)}var y=o.sibling;if(null!==y){y.return=o.return,Zs=y;break e}Zs=o.return}}var b=e.current;for(Zs=b;null!==Zs;){var x=(s=Zs).child;if(0!==(2064&s.subtreeFlags)&&null!==x)x.return=s,Zs=x;else e:for(s=b;null!==Zs;){if(0!==(2048&(l=Zs).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(k){Cu(l,l.return,k)}if(l===s){Zs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Zs=w;break e}Zs=l.return}}if(Al=a,Ua(),it&&"function"===typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(at,e)}catch(k){}r=!0}return r}finally{bt=n,jl.transition=t}}return!1}function Su(e,t,n){e=Fi(e,t=hs(0,t=us(n,t),1),1),t=eu(),null!==e&&(vt(e,1,t),ru(e,t))}function Cu(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Gl||!Gl.has(r))){t=Fi(t,e=ps(t,e=us(n,e),1),1),e=eu(),null!==t&&(vt(t,1,e),ru(t,e));break}}t=t.return}}function Eu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Tl===e&&(Ll&n)===n&&(4===Ol||3===Ol&&(130023424&Ll)===Ll&&500>Ye()-Il?fu(e,0):Vl|=n),ru(e,t)}function Pu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Oi(e,t))&&(vt(e,t,n),ru(e,n))}function ju(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Pu(e,n)}function Au(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}null!==r&&r.delete(t),Pu(e,n)}function Tu(e,t){return qe(e,t)}function Nu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lu(e,t,n,r){return new Nu(e,t,n,r)}function Mu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ru(e,t){var n=e.alternate;return null===n?((n=Lu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ou(e,t,n,r,a,o){var s=2;if(r=e,"function"===typeof e)Mu(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case S:return zu(n.children,a,o,t);case C:s=8,a|=8;break;case E:return(e=Lu(12,n,t,2|a)).elementType=E,e.lanes=o,e;case T:return(e=Lu(13,n,t,a)).elementType=T,e.lanes=o,e;case N:return(e=Lu(19,n,t,a)).elementType=N,e.lanes=o,e;case R:return Du(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case P:s=10;break e;case j:s=9;break e;case A:s=11;break e;case L:s=14;break e;case M:s=16,r=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Lu(s,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function zu(e,t,n,r){return(e=Lu(7,e,r,t)).lanes=n,e}function Du(e,t,n,r){return(e=Lu(22,e,r,t)).elementType=R,e.lanes=n,e.stateNode={isHidden:!1},e}function _u(e,t,n){return(e=Lu(6,e,null,t)).lanes=n,e}function Vu(e,t,n){return(t=Lu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Fu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Bu(e,t,n,r,a,i,o,s,l){return e=new Fu(e,t,n,s,l),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Lu(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Di(i),e}function Iu(e){if(!e)return ja;e:{if(Ue(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ma(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var n=e.type;if(Ma(n))return za(e,n,t)}return t}function Uu(e,t,n,r,a,i,o,s,l){return(e=Bu(n,r,!0,e,0,i,0,s,l)).context=Iu(null),n=e.current,(i=Vi(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Fi(n,i,a),e.current.lanes=a,vt(e,a,r),ru(e,r),e}function Hu(e,t,n,r){var a=t.current,i=eu(),o=tu(a);return n=Iu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Vi(i,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Fi(a,t,o))&&(nu(e,a,o,i),Bi(e,a,o)),o}function Wu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $u(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Gu(e,t){$u(e,t),(e=e.alternate)&&$u(e,t)}Sl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ta.current)bs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:Ts(t),hi();break;case 5:Yi(t);break;case 1:Ma(t.type)&&Da(t);break;case 4:Ki(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Pa(ki,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Pa(Ji,1&Ji.current),t.flags|=128,null):0!==(n&t.child.childLanes)?_s(e,t,n):(Pa(Ji,1&Ji.current),null!==(e=Ws(e,t,n))?e.sibling:null);Pa(Ji,1&Ji.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Us(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Pa(Ji,Ji.current),r)break;return null;case 22:case 23:return t.lanes=0,Cs(e,t,n)}return Ws(e,t,n)}(e,t,n);bs=0!==(131072&e.flags)}else bs=!1,ai&&0!==(1048576&t.flags)&&Ja(t,Ga,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Hs(e,t),e=t.pendingProps;var a=La(t,Aa.current);Ti(t,n),a=go(null,t,r,e,a,n);var o=vo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ma(r)?(o=!0,Da(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Di(t),a.updater=as,t.stateNode=a,a._reactInternals=t,ls(t,r,e,n),t=As(null,t,r,!0,o,n)):(t.tag=0,ai&&o&&ei(t),xs(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Hs(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Mu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===A)return 11;if(e===L)return 14}return 2}(r),e=ns(r,e),a){case 0:t=Ps(null,t,r,e,n);break e;case 1:t=js(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=ks(null,t,r,ns(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ps(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 1:return r=t.type,a=t.pendingProps,js(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 3:e:{if(Ts(t),null===e)throw Error(i(387));r=t.pendingProps,a=(o=t.memoizedState).element,_i(e,t),Ui(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ns(e,t,r,n,a=us(Error(i(423)),t));break e}if(r!==a){t=Ns(e,t,r,n,a=us(Error(i(424)),t));break e}for(ri=ua(t.stateNode.containerInfo.firstChild),ni=t,ai=!0,ii=null,n=wi(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(hi(),r===a){t=Ws(e,t,n);break e}xs(e,t,r,n)}t=t.child}return t;case 5:return Yi(t),null===e&&ui(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,s=a.children,na(r,a)?s=null:null!==o&&na(r,o)&&(t.flags|=32),Es(e,t),xs(e,t,s,n),t.child;case 6:return null===e&&ui(t),null;case 13:return _s(e,t,n);case 4:return Ki(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xi(t,null,r,n):xs(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,ws(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 7:return xs(e,t,t.pendingProps,n),t.child;case 8:case 12:return xs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,s=a.value,Pa(ki,r._currentValue),r._currentValue=s,null!==o)if(sr(o.value,s)){if(o.children===a.children&&!Ta.current){t=Ws(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var l=o.dependencies;if(null!==l){s=o.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=Vi(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),Ai(o.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===o.tag)s=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(s=o.return))throw Error(i(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Ai(s,n,t),s=o.sibling}else s=o.child;if(null!==s)s.return=o;else for(s=o;null!==s;){if(s===t){s=null;break}if(null!==(o=s.sibling)){o.return=s.return,s=o;break}s=s.return}o=s}xs(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ti(t,n),r=r(a=Ni(a)),t.flags|=1,xs(e,t,r,n),t.child;case 14:return a=ns(r=t.type,t.pendingProps),ks(e,t,r,a=ns(r.type,a),n);case 15:return Ss(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ns(r,a),Hs(e,t),t.tag=1,Ma(r)?(e=!0,Da(t)):e=!1,Ti(t,n),os(t,r,a),ls(t,r,a,n),As(null,t,r,!0,e,n);case 19:return Us(e,t,n);case 22:return Cs(e,t,n)}throw Error(i(156,t.tag))};var qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Qu(e){this._internalRoot=e}function Ku(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function Ju(e,t,n,r,a){var i=n._reactRootContainer;if(i){var o=i;if("function"===typeof a){var s=a;a=function(){var e=Wu(o);s.call(e)}}Hu(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var i=r;r=function(){var e=Wu(o);i.call(e)}}var o=Uu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=o,e[pa]=o.current,Ur(8===e.nodeType?e.parentNode:e),cu(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var s=r;r=function(){var e=Wu(l);s.call(e)}}var l=Bu(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=l,e[pa]=l.current,Ur(8===e.nodeType?e.parentNode:e),cu(function(){Hu(t,l,n,r)}),l}(n,t,e,a,r);return Wu(o)}Ku.prototype.render=Qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Hu(e,t,null,null)},Ku.prototype.unmount=Qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Hu(null,e,null,null)}),t[pa]=null}},Ku.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ct();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rt.length&&0!==t&&t<Rt[n].priority;n++);Rt.splice(n,0,e),0===n&&_t(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ru(t,Ye()),0===(6&Al)&&(Ul=Ye()+500,Ua()))}break;case 13:cu(function(){var t=Oi(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Gu(e,1)}},kt=function(e){if(13===e.tag){var t=Oi(e,134217728);if(null!==t)nu(t,e,134217728,eu());Gu(e,134217728)}},St=function(e){if(13===e.tag){var t=tu(e),n=Oi(e,t);if(null!==n)nu(n,e,t,eu());Gu(e,t)}},Ct=function(){return bt},Et=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},ke=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(i(90));q(r),Z(r,a)}}}break;case"textarea":ie(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ae=uu,Te=cu;var ec={usingClientEntryPoint:!1,Events:[ba,xa,wa,Pe,je,uu]},tc={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),it=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(i(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xu(e))throw Error(i(299));var n=!1,r="",a=qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Bu(e,1,!1,null,0,n,0,r,a),e[pa]=t.current,Ur(8===e.nodeType?e.parentNode:e),new Qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Yu(t))throw Error(i(200));return Ju(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xu(e))throw Error(i(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",s=qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Uu(t,null,e,1,null!=n?n:null,a,0,o,s),e[pa]=t.current,Ur(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Ku(t)},t.render=function(e,t,n){if(!Yu(t))throw Error(i(200));return Ju(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yu(e))throw Error(i(40));return!!e._reactRootContainer&&(cu(function(){Ju(null,null,e,!1,function(){e._reactRootContainer=null,e[pa]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yu(n))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return Ju(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},344:(e,t,n)=>{n.d(t,{dHv:()=>d,b2w:()=>f,Ylv:()=>h,Lyu:()=>p,ABC:()=>m,q9z:()=>v,OLr:()=>g,bv7:()=>y,huF:()=>b,c$c:()=>x,mqD:()=>w,FMC:()=>k,eVK:()=>S,XtC:()=>C,DAO:()=>E,X2V:()=>P,gpD:()=>j,cIQ:()=>A,TMu:()=>T,$xg:()=>N,zeF:()=>M,QLA:()=>L,wXT:()=>R,UA$:()=>O,_AI:()=>z,tWn:()=>D,rqL:()=>_,QJT:()=>V,d8c:()=>F,TF4:()=>B,GRP:()=>I,Zt5:()=>U,s00:()=>H,xR_:()=>W,pMz:()=>$,nfp:()=>G,I7w:()=>q,pCw:()=>Q,K7t:()=>K,Q3K:()=>X,gOg:()=>Y,WWR:()=>Z,thT:()=>J,IG3:()=>ee,pT4:()=>te,XPP:()=>ne,qRc:()=>re,U_s:()=>ae});var r=n(9643),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r.createContext&&r.createContext(a),o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},o.apply(this,arguments)},s=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};function l(e){return e&&e.map(function(e,t){return r.createElement(e.tag,o({key:t},e.attr),l(e.child))})}function u(e){return function(t){return r.createElement(c,o({attr:o({},e.attr)},t),l(e.child))}}function c(e){var t=function(t){var n,a=e.attr,i=e.size,l=e.title,u=s(e,["attr","size","title"]),c=i||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,u,{className:n,style:o(o({color:e.color||t.color},t.style),e.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),l&&r.createElement("title",null,l),e.children)};return void 0!==i?r.createElement(i.Consumer,null,function(e){return t(e)}):t(a)}function d(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"}}]})(e)}function f(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"}}]})(e)}function h(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(e)}function p(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(e)}function m(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"}},{tag:"path",attr:{d:"M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"}}]})(e)}function g(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"}}]})(e)}function v(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(e)}function y(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function b(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(e)}function x(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"}},{tag:"path",attr:{d:"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"}}]})(e)}function w(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(e)}function k(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(e)}function S(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"}}]})(e)}function C(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z",clipRule:"evenodd"}}]})(e)}function E(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(e)}function P(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z"}},{tag:"path",attr:{d:"M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z"}},{tag:"path",attr:{d:"M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z"}}]})(e)}function j(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"}}]})(e)}function A(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function T(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(e)}function N(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"}},{tag:"path",attr:{d:"M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"}}]})(e)}function L(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}},{tag:"path",attr:{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"}}]})(e)}function M(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"}}]})(e)}function R(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(e)}function O(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z",clipRule:"evenodd"}}]})(e)}function z(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"}}]})(e)}function D(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z",clipRule:"evenodd"}}]})(e)}function _(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(e)}function V(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z",clipRule:"evenodd"}}]})(e)}function F(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}},{tag:"path",attr:{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"}}]})(e)}function B(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function I(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function U(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"}}]})(e)}function H(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"}}]})(e)}function W(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}}]})(e)}function $(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(e)}function G(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"}}]})(e)}function q(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"}}]})(e)}function Q(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z",clipRule:"evenodd"}}]})(e)}function K(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"}}]})(e)}function X(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z",clipRule:"evenodd"}}]})(e)}function Y(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}}]})(e)}function Z(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(e)}function J(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(e)}function ee(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(e)}function te(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(e)}function ne(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"}}]})(e)}function re(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function ae(e){return u({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}},467:(e,t,n)=>{n.d(t,{p:()=>l});var r=n(8957),a=n(9644),i=n(9643),o=n(6507);const s=["label","placeholder","value","onChange","type","error","required","disabled","className","aria-describedby","aria-invalid","description"],l=e=>{let{label:t,placeholder:n,value:l,onChange:u,type:c="text",error:d,required:f=!1,disabled:h=!1,className:p="","aria-describedby":m,"aria-invalid":g,description:v}=e,y=(0,a.A)(e,s);const b=(0,i.useId)(),x="".concat(b,"-error"),w="".concat(b,"-description"),k="w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ".concat(d?"border-red-500":"border-gray-600 focus:border-primary-500"," ").concat(p),S=[d?x:null,v?w:null,m].filter(Boolean).join(" ")||void 0;return(0,o.jsxs)("div",{className:"w-full",children:[t&&(0,o.jsxs)("label",{htmlFor:b,className:"block text-sm font-medium text-gray-300 mb-1",children:[t,f&&(0,o.jsx)("span",{className:"text-red-500 ml-1","aria-label":"required",children:"*"})]}),v&&(0,o.jsx)("p",{id:w,className:"text-sm text-gray-400 mb-1",children:v}),(0,o.jsx)("input",(0,r.A)({id:b,type:c,value:l,onChange:e=>u(e.target.value),placeholder:n,disabled:h,required:f,"aria-invalid":g||!!d,"aria-describedby":S,className:k},y)),d&&(0,o.jsx)("p",{id:x,className:"mt-1 text-sm text-red-500",role:"alert",children:d})]})}},755:(e,t,n)=>{e.exports=n(5293)},1241:(e,t,n)=>{var r=n(9643),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,i={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!l.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:i,_owner:s.current}}t.Fragment=i,t.jsx=u,t.jsxs=u},1261:(e,t,n)=>{e.exports=n(3498)},1277:(e,t,n)=>{e.exports=n(3234)},1721:(e,t,n)=>{n.d(t,{C:()=>v,s:()=>g});var r=n(8957),a=n(9643),i=n(7572),o=n(8002),s=n(6507);const l=e=>{let{isOpen:t,onClose:n,children:r,title:l,className:u="",closeOnOverlayClick:c=!0,closeOnEscape:d=!0,size:f="md"}=e;const h=(0,a.useRef)(null),p=(0,a.useRef)(null);(0,a.useEffect)(()=>(t?(p.current=document.activeElement,document.body.style.overflow="hidden",setTimeout(()=>{var e;null===(e=h.current)||void 0===e||e.focus()},100)):(document.body.style.overflow="unset",p.current&&p.current.focus()),()=>{document.body.style.overflow="unset"}),[t]),(0,a.useEffect)(()=>{const e=e=>{if("Escape"===e.key&&d&&n(),"Tab"===e.key&&t){var r;const t=null===(r=h.current)||void 0===r?void 0:r.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(t&&t.length>0){const n=t[0],r=t[t.length-1];e.shiftKey?document.activeElement===n&&(e.preventDefault(),r.focus()):document.activeElement===r&&(e.preventDefault(),n.focus())}}};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,d,n]);return(0,s.jsx)(i.N,{children:t&&(0,s.jsxs)(o.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center p-4",initial:"hidden",animate:"visible",exit:"exit",children:[(0,s.jsx)(o.P.div,{className:"absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm",onClick:c?n:void 0,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.2,ease:"easeOut"}},exit:{opacity:0,transition:{duration:.15,ease:"easeIn"}}},initial:"hidden",animate:"visible",exit:"exit"}),(0,s.jsxs)(o.P.div,{ref:h,className:"\n              relative w-full ".concat({sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"}[f]," bg-background-secondary \n              border border-gray-600 rounded-lg shadow-xl\n              ").concat(u,"\n            "),variants:{hidden:{opacity:0,scale:.9,y:-30,rotateX:15},visible:{opacity:1,scale:1,y:0,rotateX:0,transition:{type:"spring",damping:20,stiffness:300,mass:.8,opacity:{duration:.2}}},exit:{opacity:0,scale:.95,y:-10,transition:{duration:.15,ease:"easeIn"}}},initial:"hidden",animate:"visible",exit:"exit",role:"dialog","aria-modal":"true","aria-labelledby":l?"dialog-title":void 0,"aria-describedby":"dialog-content",tabIndex:-1,children:[l&&(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-600",children:(0,s.jsx)("h2",{id:"dialog-title",className:"text-lg font-semibold text-white",children:l})}),(0,s.jsx)("div",{id:"dialog-content",className:"px-6 py-4",children:r})]})]})})};var u=n(4859);const c=e=>{let{message:t,priority:n="polite",clearAfter:r=3e3}=e;const[i,o]=(0,a.useState)("");return(0,a.useEffect)(()=>{if(t&&(o(t),r>0)){const e=setTimeout(()=>{o("")},r);return()=>clearTimeout(e)}},[t,r]),(0,s.jsx)("div",{"aria-live":n,"aria-atomic":"true",className:"sr-only",role:"status",children:i})},d=e=>{let{isOpen:t,onClose:n,title:r,message:a,confirmText:i="OK",variant:d="info"}=e;const f={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c,{message:t?(()=>{const e=r?"".concat(r,": "):"";return"".concat(e).concat(a)})():"",priority:"error"===d?"assertive":"polite"}),(0,s.jsx)(l,{isOpen:t,onClose:n,title:r,size:"sm",closeOnOverlayClick:!1,children:(0,s.jsxs)(o.P.div,{className:"text-center",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},initial:"hidden",animate:"visible",children:[(0,s.jsx)(o.P.div,{variants:f,children:(()=>{switch(d){case"success":return(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})});case"warning":return(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})});case"error":return(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})});default:return(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}})()}),(0,s.jsx)(o.P.div,{className:"mt-4",variants:f,children:(0,s.jsx)("p",{className:"text-white leading-relaxed whitespace-pre-line",children:a})}),(0,s.jsx)(o.P.div,{className:"mt-6",variants:f,children:(0,s.jsx)(u.$,{onClick:n,variant:"error"===d?"danger":"primary",className:"w-full",autoFocus:!0,children:i})})]})})]})},f=e=>{let{isOpen:t,onClose:n,onConfirm:r,title:a,message:i,confirmText:c="Confirm",cancelText:d="Cancel",variant:f="info",isLoading:h=!1,buttonLayout:p="default",additionalContent:m}=e;const g={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}};return(0,s.jsx)(l,{isOpen:t,onClose:n,title:a,size:"sm",closeOnOverlayClick:!1,children:(0,s.jsxs)(o.P.div,{className:"text-center",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},initial:"hidden",animate:"visible",children:[(0,s.jsx)(o.P.div,{variants:g,children:(()=>{switch(f){case"warning":return(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})});case"danger":return(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})});default:return(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}})()}),(0,s.jsx)(o.P.div,{className:"mt-4",variants:g,children:(0,s.jsx)("p",{className:"text-white leading-relaxed whitespace-pre-line",children:i})}),m&&(0,s.jsx)(o.P.div,{className:"mt-4",variants:g,children:m}),(0,s.jsxs)(o.P.div,{className:"mt-6 ".concat("corners"===p?"flex justify-between items-center":"flex flex-col-reverse sm:flex-row sm:gap-3"),variants:g,children:[(0,s.jsx)(u.$,{onClick:n,variant:"secondary",className:"corners"===p?"px-6":"w-full sm:w-auto mt-3 sm:mt-0",disabled:h,children:d}),(0,s.jsx)(u.$,{onClick:()=>{r()},variant:"danger"===f?"danger":"primary",className:"corners"===p?"px-6":"w-full sm:w-auto",isLoading:h,autoFocus:!0,children:c})]})]})})},h=e=>{let{isOpen:t,onClose:n,onConfirm:r,title:i,message:c,placeholder:d="",defaultValue:f="",confirmText:h="OK",cancelText:p="Cancel",inputType:m="text",validation:g,isLoading:v=!1}=e;const[y,b]=(0,a.useState)(f),[x,w]=(0,a.useState)(null),k=(0,a.useRef)(null);(0,a.useEffect)(()=>{t&&(b(f),w(null),setTimeout(()=>{var e,t;null===(e=k.current)||void 0===e||e.focus(),null===(t=k.current)||void 0===t||t.select()},150))},[t,f]);const S=()=>{const e=y.trim();if(g){const t=g(e);if(t)return void w(t)}r(e)},C=!x&&y.trim().length>0,E={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}};return(0,s.jsx)(l,{isOpen:t,onClose:n,title:i,size:"md",closeOnOverlayClick:!1,children:(0,s.jsxs)(o.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},initial:"hidden",animate:"visible",children:[(0,s.jsx)(o.P.div,{className:"mb-4",variants:E,children:(0,s.jsx)("p",{className:"text-white leading-relaxed whitespace-pre-line",children:c})}),(0,s.jsxs)(o.P.div,{className:"mb-4",variants:E,children:[(0,s.jsx)(o.P.input,{ref:k,type:m,value:y,onChange:e=>{const t=e.target.value;b(t),x&&w(null)},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),S())},placeholder:d,disabled:v,className:"\n              w-full px-3 py-2 bg-background-primary border rounded-md text-white \n              placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors\n              ".concat(x?"border-red-500 focus:ring-red-500":"border-gray-600 focus:ring-primary-500","\n              disabled:opacity-50 disabled:cursor-not-allowed\n            "),"aria-describedby":x?"input-error":void 0,"aria-invalid":!!x,"aria-required":"true","aria-label":d||"Input field",initial:{scale:.95},animate:{scale:1},transition:{delay:.2,duration:.2}}),x&&(0,s.jsx)(o.P.p,{id:"input-error",className:"mt-2 text-sm text-red-400",role:"alert",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.2},children:x})]}),(0,s.jsxs)(o.P.div,{className:"flex flex-col-reverse sm:flex-row sm:gap-3",variants:E,children:[(0,s.jsx)(u.$,{onClick:n,variant:"secondary",className:"w-full sm:w-auto mt-3 sm:mt-0",disabled:v,children:p}),(0,s.jsx)(u.$,{onClick:S,variant:"primary",className:"w-full sm:w-auto",disabled:!C,isLoading:v,children:h})]})]})})},p=e=>{let{checked:t,onChange:n,disabled:r=!1,className:a=""}=e;return(0,s.jsxs)("div",{className:"flex items-center space-x-2 ".concat(a),children:[(0,s.jsx)("input",{type:"checkbox",id:"never-ask-again",checked:t,onChange:e=>n(e.target.checked),disabled:r,className:"w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed"}),(0,s.jsx)("label",{htmlFor:"never-ask-again",className:"text-sm text-gray-300 select-none ".concat(r?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:"Never ask again"})]})},m=(0,a.createContext)(void 0),g=()=>{const e=(0,a.useContext)(m);if(!e)throw new Error("useDialog must be used within a DialogProvider");return e},v=e=>{let{children:t}=e;const[n,i]=(0,a.useState)({type:null,isOpen:!1,options:null,resolve:null,isLoading:!1}),[o,l]=(0,a.useState)(!1),u=(0,a.useCallback)(()=>{i(e=>(0,r.A)((0,r.A)({},e),{},{isOpen:!1,isLoading:!1})),setTimeout(()=>{i({type:null,isOpen:!1,options:null,resolve:null,isLoading:!1}),l(!1)},200)},[]),c=(0,a.useCallback)(e=>new Promise(t=>{i({type:"alert",isOpen:!0,options:e,resolve:t,isLoading:!1})}),[]),g=(0,a.useCallback)(e=>new Promise(t=>{i({type:"confirm",isOpen:!0,options:e,resolve:t,isLoading:!1})}),[]),v=(0,a.useCallback)(e=>new Promise(t=>{i({type:"prompt",isOpen:!0,options:e,resolve:t,isLoading:!1})}),[]),y=(0,a.useCallback)(()=>{n.resolve&&n.resolve(void 0),u()},[n.resolve,u]),b=(0,a.useCallback)(()=>{n.resolve&&n.resolve(!1),u()},[n.resolve,u]),x=(0,a.useCallback)(()=>{n.resolve&&n.resolve(!0),u()},[n.resolve,u]),w=(0,a.useCallback)(()=>{n.resolve&&n.resolve(null),u()},[n.resolve,u]),k=(0,a.useCallback)(e=>{n.resolve&&n.resolve(e),u()},[n.resolve,u]),S={alert:c,confirm:g,prompt:v,closeDialog:u};return(0,s.jsxs)(m.Provider,{value:S,children:[t,"alert"===n.type&&n.options&&(0,s.jsx)(d,(0,r.A)({isOpen:n.isOpen,onClose:y},n.options)),"confirm"===n.type&&n.options&&(()=>{const e=n.options,t=e.showNeverAskAgain?(0,s.jsx)(p,{checked:o,onChange:t=>{var n;l(t),null===(n=e.onNeverAskAgainChange)||void 0===n||n.call(e,t)},disabled:n.isLoading}):void 0;return(0,s.jsx)(f,(0,r.A)({isOpen:n.isOpen,onClose:b,onConfirm:x,isLoading:n.isLoading,additionalContent:t},e))})(),"prompt"===n.type&&n.options&&(0,s.jsx)(h,(0,r.A)({isOpen:n.isOpen,onClose:w,onConfirm:k,isLoading:n.isLoading},n.options))]})}},1820:(e,t,n)=>{n.d(t,{S:()=>i});n(9643);var r=n(344),a=n(6507);const i=e=>{let{id:t,label:n,checked:i,onChange:o,disabled:s=!1,error:l,description:u,size:c="md",className:d=""}=e;const f=(()=>{switch(c){case"sm":return{checkbox:"w-4 h-4",icon:"w-3 h-3",label:"text-sm",description:"text-xs"};case"lg":return{checkbox:"w-6 h-6",icon:"w-4 h-4",label:"text-lg",description:"text-sm"};default:return{checkbox:"w-5 h-5",icon:"w-3.5 h-3.5",label:"text-base",description:"text-sm"}}})(),h=t||"checkbox-".concat(Math.random().toString(36).substr(2,9));return(0,a.jsxs)("div",{className:"flex items-start space-x-3 ".concat(d),children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:h,type:"checkbox",checked:i,onChange:e=>{o(e.target.checked)},disabled:s,className:"sr-only","aria-describedby":u?"".concat(h,"-description"):void 0}),(0,a.jsx)("div",{className:"\n              ".concat(f.checkbox,"\n              border-2 rounded-md cursor-pointer transition-all duration-200\n              ").concat(i?"bg-primary-500 border-primary-500":"bg-background-secondary border-gray-600 hover:border-gray-500","\n              ").concat(s?"opacity-50 cursor-not-allowed":"hover:shadow-sm","\n              ").concat(l?"border-red-500":"","\n              flex items-center justify-center\n            "),onClick:()=>!s&&o(!i),children:i&&(0,a.jsx)(r.q9z,{className:"".concat(f.icon," text-white"),"aria-hidden":"true"})})]})}),(n||u)&&(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[n&&(0,a.jsx)("label",{htmlFor:h,className:"\n                ".concat(f.label,"\n                font-medium cursor-pointer\n                ").concat(s?"text-gray-500":"text-gray-300 hover:text-white","\n                ").concat(l?"text-red-400":"","\n                block\n              "),children:n}),u&&(0,a.jsx)("p",{id:"".concat(h,"-description"),className:"\n                ".concat(f.description,"\n                ").concat(s?"text-gray-600":"text-gray-400","\n                mt-1\n              "),children:u}),l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l})]})]})}},2086:(e,t,n)=>{n.d(t,{A:()=>l,n:()=>s});var r=n(8957),a=n(5914),i=n(2864);const o=new class{setToken(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token"),t?localStorage.setItem("auth_token",e):sessionStorage.setItem("auth_token",e)}getToken(){return localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}clearToken(){localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token")}async signUp(e,t,n){try{const r=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t,name:n})}),a=await r.json();return a.success&&a.token&&this.setToken(a.token,!0),a}catch(r){return{success:!1,error:"Network error during signup"}}}async signIn(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];try{const r=await(0,i.aO)("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();return a.success&&a.token&&this.setToken(a.token,n),a}catch(r){return{success:!1,error:"Network error during login"}}}async signOut(){try{const e=this.getToken();e&&await(0,i.aO)("/api/auth/logout",{method:"POST",headers:{Authorization:"Bearer ".concat(e)}})}finally{this.clearToken()}}async getCurrentUser(){try{const e=this.getToken();if(!e)return null;const t=new AbortController,n=setTimeout(()=>t.abort(),1e4),r=await(0,i.aO)("/api/auth/user",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json","X-Request-ID":"auth-".concat(Date.now(),"-").concat(Math.random())},signal:t.signal});if(clearTimeout(n),!r.ok)return 401===r.status&&(console.log("Token expired or invalid, clearing..."),this.clearToken()),null;const a=await r.json();return a.success&&a.data?a.data:null}catch(e){return console.error("getCurrentUser error:",e),e instanceof Error&&("AbortError"===e.name||e.message.includes("fetch"))&&(console.log("Network error or timeout, clearing token..."),this.clearToken()),null}}isAuthenticated(){return!!this.getToken()}async signInWithGoogle(){try{const e="".concat(window.location.origin,"/auth/callback");console.log("Initiating Google OAuth with redirect:",e);const t=await(0,i.aO)("/api/auth/google",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({redirectTo:e})}),n=await t.json();return!t.ok||n.error?(console.error("Google OAuth initiation failed:",n),{error:n.details||n.error||"Failed to initiate Google sign-in"}):n.url?(console.log("Redirecting to Google OAuth URL"),window.location.href=n.url,{}):{error:"No OAuth URL provided by server"}}catch(e){return console.error("Google OAuth error:",e),{error:"Network error during Google sign-in"}}}async handleOAuthCallback(){try{console.log("Processing OAuth callback...");const e=new URLSearchParams(window.location.search),t=e.get("error"),n=e.get("error_description");if(t)return console.error("OAuth error received:",{error:t,errorDescription:n}),{success:!1,error:n||t||"OAuth authentication failed"};const r=e.get("code"),a=e.get("state");if(console.log("OAuth callback parameters:",{hasCode:!!r,hasState:!!a}),!r){const e=window.location.hash.substring(1),t=new URLSearchParams(e),n=t.get("access_token");return n?(console.log("Found access token in URL fragment, using implicit flow"),await this.handleImplicitFlowCallback(n,t)):{success:!1,error:"No authorization code or access token received from OAuth provider"}}console.log("Processing PKCE flow with authorization code");const o=await(0,i.aO)("/api/auth/oauth-callback",{method:"POST",headers:{"Content-Type":"application/json","X-Request-ID":"oauth-pkce-".concat(Date.now(),"-").concat(Math.random())},body:JSON.stringify({code:r,state:a})}),s=await o.json();return s.success&&s.token&&s.user?(console.log("OAuth callback successful, storing token"),this.setToken(s.token,!0),{success:!0,user:s.user}):(console.error("OAuth callback failed:",s),{success:!1,error:s.details||s.error||"Failed to process OAuth callback"})}catch(e){return console.error("OAuth callback error:",e),{success:!1,error:"Network error during OAuth callback processing"}}}async handleImplicitFlowCallback(e,t){try{const n=t.get("refresh_token"),r=t.get("expires_in"),a=await(0,i.aO)("/api/auth/oauth-user",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e),"X-Request-ID":"oauth-".concat(Date.now(),"-").concat(Math.random())},body:JSON.stringify({access_token:e,refresh_token:n,expires_in:r})}),o=await a.json();return o.success&&o.user?(this.setToken(e,!0),{success:!0,user:o.user}):{success:!1,error:o.error||"Failed to validate OAuth token"}}catch(n){return console.error("Implicit flow callback error:",n),{success:!1,error:"Failed to process implicit flow callback"}}}},s=(0,a.vt)((e,t)=>({user:null,isLoading:!0,isAuthenticated:!1,login:async function(t,n){let r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e({isLoading:!0});try{const a=await o.signIn(t,n,r);return a.success&&a.user?(e({user:a.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:a.error||"Login failed"})}catch(a){return e({isLoading:!1}),{success:!1,error:"Network error"}}},signup:async(t,n,r)=>{e({isLoading:!0});try{const a=await o.signUp(t,n,r);return a.success&&a.user?(e({user:a.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:a.error||"Signup failed"})}catch(a){return e({isLoading:!1}),{success:!1,error:"Network error"}}},signInWithGoogle:async()=>{e({isLoading:!0});try{const t=await o.signInWithGoogle();return t.error?(e({isLoading:!1}),{success:!1,error:t.error}):{success:!0}}catch(t){return e({isLoading:!1}),{success:!1,error:"Failed to initiate Google sign in"}}},handleOAuthCallback:async()=>{e({isLoading:!0});try{const t=await o.handleOAuthCallback();return t.success&&t.user?(e({user:t.user,isAuthenticated:!0,isLoading:!1}),{success:!0,user:t.user}):(e({isLoading:!1}),{success:!1,error:t.error||"OAuth callback failed"})}catch(t){return e({isLoading:!1}),{success:!1,error:"Network error during OAuth callback"}}},logout:async()=>{e({isLoading:!0});try{await o.signOut()}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},checkAuth:async()=>{e({isLoading:!0});try{if(!o.isAuthenticated())return void e({user:null,isAuthenticated:!1,isLoading:!1});const t=new Promise((e,t)=>{setTimeout(()=>t(new Error("Auth check timeout")),15e3)}),n=o.getCurrentUser(),r=await Promise.race([n,t]);r?e({user:r,isAuthenticated:!0,isLoading:!1}):(await o.signOut(),e({user:null,isAuthenticated:!1,isLoading:!1}))}catch(t){console.error("Auth check failed:",t);try{await o.signOut()}catch(n){console.error("Error during signOut:",n)}e({user:null,isAuthenticated:!1,isLoading:!1})}},updateUser:n=>{const{user:a}=t();a&&e({user:(0,r.A)((0,r.A)({},a),n)})}})),l=s},2864:(e,t,n)=>{n.d(t,{aO:()=>s,dQ:()=>o,z9:()=>l});var r=n(8957),a=n(5914);const i=new Map,o=(0,a.vt)((e,t)=>({isOnline:navigator.onLine,isBackendAvailable:!1,lastChecked:null,retryCount:0,isChecking:!1,checkConnection:async()=>{const n=t();if(n.isChecking)return n.isBackendAvailable;e({isChecking:!0});try{if(!navigator.onLine)return e({isOnline:!1,isBackendAvailable:!1,lastChecked:new Date,isChecking:!1}),!1;const t=new AbortController,r=setTimeout(()=>t.abort(),5e3),a=await fetch("/api/health",{method:"GET",signal:t.signal,headers:{"Cache-Control":"no-cache"}});clearTimeout(r);const i=a.ok;return e({isOnline:!0,isBackendAvailable:i,lastChecked:new Date,retryCount:i?0:n.retryCount+1,isChecking:!1}),i}catch(r){return e({isOnline:navigator.onLine,isBackendAvailable:!1,lastChecked:new Date,retryCount:n.retryCount+1,isChecking:!1}),!1}},setOffline:()=>e({isOnline:!1,isBackendAvailable:!1}),setOnline:()=>e({isOnline:!0}),reset:()=>e({isOnline:navigator.onLine,isBackendAvailable:!1,lastChecked:null,retryCount:0,isChecking:!1})})),s=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const{checkConnection:a,isBackendAvailable:l,retryCount:u}=o.getState(),c="".concat(t.method||"GET","-").concat(e,"-").concat(JSON.stringify(t.body||{}));if(i.has(c))return i.get(c);const d=(async()=>{try{if(!l){if(!await a())throw new Error("Backend server is not available")}const n=new AbortController,i=setTimeout(()=>n.abort(),1e4),s=await fetch(e,(0,r.A)((0,r.A)({},t),{},{signal:n.signal}));return clearTimeout(i),s.ok&&!l&&o.getState().checkConnection(),s}catch(d){if(o.getState().setOffline(),n&&u<5){const n=Math.min(1e3*Math.pow(2,u),3e4);return await new Promise(e=>setTimeout(e,n)),s(e,t,!1)}throw d}finally{i.delete(c)}})();return i.set(c,d),d},l=()=>{const{checkConnection:e,setOnline:t,setOffline:n}=o.getState();window.addEventListener("online",()=>{t(),e()}),window.addEventListener("offline",n);e().then(()=>{setInterval(()=>{const{isBackendAvailable:t,isChecking:n}=o.getState();t||n||e()},3e4)})}},3035:(e,t,n)=>{n.d(t,{E:()=>a});var r=n(9643);const a=n(7633).B?r.useLayoutEffect:r.useEffect},3234:(e,t,n)=>{var r=n(9643);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},i=r.useState,o=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(r){return!0}}var c="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),a=r[0].inst,c=r[1];return s(function(){a.value=n,a.getSnapshot=t,u(a)&&c({inst:a})},[e,n,t]),o(function(){return u(a)&&c({inst:a}),e(function(){u(a)&&c({inst:a})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},3299:(e,t,n)=>{function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}var a;n.d(t,{AO:()=>d,Gh:()=>O,HS:()=>z,Oi:()=>s,Rr:()=>f,pX:()=>B,pb:()=>N,rc:()=>a,tH:()=>F,ue:()=>m,yD:()=>R,zR:()=>o}),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(a||(a={}));const i="popstate";function o(e){return void 0===e&&(e={}),h(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return c("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:d(t)},null,e)}function s(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function l(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function u(e,t){return{usr:e.state,key:e.key,idx:t}}function c(e,t,n,a){return void 0===n&&(n=null),r({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?f(t):t,{state:n,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function d(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function f(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function h(e,t,n,o){void 0===o&&(o={});let{window:l=document.defaultView,v5Compat:f=!1}=o,h=l.history,p=a.Pop,m=null,g=v();function v(){return(h.state||{idx:null}).idx}function y(){p=a.Pop;let e=v(),t=null==e?null:e-g;g=e,m&&m({action:p,location:x.location,delta:t})}function b(e){let t="null"!==l.location.origin?l.location.origin:l.location.href,n="string"===typeof e?e:d(e);return n=n.replace(/ $/,"%20"),s(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==g&&(g=0,h.replaceState(r({},h.state,{idx:g}),""));let x={get action(){return p},get location(){return e(l,h)},listen(e){if(m)throw new Error("A history only accepts one active listener");return l.addEventListener(i,y),m=e,()=>{l.removeEventListener(i,y),m=null}},createHref:e=>t(l,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){p=a.Push;let r=c(x.location,e,t);n&&n(r,e),g=v()+1;let i=u(r,g),o=x.createHref(r);try{h.pushState(i,"",o)}catch(s){if(s instanceof DOMException&&"DataCloneError"===s.name)throw s;l.location.assign(o)}f&&m&&m({action:p,location:x.location,delta:1})},replace:function(e,t){p=a.Replace;let r=c(x.location,e,t);n&&n(r,e),g=v();let i=u(r,g),o=x.createHref(r);h.replaceState(i,"",o),f&&m&&m({action:p,location:x.location,delta:0})},go:e=>h.go(e)};return x}var p;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(p||(p={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function m(e,t,n){return void 0===n&&(n="/"),g(e,t,n,!1)}function g(e,t,n,r){let a=N(("string"===typeof t?f(t):t).pathname||"/",n);if(null==a)return null;let i=v(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(i);let o=null;for(let s=0;null==o&&s<i.length;++s){let e=T(a);o=j(i[s],e,r)}return o}function v(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,i)=>{let o={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(s(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let l=z([r,o.relativePath]),u=n.concat(o);e.children&&e.children.length>0&&(s(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+l+'".'),v(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:P(l,e.index),routesMeta:u})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of y(e.path))a(e,t,r);else a(e,t)}),t}function y(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===r.length)return a?[i,""]:[i];let o=y(r.join("/")),s=[];return s.push(...o.map(e=>""===e?i:[i,e].join("/"))),a&&s.push(...o),s.map(t=>e.startsWith("/")&&""===t?"/":t)}const b=/^:[\w-]+$/,x=3,w=2,k=1,S=10,C=-2,E=e=>"*"===e;function P(e,t){let n=e.split("/"),r=n.length;return n.some(E)&&(r+=C),t&&(r+=w),n.filter(e=>!E(e)).reduce((e,t)=>e+(b.test(t)?x:""===t?k:S),r)}function j(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},i="/",o=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,u="/"===i?t:t.slice(i.length)||"/",c=A({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=A({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),o.push({params:a,pathname:z([i,c.pathname]),pathnameBase:D(z([i,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(i=z([i,c.pathnameBase]))}return o}function A(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);l("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let i=new RegExp(a,t?void 0:"i");return[i,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";o=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:i,pathnameBase:o,pattern:e}}function T(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return l(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function N(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function L(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function M(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function R(e,t){let n=M(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function O(e,t,n,a){let i;void 0===a&&(a=!1),"string"===typeof e?i=f(e):(i=r({},e),s(!i.pathname||!i.pathname.includes("?"),L("?","pathname","search",i)),s(!i.pathname||!i.pathname.includes("#"),L("#","pathname","hash",i)),s(!i.search||!i.search.includes("#"),L("#","search","hash",i)));let o,l=""===e||""===i.pathname,u=l?"/":i.pathname;if(null==u)o=n;else{let e=t.length-1;if(!a&&u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}o=e>=0?t[e]:"/"}let c=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?f(e):e,i=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:i,search:_(r),hash:V(a)}}(i,o),d=u&&"/"!==u&&u.endsWith("/"),h=(l||"."===u)&&n.endsWith("/");return c.pathname.endsWith("/")||!d&&!h||(c.pathname+="/"),c}const z=e=>e.join("/").replace(/\/\/+/g,"/"),D=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),_=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",V=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class F extends Error{}function B(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const I=["post","put","patch","delete"],U=(new Set(I),["get",...I]);new Set(U),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred")},3347:(e,t,n)=>{n.d(t,{WG:()=>s,Gt:()=>o,uv:()=>l,Ci:()=>u});var r=n(6318);class a{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}const i=["prepare","read","update","preRender","render","postRender"];const{schedule:o,cancel:s,state:l,steps:u}=function(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},s=i.reduce((e,t)=>(e[t]=function(e){let t=new a,n=new a,r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:function(e){const a=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&i,o=a?t:n;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&s.add(e),o.add(e)&&a&&i&&(r=t.order.length),e},cancel:e=>{n.remove(e),s.delete(e)},process:a=>{if(i)o=!0;else{if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let n=0;n<r;n++){const r=t.order[n];r(a),s.has(r)&&(l.schedule(r),e())}i=!1,o&&(o=!1,l.process(a))}}};return l}(()=>n=!0),e),{}),l=e=>s[e].process(o),u=()=>{const a=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(a-o.timestamp,40),1),o.timestamp=a,o.isProcessing=!0,i.forEach(l),o.isProcessing=!1,n&&t&&(r=!1,e(u))},c=i.reduce((t,a)=>{const i=s[a];return t[a]=function(t){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,o.isProcessing||e(u)),i.schedule(t,a,s)},t},{});return{schedule:c,cancel:e=>i.forEach(t=>s[t].cancel(e)),state:o,steps:s}}("undefined"!==typeof requestAnimationFrame?requestAnimationFrame:r.l,!0)},3498:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<i(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>i(l,n))u<a&&0>i(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<a&&0>i(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,R(k);else{var t=r(c);null!==t&&O(w,t.startTime-e)}}function k(e,n){m=!1,g&&(g=!1,y(P),P=-1),p=!0;var i=h;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!T());){var o=f.callback;if("function"===typeof o){f.callback=null,h=f.priorityLevel;var s=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&a(u),x(n)}else a(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&O(w,d.startTime-n),l=!1}return l}finally{f=null,h=i,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,C=!1,E=null,P=-1,j=5,A=-1;function T(){return!(t.unstable_now()-A<j)}function N(){if(null!==E){var e=t.unstable_now();A=e;var n=!0;try{n=E(!0,e)}finally{n?S():(C=!1,E=null)}}else C=!1}if("function"===typeof b)S=function(){b(N)};else if("undefined"!==typeof MessageChannel){var L=new MessageChannel,M=L.port2;L.port1.onmessage=N,S=function(){M.postMessage(null)}}else S=function(){v(N,0)};function R(e){E=e,C||(C=!0,S())}function O(e,n){P=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,R(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,a,i){var o=t.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?o+i:o:i=o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:s=i+s,sortIndex:-1},i>o?(e.sortIndex=i,n(c,e),null===r(u)&&e===r(c)&&(g?(y(P),P=-1):g=!0,O(w,i-o))):(e.sortIndex=s,n(u,e),m||p||(m=!0,R(k))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},3766:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(306)},4536:(e,t,n)=>{n.d(t,{L:()=>r});const r=(0,n(9643).createContext)({})},4779:(e,t,n)=>{n.d(t,{$:()=>a,V:()=>i});var r=n(6318);let a=r.l,i=r.l},4859:(e,t,n)=>{n.d(t,{$:()=>l});var r=n(8957),a=n(9644),i=(n(9643),n(8002)),o=n(6507);const s=["children","onClick","variant","size","isLoading","disabled","type","className","aria-label","aria-describedby","aria-expanded","aria-controls","aria-pressed","autoFocus"],l=e=>{let{children:t,onClick:n,variant:l="primary",size:u="md",isLoading:c=!1,disabled:d=!1,type:f="button",className:h="","aria-label":p,"aria-describedby":m,"aria-expanded":g,"aria-controls":v,"aria-pressed":y,autoFocus:b=!1}=e,x=(0,a.A)(e,s);const w="".concat("font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform-gpu"," ").concat({primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500 hover:shadow-lg",secondary:"border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500 hover:shadow-md",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500 hover:shadow-lg"}[l]," ").concat({sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[u]," ").concat(h);return(0,o.jsx)(i.P.button,(0,r.A)((0,r.A)({type:f,onClick:n,disabled:d||c,className:w,"aria-label":p,"aria-describedby":m,"aria-expanded":g,"aria-controls":v,"aria-pressed":y,"aria-busy":c,autoFocus:b,variants:{hover:{scale:1.02,transition:{duration:.15,ease:"easeOut"}},tap:{scale:.98,transition:{duration:.1,ease:"easeInOut"}}},whileHover:d||c?void 0:"hover",whileTap:d||c?void 0:"tap"},x),{},{children:c?(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,o.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,o.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):t}))}},5293:(e,t,n)=>{var r=n(9643),a=n(1277);var i="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=a.useSyncExternalStore,s=r.useRef,l=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=u(function(){function e(e){if(!l){if(l=!0,o=e,e=r(e),void 0!==a&&f.hasValue){var t=f.value;if(a(t,e))return s=t}return s=e}if(t=s,i(o,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(o=e,t):(o=e,s=n)}var o,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,a]);var h=o(e,d[0],d[1]);return l(function(){f.hasValue=!0,f.value=h},[h]),c(h),h}},5914:(e,t,n)=>{n.d(t,{vt:()=>f});const r=e=>{let t;const n=new Set,r=(e,r)=>{const a="function"===typeof e?e(t):e;if(!Object.is(a,t)){const e=t;t=(null!=r?r:"object"!==typeof a||null===a)?a:Object.assign({},t,a),n.forEach(n=>n(t,e))}},a=()=>t,i={setState:r,getState:a,getInitialState:()=>o,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},o=t=e(r,a,i);return i},a=e=>e?r(e):r;var i=n(9643),o=n(755);const{useDebugValue:s}=i,{useSyncExternalStoreWithSelector:l}=o;let u=!1;const c=e=>e;const d=e=>{"function"!==typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t="function"===typeof e?a(e):e,n=(e,n)=>function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c,n=arguments.length>2?arguments[2]:void 0;n&&!u&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),u=!0);const r=l(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return s(r),r}(t,e,n);return Object.assign(n,t),n},f=e=>e?d(e):d},6168:(e,t,n)=>{n.d(t,{M:()=>a});var r=n(9643);function a(e){const t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},6318:(e,t,n)=>{n.d(t,{l:()=>r});const r=e=>e},6507:(e,t,n)=>{e.exports=n(1241)},7192:(e,t,n)=>{var r;n.d(t,{$P:()=>h,BV:()=>_,C5:()=>O,Ix:()=>D,V8:()=>R,Zp:()=>v,g:()=>y,jb:()=>u,qh:()=>z,x$:()=>b,zy:()=>m});var a=n(9643),i=n(3299);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o.apply(this,arguments)}const s=a.createContext(null);const l=a.createContext(null);const u=a.createContext(null);const c=a.createContext(null);const d=a.createContext({outlet:null,matches:[],isDataRoute:!1});const f=a.createContext(null);function h(e,t){let{relative:n}=void 0===t?{}:t;p()||(0,i.Oi)(!1);let{basename:r,navigator:o}=a.useContext(u),{hash:s,pathname:l,search:c}=b(e,{relative:n}),d=l;return"/"!==r&&(d="/"===l?r:(0,i.HS)([r,l])),o.createHref({pathname:d,search:c,hash:s})}function p(){return null!=a.useContext(c)}function m(){return p()||(0,i.Oi)(!1),a.useContext(c).location}function g(e){a.useContext(u).static||a.useLayoutEffect(e)}function v(){let{isDataRoute:e}=a.useContext(d);return e?function(){let{router:e}=A(P.UseNavigateStable),t=N(j.UseNavigateStable),n=a.useRef(!1);g(()=>{n.current=!0});let r=a.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,o({fromRouteId:t},a)))},[e,t]);return r}():function(){p()||(0,i.Oi)(!1);let e=a.useContext(s),{basename:t,future:n,navigator:r}=a.useContext(u),{matches:o}=a.useContext(d),{pathname:l}=m(),c=JSON.stringify((0,i.yD)(o,n.v7_relativeSplatPath)),f=a.useRef(!1);return g(()=>{f.current=!0}),a.useCallback(function(n,a){if(void 0===a&&(a={}),!f.current)return;if("number"===typeof n)return void r.go(n);let o=(0,i.Gh)(n,JSON.parse(c),l,"path"===a.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:(0,i.HS)([t,o.pathname])),(a.replace?r.replace:r.push)(o,a.state,a)},[t,r,c,l,e])}()}function y(){let{matches:e}=a.useContext(d),t=e[e.length-1];return t?t.params:{}}function b(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=a.useContext(u),{matches:o}=a.useContext(d),{pathname:s}=m(),l=JSON.stringify((0,i.yD)(o,r.v7_relativeSplatPath));return a.useMemo(()=>(0,i.Gh)(e,JSON.parse(l),s,"path"===n),[e,l,s,n])}function x(e,t,n,r){p()||(0,i.Oi)(!1);let{navigator:s}=a.useContext(u),{matches:l}=a.useContext(d),f=l[l.length-1],h=f?f.params:{},g=(f&&f.pathname,f?f.pathnameBase:"/");f&&f.route;let v,y=m();if(t){var b;let e="string"===typeof t?(0,i.Rr)(t):t;"/"===g||(null==(b=e.pathname)?void 0:b.startsWith(g))||(0,i.Oi)(!1),v=e}else v=y;let x=v.pathname||"/",w=x;if("/"!==g){let e=g.replace(/^\//,"").split("/");w="/"+x.replace(/^\//,"").split("/").slice(e.length).join("/")}let k=(0,i.ue)(e,{pathname:w});let S=E(k&&k.map(e=>Object.assign({},e,{params:Object.assign({},h,e.params),pathname:(0,i.HS)([g,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?g:(0,i.HS)([g,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),l,n,r);return t&&S?a.createElement(c.Provider,{value:{location:o({pathname:"/",search:"",hash:"",state:null,key:"default"},v),navigationType:i.rc.Pop}},S):S}function w(){let e=function(){var e;let t=a.useContext(f),n=T(j.UseRouteError),r=N(j.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=(0,i.pX)(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,null)}const k=a.createElement(w,null);class S extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(d.Provider,{value:this.props.routeContext},a.createElement(f.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function C(e){let{routeContext:t,match:n,children:r}=e,i=a.useContext(s);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(d.Provider,{value:t},r)}function E(e,t,n,r){var o;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var s;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(s=r)&&s.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let l=e,u=null==(o=n)?void 0:o.errors;if(null!=u){let e=l.findIndex(e=>e.route.id&&void 0!==(null==u?void 0:u[e.route.id]));e>=0||(0,i.Oi)(!1),l=l.slice(0,Math.min(l.length,e+1))}let c=!1,d=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<l.length;a++){let e=l[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(d=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){c=!0,l=d>=0?l.slice(0,d+1):[l[0]];break}}}return l.reduceRight((e,r,i)=>{let o,s=!1,f=null,h=null;var p;n&&(o=u&&r.route.id?u[r.route.id]:void 0,f=r.route.errorElement||k,c&&(d<0&&0===i?(p="route-fallback",!1||L[p]||(L[p]=!0),s=!0,h=null):d===i&&(s=!0,h=r.route.hydrateFallbackElement||null)));let m=t.concat(l.slice(0,i+1)),g=()=>{let t;return t=o?f:s?h:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(C,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===i)?a.createElement(S,{location:n.location,revalidation:n.revalidation,component:f,error:o,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()},null)}var P=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(P||{}),j=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(j||{});function A(e){let t=a.useContext(s);return t||(0,i.Oi)(!1),t}function T(e){let t=a.useContext(l);return t||(0,i.Oi)(!1),t}function N(e){let t=function(){let e=a.useContext(d);return e||(0,i.Oi)(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||(0,i.Oi)(!1),n.route.id}const L={};const M=(e,t,n)=>{};function R(e,t){void 0===(null==e?void 0:e.v7_startTransition)&&M("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),void 0!==(null==e?void 0:e.v7_relativeSplatPath)||t&&void 0!==t.v7_relativeSplatPath||M("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(void 0===t.v7_fetcherPersist&&M("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),void 0===t.v7_normalizeFormMethod&&M("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),void 0===t.v7_partialHydration&&M("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),void 0===t.v7_skipActionErrorRevalidation&&M("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}(r||(r=n.t(a,2))).startTransition;function O(e){let{to:t,replace:n,state:r,relative:o}=e;p()||(0,i.Oi)(!1);let{future:s,static:l}=a.useContext(u),{matches:c}=a.useContext(d),{pathname:f}=m(),h=v(),g=(0,i.Gh)(t,(0,i.yD)(c,s.v7_relativeSplatPath),f,"path"===o),y=JSON.stringify(g);return a.useEffect(()=>h(JSON.parse(y),{replace:n,state:r,relative:o}),[h,y,o,n,r]),null}function z(e){(0,i.Oi)(!1)}function D(e){let{basename:t="/",children:n=null,location:r,navigationType:s=i.rc.Pop,navigator:l,static:d=!1,future:f}=e;p()&&(0,i.Oi)(!1);let h=t.replace(/^\/*/,"/"),m=a.useMemo(()=>({basename:h,navigator:l,static:d,future:o({v7_relativeSplatPath:!1},f)}),[h,f,l,d]);"string"===typeof r&&(r=(0,i.Rr)(r));let{pathname:g="/",search:v="",hash:y="",state:b=null,key:x="default"}=r,w=a.useMemo(()=>{let e=(0,i.pb)(g,h);return null==e?null:{location:{pathname:e,search:v,hash:y,state:b,key:x},navigationType:s}},[h,g,v,y,b,x,s]);return null==w?null:a.createElement(u.Provider,{value:m},a.createElement(c.Provider,{children:n,value:w}))}function _(e){let{children:t,location:n}=e;return x(V(t),n)}new Promise(()=>{});a.Component;function V(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,(e,r)=>{if(!a.isValidElement(e))return;let o=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,V(e.props.children,o));e.type!==z&&(0,i.Oi)(!1),e.props.index&&e.props.children&&(0,i.Oi)(!1);let s={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(s.children=V(e.props.children,o)),n.push(s)}),n}},7572:(e,t,n)=>{n.d(t,{N:()=>g});var r=n(9643),a=n(3035);function i(){const e=(0,r.useRef)(!1);return(0,a.E)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var o=n(3347);var s=n(7984),l=n(6168);class u extends r.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c(e){let{children:t,isPresent:n}=e;const a=(0,r.useId)(),i=(0,r.useRef)(null),o=(0,r.useRef)({width:0,height:0,top:0,left:0});return(0,r.useInsertionEffect)(()=>{const{width:e,height:t,top:r,left:s}=o.current;if(n||!i.current||!e||!t)return;i.current.dataset.motionPopId=a;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[n]),r.createElement(u,{isPresent:n,childRef:i,sizeRef:o},r.cloneElement(t,{ref:i}))}const d=e=>{let{children:t,initial:n,isPresent:a,onExitComplete:i,custom:o,presenceAffectsLayout:u,mode:d}=e;const h=(0,l.M)(f),p=(0,r.useId)(),m=(0,r.useMemo)(()=>({id:p,initial:n,isPresent:a,custom:o,onExitComplete:e=>{h.set(e,!0);for(const t of h.values())if(!t)return;i&&i()},register:e=>(h.set(e,!1),()=>h.delete(e))}),u?void 0:[a]);return(0,r.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[a]),r.useEffect(()=>{!a&&!h.size&&i&&i()},[a]),"popLayout"===d&&(t=r.createElement(c,{isPresent:a},t)),r.createElement(s.t.Provider,{value:m},t)};function f(){return new Map}var h=n(4536);var p=n(4779);const m=e=>e.key||"";const g=e=>{let{children:t,custom:n,initial:s=!0,onExitComplete:l,exitBeforeEnter:u,presenceAffectsLayout:c=!0,mode:f="sync"}=e;(0,p.V)(!u,"Replace exitBeforeEnter with mode='wait'");const g=(0,r.useContext)(h.L).forceRender||function(){const e=i(),[t,n]=(0,r.useState)(0),a=(0,r.useCallback)(()=>{e.current&&n(t+1)},[t]);return[(0,r.useCallback)(()=>o.Gt.postRender(a),[a]),t]}()[0],v=i(),y=function(e){const t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}(t);let b=y;const x=(0,r.useRef)(new Map).current,w=(0,r.useRef)(b),k=(0,r.useRef)(new Map).current,S=(0,r.useRef)(!0);var C;if((0,a.E)(()=>{S.current=!1,function(e,t){e.forEach(e=>{const n=m(e);t.set(n,e)})}(y,k),w.current=b}),C=()=>{S.current=!0,k.clear(),x.clear()},(0,r.useEffect)(()=>()=>C(),[]),S.current)return r.createElement(r.Fragment,null,b.map(e=>r.createElement(d,{key:m(e),isPresent:!0,initial:!!s&&void 0,presenceAffectsLayout:c,mode:f},e)));b=[...b];const E=w.current.map(m),P=y.map(m),j=E.length;for(let r=0;r<j;r++){const e=E[r];-1!==P.indexOf(e)||x.has(e)||x.set(e,void 0)}return"wait"===f&&x.size&&(b=[]),x.forEach((e,t)=>{if(-1!==P.indexOf(t))return;const a=k.get(t);if(!a)return;const i=E.indexOf(t);let o=e;if(!o){const e=()=>{x.delete(t);const e=Array.from(k.keys()).filter(e=>!P.includes(e));if(e.forEach(e=>k.delete(e)),w.current=y.filter(n=>{const r=m(n);return r===t||e.includes(r)}),!x.size){if(!1===v.current)return;g(),l&&l()}};o=r.createElement(d,{key:m(a),isPresent:!1,onExitComplete:e,custom:n,presenceAffectsLayout:c,mode:f},a),x.set(t,o)}b.splice(i,0,o)}),b=b.map(e=>{const t=e.key;return x.has(t)?e:r.createElement(d,{key:m(e),isPresent:!0,presenceAffectsLayout:c,mode:f},e)}),r.createElement(r.Fragment,null,x.size?b:b.map(e=>(0,r.cloneElement)(e)))}},7633:(e,t,n)=>{n.d(t,{B:()=>r});const r="undefined"!==typeof document},7984:(e,t,n)=>{n.d(t,{t:()=>r});const r=(0,n(9643).createContext)(null)},8002:(e,t,n)=>{n.d(t,{P:()=>so});var r=n(8957),a=n(9643);const i=(0,a.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),o=(0,a.createContext)({});var s=n(7984),l=n(3035);const u=(0,a.createContext)({strict:!1}),c=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),d="data-"+c("framerAppearId");function f(e){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function h(e){return"string"===typeof e||Array.isArray(e)}function p(e){return null!==e&&"object"===typeof e&&"function"===typeof e.start}const m=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],g=["initial",...m];function v(e){return p(e.animate)||g.some(t=>h(e[t]))}function y(e){return Boolean(v(e)||e.variants)}function b(e){const{initial:t,animate:n}=function(e,t){if(v(e)){const{initial:t,animate:n}=e;return{initial:!1===t||h(t)?t:void 0,animate:h(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,a.useContext)(o));return(0,a.useMemo)(()=>({initial:t,animate:n}),[x(t),x(n)])}function x(e){return Array.isArray(e)?e.join(" "):e}const w={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},k={};for(const uo in w)k[uo]={isEnabled:e=>w[uo].some(t=>!!e[t])};var S=n(7633),C=n(4536);const E=(0,a.createContext)({}),P=Symbol.for("motionComponentSymbol");function j(e){let{preloadedFeatures:t,createVisualElement:n,useRender:c,useVisualState:h,Component:p}=e;t&&function(e){for(const t in e)k[t]=(0,r.A)((0,r.A)({},k[t]),e[t])}(t);const m=(0,a.forwardRef)(function(e,m){let g;const v=(0,r.A)((0,r.A)((0,r.A)({},(0,a.useContext)(i)),e),{},{layoutId:A(e)}),{isStatic:y}=v,x=b(e),w=h(e,y);if(!y&&S.B){x.visualElement=function(e,t,n,r){const{visualElement:c}=(0,a.useContext)(o),f=(0,a.useContext)(u),h=(0,a.useContext)(s.t),p=(0,a.useContext)(i).reducedMotion,m=(0,a.useRef)();r=r||f.renderer,!m.current&&r&&(m.current=r(e,{visualState:t,parent:c,props:n,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:p}));const g=m.current;(0,a.useInsertionEffect)(()=>{g&&g.update(n,h)});const v=(0,a.useRef)(Boolean(n[d]&&!window.HandoffComplete));return(0,l.E)(()=>{g&&(g.render(),v.current&&g.animationState&&g.animationState.animateChanges())}),(0,a.useEffect)(()=>{g&&(g.updateFeatures(),!v.current&&g.animationState&&g.animationState.animateChanges(),v.current&&(v.current=!1,window.HandoffComplete=!0))}),g}(p,w,v,n);const e=(0,a.useContext)(E),r=(0,a.useContext)(u).strict;x.visualElement&&(g=x.visualElement.loadFeatures(v,r,t,e))}return a.createElement(o.Provider,{value:x},g&&x.visualElement?a.createElement(g,(0,r.A)({visualElement:x.visualElement},v)):null,c(p,e,function(e,t,n){return(0,a.useCallback)(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):f(n)&&(n.current=r))},[t])}(w,x.visualElement,m),w,y,x.visualElement))});return m[P]=p,m}function A(e){let{layoutId:t}=e;const n=(0,a.useContext)(C.L).id;return n&&void 0!==t?n+"-"+t:t}function T(e){function t(t){return j(e(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}))}if("undefined"===typeof Proxy)return t;const n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}const N=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function L(e){return"string"===typeof e&&!e.includes("-")&&!!(N.indexOf(e)>-1||/[A-Z]/.test(e))}const M={};const R=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],O=new Set(R);function z(e,t){let{layout:n,layoutId:r}=t;return O.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!M[e]||"opacity"===e)}const D=e=>Boolean(e&&e.getVelocity),_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},V=R.length;const F=e=>t=>"string"===typeof t&&t.startsWith(e),B=F("--"),I=F("var(--"),U=(e,t)=>t&&"number"===typeof e?t.transform(e):e,H=(e,t,n)=>Math.min(Math.max(n,e),t),W={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},$=(0,r.A)((0,r.A)({},W),{},{transform:e=>H(0,1,e)}),G=(0,r.A)((0,r.A)({},W),{},{default:1}),q=e=>Math.round(1e5*e)/1e5,Q=/(-)?([\d]*\.?[\d])+/g,K=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,X=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Y(e){return"string"===typeof e}const Z=e=>({test:t=>Y(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),J=Z("deg"),ee=Z("%"),te=Z("px"),ne=Z("vh"),re=Z("vw"),ae=(0,r.A)((0,r.A)({},ee),{},{parse:e=>ee.parse(e)/100,transform:e=>ee.transform(100*e)}),ie=(0,r.A)((0,r.A)({},W),{},{transform:Math.round}),oe={borderWidth:te,borderTopWidth:te,borderRightWidth:te,borderBottomWidth:te,borderLeftWidth:te,borderRadius:te,radius:te,borderTopLeftRadius:te,borderTopRightRadius:te,borderBottomRightRadius:te,borderBottomLeftRadius:te,width:te,maxWidth:te,height:te,maxHeight:te,size:te,top:te,right:te,bottom:te,left:te,padding:te,paddingTop:te,paddingRight:te,paddingBottom:te,paddingLeft:te,margin:te,marginTop:te,marginRight:te,marginBottom:te,marginLeft:te,rotate:J,rotateX:J,rotateY:J,rotateZ:J,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:J,skewX:J,skewY:J,distance:te,translateX:te,translateY:te,translateZ:te,x:te,y:te,z:te,perspective:te,transformPerspective:te,opacity:$,originX:ae,originY:ae,originZ:te,zIndex:ie,fillOpacity:$,strokeOpacity:$,numOctaves:ie};function se(e,t,n,r){const{style:a,vars:i,transform:o,transformOrigin:s}=e;let l=!1,u=!1,c=!0;for(const d in t){const e=t[d];if(B(d)){i[d]=e;continue}const n=oe[d],r=U(e,n);if(O.has(d)){if(l=!0,o[d]=r,!c)continue;e!==(n.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,s[d]=r):a[d]=r}if(t.transform||(l||r?a.transform=function(e,t,n,r){let{enableHardwareAcceleration:a=!0,allowTransformNone:i=!0}=t,o="";for(let s=0;s<V;s++){const t=R[s];void 0!==e[t]&&(o+="".concat(_[t]||t,"(").concat(e[t],") "))}return a&&!e.z&&(o+="translateZ(0)"),o=o.trim(),r?o=r(e,n?"":o):i&&n&&(o="none"),o}(e.transform,n,c,r):a.transform&&(a.transform="none")),u){const{originX:e="50%",originY:t="50%",originZ:n=0}=s;a.transformOrigin="".concat(e," ").concat(t," ").concat(n)}}const le=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ue(e,t,n){for(const r in t)D(t[r])||z(r,n)||(e[r]=t[r])}function ce(e,t,n){const r={};return ue(r,e.style||{},e),Object.assign(r,function(e,t,n){let{transformTemplate:r}=e;return(0,a.useMemo)(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return se(e,t,{enableHardwareAcceleration:!n},r),Object.assign({},e.vars,e.style)},[t])}(e,t,n)),e.transformValues?e.transformValues(r):r}function de(e,t,n){const r={},a=ce(e,t,n);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,a.userSelect=a.WebkitUserSelect=a.WebkitTouchCallout="none",a.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=a,r}const fe=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function he(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||fe.has(e)}let pe=e=>!he(e);try{(me=require("@emotion/is-prop-valid").default)&&(pe=e=>e.startsWith("on")?!he(e):me(e))}catch(lo){}var me;var ge=n(9644);function ve(e,t,n){return"string"===typeof e?e:te.transform(t+n*e)}const ye={offset:"stroke-dashoffset",array:"stroke-dasharray"},be={offset:"strokeDashoffset",array:"strokeDasharray"};const xe=["attrX","attrY","attrScale","originX","originY","pathLength","pathSpacing","pathOffset"];function we(e,t,n,r,a){let{attrX:i,attrY:o,attrScale:s,originX:l,originY:u,pathLength:c,pathSpacing:d=1,pathOffset:f=0}=t;if(se(e,(0,ge.A)(t,xe),n,a),r)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:h,style:p,dimensions:m}=e;h.transform&&(m&&(p.transform=h.transform),delete h.transform),m&&(void 0!==l||void 0!==u||p.transform)&&(p.transformOrigin=function(e,t,n){const r=ve(t,e.x,e.width),a=ve(n,e.y,e.height);return"".concat(r," ").concat(a)}(m,void 0!==l?l:.5,void 0!==u?u:.5)),void 0!==i&&(h.x=i),void 0!==o&&(h.y=o),void 0!==s&&(h.scale=s),void 0!==c&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const i=a?ye:be;e[i.offset]=te.transform(-r);const o=te.transform(t),s=te.transform(n);e[i.array]="".concat(o," ").concat(s)}(h,c,d,f,!1)}const ke=()=>(0,r.A)((0,r.A)({},{style:{},transform:{},transformOrigin:{},vars:{}}),{},{attrs:{}}),Se=e=>"string"===typeof e&&"svg"===e.toLowerCase();function Ce(e,t,n,i){const o=(0,a.useMemo)(()=>{const n=ke();return we(n,t,{enableHardwareAcceleration:!1},Se(i),e.transformTemplate),(0,r.A)((0,r.A)({},n.attrs),{},{style:(0,r.A)({},n.style)})},[t]);if(e.style){const t={};ue(t,e.style,e),o.style=(0,r.A)((0,r.A)({},t),o.style)}return o}function Ee(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(t,n,i,o,s)=>{let{latestValues:l}=o;const u=(L(t)?Ce:de)(n,l,s,t),c=function(e,t,n){const r={};for(const a in e)"values"===a&&"object"===typeof e.values||(pe(a)||!0===n&&he(a)||!t&&!he(a)||e.draggable&&a.startsWith("onDrag"))&&(r[a]=e[a]);return r}(n,"string"===typeof t,e),d=(0,r.A)((0,r.A)((0,r.A)({},c),u),{},{ref:i}),{children:f}=n,h=(0,a.useMemo)(()=>D(f)?f.get():f,[f]);return(0,a.createElement)(t,(0,r.A)((0,r.A)({},d),{},{children:h}))}}function Pe(e,t,n,r){let{style:a,vars:i}=t;Object.assign(e.style,a,r&&r.getProjectionStyles(n));for(const o in i)e.style.setProperty(o,i[o])}const je=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ae(e,t,n,r){Pe(e,t,void 0,r);for(const a in t.attrs)e.setAttribute(je.has(a)?a:c(a),t.attrs[a])}function Te(e,t){const{style:n}=e,r={};for(const a in n)(D(n[a])||t.style&&D(t.style[a])||z(a,e))&&(r[a]=n[a]);return r}function Ne(e,t){const n=Te(e,t);for(const r in e)if(D(e[r])||D(t[r])){n[-1!==R.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]}return n}function Le(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,a)),"string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,a)),t}var Me=n(6168);const Re=e=>Array.isArray(e),Oe=e=>Re(e)?e[e.length-1]||0:e;function ze(e){const t=D(e)?e.get():e;return n=t,Boolean(n&&"object"===typeof n&&n.mix&&n.toValue)?t.toValue():t;var n}const De=["transitionEnd","transition"];const _e=e=>(t,n)=>{const r=(0,a.useContext)(o),i=(0,a.useContext)(s.t),l=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:a,createRenderState:i,onMount:o}=e;const s={latestValues:Ve(t,n,r,a),renderState:i()};return o&&(s.mount=e=>o(t,e,s)),s}(e,t,r,i);return n?l():(0,Me.M)(l)};function Ve(e,t,n,r){const a={},i=r(e,{});for(const f in i)a[f]=ze(i[f]);let{initial:o,animate:s}=e;const l=v(e),u=y(e);t&&u&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===s&&(s=t.animate));let c=!!n&&!1===n.initial;c=c||!1===o;const d=c?s:o;if(d&&"boolean"!==typeof d&&!p(d)){(Array.isArray(d)?d:[d]).forEach(t=>{const n=Le(e,t);if(!n)return;const{transitionEnd:r,transition:i}=n,o=(0,ge.A)(n,De);for(const e in o){let t=o[e];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(a[e]=t)}for(const e in r)a[e]=r[e]})}return a}var Fe=n(3347);const Be={useVisualState:_e({scrapeMotionValuesFromProps:Ne,createRenderState:ke,onMount:(e,t,n)=>{let{renderState:r,latestValues:a}=n;Fe.Gt.read(()=>{try{r.dimensions="function"===typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),Fe.Gt.render(()=>{we(r,a,{enableHardwareAcceleration:!1},Se(t.tagName),e.transformTemplate),Ae(t,r)})}})},Ie={useVisualState:_e({scrapeMotionValuesFromProps:Te,createRenderState:le})};function Ue(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const He=e=>"mouse"===e.pointerType?"number"!==typeof e.button||e.button<=0:!1!==e.isPrimary;function We(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{point:{x:e[t+"X"],y:e[t+"Y"]}}}function $e(e,t,n,r){return Ue(e,t,(e=>t=>He(t)&&e(t,We(t)))(n),r)}const Ge=(e,t)=>n=>t(e(n)),qe=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(Ge)};function Qe(e){let t=null;return()=>{const n=()=>{t=null};return null===t&&(t=e,n)}}const Ke=Qe("dragHorizontal"),Xe=Qe("dragVertical");function Ye(e){let t=!1;if("y"===e)t=Xe();else if("x"===e)t=Ke();else{const e=Ke(),n=Xe();e&&n?t=()=>{e(),n()}:(e&&e(),n&&n())}return t}function Ze(){const e=Ye(!0);return!e||(e(),!1)}class Je{constructor(e){this.isMounted=!1,this.node=e}update(){}}function et(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End");return $e(e.current,n,(n,a)=>{if("touch"===n.pointerType||Ze())return;const i=e.getProps();e.animationState&&i.whileHover&&e.animationState.setActive("whileHover",t),i[r]&&Fe.Gt.update(()=>i[r](n,a))},{passive:!e.getProps()[r]})}const tt=(e,t)=>!!t&&(e===t||tt(e,t.parentElement));var nt=n(6318);function rt(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,We(n))}const at=["root"],it=new WeakMap,ot=new WeakMap,st=e=>{const t=it.get(e.target);t&&t(e)},lt=e=>{e.forEach(st)};function ut(e,t,n){const a=function(e){let{root:t}=e,n=(0,ge.A)(e,at);const a=t||document;ot.has(a)||ot.set(a,{});const i=ot.get(a),o=JSON.stringify(n);return i[o]||(i[o]=new IntersectionObserver(lt,(0,r.A)({root:t},n))),i[o]}(t);return it.set(e,n),a.observe(e),()=>{it.delete(e),a.unobserve(e)}}const ct={some:0,all:1};const dt={inView:{Feature:class extends Je{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:a}=e,i={root:t?t.current:void 0,rootMargin:n,threshold:"number"===typeof r?r:ct[r]};return ut(this.node.current,i,e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,a&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),i=t?n:r;i&&i(e)})}mount(){this.startObserver()}update(){if("undefined"===typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node,n=["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:n={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==n[e]}(e,t));n&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Je{constructor(){super(...arguments),this.removeStartListeners=nt.l,this.removeEndListeners=nt.l,this.removeAccessibleListeners=nt.l,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),r=$e(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:r,globalTapTarget:a}=this.node.getProps();Fe.Gt.update(()=>{a||tt(this.node.current,e.target)?n&&n(e,t):r&&r(e,t)})},{passive:!(n.onTap||n.onPointerUp)}),a=$e(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=qe(r,a),this.startPress(e,t)},this.startAccessiblePress=()=>{const e=Ue(this.node.current,"keydown",e=>{if("Enter"!==e.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=Ue(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&rt("up",(e,t)=>{const{onTap:n}=this.node.getProps();n&&Fe.Gt.update(()=>n(e,t))})}),rt("down",(e,t)=>{this.startPress(e,t)})}),t=Ue(this.node.current,"blur",()=>{this.isPressing&&rt("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=qe(e,t)}}startPress(e,t){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&Fe.Gt.update(()=>n(e,t))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Ze()}cancelPress(e,t){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&Fe.Gt.update(()=>n(e,t))}mount(){const e=this.node.getProps(),t=$e(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=Ue(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=qe(t,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends Je{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=qe(Ue(this.node.current,"focus",()=>this.onFocus()),Ue(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Je{mount(){this.unmount=qe(et(this.node,!0),et(this.node,!1))}unmount(){}}}};function ft(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function ht(e,t,n){const r=e.getProps();return Le(r,t,void 0!==n?n:r.custom,function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.get()),t}(e),function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.getVelocity()),t}(e))}var pt=n(4779);const mt=e=>1e3*e,gt=e=>e/1e3,vt=!1,yt=e=>Array.isArray(e)&&"number"===typeof e[0];function bt(e){return Boolean(!e||"string"===typeof e&&wt[e]||yt(e)||Array.isArray(e)&&e.every(bt))}const xt=e=>{let[t,n,r,a]=e;return"cubic-bezier(".concat(t,", ").concat(n,", ").concat(r,", ").concat(a,")")},wt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:xt([0,.65,.55,1]),circOut:xt([.55,0,1,.45]),backIn:xt([.31,.01,.66,-.59]),backOut:xt([.33,1.53,.69,.99])};function kt(e){if(e)return yt(e)?xt(e):Array.isArray(e)?e.map(kt):wt[e]}const St=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function Ct(e,t,n,r){if(e===t&&n===r)return nt.l;const a=t=>function(e,t,n,r,a){let i,o,s=0;do{o=t+(n-t)/2,i=St(o,r,a)-e,i>0?n=o:t=o}while(Math.abs(i)>1e-7&&++s<12);return o}(t,0,1,e,n);return e=>0===e||1===e?e:St(a(e),t,r)}const Et=Ct(.42,0,1,1),Pt=Ct(0,0,.58,1),jt=Ct(.42,0,.58,1),At=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Tt=e=>t=>1-e(1-t),Nt=e=>1-Math.sin(Math.acos(e)),Lt=Tt(Nt),Mt=At(Nt),Rt=Ct(.33,1.53,.69,.99),Ot=Tt(Rt),zt=At(Ot),Dt={linear:nt.l,easeIn:Et,easeInOut:jt,easeOut:Pt,circIn:Nt,circInOut:Mt,circOut:Lt,backIn:Ot,backInOut:zt,backOut:Rt,anticipate:e=>(e*=2)<1?.5*Ot(e):.5*(2-Math.pow(2,-10*(e-1)))},_t=e=>{if(Array.isArray(e)){(0,pt.V)(4===e.length,"Cubic bezier arrays must contain four numerical values.");const[t,n,r,a]=e;return Ct(t,n,r,a)}return"string"===typeof e?((0,pt.V)(void 0!==Dt[e],"Invalid easing type '".concat(e,"'")),Dt[e]):e},Vt=(e,t)=>n=>Boolean(Y(n)&&X.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Ft=(e,t,n)=>r=>{if(!Y(r))return r;const[a,i,o,s]=r.match(Q);return{[e]:parseFloat(a),[t]:parseFloat(i),[n]:parseFloat(o),alpha:void 0!==s?parseFloat(s):1}},Bt=(0,r.A)((0,r.A)({},W),{},{transform:e=>Math.round((e=>H(0,255,e))(e))}),It={test:Vt("rgb","red"),parse:Ft("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:a=1}=e;return"rgba("+Bt.transform(t)+", "+Bt.transform(n)+", "+Bt.transform(r)+", "+q($.transform(a))+")"}};const Ut={test:Vt("#"),parse:function(e){let t="",n="",r="",a="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),a=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),a=e.substring(4,5),t+=t,n+=n,r+=r,a+=a),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:a?parseInt(a,16)/255:1}},transform:It.transform},Ht={test:Vt("hsl","hue"),parse:Ft("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:a=1}=e;return"hsla("+Math.round(t)+", "+ee.transform(q(n))+", "+ee.transform(q(r))+", "+q($.transform(a))+")"}},Wt={test:e=>It.test(e)||Ut.test(e)||Ht.test(e),parse:e=>It.test(e)?It.parse(e):Ht.test(e)?Ht.parse(e):Ut.parse(e),transform:e=>Y(e)?e:e.hasOwnProperty("red")?It.transform(e):Ht.transform(e)},$t=(e,t,n)=>-n*e+n*t+e;function Gt(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}const qt=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Qt=[Ut,It,Ht];function Kt(e){const t=(n=e,Qt.find(e=>e.test(n)));var n;(0,pt.V)(Boolean(t),"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."));let r=t.parse(e);return t===Ht&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:a}=e;t/=360,n/=100,r/=100;let i=0,o=0,s=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,a=2*r-e;i=Gt(a,e,t+1/3),o=Gt(a,e,t),s=Gt(a,e,t-1/3)}else i=o=s=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:a}}(r)),r}const Xt=(e,t)=>{const n=Kt(e),a=Kt(t),i=(0,r.A)({},n);return e=>(i.red=qt(n.red,a.red,e),i.green=qt(n.green,a.green,e),i.blue=qt(n.blue,a.blue,e),i.alpha=$t(n.alpha,a.alpha,e),It.transform(i))};const Yt={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:nt.l},Zt={regex:K,countKey:"Colors",token:"${c}",parse:Wt.parse},Jt={regex:Q,countKey:"Numbers",token:"${n}",parse:W.parse};function en(e,t){let{regex:n,countKey:r,token:a,parse:i}=t;const o=e.tokenised.match(n);o&&(e["num"+r]=o.length,e.tokenised=e.tokenised.replace(n,a),e.values.push(...o.map(i)))}function tn(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&en(n,Yt),en(n,Zt),en(n,Jt),n}function nn(e){return tn(e).values}function rn(e){const{values:t,numColors:n,numVars:r,tokenised:a}=tn(e),i=t.length;return e=>{let t=a;for(let a=0;a<i;a++)t=a<r?t.replace(Yt.token,e[a]):a<r+n?t.replace(Zt.token,Wt.transform(e[a])):t.replace(Jt.token,q(e[a]));return t}}const an=e=>"number"===typeof e?0:e;const on={test:function(e){var t,n;return isNaN(e)&&Y(e)&&((null===(t=e.match(Q))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(K))||void 0===n?void 0:n.length)||0)>0},parse:nn,createTransformer:rn,getAnimatableNone:function(e){const t=nn(e);return rn(e)(t.map(an))}},sn=(e,t)=>n=>"".concat(n>0?t:e);function ln(e,t){return"number"===typeof e?n=>$t(e,t,n):Wt.test(e)?Xt(e,t):e.startsWith("var(")?sn(e,t):dn(e,t)}const un=(e,t)=>{const n=[...e],r=n.length,a=e.map((e,n)=>ln(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=a[t](e);return n}},cn=(e,t)=>{const n=(0,r.A)((0,r.A)({},e),t),a={};for(const r in n)void 0!==e[r]&&void 0!==t[r]&&(a[r]=ln(e[r],t[r]));return e=>{for(const t in a)n[t]=a[t](e);return n}},dn=(e,t)=>{const n=on.createTransformer(t),r=tn(e),a=tn(t);return r.numVars===a.numVars&&r.numColors===a.numColors&&r.numNumbers>=a.numNumbers?qe(un(r.values,a.values),n):((0,pt.$)(!0,"Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.")),sn(e,t))},fn=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r},hn=(e,t)=>n=>$t(e,t,n);function pn(e,t,n){const r=[],a=n||("number"===typeof(i=e[0])?hn:"string"===typeof i?Wt.test(i)?Xt:dn:Array.isArray(i)?un:"object"===typeof i?cn:hn);var i;const o=e.length-1;for(let s=0;s<o;s++){let n=a(e[s],e[s+1]);if(t){const e=Array.isArray(t)?t[s]||nt.l:t;n=qe(e,n)}r.push(n)}return r}function mn(e,t){let{clamp:n=!0,ease:r,mixer:a}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=e.length;if((0,pt.V)(i===t.length,"Both input and output ranges must be the same length"),1===i)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=pn(t,r,a),s=o.length,l=t=>{let n=0;if(s>1)for(;n<e.length-2&&!(t<e[n+1]);n++);const r=fn(e[n],e[n+1],t);return o[n](r)};return n?t=>l(H(e[0],e[i-1],t)):l}function gn(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const a=fn(0,t,r);e.push($t(n,1,a))}}(t,e.length-1),t}function vn(e){let{duration:t=300,keyframes:n,times:r,ease:a="easeInOut"}=e;const i=(e=>Array.isArray(e)&&"number"!==typeof e[0])(a)?a.map(_t):_t(a),o={done:!1,value:n[0]},s=function(e,t){return e.map(e=>e*t)}(r&&r.length===n.length?r:gn(n),t),l=mn(s,n,{ease:Array.isArray(i)?i:(u=n,c=i,u.map(()=>c||jt).splice(0,u.length-1))});var u,c;return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}function yn(e,t){return t?e*(1e3/t):0}function bn(e,t,n){const r=Math.max(t-5,0);return yn(n-e(r),t-r)}const xn=.001;function wn(e){let t,n,{duration:r=800,bounce:a=.25,velocity:i=0,mass:o=1}=e;(0,pt.$)(r<=mt(10),"Spring duration must be 10 seconds or less");let s=1-a;s=H(.05,1,s),r=H(.01,10,gt(r)),s<1?(t=e=>{const t=e*s,n=t*r,a=t-i,o=Sn(e,s),l=Math.exp(-n);return xn-a/o*l},n=e=>{const n=e*s*r,a=n*i+i,o=Math.pow(s,2)*Math.pow(e,2)*r,l=Math.exp(-n),u=Sn(Math.pow(e,2),s);return(-t(e)+xn>0?-1:1)*((a-o)*l)/u}):(t=e=>Math.exp(-e*r)*((e-i)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(i-e)));const l=function(e,t,n){let r=n;for(let a=1;a<kn;a++)r-=e(r)/t(r);return r}(t,n,5/r);if(r=mt(r),isNaN(l))return{stiffness:100,damping:10,duration:r};{const e=Math.pow(l,2)*o;return{stiffness:e,damping:2*s*Math.sqrt(o*e),duration:r}}}const kn=12;function Sn(e,t){return e*Math.sqrt(1-t*t)}const Cn=["keyframes","restDelta","restSpeed"],En=["duration","bounce"],Pn=["stiffness","damping","mass"];function jn(e,t){return t.some(t=>void 0!==e[t])}function An(e){let{keyframes:t,restDelta:n,restSpeed:a}=e,i=(0,ge.A)(e,Cn);const o=t[0],s=t[t.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:d,duration:f,velocity:h,isResolvedFromDuration:p}=function(e){let t=(0,r.A)({velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1},e);if(!jn(e,Pn)&&jn(e,En)){const n=wn(e);t=(0,r.A)((0,r.A)((0,r.A)({},t),n),{},{mass:1}),t.isResolvedFromDuration=!0}return t}((0,r.A)((0,r.A)({},i),{},{velocity:-gt(i.velocity||0)})),m=h||0,g=c/(2*Math.sqrt(u*d)),v=s-o,y=gt(Math.sqrt(u/d)),b=Math.abs(v)<5;let x;if(a||(a=b?.01:2),n||(n=b?.005:.5),g<1){const e=Sn(y,g);x=t=>{const n=Math.exp(-g*y*t);return s-n*((m+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}}else if(1===g)x=e=>s-Math.exp(-y*e)*(v+(m+y*v)*e);else{const e=y*Math.sqrt(g*g-1);x=t=>{const n=Math.exp(-g*y*t),r=Math.min(e*t,300);return s-n*((m+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}return{calculatedDuration:p&&f||null,next:e=>{const t=x(e);if(p)l.done=e>=f;else{let r=m;0!==e&&(r=g<1?bn(x,e,t):0);const i=Math.abs(r)<=a,o=Math.abs(s-t)<=n;l.done=i&&o}return l.value=l.done?s:t,l}}}function Tn(e){let{keyframes:t,velocity:n=0,power:r=.8,timeConstant:a=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:u,restDelta:c=.5,restSpeed:d}=e;const f=t[0],h={done:!1,value:f},p=e=>void 0===l?u:void 0===u||Math.abs(l-e)<Math.abs(u-e)?l:u;let m=r*n;const g=f+m,v=void 0===s?g:s(g);v!==g&&(m=v-f);const y=e=>-m*Math.exp(-e/a),b=e=>v+y(e),x=e=>{const t=y(e),n=b(e);h.done=Math.abs(t)<=c,h.value=h.done?v:n};let w,k;const S=e=>{var t;(t=h.value,void 0!==l&&t<l||void 0!==u&&t>u)&&(w=e,k=An({keyframes:[h.value,p(h.value)],velocity:bn(b,e,h.value),damping:i,stiffness:o,restDelta:c,restSpeed:d}))};return S(0),{calculatedDuration:null,next:e=>{let t=!1;return k||void 0!==w||(t=!0,x(e),S(e)),void 0!==w&&e>w?k.next(e-w):(!t&&x(e),h)}}}const Nn=e=>{const t=t=>{let{timestamp:n}=t;return e(n)};return{start:()=>Fe.Gt.update(t,!0),stop:()=>(0,Fe.WG)(t),now:()=>Fe.uv.isProcessing?Fe.uv.timestamp:performance.now()}};function Ln(e){let t=0;let n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}const Mn=["autoplay","delay","driver","keyframes","type","repeat","repeatDelay","repeatType","onPlay","onStop","onComplete","onUpdate"],Rn={decay:Tn,inertia:Tn,tween:vn,keyframes:vn,spring:An};function On(e){let t,n,{autoplay:a=!0,delay:i=0,driver:o=Nn,keyframes:s,type:l="keyframes",repeat:u=0,repeatDelay:c=0,repeatType:d="loop",onPlay:f,onStop:h,onComplete:p,onUpdate:m}=e,g=(0,ge.A)(e,Mn),v=1,y=!1;const b=()=>{n=new Promise(e=>{t=e})};let x;b();const w=Rn[l]||vn;let k;w!==vn&&"number"!==typeof s[0]&&(k=mn([0,100],s,{clamp:!1}),s=[0,100]);const S=w((0,r.A)((0,r.A)({},g),{},{keyframes:s}));let C;"mirror"===d&&(C=w((0,r.A)((0,r.A)({},g),{},{keyframes:[...s].reverse(),velocity:-(g.velocity||0)})));let E="idle",P=null,j=null,A=null;null===S.calculatedDuration&&u&&(S.calculatedDuration=Ln(S));const{calculatedDuration:T}=S;let N=1/0,L=1/0;null!==T&&(N=T+c,L=N*(u+1)-c);let M=0;const R=e=>{if(null===j)return;v>0&&(j=Math.min(j,e)),v<0&&(j=Math.min(e-L/v,j)),M=null!==P?P:Math.round(e-j)*v;const t=M-i*(v>=0?1:-1),n=v>=0?t<0:t>L;M=Math.max(t,0),"finished"===E&&null===P&&(M=L);let r=M,a=S;if(u){const e=Math.min(M,L)/N;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,u+1);Boolean(t%2)&&("reverse"===d?(n=1-n,c&&(n-=c/N)):"mirror"===d&&(a=C)),r=H(0,1,n)*N}const o=n?{done:!1,value:s[0]}:a.next(r);k&&(o.value=k(o.value));let{done:l}=o;n||null===T||(l=v>=0?M>=L:M<=0);const f=null===P&&("finished"===E||"running"===E&&l);return m&&m(o.value),f&&D(),o},O=()=>{x&&x.stop(),x=void 0},z=()=>{E="idle",O(),t(),b(),j=A=null},D=()=>{E="finished",p&&p(),O(),t()},_=()=>{if(y)return;x||(x=o(R));const e=x.now();f&&f(),null!==P?j=e-P:j&&"finished"!==E||(j=e),"finished"===E&&b(),A=j,P=null,E="running",x.start()};a&&_();const V={then:(e,t)=>n.then(e,t),get time(){return gt(M)},set time(e){e=mt(e),M=e,null===P&&x&&0!==v?j=x.now()-e/v:P=e},get duration(){const e=null===S.calculatedDuration?Ln(S):S.calculatedDuration;return gt(e)},get speed(){return v},set speed(e){e!==v&&x&&(v=e,V.time=gt(M))},get state(){return E},play:_,pause:()=>{E="paused",P=M},stop:()=>{y=!0,"idle"!==E&&(E="idle",h&&h(),z())},cancel:()=>{null!==A&&R(A),z()},complete:()=>{E="finished"},sample:e=>(j=0,R(e))};return V}const zn=["onUpdate","onComplete"],Dn=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),_n=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function Vn(e,t,n){let{onUpdate:a,onComplete:i}=n,o=(0,ge.A)(n,zn);if(!(Dn()&&_n.has(t)&&!o.repeatDelay&&"mirror"!==o.repeatType&&0!==o.damping&&"inertia"!==o.type))return!1;let s,l,u=!1,c=!1;const d=()=>{l=new Promise(e=>{s=e})};d();let{keyframes:f,duration:h=300,ease:p,times:m}=o;if(((e,t)=>"spring"===t.type||"backgroundColor"===e||!bt(t.ease))(t,o)){const e=On((0,r.A)((0,r.A)({},o),{},{repeat:0,delay:0}));let t={done:!1,value:f[0]};const n=[];let a=0;for(;!t.done&&a<2e4;)t=e.sample(a),n.push(t.value),a+=10;m=void 0,f=n,h=a-10,p="linear"}const g=function(e,t,n){let{delay:r=0,duration:a,repeat:i=0,repeatType:o="loop",ease:s,times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const u={[t]:n};l&&(u.offset=l);const c=kt(s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:a,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:"reverse"===o?"alternate":"normal"})}(e.owner.current,t,f,(0,r.A)((0,r.A)({},o),{},{duration:h,ease:p,times:m})),v=()=>{c=!1,g.cancel()},y=()=>{c=!0,Fe.Gt.update(v),s(),d()};g.onfinish=()=>{c||(e.set(function(e,t){let{repeat:n,repeatType:r="loop"}=t;return e[n&&"loop"!==r&&n%2===1?0:e.length-1]}(f,o)),i&&i(),y())};return{then:(e,t)=>l.then(e,t),attachTimeline:e=>(g.timeline=e,g.onfinish=null,nt.l),get time(){return gt(g.currentTime||0)},set time(e){g.currentTime=mt(e)},get speed(){return g.playbackRate},set speed(e){g.playbackRate=e},get duration(){return gt(h)},play:()=>{u||(g.play(),(0,Fe.WG)(v))},pause:()=>g.pause(),stop:()=>{if(u=!0,"idle"===g.playState)return;const{currentTime:t}=g;if(t){const n=On((0,r.A)((0,r.A)({},o),{},{autoplay:!1}));e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}y()},complete:()=>{c||g.finish()},cancel:y}}const Fn={type:"spring",stiffness:500,damping:25,restSpeed:10},Bn={type:"keyframes",duration:.8},In={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Un=(e,t)=>{let{keyframes:n}=t;return n.length>2?Bn:O.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:Fn:In},Hn=(e,t)=>"zIndex"!==e&&(!("number"!==typeof t&&!Array.isArray(t))||!("string"!==typeof t||!on.test(t)&&"0"!==t||t.startsWith("url("))),Wn=new Set(["brightness","contrast","saturate","opacity"]);function $n(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(Q)||[];if(!r)return e;const a=n.replace(r,"");let i=Wn.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+a+")"}const Gn=/([a-z-]*)\(.*?\)/g,qn=(0,r.A)((0,r.A)({},on),{},{getAnimatableNone:e=>{const t=e.match(Gn);return t?t.map($n).join(" "):e}}),Qn=(0,r.A)((0,r.A)({},oe),{},{color:Wt,backgroundColor:Wt,outlineColor:Wt,fill:Wt,stroke:Wt,borderColor:Wt,borderTopColor:Wt,borderRightColor:Wt,borderBottomColor:Wt,borderLeftColor:Wt,filter:qn,WebkitFilter:qn}),Kn=e=>Qn[e];function Xn(e,t){let n=Kn(e);return n!==qn&&(n=on),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Yn=e=>/^0[^.\s]+$/.test(e);function Zn(e){return"number"===typeof e?0===e:null!==e?"none"===e||"0"===e||Yn(e):void 0}const Jn=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"];function er(e,t){return e[t]||e.default||e}const tr=!1,nr=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return i=>{const o=er(a,e)||{},s=o.delay||a.delay||0;let{elapsed:l=0}=a;l-=mt(s);const u=function(e,t,n,r){const a=Hn(t,n);let i;i=Array.isArray(n)?[...n]:[null,n];const o=void 0!==r.from?r.from:e.get();let s;const l=[];for(let u=0;u<i.length;u++)null===i[u]&&(i[u]=0===u?o:i[u-1]),Zn(i[u])&&l.push(u),"string"===typeof i[u]&&"none"!==i[u]&&"0"!==i[u]&&(s=i[u]);if(a&&l.length&&s)for(let u=0;u<l.length;u++)i[l[u]]=Xn(t,s);return i}(t,e,n,o),c=u[0],d=u[u.length-1],f=Hn(e,c),h=Hn(e,d);(0,pt.$)(f===h,"You are trying to animate ".concat(e,' from "').concat(c,'" to "').concat(d,'". ').concat(c," is not an animatable value - to enable this animation set ").concat(c," to a value animatable to ").concat(d," via the `style` property."));let p=(0,r.A)((0,r.A)({keyframes:u,velocity:t.getVelocity(),ease:"easeOut"},o),{},{delay:-l,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}});if(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:a,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:u,elapsed:c}=e,d=(0,ge.A)(e,Jn);return!!Object.keys(d).length}(o)||(p=(0,r.A)((0,r.A)({},p),Un(e,p))),p.duration&&(p.duration=mt(p.duration)),p.repeatDelay&&(p.repeatDelay=mt(p.repeatDelay)),!f||!h||vt||!1===o.type||tr)return function(e){let{keyframes:t,delay:n,onUpdate:r,onComplete:a}=e;const i=()=>(r&&r(t[t.length-1]),a&&a(),{time:0,speed:1,duration:0,play:nt.l,pause:nt.l,stop:nt.l,then:e=>(e(),Promise.resolve()),cancel:nt.l,complete:nt.l});return n?On({keyframes:[0,1],duration:0,delay:n,onComplete:i}):i()}(vt?(0,r.A)((0,r.A)({},p),{},{delay:0}):p);if(!a.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const n=Vn(t,e,p);if(n)return n}return On(p)}};function rr(e){return Boolean(D(e)&&e.add)}const ar=e=>/^\-?\d*\.?\d+$/.test(e);function ir(e,t){-1===e.indexOf(t)&&e.push(t)}function or(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class sr{constructor(){this.subscriptions=[]}add(e){return ir(this.subscriptions,e),()=>or(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let a=0;a<r;a++){const r=this.subscriptions[a];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const lr={current:void 0};class ur{constructor(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var r;this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t.prev=t.current,t.current=e;const{delta:r,timestamp:a}=Fe.uv;t.lastUpdated!==a&&(t.timeDelta=r,t.lastUpdated=a,Fe.Gt.postRender(t.scheduleVelocityCheck)),t.prev!==t.current&&t.events.change&&t.events.change.notify(t.current),t.events.velocityChange&&t.events.velocityChange.notify(t.getVelocity()),n&&t.events.renderRequest&&t.events.renderRequest.notify(t.current)},this.scheduleVelocityCheck=()=>Fe.Gt.postRender(this.velocityCheck),this.velocityCheck=e=>{let{timestamp:t}=e;t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=(r=this.current,!isNaN(parseFloat(r))),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new sr);const n=this.events[e].add(t);return"change"===e?()=>{n(),Fe.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=e,this.timeDelta=n}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return lr.current&&lr.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?yn(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function cr(e,t){return new ur(e,t)}const dr=e=>t=>t.test(e),fr=[W,te,ee,J,re,ne,{test:e=>"auto"===e,parse:e=>e}],hr=e=>fr.find(dr(e)),pr=[...fr,Wt,on],mr=e=>pr.find(dr(e)),gr=["transitionEnd","transition"];function vr(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,cr(n))}function yr(e,t){const n=ht(e,t);let a=n?e.makeTargetAnimatable(n,!1):{},{transitionEnd:i={},transition:o={}}=a,s=(0,ge.A)(a,gr);s=(0,r.A)((0,r.A)({},s),i);for(const r in s){vr(e,r,Oe(s[r]))}}function br(e,t){if(!t)return;return(t[e]||t.default||t).from}const xr=["transition","transitionEnd"];function wr(e,t){let{protectedKeys:n,needsAnimating:r}=e;const a=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,a}function kr(e,t){const n=e.get();if(!Array.isArray(t))return n!==t;for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}function Sr(e,t){let{delay:n=0,transitionOverride:a,type:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=e.makeTargetAnimatable(t),{transition:s=e.getDefaultTransition(),transitionEnd:l}=o,u=(0,ge.A)(o,xr);const c=e.getValue("willChange");a&&(s=a);const f=[],h=i&&e.animationState&&e.animationState.getState()[i];for(const p in u){const t=e.getValue(p),a=u[p];if(!t||void 0===a||h&&wr(h,p))continue;const i=(0,r.A)({delay:n,elapsed:0},er(s||{},p));if(window.HandoffAppearAnimations){const n=e.getProps()[d];if(n){const e=window.HandoffAppearAnimations(n,p,t,Fe.Gt);null!==e&&(i.elapsed=e,i.isHandoff=!0)}}let o=!i.isHandoff&&!kr(t,a);if("spring"===i.type&&(t.getVelocity()||i.velocity)&&(o=!1),t.animation&&(o=!1),o)continue;t.start(nr(p,t,a,e.shouldReduceMotion&&O.has(p)?{type:!1}:i));const l=t.animation;rr(c)&&(c.add(p),l.then(()=>c.remove(p))),f.push(l)}return l&&Promise.all(f).then(()=>{l&&yr(e,l)}),f}function Cr(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a=ht(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=a||{};n.transitionOverride&&(i=n.transitionOverride);const o=a?()=>Promise.all(Sr(e,a,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:o=0,staggerChildren:s,staggerDirection:l}=i;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=arguments.length>5?arguments[5]:void 0;const s=[],l=(e.variantChildren.size-1)*a,u=1===i?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*a}:function(){return l-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*a};return Array.from(e.variantChildren).sort(Er).forEach((e,a)=>{e.notify("AnimationStart",t),s.push(Cr(e,t,(0,r.A)((0,r.A)({},o),{},{delay:n+u(a)})).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,o+a,s,l,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}return Promise.all([o(),s(n.delay)])}function Er(e,t){return e.sortNodePosition(t)}const Pr=["transition","transitionEnd"],jr=[...m].reverse(),Ar=m.length;function Tr(e){return t=>Promise.all(t.map(t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const a=t.map(t=>Cr(e,t,r));n=Promise.all(a)}else if("string"===typeof t)n=Cr(e,t,r);else{const a="function"===typeof t?ht(e,t,r.custom):t;n=Promise.all(Sr(e,a,r))}return n.then(()=>e.notify("AnimationComplete",t))}(e,n,r)}))}function Nr(e){let t=Tr(e);const n={animate:Mr(!0),whileInView:Mr(),whileHover:Mr(),whileTap:Mr(),whileDrag:Mr(),whileFocus:Mr(),exit:Mr()};let a=!0;const i=(t,n)=>{const a=ht(e,n);if(a){const{transition:e,transitionEnd:n}=a,i=(0,ge.A)(a,Pr);t=(0,r.A)((0,r.A)((0,r.A)({},t),i),n)}return t};function o(o,s){const l=e.getProps(),u=e.getVariantContext(!0)||{},c=[],d=new Set;let f={},m=1/0;for(let t=0;t<Ar;t++){const g=jr[t],v=n[g],y=void 0!==l[g]?l[g]:u[g],b=h(y),x=g===s?v.isActive:null;!1===x&&(m=t);let w=y===u[g]&&y!==l[g]&&b;if(w&&a&&e.manuallyAnimateOnMount&&(w=!1),v.protectedKeys=(0,r.A)({},f),!v.isActive&&null===x||!y&&!v.prevProp||p(y)||"boolean"===typeof y)continue;let k=Lr(v.prevProp,y)||g===s&&v.isActive&&!w&&b||t>m&&b,S=!1;const C=Array.isArray(y)?y:[y];let E=C.reduce(i,{});!1===x&&(E={});const{prevResolvedValues:P={}}=v,j=(0,r.A)((0,r.A)({},P),E),A=e=>{k=!0,d.has(e)&&(S=!0,d.delete(e)),v.needsAnimating[e]=!0};for(const e in j){const t=E[e],n=P[e];if(f.hasOwnProperty(e))continue;let r=!1;r=Re(t)&&Re(n)?!ft(t,n):t!==n,r?void 0!==t?A(e):d.add(e):void 0!==t&&d.has(e)?A(e):v.protectedKeys[e]=!0}v.prevProp=y,v.prevResolvedValues=E,v.isActive&&(f=(0,r.A)((0,r.A)({},f),E)),a&&e.blockInitialAnimation&&(k=!1),!k||w&&!S||c.push(...C.map(e=>({animation:e,options:(0,r.A)({type:g},o)})))}if(d.size){const t={};d.forEach(n=>{const r=e.getBaseTarget(n);void 0!==r&&(t[n]=r)}),c.push({animation:t})}let g=Boolean(c.length);return!a||!1!==l.initial&&l.initial!==l.animate||e.manuallyAnimateOnMount||(g=!1),a=!1,g?t(c):Promise.resolve()}return{animateChanges:o,setActive:function(t,r,a){var i;if(n[t].isActive===r)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)}),n[t].isActive=r;const s=o(a,t);for(const e in n)n[e].protectedKeys={};return s},setAnimateFunction:function(n){t=n(e)},getState:()=>n}}function Lr(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!ft(t,e)}function Mr(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let Rr=0;const Or={animation:{Feature:class extends Je{constructor(e){super(e),e.animationState||(e.animationState=Nr(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),p(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends Je{constructor(){super(...arguments),this.id=Rr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t,custom:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const a=this.node.animationState.setActive("exit",!e,{custom:null!==n&&void 0!==n?n:this.node.getProps().custom});t&&!e&&a.then(()=>t(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}}},zr=(e,t)=>Math.abs(e-t);class Dr{constructor(e,t){let{transformPagePoint:n,contextWindow:a,dragSnapToOrigin:i=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=Fr(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=zr(e.x,t.x),r=zr(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:a}=e,{timestamp:i}=Fe.uv;this.history.push((0,r.A)((0,r.A)({},a),{},{timestamp:i}));const{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=_r(t,this.transformPagePoint),Fe.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r,resumeAnimation:a}=this.handlers;if(this.dragSnapToOrigin&&a&&a(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const i=Fr("pointercancel"===e.type?this.lastMoveEventInfo:_r(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,i),r&&r(e,i)},!He(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=a||window;const o=_r(We(e),this.transformPagePoint),{point:s}=o,{timestamp:l}=Fe.uv;this.history=[(0,r.A)((0,r.A)({},s),{},{timestamp:l})];const{onSessionStart:u}=t;u&&u(e,Fr(o,this.history)),this.removeListeners=qe($e(this.contextWindow,"pointermove",this.handlePointerMove),$e(this.contextWindow,"pointerup",this.handlePointerUp),$e(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,Fe.WG)(this.updatePoint)}}function _r(e,t){return t?{point:t(e.point)}:e}function Vr(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Fr(e,t){let{point:n}=e;return{point:n,delta:Vr(n,Ir(t)),offset:Vr(n,Br(t)),velocity:Ur(t,.1)}}function Br(e){return e[0]}function Ir(e){return e[e.length-1]}function Ur(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const a=Ir(e);for(;n>=0&&(r=e[n],!(a.timestamp-r.timestamp>mt(t)));)n--;if(!r)return{x:0,y:0};const i=gt(a.timestamp-r.timestamp);if(0===i)return{x:0,y:0};const o={x:(a.x-r.x)/i,y:(a.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Hr(e){return e.max-e.min}function Wr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01;return Math.abs(e-t)<=n}function $r(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=$t(t.min,t.max,e.origin),e.scale=Hr(n)/Hr(t),(Wr(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=$t(n.min,n.max,e.origin)-e.originPoint,(Wr(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Gr(e,t,n,r){$r(e.x,t.x,n.x,r?r.originX:void 0),$r(e.y,t.y,n.y,r?r.originY:void 0)}function qr(e,t,n){e.min=n.min+t.min,e.max=e.min+Hr(t)}function Qr(e,t,n){e.min=t.min-n.min,e.max=e.min+Hr(t)}function Kr(e,t,n){Qr(e.x,t.x,n.x),Qr(e.y,t.y,n.y)}function Xr(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function Yr(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const Zr=.35;function Jr(e,t,n){return{min:ea(e,t),max:ea(e,n)}}function ea(e,t){return"number"===typeof e?e:e[t]||0}const ta=()=>({x:{min:0,max:0},y:{min:0,max:0}});function na(e){return[e("x"),e("y")]}function ra(e){let{top:t,left:n,right:r,bottom:a}=e;return{x:{min:n,max:r},y:{min:t,max:a}}}function aa(e){return void 0===e||1===e}function ia(e){let{scale:t,scaleX:n,scaleY:r}=e;return!aa(t)||!aa(n)||!aa(r)}function oa(e){return ia(e)||sa(e)||e.z||e.rotate||e.rotateX||e.rotateY}function sa(e){return la(e.x)||la(e.y)}function la(e){return e&&"0%"!==e}function ua(e,t,n){return n+t*(e-n)}function ca(e,t,n,r,a){return void 0!==a&&(e=ua(e,a,r)),ua(e,n,r)+t}function da(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0;e.min=ca(e.min,t,n,r,a),e.max=ca(e.max,t,n,r,a)}function fa(e,t){let{x:n,y:r}=t;da(e.x,n.translate,n.scale,n.originPoint),da(e.y,r.translate,r.scale,r.originPoint)}function ha(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function pa(e,t){e.min=e.min+t,e.max=e.max+t}function ma(e,t,n){let[r,a,i]=n;const o=void 0!==t[i]?t[i]:.5,s=$t(e.min,e.max,o);da(e,t[r],t[a],s,t.scale)}const ga=["x","scaleX","originX"],va=["y","scaleY","originY"];function ya(e,t){ma(e.x,t,ga),ma(e.y,t,va)}function ba(e,t){return ra(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const xa=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},wa=new WeakMap;class ka{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e){let{snapToCursor:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:r}=this.getProps();this.panSession=new Dr(e,{onSessionStart:e=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(We(e,"page").point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:a}=this.getProps();if(n&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Ye(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),na(e=>{let t=this.getAxisMotionValue(e).get()||0;if(ee.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=Hr(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t}),a&&Fe.Gt.update(()=>a(e,t),!1,!0);const{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:a,onDrag:i}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:o}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(o),void(null!==this.currentDirection&&a&&a(this.currentDirection));this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),i&&i(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>na(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:xa(this.visualElement)})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&Fe.Gt.update(()=>a(e,t))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!Sa(e,r,this.currentDirection))return;const a=this.getAxisMotionValue(e);let i=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(i=function(e,t,n){let{min:r,max:a}=t;return void 0!==r&&e<r?e=n?$t(r,e,n.min):Math.max(e,r):void 0!==a&&e>a&&(e=n?$t(a,e,n.max):Math.min(e,a)),e}(i,this.constraints[e],this.elastic[e])),a.set(i)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,a=this.constraints;t&&f(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!r)&&function(e,t){let{top:n,left:r,bottom:a,right:i}=t;return{x:Xr(e.x,r,i),y:Xr(e.y,n,a)}}(r.layoutBox,t),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Zr;return!1===e?e=0:!0===e&&(e=Zr),{x:Jr(e,"left","right"),y:Jr(e,"top","bottom")}}(n),a!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&na(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!f(e))return!1;const n=e.current;(0,pt.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");const{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const a=function(e,t,n){const r=ba(e,n),{scroll:a}=t;return a&&(pa(r.x,a.offset.x),pa(r.y,a.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let i=function(e,t){return{x:Yr(e.x,t.x),y:Yr(e.y,t.y)}}(r.layout.layoutBox,a);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(i));this.hasMutatedConstraints=!!e,e&&(i=ra(e))}return i}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:a,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),l=this.constraints||{},u=na(s=>{if(!Sa(s,t,this.currentDirection))return;let u=l&&l[s]||{};o&&(u={min:0,max:0});const c=a?200:1e6,d=a?40:1e7,f=(0,r.A)((0,r.A)({type:"inertia",velocity:n?e[s]:0,bounceStiffness:c,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10},i),u);return this.startAxisValueAnimation(s,f)});return Promise.all(u).then(s)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return n.start(nr(e,n,0,t))}stopAnimation(){na(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){na(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t="_drag"+e.toUpperCase(),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){na(t=>{const{drag:n}=this.getProps();if(!Sa(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,a=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:i}=r.layout.layoutBox[t];a.set(e[t]-$t(n,i,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!f(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};na(e=>{const t=this.getAxisMotionValue(e);if(t){const n=t.get();r[e]=function(e,t){let n=.5;const r=Hr(e),a=Hr(t);return a>r?n=fn(t.min,t.max-r,e.min):r>a&&(n=fn(e.min,e.max-a,t.min)),H(0,1,n)}({min:n,max:n},this.constraints[e])}});const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),na(t=>{if(!Sa(t,e,null))return;const n=this.getAxisMotionValue(t),{min:a,max:i}=this.constraints[t];n.set($t(a,i,r[t]))})}addListeners(){if(!this.visualElement.current)return;wa.set(this.visualElement,this);const e=$e(this.visualElement.current,"pointerdown",e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{const{dragConstraints:e}=this.getProps();f(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),t();const a=Ue(window,"resize",()=>this.scalePositionWithinConstraints()),i=n.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&(na(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{a(),e(),r(),i&&i()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:a=!1,dragConstraints:i=!1,dragElastic:o=Zr,dragMomentum:s=!0}=e;return(0,r.A)((0,r.A)({},e),{},{drag:t,dragDirectionLock:n,dragPropagation:a,dragConstraints:i,dragElastic:o,dragMomentum:s})}}function Sa(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const Ca=e=>(t,n)=>{e&&Fe.Gt.update(()=>e(t,n))};const Ea={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Pa(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ja={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!te.test(e))return e;e=parseFloat(e)}const n=Pa(e,t.target.x),r=Pa(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},Aa={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const a=e,i=on.parse(e);if(i.length>5)return a;const o=on.createTransformer(e),s="number"!==typeof i[0]?1:0,l=r.x.scale*n.x,u=r.y.scale*n.y;i[0+s]/=l,i[1+s]/=u;const c=$t(l,u,.5);return"number"===typeof i[2+s]&&(i[2+s]/=c),"number"===typeof i[3+s]&&(i[3+s]/=c),o(i)}};class Ta extends a.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:a}=this.props,{projection:i}=e;var o;o=La,Object.assign(M,o),i&&(t.group&&t.group.add(i),n&&n.register&&a&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions((0,r.A)((0,r.A)({},i.options),{},{onExitComplete:()=>this.safeToRemove()}))),Ea.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:a}=this.props,i=n.projection;return i?(i.isPresent=a,r||e.layoutDependency!==t||void 0===t?i.willUpdate():this.safeToRemove(),e.isPresent!==a&&(a?i.promote():i.relegate()||Fe.Gt.postRender(()=>{const e=i.getStack();e&&e.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Na(e){const[t,n]=function(){const e=(0,a.useContext)(s.t);if(null===e)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=(0,a.useId)();return(0,a.useEffect)(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}(),i=(0,a.useContext)(C.L);return a.createElement(Ta,(0,r.A)((0,r.A)({},e),{},{layoutGroup:i,switchLayoutGroup:(0,a.useContext)(E),isPresent:t,safeToRemove:n}))}const La={borderRadius:(0,r.A)((0,r.A)({},ja),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:ja,borderTopRightRadius:ja,borderBottomLeftRadius:ja,borderBottomRightRadius:ja,boxShadow:Aa},Ma=["TopLeft","TopRight","BottomLeft","BottomRight"],Ra=Ma.length,Oa=e=>"string"===typeof e?parseFloat(e):e,za=e=>"number"===typeof e||te.test(e);function Da(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const _a=Fa(0,.5,Lt),Va=Fa(.5,.95,nt.l);function Fa(e,t,n){return r=>r<e?0:r>t?1:n(fn(e,t,r))}function Ba(e,t){e.min=t.min,e.max=t.max}function Ia(e,t){Ba(e.x,t.x),Ba(e.y,t.y)}function Ua(e,t,n,r,a){return e=ua(e-=t,1/n,r),void 0!==a&&(e=ua(e,1/a,r)),e}function Ha(e,t,n,r,a){let[i,o,s]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,a=arguments.length>4?arguments[4]:void 0,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;ee.test(t)&&(t=parseFloat(t),t=$t(o.min,o.max,t/100)-o.min);if("number"!==typeof t)return;let s=$t(i.min,i.max,r);e===i&&(s-=t),e.min=Ua(e.min,t,n,s,a),e.max=Ua(e.max,t,n,s,a)}(e,t[i],t[o],t[s],t.scale,r,a)}const Wa=["x","scaleX","originX"],$a=["y","scaleY","originY"];function Ga(e,t,n,r){Ha(e.x,t,Wa,n?n.x:void 0,r?r.x:void 0),Ha(e.y,t,$a,n?n.y:void 0,r?r.y:void 0)}function qa(e){return 0===e.translate&&1===e.scale}function Qa(e){return qa(e.x)&&qa(e.y)}function Ka(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Xa(e){return Hr(e.x)/Hr(e.y)}class Ya{constructor(){this.members=[]}add(e){ir(this.members,e),e.scheduleRender()}remove(e){if(or(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex(t=>e===t);if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Za(e,t,n){let r="";const a=e.x.translate/t.x,i=e.y.translate/t.y;if((a||i)&&(r="translate3d(".concat(a,"px, ").concat(i,"px, 0) ")),1===t.x&&1===t.y||(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{rotate:e,rotateX:t,rotateY:a}=n;e&&(r+="rotate(".concat(e,"deg) ")),t&&(r+="rotateX(".concat(t,"deg) ")),a&&(r+="rotateY(".concat(a,"deg) "))}const o=e.x.scale*t.x,s=e.y.scale*t.y;return 1===o&&1===s||(r+="scale(".concat(o,", ").concat(s,")")),r||"none"}const Ja=(e,t)=>e.depth-t.depth;class ei{constructor(){this.children=[],this.isDirty=!1}add(e){ir(this.children,e),this.isDirty=!0}remove(e){or(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Ja),this.isDirty=!1,this.children.forEach(e)}}const ti=["","X","Y","Z"],ni={visibility:"hidden"};let ri=0;const ai={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function ii(e){let{attachResizeListener:t,defaultParent:n,measureScroll:a,checkIsScrollRoot:i,resetTransform:o}=e;return class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===n||void 0===n?void 0:n();this.id=ri++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{var e;this.projectionUpdateScheduled=!1,ai.totalNodes=ai.resolvedTargetDeltas=ai.recalculatedProjection=0,this.nodes.forEach(li),this.nodes.forEach(mi),this.nodes.forEach(gi),this.nodes.forEach(ui),e=ai,window.MotionDebug&&window.MotionDebug.record(e)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new ei)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new sr),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];t&&t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root.hasTreeAnimated;if(this.instance)return;var a;this.isSVG=(a=e)instanceof SVGElement&&"svg"!==a.tagName,this.instance=e;const{layoutId:i,layout:o,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||i)&&(this.isLayoutDirty=!0),t){let n;const r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){const n=performance.now(),r=a=>{let{timestamp:i}=a;const o=i-n;o>=t&&((0,Fe.WG)(r),e(o-t))};return Fe.Gt.read(r,!0),()=>(0,Fe.WG)(r)}(r,250),Ea.hasAnimatedSinceResize&&(Ea.hasAnimatedSinceResize=!1,this.nodes.forEach(pi))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||o)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n,hasRelativeTargetChanged:a,layout:i}=e;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||s.getDefaultTransition()||ki,{onLayoutAnimationStart:l,onLayoutAnimationComplete:u}=s.getProps(),c=!this.targetLayout||!Ka(this.targetLayout,i)||a,d=!n&&a;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||d||n&&(c||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,d);const e=(0,r.A)((0,r.A)({},er(o,"layout")),{},{onPlay:l,onComplete:u});(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else n||pi(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,Fe.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(vi),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){const e=this.path[a];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(di);this.isUpdating||this.nodes.forEach(fi),this.isUpdating=!1,this.nodes.forEach(hi),this.nodes.forEach(oi),this.nodes.forEach(si),this.clearAllSnapshots();const e=performance.now();Fe.uv.delta=H(0,1e3/60,e-Fe.uv.timestamp),Fe.uv.timestamp=e,Fe.uv.isProcessing=!0,Fe.Ci.update.process(Fe.uv),Fe.Ci.preRender.process(Fe.uv),Fe.Ci.render.process(Fe.uv),Fe.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(ci),this.sharedNodes.forEach(yi)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Fe.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Fe.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:i(this.instance),offset:a(this.instance)})}resetTransform(){if(!o)return;const e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!Qa(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&(t||oa(this.latestValues)||a)&&(o(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),Ei((r=n).x),Ei(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox(),{scroll:n}=this.root;return n&&(pa(t.x,n.offset.x),pa(t.y,n.offset.y)),t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};Ia(t,e);for(let n=0;n<this.path.length;n++){const r=this.path[n],{scroll:a,options:i}=r;if(r!==this.root&&a&&i.layoutScroll){if(a.isRoot){Ia(t,e);const{scroll:n}=this.root;n&&(pa(t.x,-n.offset.x),pa(t.y,-n.offset.y))}pa(t.x,a.offset.x),pa(t.y,a.offset.y)}}return t}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};Ia(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&ya(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),oa(e.latestValues)&&ya(n,e.latestValues)}return oa(this.latestValues)&&ya(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};Ia(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!oa(e.latestValues))continue;ia(e.latestValues)&&e.updateSnapshot();const r=ta();Ia(r,e.measurePageBox()),Ga(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return oa(this.latestValues)&&Ga(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options=(0,r.A)((0,r.A)((0,r.A)({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Fe.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];var t;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:a,layoutId:i}=this.options;if(this.layout&&(a||i)){if(this.resolvedRelativeTargetAt=Fe.uv.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Kr(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),Ia(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,s,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,l=this.relativeParent.target,qr(o.x,s.x,l.x),qr(o.y,s.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Ia(this.target,this.layout.layoutBox),fa(this.target,this.targetDelta)):Ia(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Kr(this.relativeTargetOrigin,this.target,e.target),Ia(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ai.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!ia(this.parent.latestValues)&&!sa(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Fe.uv.timestamp&&(r=!1),r)return;const{layout:a,layoutId:i}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!a&&!i)return;Ia(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const a=n.length;if(!a)return;let i,o;t.x=t.y=1;for(let s=0;s<a;s++){i=n[s],o=i.projectionDelta;const a=i.instance;a&&a.style&&"contents"===a.style.display||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ya(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,fa(e,o)),r&&oa(i.latestValues)&&ya(e,i.latestValues))}t.x=ha(t.x),t.y=ha(t.y)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox);const{target:l}=t;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;Gr(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=Za(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===o&&this.treeScale.y===s||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),ai.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.options.scheduleRender&&this.options.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.snapshot,a=n?n.latestValues:{},i=(0,r.A)({},this.latestValues),o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const s={x:{min:0,max:0},y:{min:0,max:0}},l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=Boolean(l&&!c&&!0===this.options.crossfade&&!this.path.some(wi));let f;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;var r,u,h,p,m,g;bi(o.x,e.x,n),bi(o.y,e.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Kr(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,m=s,g=n,xi(h.x,p.x,m.x,g),xi(h.y,p.y,m.y,g),f&&(r=this.relativeTarget,u=f,r.x.min===u.x.min&&r.x.max===u.x.max&&r.y.min===u.y.min&&r.y.max===u.y.max)&&(this.isProjectionDirty=!1),f||(f={x:{min:0,max:0},y:{min:0,max:0}}),Ia(f,this.relativeTarget)),l&&(this.animationValues=i,function(e,t,n,r,a,i){a?(e.opacity=$t(0,void 0!==n.opacity?n.opacity:1,_a(r)),e.opacityExit=$t(void 0!==t.opacity?t.opacity:1,0,Va(r))):i&&(e.opacity=$t(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let o=0;o<Ra;o++){const a="border".concat(Ma[o],"Radius");let i=Da(t,a),s=Da(n,a);void 0===i&&void 0===s||(i||(i=0),s||(s=0),0===i||0===s||za(i)===za(s)?(e[a]=Math.max($t(Oa(i),Oa(s),r),0),(ee.test(s)||ee.test(i))&&(e[a]+="%")):e[a]=s)}(t.rotate||n.rotate)&&(e.rotate=$t(t.rotate||0,n.rotate||0,r))}(i,a,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,Fe.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Fe.Gt.update(()=>{Ea.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){const r=D(e)?e:cr(e);return r.start(nr("",r,t,n)),r.animation}(0,1e3,(0,r.A)((0,r.A)({},e),{},{onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:a}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&Pi(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=Hr(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=Hr(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}Ia(t,n),ya(t,a),Gr(this.projectionDeltaWithTransform,this.layoutCorrected,t,a)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new Ya);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(t=!0),!t)return;const r={};for(let a=0;a<ti.length;a++){const t="rotate"+ti[a];n[t]&&(r[t]=n[t],e.setStaticValue(t,0))}e.render();for(const a in r)e.setStaticValue(a,r[a]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ni;const r={visibility:""},a=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=ze(null===e||void 0===e?void 0:e.pointerEvents)||"",r.transform=a?a(this.latestValues,""):"none",r;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ze(null===e||void 0===e?void 0:e.pointerEvents)||""),this.hasProjected&&!oa(this.latestValues)&&(t.transform=a?a({},""):"none",this.hasProjected=!1),t}const o=i.animationValues||i.latestValues;this.applyTransformsToTarget(),r.transform=Za(this.projectionDeltaWithTransform,this.treeScale,o),a&&(r.transform=a(o,r.transform));const{x:s,y:l}=this.projectionDelta;r.transformOrigin="".concat(100*s.origin,"% ").concat(100*l.origin,"% 0"),i.animationValues?r.opacity=i===this?null!==(n=null!==(t=o.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=i===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0;for(const u in M){if(void 0===o[u])continue;const{correct:e,applyTo:t}=M[u],n="none"===r.transform?o[u]:e(o[u],i);if(t){const e=t.length;for(let a=0;a<e;a++)r[t[a]]=n}else r[u]=n}return this.options.layoutId&&(r.pointerEvents=i===this?ze(null===e||void 0===e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(di),this.root.sharedNodes.clear()}}}function oi(e){e.updateLayout()}function si(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:a}=e.options,i=n.source!==e.layout.source;"size"===a?na(e=>{const r=i?n.measuredBox[e]:n.layoutBox[e],a=Hr(r);r.min=t[e].min,r.max=r.min+a}):Pi(a,n.layoutBox,t)&&na(r=>{const a=i?n.measuredBox[r]:n.layoutBox[r],o=Hr(t[r]);a.max=a.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Gr(o,t,n.layoutBox);const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};i?Gr(s,e.applyTransform(r,!0),n.measuredBox):Gr(s,t,n.layoutBox);const l=!Qa(o);let u=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:a,layout:i}=r;if(a&&i){const o={x:{min:0,max:0},y:{min:0,max:0}};Kr(o,n.layoutBox,a.layoutBox);const s={x:{min:0,max:0},y:{min:0,max:0}};Kr(s,t,i.layoutBox),Ka(o,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function li(e){ai.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function ui(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ci(e){e.clearSnapshot()}function di(e){e.clearMeasurements()}function fi(e){e.isLayoutDirty=!1}function hi(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function pi(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function mi(e){e.resolveTargetDelta()}function gi(e){e.calcProjection()}function vi(e){e.resetRotation()}function yi(e){e.removeLeadSnapshot()}function bi(e,t,n){e.translate=$t(t.translate,0,n),e.scale=$t(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function xi(e,t,n,r){e.min=$t(t.min,n.min,r),e.max=$t(t.max,n.max,r)}function wi(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const ki={duration:.45,ease:[.4,0,.1,1]},Si=e=>"undefined"!==typeof navigator&&navigator.userAgent.toLowerCase().includes(e),Ci=Si("applewebkit/")&&!Si("chrome/")?Math.round:nt.l;function Ei(e){e.min=Ci(e.min),e.max=Ci(e.max)}function Pi(e,t,n){return"position"===e||"preserve-aspect"===e&&!Wr(Xa(t),Xa(n),.2)}const ji=ii({attachResizeListener:(e,t)=>Ue(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ai={current:void 0},Ti=ii({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ai.current){const e=new ji({});e.mount(window),e.setOptions({layoutScroll:!0}),Ai.current=e}return Ai.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),Ni={pan:{Feature:class extends Je{constructor(){super(...arguments),this.removePointerDownListener=nt.l}onPointerDown(e){this.session=new Dr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:xa(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:Ca(e),onStart:Ca(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&Fe.Gt.update(()=>r(e,t))}}}mount(){this.removePointerDownListener=$e(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Je{constructor(e){super(e),this.removeGroupControls=nt.l,this.removeListeners=nt.l,this.controls=new ka(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||nt.l}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Ti,MeasureLayout:Na}};const Li=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Mi(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;(0,pt.V)(n<=4,'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.'));const[r,a]=function(e){const t=Li.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const e=i.trim();return ar(e)?parseFloat(e):e}return I(a)?Mi(a,t,n+1):a}function Ri(e,t,n){let a=Object.assign({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(t),t));const i=e.current;if(!(i instanceof Element))return{target:a,transitionEnd:n};n&&(n=(0,r.A)({},n)),e.values.forEach(e=>{const t=e.get();if(!I(t))return;const n=Mi(t,i);n&&e.set(n)});for(const r in a){const e=a[r];if(!I(e))continue;const t=Mi(e,i);t&&(a[r]=t,n||(n={}),void 0===n[r]&&(n[r]=e))}return{target:a,transitionEnd:n}}const Oi=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),zi=e=>Oi.has(e),Di=e=>e===W||e===te,_i=(e,t)=>parseFloat(e.split(", ")[t]),Vi=(e,t)=>(n,r)=>{let{transform:a}=r;if("none"===a||!a)return 0;const i=a.match(/^matrix3d\((.+)\)$/);if(i)return _i(i[1],t);{const t=a.match(/^matrix\((.+)\)$/);return t?_i(t[1],e):0}},Fi=new Set(["x","y","z"]),Bi=R.filter(e=>!Fi.has(e));const Ii={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:a="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(a)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:a="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(a)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:Vi(4,13),y:Vi(5,14)};Ii.translateX=Ii.x,Ii.translateY=Ii.y;const Ui=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t=(0,r.A)({},t),a=(0,r.A)({},a);const i=Object.keys(t).filter(zi);let o=[],s=!1;const l=[];if(i.forEach(r=>{const i=e.getValue(r);if(!e.hasValue(r))return;let u=n[r],c=hr(u);const d=t[r];let f;if(Re(d)){const e=d.length,t=null===d[0]?1:0;u=d[t],c=hr(u);for(let n=t;n<e&&null!==d[n];n++)f?(0,pt.V)(hr(d[n])===f,"All keyframes must be of the same type"):(f=hr(d[n]),(0,pt.V)(f===c||Di(c)&&Di(f),"Keyframes must be of the same dimension as the current value"))}else f=hr(d);if(c!==f)if(Di(c)&&Di(f)){const e=i.get();"string"===typeof e&&i.set(parseFloat(e)),"string"===typeof d?t[r]=parseFloat(d):Array.isArray(d)&&f===te&&(t[r]=d.map(parseFloat))}else(null===c||void 0===c?void 0:c.transform)&&(null===f||void 0===f?void 0:f.transform)&&(0===u||0===d)?0===u?i.set(f.transform(u)):t[r]=c.transform(d):(s||(o=function(e){const t=[];return Bi.forEach(n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),s=!0),l.push(r),a[r]=void 0!==a[r]?a[r]:t[r],i.jump(d))}),l.length){const n=l.indexOf("height")>=0?window.pageYOffset:null,r=((e,t,n)=>{const r=t.measureViewportBox(),a=t.current,i=getComputedStyle(a),{display:o}=i,s={};"none"===o&&t.setStaticValue("display",e.display||"block"),n.forEach(e=>{s[e]=Ii[e](r,i)}),t.render();const l=t.measureViewportBox();return n.forEach(n=>{const r=t.getValue(n);r&&r.jump(s[n]),e[n]=Ii[n](l,i)}),e})(t,e,l);return o.length&&o.forEach(t=>{let[n,r]=t;e.getValue(n).set(r)}),e.render(),S.B&&null!==n&&window.scrollTo({top:n}),{target:r,transitionEnd:a}}return{target:t,transitionEnd:a}};function Hi(e,t,n,r){return(e=>Object.keys(e).some(zi))(t)?Ui(e,t,n,r):{target:t,transitionEnd:r}}const Wi={current:null},$i={current:!1};const Gi=new WeakMap,qi=["willChange"],Qi=["children"],Ki=Object.keys(k),Xi=Ki.length,Yi=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Zi=g.length;class Ji{constructor(e){let{parent:t,props:n,presenceContext:a,reducedMotionConfig:i,visualState:o}=e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>Fe.Gt.render(this.render,!1,!0);const{latestValues:l,renderState:u}=o;this.latestValues=l,this.baseTarget=(0,r.A)({},l),this.initialValues=n.initial?(0,r.A)({},l):{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=a,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=v(n),this.isVariantNode=y(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const c=this.scrapeMotionValuesFromProps(n,{}),{willChange:d}=c,f=(0,ge.A)(c,qi);for(const r in f){const e=f[r];void 0!==l[r]&&D(e)&&(e.set(l[r],!1),rr(d)&&d.add(r))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,Gi.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),$i.current||function(){if($i.current=!0,S.B)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Wi.current=e.matches;e.addListener(t),t()}else Wi.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Wi.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Gi.delete(this.current),this.projection&&this.projection.unmount(),(0,Fe.WG)(this.notifyUpdate),(0,Fe.WG)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){const n=O.has(e),r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&Fe.Gt.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{r(),a()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures(e,t,n,r){let a,i,{children:o}=e,s=(0,ge.A)(e,Qi);for(let l=0;l<Xi;l++){const e=Ki[l],{isEnabled:t,Feature:n,ProjectionNode:r,MeasureLayout:o}=k[e];r&&(a=r),t(s)&&(!this.features[e]&&n&&(this.features[e]=new n(this)),o&&(i=o))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&a){this.projection=new a(this.latestValues,this.parent&&this.parent.projection);const{layoutId:e,layout:t,drag:n,dragConstraints:i,layoutScroll:o,layoutRoot:l}=s;this.projection.setOptions({layoutId:e,layout:t,alwaysMeasureLayout:Boolean(n)||i&&f(i),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"===typeof t?t:"both",initialPromotionConfig:r,layoutScroll:o,layoutRoot:l})}return i}updateFeatures(){for(const e in this.features){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<Yi.length;n++){const t=Yi[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){const{willChange:r}=t;for(const a in t){const i=t[a],o=n[a];if(D(i))e.addValue(a,i),rr(r)&&r.add(a);else if(D(o))e.addValue(a,cr(i,{owner:e})),rr(r)&&r.remove(a);else if(o!==i)if(e.hasValue(a)){const t=e.getValue(a);!t.hasAnimated&&t.set(i)}else{const t=e.getStaticValue(a);e.addValue(a,cr(void 0!==t?t:i,{owner:e}))}}for(const a in n)void 0===t[a]&&e.removeValue(a);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0])return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}const e={};for(let t=0;t<Zi;t++){const n=g[t],r=this.props[n];(h(r)||!1===r)&&(e[n]=r)}return e}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=cr(t,{owner:this}),this.addValue(e,n)),n}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:n}=this.props,r="string"===typeof n||"object"===typeof n?null===(t=Le(this.props,n))||void 0===t?void 0:t[e]:void 0;if(n&&void 0!==r)return r;const a=this.getBaseTargetFromProps(this.props,e);return void 0===a||D(a)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:a}on(e,t){return this.events[e]||(this.events[e]=new sr),this.events[e].add(t)}notify(e){if(this.events[e]){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.events[e].notify(...n)}}}const eo=["transition","transitionEnd"];class to extends Ji{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}makeTargetAnimatableFromInstance(e,t,n){let{transition:a,transitionEnd:i}=e,o=(0,ge.A)(e,eo),{transformValues:s}=t,l=function(e,t,n){const r={};for(const a in e){const e=br(a,t);if(void 0!==e)r[a]=e;else{const e=n.getValue(a);e&&(r[a]=e.get())}}return r}(o,a||{},this);if(s&&(i&&(i=s(i)),o&&(o=s(o)),l&&(l=s(l))),n){!function(e,t,n){var r,a;const i=Object.keys(t).filter(t=>!e.hasValue(t)),o=i.length;if(o)for(let s=0;s<o;s++){const o=i[s],l=t[o];let u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(a=null!==(r=n[o])&&void 0!==r?r:e.readValue(o))&&void 0!==a?a:t[o]),void 0!==u&&null!==u&&("string"===typeof u&&(ar(u)||Yn(u))?u=parseFloat(u):!mr(u)&&on.test(l)&&(u=Xn(o,l)),e.addValue(o,cr(u,{owner:e})),void 0===n[o]&&(n[o]=u),null!==u&&e.setBaseTarget(o,u))}}(this,o,l);const e=((e,t,n,r)=>{const a=Ri(e,t,r);return Hi(e,t=a.target,n,r=a.transitionEnd)})(this,o,l,i);i=e.transitionEnd,o=e.target}return(0,r.A)({transition:a,transitionEnd:i},o)}}class no extends to{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(O.has(t)){const e=Kn(t);return e&&e.default||0}{const r=(n=e,window.getComputedStyle(n)),a=(B(t)?r.getPropertyValue(t):r[t])||0;return"string"===typeof a?a.trim():a}var n}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return ba(e,n)}build(e,t,n,r){se(e,t,n,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return Te(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;D(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}renderInstance(e,t,n,r){Pe(e,t,n,r)}}class ro extends to{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(O.has(t)){const e=Kn(t);return e&&e.default||0}return t=je.has(t)?t:c(t),e.getAttribute(t)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(e,t){return Ne(e,t)}build(e,t,n,r){we(e,t,n,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,n,r){Ae(e,t,0,r)}mount(e){this.isSVGTag=Se(e.tagName),super.mount(e)}}const ao=(e,t)=>L(e)?new ro(t,{enableHardwareAcceleration:!1}):new no(t,{enableHardwareAcceleration:!0}),io={layout:{ProjectionNode:Ti,MeasureLayout:Na}},oo=(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},Or),dt),Ni),io),so=T((e,t)=>function(e,t,n,a){let{forwardMotionProps:i=!1}=t;const o=L(e)?Be:Ie;return(0,r.A)((0,r.A)({},o),{},{preloadedFeatures:n,useRender:Ee(i),createVisualElement:a,Component:e})}(e,t,oo,ao))},8367:(e,t,n)=>{var r=n(3766);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},8957:(e,t,n)=>{function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function a(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}function i(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach(function(t){i(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}n.d(t,{A:()=>s})},9643:(e,t,n)=>{e.exports=n(226)},9644:(e,t,n)=>{function r(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}n.d(t,{A:()=>r})}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.m=e,(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var i=Object.create(null);n.r(i);var o={};e=e||[null,t({}),t([]),t(t)];for(var s=2&a&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>o[e]=()=>r[e]);return o.default=()=>r,n.d(i,o),i}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+"."+{39:"3736d5aa",59:"77c94cf7",216:"82f636d0",316:"6b56635f",383:"037f6b30",447:"4ea8605c",471:"eaa8a3bd",645:"0759e18b",689:"1d226b38",754:"ec00b189",843:"0acb241d",892:"64ae9b68"}[e]+".chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="chewy-ai-frontend:";n.l=(r,a,i,o)=>{if(e[r])e[r].push(a);else{var s,l;if(void 0!==i)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+i){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+i),s.src=r),e[r]=[a];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(h);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach(e=>e(n)),t)return t(n)},h=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var i=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=i);var o=n.p+n.u(t),s=new Error;n.l(o,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var i=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+i+": "+o+")",s.name="ChunkLoadError",s.type=i,s.request=o,a[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,i,o=r[0],s=r[1],l=r[2],u=0;if(o.some(t=>0!==e[t])){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)l(n)}for(t&&t(r);u<o.length;u++)i=o[u],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0},r=self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r=n(9643),a=n.t(r,2),i=n(8367),o=n(7192),s=n(3766),l=n.t(s,2),u=n(3299);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}function d(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const f=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(re){}new Map;const h=a.startTransition;l.flushSync,a.useId;function p(e){let{basename:t,children:n,future:a,window:i}=e,s=r.useRef();null==s.current&&(s.current=(0,u.zR)({window:i,v5Compat:!0}));let l=s.current,[c,d]=r.useState({action:l.action,location:l.location}),{v7_startTransition:f}=a||{},p=r.useCallback(e=>{f&&h?h(()=>d(e)):d(e)},[d,f]);return r.useLayoutEffect(()=>l.listen(p),[l,p]),r.useEffect(()=>(0,o.V8)(a),[a]),r.createElement(o.Ix,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:l,future:a})}const m="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,g=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,v=r.forwardRef(function(e,t){let n,{onClick:a,relative:i,reloadDocument:s,replace:l,state:h,target:p,to:v,preventScrollReset:y,viewTransition:b}=e,x=d(e,f),{basename:w}=r.useContext(o.jb),k=!1;if("string"===typeof v&&g.test(v)&&(n=v,m))try{let e=new URL(window.location.href),t=v.startsWith("//")?new URL(e.protocol+v):new URL(v),n=(0,u.pb)(t.pathname,w);t.origin===e.origin&&null!=n?v=n+t.search+t.hash:k=!0}catch(re){}let S=(0,o.$P)(v,{relative:i}),C=function(e,t){let{target:n,replace:a,state:i,preventScrollReset:s,relative:l,viewTransition:c}=void 0===t?{}:t,d=(0,o.Zp)(),f=(0,o.zy)(),h=(0,o.x$)(e,{relative:l});return r.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==a?a:(0,u.AO)(f)===(0,u.AO)(h);d(e,{replace:n,state:i,preventScrollReset:s,relative:l,viewTransition:c})}},[f,d,h,a,i,n,e,s,l,c])}(v,{replace:l,state:h,target:p,preventScrollReset:y,relative:i,viewTransition:b});return r.createElement("a",c({},x,{href:n||S,onClick:k||s?a:function(e){a&&a(e),e.defaultPrevented||C(e)},ref:t,target:p}))});var y,b;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(y||(y={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(b||(b={}));var x=n(2086),w=n(4859),k=n(467),S=n(1820),C=n(6507);const E=e=>{let{onGoogleAuth:t,isLoading:n=!1,disabled:r=!1,text:a="Continue with Google"}=e;return(0,C.jsxs)(w.$,{type:"button",onClick:t,isLoading:n,disabled:r,className:"w-full flex items-center justify-center gap-2 bg-background-secondary hover:bg-background-tertiary text-text-primary border border-border-primary font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background-primary disabled:opacity-50 disabled:cursor-not-allowed transform-gpu hover:shadow-md focus:ring-primary-500",children:[!n&&(0,C.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,C.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,C.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,C.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,C.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),a]})},P=()=>{const[e,t]=(0,r.useState)(""),[n,a]=(0,r.useState)(""),[i,s]=(0,r.useState)(!0),[l,u]=(0,r.useState)(!1),[c,d]=(0,r.useState)({}),{login:f,signInWithGoogle:h,isLoading:p,isAuthenticated:m}=(0,x.A)(),g=(0,o.Zp)();(0,r.useEffect)(()=>{m&&g("/dashboard")},[m,g]);return(0,C.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:(0,C.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8",children:[(0,C.jsxs)("div",{children:[(0,C.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Sign in to ChewyAI"}),(0,C.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",(0,C.jsx)(v,{to:"/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"create a new account"})]})]}),(0,C.jsxs)("div",{className:"space-y-4",children:[(0,C.jsx)(E,{onGoogleAuth:async()=>{u(!0),d({});try{const e=await h();e.error&&(console.error("Google sign-in failed:",e.error),d({general:e.error.includes("not configured")?"Google sign-in is not properly configured. Please contact support.":e.error||"Failed to sign in with Google"}),u(!1))}catch(e){console.error("Google sign-in error:",e),d({general:"Network error during Google sign-in"}),u(!1)}},isLoading:l,disabled:p,text:"Sign in with Google"}),(0,C.jsxs)("div",{className:"relative",children:[(0,C.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,C.jsx)("div",{className:"w-full border-t border-gray-600"})}),(0,C.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,C.jsx)("span",{className:"px-2 bg-background-primary text-gray-400",children:"Or continue with email"})})]})]}),(0,C.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:async t=>{if(t.preventDefault(),!(()=>{const t={};return e?/\S+@\S+\.\S+/.test(e)||(t.email="Email is invalid"):t.email="Email is required",n||(t.password="Password is required"),d(t),0===Object.keys(t).length})())return;const r=await f(e,n,i);r.success?g("/dashboard"):d({general:r.error})},children:[c.general&&(0,C.jsx)("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:c.general}),(0,C.jsxs)("div",{className:"space-y-4",children:[(0,C.jsx)(k.p,{label:"Email address",type:"email",value:e,onChange:t,error:c.email,placeholder:"Enter your email",required:!0}),(0,C.jsx)(k.p,{label:"Password",type:"password",value:n,onChange:a,error:c.password,placeholder:"Enter your password",required:!0})]}),(0,C.jsx)("div",{className:"flex items-center justify-between",children:(0,C.jsx)(S.S,{checked:i,onChange:s,label:"Stay signed in",description:"Keep me logged in across browser sessions",size:"sm"})}),(0,C.jsx)(w.$,{type:"submit",isLoading:p,disabled:l,className:"w-full",size:"lg",children:"Sign in"})]})]})})};var j=n(8957);const A=()=>{const[e,t]=(0,r.useState)({name:"",email:"",password:"",confirmPassword:""}),[n,a]=(0,r.useState)(!1),[i,s]=(0,r.useState)({}),{signup:l,signInWithGoogle:u,isLoading:c}=(0,x.A)(),d=(0,o.Zp)(),f=(e,n)=>t(t=>(0,j.A)((0,j.A)({},t),{},{[e]:n}));return(0,C.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:(0,C.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8",children:[(0,C.jsxs)("div",{children:[(0,C.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Create your account"}),(0,C.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",(0,C.jsx)(v,{to:"/login",className:"font-medium text-primary-500 hover:text-primary-400",children:"sign in to your existing account"})]})]}),(0,C.jsxs)("div",{className:"space-y-4",children:[(0,C.jsx)(E,{onGoogleAuth:async()=>{a(!0),s({});try{const e=await u();e.error&&(console.error("Google sign-up failed:",e.error),s({general:e.error.includes("not configured")?"Google sign-up is not properly configured. Please contact support.":e.error||"Failed to sign up with Google"}),a(!1))}catch(e){console.error("Google sign-up error:",e),s({general:"Network error during Google sign-up"}),a(!1)}},isLoading:n,disabled:c,text:"Sign up with Google"}),(0,C.jsxs)("div",{className:"relative",children:[(0,C.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,C.jsx)("div",{className:"w-full border-t border-gray-600"})}),(0,C.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,C.jsx)("span",{className:"px-2 bg-background-primary text-gray-400",children:"Or continue with email"})})]})]}),(0,C.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:async t=>{if(t.preventDefault(),!(()=>{const t={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(t.email="Email is invalid"):t.email="Email is required",e.password?e.password.length<6&&(t.password="Password must be at least 6 characters"):t.password="Password is required",e.password!==e.confirmPassword&&(t.confirmPassword="Passwords do not match"),s(t),0===Object.keys(t).length})())return;const n=await l(e.email,e.password,e.name||void 0);n.success?d("/dashboard"):s({general:n.error||"Signup failed"})},children:[i.general&&(0,C.jsx)("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:i.general}),(0,C.jsxs)("div",{className:"space-y-4",children:[(0,C.jsx)(k.p,{label:"Full Name (Optional)",value:e.name,onChange:e=>f("name",e),placeholder:"Enter your full name"}),(0,C.jsx)(k.p,{label:"Email address",type:"email",value:e.email,onChange:e=>f("email",e),error:i.email,placeholder:"Enter your email",required:!0}),(0,C.jsx)(k.p,{label:"Password",type:"password",value:e.password,onChange:e=>f("password",e),error:i.password,placeholder:"Create a password",required:!0}),(0,C.jsx)(k.p,{label:"Confirm Password",type:"password",value:e.confirmPassword,onChange:e=>f("confirmPassword",e),error:i.confirmPassword,placeholder:"Confirm your password",required:!0})]}),(0,C.jsx)(w.$,{type:"submit",isLoading:c,disabled:n,className:"w-full",size:"lg",children:"Create Account"})]})]})})},T=()=>{const[e,t]=(0,r.useState)(null),[n,a]=(0,r.useState)(!1),{handleOAuthCallback:i}=(0,x.A)(),s=(0,o.Zp)(),l=(0,r.useRef)(!1);return(0,r.useEffect)(()=>{if(l.current||n)return void console.log("AuthCallback: Already processing or completed, skipping");(async()=>{if(!l.current&&!n){l.current=!0,a(!0),console.log("AuthCallback: Starting OAuth callback processing");try{const e=await i();e.success?(console.log("AuthCallback: OAuth successful, redirecting to dashboard"),s("/dashboard",{replace:!0})):(console.error("AuthCallback: OAuth failed:",e.error),t(e.error||"Authentication failed"),setTimeout(()=>{s("/login",{replace:!0})},5e3))}catch(e){console.error("AuthCallback: Exception during processing:",e),t("Failed to process authentication"),setTimeout(()=>{s("/login",{replace:!0})},5e3)}finally{a(!1)}}})()},[i,s,n]),e?(0,C.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:(0,C.jsxs)("div",{className:"max-w-md w-full text-center space-y-4 p-8",children:[(0,C.jsxs)("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:[(0,C.jsx)("h3",{className:"text-lg font-semibold",children:"Authentication Error"}),(0,C.jsx)("p",{className:"mt-2",children:e})]}),(0,C.jsx)("p",{className:"text-gray-400 mb-4",children:"Redirecting to login page in 5 seconds..."}),(0,C.jsx)("button",{onClick:()=>s("/login",{replace:!0}),className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"Go to Login Now"})]})}):(0,C.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:(0,C.jsxs)("div",{className:"max-w-md w-full text-center space-y-4 p-8",children:[(0,C.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"}),(0,C.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Completing Authentication..."}),(0,C.jsx)("p",{className:"text-gray-400",children:"Please wait while we sign you in."})]})})},N=e=>{let{children:t}=e;const{isAuthenticated:n,isLoading:a,checkAuth:i}=(0,x.A)(),s=(0,o.zy)();return(0,r.useEffect)(()=>{n||a||i()},[n,a,i]),a?(0,C.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:(0,C.jsxs)("div",{className:"text-center",children:[(0,C.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),(0,C.jsx)("p",{className:"text-gray-400",children:"Checking authentication..."})]})}):n?(0,C.jsx)(C.Fragment,{children:t}):(0,C.jsx)(o.C5,{to:"/login",state:{from:s},replace:!0})};var L=n(7572),M=n(8002),R=n(344);const O=e=>{let{credits:t,showDetails:n=!1,size:r="md",variant:a="inline"}=e;const i=(0,o.Zp)(),s=(()=>{switch(r){case"sm":return{primary:"text-sm",secondary:"text-xs",icon:"w-4 h-4"};case"lg":return{primary:"text-lg",secondary:"text-sm",icon:"w-6 h-6"};default:return{primary:"text-sm",secondary:"text-xs",icon:"w-5 h-5"}}})(),l=t<5,u=0===t;return(0,C.jsxs)("div",{className:(()=>{const e="flex items-center justify-between transition-all duration-200";switch(a){case"sidebar":return"".concat(e," bg-background-tertiary rounded-lg p-3 border border-gray-700 hover:border-primary-500/50");case"card":return"".concat(e," bg-background-secondary rounded-lg p-4 border border-gray-600");default:return"".concat(e," bg-gray-800/50 rounded-lg p-2")}})(),children:[(0,C.jsxs)("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[(0,C.jsx)("div",{className:"\n          flex items-center justify-center rounded-full p-1.5\n          ".concat(u?"bg-red-500/20 text-red-400":l?"bg-yellow-500/20 text-yellow-400":"bg-primary-500/20 text-primary-400","\n        "),children:(0,C.jsx)(R.pCw,{className:s.icon})}),(0,C.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,C.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,C.jsx)("span",{className:"".concat(s.primary," font-medium text-white"),children:t}),(0,C.jsxs)("span",{className:"".concat(s.secondary," text-gray-400"),children:["credit",1!==t?"s":""]})]}),"sidebar"===a&&(0,C.jsx)("div",{className:"".concat(s.secondary," text-gray-500 truncate"),children:u?"No credits remaining":l?"Running low":"Available for AI generation"})]})]}),(l||n)&&(0,C.jsx)("button",{onClick:()=>{i("/credits")},className:"\n            flex items-center justify-center rounded-lg transition-colors\n            ".concat("sidebar"===a?"p-2 hover:bg-primary-500/20 text-primary-400 hover:text-primary-300":"p-1.5 hover:bg-gray-700 text-gray-400 hover:text-white","\n          "),title:"Buy more credits","aria-label":"Buy more credits",children:(0,C.jsx)(R.s00,{className:s.icon})})]})};var z=n(2864);const D=e=>{let{showWhenOnline:t=!1,position:n="top",className:a=""}=e;const{isOnline:i,isBackendAvailable:o,lastChecked:s,retryCount:l,isChecking:u,checkConnection:c}=(0,z.dQ)(),[d,f]=(0,r.useState)(!1),[h,p]=(0,r.useState)(null),m=i&&o,g=!i||!o;(0,r.useEffect)(()=>{if(g)f(!0),h&&(clearTimeout(h),p(null));else if(m)if(t){f(!0);const e=setTimeout(()=>{f(!1)},3e3);p(e)}else f(!1);return()=>{h&&clearTimeout(h)}},[i,o,t]);const v=i?o?{icon:R.qRc,message:"Connected",bgColor:"bg-green-500",textColor:"text-white",showRetry:!1}:{icon:R.TMu,message:l>3?"Server temporarily unavailable":"Connecting to server...",bgColor:"bg-yellow-500",textColor:"text-white",showRetry:l>2}:{icon:R.TMu,message:"No internet connection",bgColor:"bg-red-500",textColor:"text-white",showRetry:!1},y=v.icon;if(!d)return null;const b="top"===n?"top-4 left-1/2 transform -translate-x-1/2":"bottom-4 left-1/2 transform -translate-x-1/2";return(0,C.jsx)(L.N,{children:(0,C.jsxs)(M.P.div,{initial:{opacity:0,y:"top"===n?-50:50},animate:{opacity:1,y:0},exit:{opacity:0,y:"top"===n?-50:50},className:"\n          fixed ".concat(b," z-50 \n          ").concat(v.bgColor," ").concat(v.textColor,"\n          px-4 py-2 rounded-lg shadow-lg border border-gray-600\n          flex items-center space-x-2 text-sm font-medium\n          ").concat(a,"\n        "),children:[(0,C.jsx)(y,{className:"w-4 h-4 ".concat(u?"animate-spin":"")}),(0,C.jsx)("span",{children:v.message}),v.showRetry&&(0,C.jsx)("button",{onClick:async()=>{await c()},disabled:u,className:"ml-2 p-1 rounded hover:bg-black/20 transition-colors disabled:opacity-50","aria-label":"Retry connection",children:(0,C.jsx)(R.pMz,{className:"w-3 h-3 ".concat(u?"animate-spin":"")})}),s&&g&&(0,C.jsxs)("span",{className:"text-xs opacity-75 ml-2",children:["Last checked: ",s.toLocaleTimeString()]})]})})},_=e=>{let{className:t=""}=e;const{isOnline:n,isBackendAvailable:r,isChecking:a}=(0,z.dQ)(),i=n&&r;return(0,C.jsxs)("div",{className:"flex items-center space-x-2 ".concat(t),title:n?r?"Connected":"Server unavailable":"No internet connection",children:[(0,C.jsx)("div",{className:"\n          w-2 h-2 rounded-full ".concat(n?r?"bg-green-500":"bg-yellow-500":"bg-red-500","\n          ").concat(a?"animate-pulse":"","\n        ")}),(0,C.jsx)("span",{className:"text-xs text-gray-400",children:i?"Online":"Offline"})]})},V=[{id:"study-sets",label:"Study Sets",path:"/dashboard",icon:R.eVK,description:"Manage and study your flashcard sets"},{id:"documents",label:"Documents",path:"/documents",icon:R.gpD,description:"Manage your documents"},{id:"analytics",label:"Analytics",path:"/analytics",icon:R.Lyu,description:"Study progress and insights"},{id:"credits",label:"Credits",path:"/credits",icon:R.XtC,description:"Manage your AI credits"},{id:"help",label:"Help",path:"/help",icon:R.dHv,description:"Get help and support"},{id:"settings",label:"Settings",path:"/settings",icon:R.FMC,description:"Account and app settings"}],F=e=>{var t,n,a,i,s;let{isOpen:l,onToggle:u}=e;const c=(0,o.Zp)(),d=(0,o.zy)(),[f,h]=(0,r.useState)(!1),{user:p}=(0,x.A)();(0,r.useEffect)(()=>{const e=()=>{h(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const m={open:{opacity:1,x:0,transition:{type:"spring",stiffness:300,damping:30}},closed:{opacity:0,x:-20,transition:{duration:.2}}};return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(L.N,{children:f&&l&&(0,C.jsx)(M.P.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden",variants:{open:{opacity:1,transition:{duration:.2}},closed:{opacity:0,transition:{duration:.2}}},initial:"closed",animate:"open",exit:"closed",onClick:u})}),(0,C.jsx)(M.P.aside,{className:"\n          fixed top-0 left-0 h-full bg-background-secondary border-r border-border-primary z-50\n          ".concat(f?"w-80":"w-64","\n          md:relative md:translate-x-0\n        "),variants:{open:{x:0,transition:{type:"spring",stiffness:300,damping:30}},closed:{x:"-100%",transition:{type:"spring",stiffness:300,damping:30}}},initial:f?"closed":"open",animate:l?"open":"closed","aria-label":"Main navigation",children:(0,C.jsxs)("div",{className:"flex flex-col h-full",children:[(0,C.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[(0,C.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,C.jsx)("div",{className:"w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center",children:(0,C.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,C.jsx)("h1",{className:"text-xl font-bold text-white",children:"ChewyAI"})]}),f&&(0,C.jsx)("button",{onClick:u,className:"p-2 rounded-lg hover:bg-background-tertiary transition-colors","aria-label":"Close navigation",children:(0,C.jsx)(R.U_s,{className:"w-5 h-5 text-gray-400"})})]}),(0,C.jsx)("nav",{className:"flex-1 px-4 py-6 overflow-y-auto",children:(0,C.jsx)("ul",{className:"space-y-2",role:"list",children:V.map((e,t)=>{const n=e.icon,r="/dashboard"===(a=e.path)?"/dashboard"===d.pathname||"/"===d.pathname:d.pathname.startsWith(a);var a;return(0,C.jsx)(M.P.li,{variants:m,initial:"closed",animate:l?"open":"closed",transition:{delay:.05*t},children:(0,C.jsxs)("button",{onClick:()=>(e=>{c(e),f&&u()})(e.path),className:"\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\n                        transition-all duration-200 group\n                        ".concat(r?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white","\n                      "),"aria-current":r?"page":void 0,"aria-describedby":"".concat(e.id,"-description"),children:[(0,C.jsx)(n,{className:"\n                          w-5 h-5 transition-colors\n                          ".concat(r?"text-primary-400":"text-gray-400 group-hover:text-white","\n                        ")}),(0,C.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,C.jsx)("span",{className:"font-medium truncate block",children:e.label}),(0,C.jsx)("span",{id:"".concat(e.id,"-description"),className:"text-xs text-gray-500 group-hover:text-gray-400 truncate block",children:e.description})]}),r&&(0,C.jsx)(M.P.div,{className:"w-2 h-2 bg-primary-400 rounded-full",layoutId:"activeIndicator",transition:{type:"spring",stiffness:300,damping:30}})]})},e.id)})})}),p&&(0,C.jsx)("div",{className:"px-4 py-3 border-t border-border-primary",children:(0,C.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,C.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm",children:(null===(t=p.name)||void 0===t||null===(n=t.charAt(0))||void 0===n?void 0:n.toUpperCase())||(null===(a=p.email)||void 0===a||null===(i=a.charAt(0))||void 0===i?void 0:i.toUpperCase())||"U"}),(0,C.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,C.jsx)("p",{className:"text-sm font-medium text-white truncate",children:p.name||(null===(s=p.email)||void 0===s?void 0:s.split("@")[0])||"User"}),(0,C.jsx)("p",{className:"text-xs text-gray-400 truncate",children:p.subscription_tier||"Free"})]})]})}),(0,C.jsxs)("div",{className:"p-4 border-t border-border-primary space-y-3",children:[p&&(0,C.jsx)(O,{credits:p.credits_remaining||0,variant:"sidebar",size:"sm",showDetails:!0}),(0,C.jsx)(_,{className:"justify-center"}),(0,C.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:[(0,C.jsx)("p",{children:"ChewyAI v1.0.0"}),(0,C.jsx)("p",{className:"mt-1",children:"AI-Powered Study Materials"})]})]})]})}),f&&!l&&(0,C.jsx)("button",{onClick:u,className:"fixed top-4 left-4 z-40 p-3 bg-background-secondary border border-border-primary rounded-lg shadow-lg md:hidden","aria-label":"Open navigation menu",children:(0,C.jsx)(R.TF4,{className:"w-6 h-6 text-white"})})]})},B=e=>{let{children:t}=e;const[n,a]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!1);(0,r.useEffect)(()=>{const e=()=>{const e=window.innerWidth<768;o(e),a(!e)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);return(0,C.jsxs)("div",{className:"min-h-screen bg-background-primary flex",children:[(0,C.jsx)(F,{isOpen:n,onToggle:()=>{a(!n)}}),(0,C.jsx)("main",{className:"\n          flex-1 transition-all duration-300 ease-in-out\n          ".concat("ml-0","\n          ").concat(i?"w-full":"","\n        "),id:"main-content",children:(0,C.jsx)("div",{className:"w-full h-full",children:t})})]})},I=e=>{let{links:t}=e;return(0,C.jsx)("nav",{className:"sr-only focus-within:not-sr-only","aria-label":"Skip navigation",children:(0,C.jsx)("div",{className:"fixed top-0 left-0 z-50 bg-primary-600 text-white p-4 rounded-br-lg shadow-lg",children:(0,C.jsx)("ul",{className:"flex flex-col space-y-2",children:t.map((e,t)=>(0,C.jsx)("li",{children:(0,C.jsx)("a",{href:e.href,className:"block px-3 py-2 text-sm font-medium rounded-md hover:bg-primary-700 focus:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600",onClick:t=>{t.preventDefault();const n=document.querySelector(e.href);n&&(n.scrollIntoView({behavior:"smooth"}),n instanceof HTMLElement&&n.focus())},children:e.label})},t))})})})},U=[{keys:["?"],description:"Show keyboard shortcuts",context:"Global"},{keys:["Escape"],description:"Close modal or cancel action",context:"Global"},{keys:["\u2190","\u2192"],description:"Navigate between cards/questions",context:"Study"},{keys:["Space","\u2191","\u2193"],description:"Flip flashcard or select answer",context:"Study"},{keys:["F"],description:"Flag current item for review",context:"Study"},{keys:["Enter"],description:"Submit answer or continue",context:"Study"},{keys:["Ctrl","Z"],description:"Undo last action",context:"Study"},{keys:["Ctrl","Y"],description:"Redo last action",context:"Study"},{keys:["1","2","3","4"],description:"Select quiz answer option",context:"Quiz"},{keys:["G","D"],description:"Go to Dashboard",context:"Navigation"},{keys:["G","O"],description:"Go to Documents",context:"Navigation"},{keys:["G","S"],description:"Go to Study Sets",context:"Navigation"},{keys:["G","A"],description:"Go to Analytics",context:"Navigation"},{keys:["Ctrl","U"],description:"Upload new document",context:"Documents"},{keys:["Ctrl","F"],description:"Search documents",context:"Documents"},{keys:["Delete"],description:"Delete selected document",context:"Documents"}],H=(0,r.memo)(e=>{let{keys:t}=e;return(0,C.jsx)("div",{className:"flex items-center space-x-1",children:t.map((e,t)=>(0,C.jsxs)(r.Fragment,{children:[t>0&&(0,C.jsx)("span",{className:"text-gray-400 text-xs",children:"+"}),(0,C.jsx)("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-200 border border-gray-300 rounded-lg",children:e})]},e))})});H.displayName="KeyboardKey";const W=(0,r.memo)(e=>{let{isOpen:t,onClose:n}=e;if((0,r.useEffect)(()=>{const e=e=>{"Escape"===e.key&&n()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,n]),!t)return null;const a=U.reduce((e,t)=>{const n=t.context||"General";return e[n]||(e[n]=[]),e[n].push(t),e},{});return(0,C.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:n,role:"dialog","aria-modal":"true","aria-labelledby":"shortcuts-title",children:(0,C.jsxs)("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:[(0,C.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,C.jsx)("h2",{id:"shortcuts-title",className:"text-xl font-semibold text-white",children:"Keyboard Shortcuts"}),(0,C.jsx)(w.$,{onClick:n,variant:"secondary",size:"sm","aria-label":"Close shortcuts modal",children:"\u2715"})]}),(0,C.jsx)("div",{className:"space-y-6",children:Object.entries(a).map(e=>{let[t,n]=e;return(0,C.jsxs)("div",{children:[(0,C.jsx)("h3",{className:"text-lg font-medium text-gray-300 mb-3 border-b border-gray-600 pb-1",children:t}),(0,C.jsx)("div",{className:"space-y-2",children:n.map((e,t)=>(0,C.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,C.jsx)("span",{className:"text-gray-400 flex-1",children:e.description}),(0,C.jsx)(H,{keys:e.keys})]},t))})]},t)})}),(0,C.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-600",children:(0,C.jsxs)("p",{className:"text-sm text-gray-500 text-center",children:["Press ",(0,C.jsx)("kbd",{className:"px-1 py-0.5 text-xs bg-gray-200 text-gray-800 rounded",children:"?"})," anytime to show this help"]})})]})})});W.displayName="KeyboardShortcutsModal";var $=n(1721);const G=(0,r.lazy)(()=>Promise.all([n.e(471),n.e(892)]).then(n.bind(n,6892)).then(e=>({default:e.Dashboard}))),q=(0,r.lazy)(()=>Promise.all([n.e(471),n.e(216),n.e(843)]).then(n.bind(n,9843)).then(e=>({default:e.DocumentsPage}))),Q=(0,r.lazy)(()=>n.e(447).then(n.bind(n,5447)).then(e=>({default:e.CreateStudySetPage}))),K=(0,r.lazy)(()=>n.e(645).then(n.bind(n,8645)).then(e=>({default:e.StudySetPage}))),X=(0,r.lazy)(()=>n.e(754).then(n.bind(n,5754)).then(e=>({default:e.StudyPage}))),Y=(0,r.lazy)(()=>n.e(316).then(n.bind(n,1316)).then(e=>({default:e.AnalyticsPage}))),Z=(0,r.lazy)(()=>n.e(689).then(n.bind(n,3689)).then(e=>({default:e.CreditsPage}))),J=(0,r.lazy)(()=>n.e(59).then(n.bind(n,4059)).then(e=>({default:e.HelpPage}))),ee=(0,r.lazy)(()=>n.e(39).then(n.bind(n,3039)).then(e=>({default:e.SettingsPage}))),te=()=>{const e=(0,o.zy)(),{showShortcutsModal:t,setShowShortcutsModal:n}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{enableGlobalShortcuts:t=!0,enableNavigationShortcuts:n=!0}=e,a=(0,o.Zp)(),[i,s]=(0,r.useState)(!1),[l,u]=(0,r.useState)([]);return(0,r.useEffect)(()=>{if(!t)return;const e=e=>{var t;if(!(e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement||"true"===(null===(t=e.target)||void 0===t?void 0:t.contentEditable))){if("?"===e.key&&!e.ctrlKey&&!e.metaKey&&!e.altKey)return e.preventDefault(),void s(!0);if("Escape"===e.key)return s(!1),void u([]);if(n){if("g"===e.key.toLowerCase()&&!e.ctrlKey&&!e.metaKey&&!e.altKey)return e.preventDefault(),u(["g"]),void setTimeout(()=>u([]),2e3);if(l.includes("g")){switch(e.preventDefault(),e.key.toLowerCase()){case"d":case"s":a("/dashboard");break;case"o":a("/documents");break;case"a":a("/analytics")}return void u([])}}if(e.ctrlKey||e.metaKey)switch(e.key.toLowerCase()){case"u":if("/documents"===window.location.pathname){e.preventDefault();const t=new CustomEvent("trigger-upload");document.dispatchEvent(t)}break;case"f":if("/documents"===window.location.pathname){e.preventDefault();const t=document.querySelector('input[placeholder*="search" i]');t&&t.focus()}}}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,n,a,l]),{showShortcutsModal:i,setShowShortcutsModal:s,lastKeySequence:l}}(),{checkAuth:a,isLoading:i,isAuthenticated:s}=(0,x.n)();(0,r.useEffect)(()=>{(0,z.z9)();(async()=>{try{(await fetch("/api/health",{method:"GET",signal:AbortSignal.timeout(5e3)})).ok||console.warn("Backend health check failed, proceeding with auth check anyway")}catch(e){console.warn("Backend appears to be down, clearing any stored tokens:",e),localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token")}a()})();const e=setTimeout(()=>{console.warn("Auth check taking too long, forcing loading state to false"),x.n.setState({isLoading:!1})},3e4);return()=>clearTimeout(e)},[a]);return i?(0,C.jsx)("div",{className:"min-h-screen bg-background-primary flex items-center justify-center",children:(0,C.jsxs)("div",{className:"text-center max-w-md mx-auto p-8",children:[(0,C.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),(0,C.jsx)("p",{className:"text-gray-400 mb-4",children:"Checking authentication..."}),(0,C.jsx)("p",{className:"text-gray-500 text-sm mb-6",children:"This may take a few seconds while we verify your session."}),(0,C.jsx)("button",{onClick:()=>{localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token"),x.n.setState({isLoading:!1,isAuthenticated:!1,user:null})},className:"text-primary-400 hover:text-primary-300 text-sm underline transition-colors",children:"Having trouble? Click here to skip to login"})]})}):(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(I,{links:(()=>{const t=e.pathname;return"/dashboard"===t?[{href:"#main-content",label:"Skip to main content"},{href:"#study-sets",label:"Skip to study sets list"},{href:"#create-study-set",label:"Skip to create study set"}]:t.startsWith("/study/")?[{href:"#main-content",label:"Skip to main content"},{href:"#flashcard",label:"Skip to flashcard"},{href:"#navigation-controls",label:"Skip to navigation controls"}]:"/documents"===t?[{href:"#main-content",label:"Skip to main content"},{href:"#upload-section",label:"Skip to upload section"},{href:"#document-list",label:"Skip to document list"}]:[{href:"#main-content",label:"Skip to main content"}]})()}),(0,C.jsx)(r.Suspense,{fallback:(0,C.jsx)("div",{className:"min-h-screen bg-background-primary flex items-center justify-center",children:(0,C.jsxs)("div",{className:"text-center",children:[(0,C.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),(0,C.jsx)("p",{className:"text-gray-400",children:"Loading..."})]})}),children:(0,C.jsxs)(o.BV,{children:[(0,C.jsx)(o.qh,{path:"/",element:s?(0,C.jsx)(o.C5,{to:"/dashboard",replace:!0}):(0,C.jsx)(o.C5,{to:"/login",replace:!0})}),(0,C.jsx)(o.qh,{path:"/login",element:(0,C.jsx)(P,{})}),(0,C.jsx)(o.qh,{path:"/signup",element:(0,C.jsx)(A,{})}),(0,C.jsx)(o.qh,{path:"/auth/callback",element:(0,C.jsx)(T,{})}),(0,C.jsx)(o.qh,{path:"/dashboard",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(G,{})})})}),(0,C.jsx)(o.qh,{path:"/documents",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(q,{})})})}),(0,C.jsx)(o.qh,{path:"/create-study-set",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(Q,{})})})}),(0,C.jsx)(o.qh,{path:"/study-sets/:id",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(K,{})})})}),(0,C.jsx)(o.qh,{path:"/study/:id/:mode",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(X,{})})})}),(0,C.jsx)(o.qh,{path:"/analytics",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(Y,{})})})}),(0,C.jsx)(o.qh,{path:"/credits",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(Z,{})})})}),(0,C.jsx)(o.qh,{path:"/help",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(J,{})})})}),(0,C.jsx)(o.qh,{path:"/settings",element:(0,C.jsx)(N,{children:(0,C.jsx)(B,{children:(0,C.jsx)(ee,{})})})})]})}),(0,C.jsx)(W,{isOpen:t,onClose:()=>n(!1)}),(0,C.jsx)(D,{showWhenOnline:!0,position:"top"})]})};const ne=function(){return(0,C.jsx)(p,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:(0,C.jsx)($.C,{children:(0,C.jsx)(te,{})})})};i.createRoot(document.getElementById("root")).render((0,C.jsx)(r.StrictMode,{children:(0,C.jsx)(ne,{})}))})();
//# sourceMappingURL=main.fcfca456.js.map