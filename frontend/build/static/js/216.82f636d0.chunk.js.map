{"version": 3, "file": "static/js/216.82f636d0.chunk.js", "mappings": "qHAAA,SAASA,IACP,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GACjE,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAIG,EAAIF,UAAUD,GAClB,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAC/D,CACA,OAAOL,CACT,EAAGJ,EAASY,MAAM,KAAMN,UAC1B,CCRA,SAASO,EAAuBR,GAC9B,QAAI,IAAWA,EAAG,MAAM,IAAIS,eAAe,6DAC3C,OAAOT,CACT,CCHA,SAASU,EAAgBP,EAAGH,GAC1B,OAAOU,EAAkBd,OAAOe,eAAiBf,OAAOe,eAAeb,OAAS,SAAUK,EAAGH,GAC3F,OAAOG,EAAES,UAAYZ,EAAGG,CAC1B,EAAGO,EAAgBP,EAAGH,EACxB,CCHA,SAASa,EAAeV,EAAGW,GACzBX,EAAEY,UAAYnB,OAAOoB,OAAOF,EAAEC,WAAYZ,EAAEY,UAAUE,YAAcd,EAAG,EAAeA,EAAGW,EAC3F,C,kBCHA,IAAII,EAAYC,OAAOC,OACnB,SAAkBC,GACd,MAAwB,kBAAVA,GAAsBA,IAAUA,CAClD,EACJ,SAASC,EAAQC,EAAOC,GACpB,OAAID,IAAUC,MAGVN,EAAUK,KAAUL,EAAUM,GAItC,CACA,SAASC,EAAeC,EAAWC,GAC/B,GAAID,EAAUxB,SAAWyB,EAAWzB,OAChC,OAAO,EAEX,IAAK,IAAI0B,EAAI,EAAGA,EAAIF,EAAUxB,OAAQ0B,IAClC,IAAKN,EAAQI,EAAUE,GAAID,EAAWC,IAClC,OAAO,EAGf,OAAO,CACX,CAyBA,QAvBA,SAAoBC,EAAUP,GAE1B,IAAIQ,OADY,IAAZR,IAAsBA,EAAUG,GAEpC,IACIM,EADAC,EAAW,GAEXC,GAAa,EAejB,OAdA,WAEI,IADA,IAAIC,EAAU,GACLC,EAAK,EAAGA,EAAKlC,UAAUC,OAAQiC,IACpCD,EAAQC,GAAMlC,UAAUkC,GAE5B,OAAIF,GAAcH,IAAaM,MAAQd,EAAQY,EAASF,KAGxDD,EAAaF,EAAStB,MAAM6B,KAAMF,GAClCD,GAAa,EACbH,EAAWM,KACXJ,EAAWE,GALAH,CAOf,CAEJ,E,cCtCMM,EAFmB,kBAAhBC,aAAuD,oBAApBA,YAAYD,IAGpD,kBAAMC,YAAYD,KAAlB,EACA,kBAAME,KAAKF,KAAX,EAMG,SAASG,EAAcC,GAC5BC,qBAAqBD,EAAUE,GAChC,CAEM,SAASC,EAAeC,EAAoBC,GACjD,IAAMC,EAAQV,IAUd,IAAMI,EAAuB,CAC3BE,GAAIK,sBATN,SAASC,IACHZ,IAAQU,GAASD,EACnBD,EAASvC,KAAK,MAEdmC,EAAUE,GAAKK,sBAAsBC,EAExC,IAMD,OAAOR,CACR,CClCD,IAAIS,GAAgB,EAGpB,SAAgBC,EAAiBC,GAC/B,QADsE,IAAvCA,IAAAA,GAAwB,IACzC,IAAVF,GAAeE,EAAa,CAC9B,IAAMC,EAAMC,SAASC,cAAc,OAC7BC,EAAQH,EAAIG,MAClBA,EAAMC,MAAQ,OACdD,EAAME,OAAS,OACfF,EAAMG,SAAW,SAEfL,SAASM,KAA6BC,YAAYR,GAEpDH,EAAOG,EAAIS,YAAcT,EAAIU,YAE3BT,SAASM,KAA6BI,YAAYX,EACrD,CAED,OAAOH,CACR,CAOD,IAAIe,EAAwC,KAQ5C,SAAgBC,EAAiBd,GAC/B,QAD6E,IAA9CA,IAAAA,GAAwB,GAC/B,OAApBa,GAA4Bb,EAAa,CAC3C,IAAMe,EAAWb,SAASC,cAAc,OAClCa,EAAaD,EAASX,MAC5BY,EAAWX,MAAQ,OACnBW,EAAWV,OAAS,OACpBU,EAAWT,SAAW,SACtBS,EAAWC,UAAY,MAEvB,IAAMC,EAAWhB,SAASC,cAAc,OAClCgB,EAAaD,EAASd,MAqB5B,OApBAe,EAAWd,MAAQ,QACnBc,EAAWb,OAAS,QAEpBS,EAASN,YAAYS,GAEnBhB,SAASM,KAA6BC,YAAYM,GAEhDA,EAASK,WAAa,EACxBP,EAAkB,uBAElBE,EAASK,WAAa,EAEpBP,EAD0B,IAAxBE,EAASK,WACO,WAEA,sBAIpBlB,SAASM,KAA6BI,YAAYG,GAE7CF,CACR,CAED,OAAOA,CACR,CCuvBD,IClsBMQ,EAAiB,SAACC,EAAeC,GAAhB,OAA8BD,CAA9B,EAavB,SAAwBE,EAATC,GAoBX,IAAAC,EAnBFC,EAmBEF,EAnBFE,cACAC,EAkBEH,EAlBFG,sBACAC,EAiBEJ,EAjBFI,YACAC,EAgBEL,EAhBFK,8BACAC,EAeEN,EAfFM,uBACAC,EAcEP,EAdFO,0BACAC,EAaER,EAbFQ,kBACAC,EAYET,EAZFS,sCACAC,EAWEV,EAXFU,cAYA,OAAAT,EAAA,SAAAU,GA2BE,SAAAC,EAAYC,GAAiB,IAAAC,EAAA,OAC3BA,EAAAH,EAAAlF,KAAA,KAAMoF,IAAN,MA3BFE,eAAsBP,EAAkBM,EAAKD,MAANlF,EAAAmF,IA0BVA,EAzB7BE,eAyB6B,EAAAF,EAxB7BG,2BAA+C,KAwBlBH,EAd7BI,MAAe,CACbC,SAAQxF,EAAAmF,GACRM,aAAa,EACbC,gBAAiB,UACjBC,aAC4C,kBAAnCR,EAAKD,MAAMU,oBACdT,EAAKD,MAAMU,oBACX,EACNC,0BAA0B,GAMCV,EA8M7BW,0BA9M6B,EAAAX,EAoN7BW,qBAAuBC,EACrB,SACEC,EACAC,EACAC,EACAC,GAJF,OAMIhB,EAAKD,MAAMkB,gBAAgD,CAC3DJ,mBAAAA,EACAC,kBAAAA,EACAC,kBAAAA,EACAC,iBAAAA,GAVJ,GArN2BhB,EAmO7BkB,mBAnO6B,EAAAlB,EAwO7BkB,cAAgBN,EACd,SACEL,EACAC,EACAE,GAHF,OAKIV,EAAKD,MAAMoB,SAAkC,CAC7CZ,gBAAAA,EACAC,aAAAA,EACAE,yBAAAA,GARJ,GAzO2BV,EA0R7BoB,mBA1R6B,EAAApB,EA2R7BoB,cAAgB,SAACrC,GACf,IAQIlB,EARJwD,EAAwCrB,EAAKD,MAArCrB,EAAR2C,EAAQ3C,UAAW4C,EAAnBD,EAAmBC,SAAUC,EAA7BF,EAA6BE,OAEvBC,EAAiBxB,EAAKyB,mBAC1B9B,GAAyC2B,EACzC3B,GAAyC4B,EACzC5B,GAAyCjB,GAI3C,GAAI8C,EAAe9G,eAAeqE,GAChClB,EAAQ2D,EAAezC,OAClB,CACL,IAAM2C,EAAStC,EAAcY,EAAKD,MAAOhB,EAAOiB,EAAKC,gBAC/C1C,EAAO+B,EAAYU,EAAKD,MAAOhB,EAAOiB,EAAKC,gBAG3C0B,EACU,eAAdjD,GAAyC,eAAX6C,EAE1BK,EAAsB,QAAdlD,EACRmD,EAAmBF,EAAeD,EAAS,EACjDF,EAAezC,GAASlB,EAAQ,CAC9BiE,SAAU,WACVC,KAAMH,OAAQI,EAAYH,EAC1BI,MAAOL,EAAQC,OAAmBG,EAClCE,IAAMP,EAAwB,EAATD,EACrB3D,OAAS4D,EAAsB,OAAPpE,EACxBO,MAAO6D,EAAepE,EAAO,OAEhC,CAED,OAAOM,CACR,EA5T4BmC,EA8T7ByB,wBA9T6B,EAAAzB,EA+T7ByB,mBAAqBb,EAAW,SAACuB,EAAQC,EAASC,GAAlB,MAAgC,CAAC,CAAjC,GA/THrC,EAwW7BsC,oBAAsB,SAACC,GACrB,IAAAC,EAAiDD,EAAME,cAA/CrE,EAARoE,EAAQpE,YAAaS,EAArB2D,EAAqB3D,WAAY6D,EAAjCF,EAAiCE,YACjC1C,EAAK2C,SAAS,SAAAC,GACZ,GAAIA,EAAUpC,eAAiB3B,EAI7B,OAAO,KAGT,IAAQH,EAAcsB,EAAKD,MAAnBrB,UAEJ8B,EAAe3B,EACnB,GAAkB,QAAdH,EAKF,OAAQH,KACN,IAAK,WACHiC,GAAgB3B,EAChB,MACF,IAAK,sBACH2B,EAAekC,EAActE,EAAcS,EAWjD,OALA2B,EAAeqC,KAAKC,IAClB,EACAD,KAAKE,IAAIvC,EAAckC,EAActE,IAGhC,CACLkC,aAAa,EACbC,gBACEqC,EAAUpC,aAAeA,EAAe,UAAY,WACtDA,aAAAA,EACAE,0BAA0B,EAE7B,EAAEV,EAAKgD,2BACT,EAlZ4BhD,EAoZ7BiD,kBAAoB,SAACV,GACnB,IAAAW,EAAkDX,EAAME,cAAhDU,EAARD,EAAQC,aAAcC,EAAtBF,EAAsBE,aAAcC,EAApCH,EAAoCG,UACpCrD,EAAK2C,SAAS,SAAAC,GACZ,GAAIA,EAAUpC,eAAiB6C,EAI7B,OAAO,KAIT,IAAM7C,EAAeqC,KAAKC,IACxB,EACAD,KAAKE,IAAIM,EAAWD,EAAeD,IAGrC,MAAO,CACL7C,aAAa,EACbC,gBACEqC,EAAUpC,aAAeA,EAAe,UAAY,WACtDA,aAAAA,EACAE,0BAA0B,EAE7B,EAAEV,EAAKgD,2BACT,EA5a4BhD,EA8a7BsD,gBAAkB,SAACC,GACjB,IAAQC,EAAaxD,EAAKD,MAAlByD,SAERxD,EAAKE,UAAcqD,EAEK,oBAAbC,EACTA,EAASD,GAEG,MAAZC,GACoB,kBAAbA,GACPA,EAAS9I,eAAe,aAExB8I,EAASC,QAAUF,EAEtB,EA5b4BvD,EA8b7BgD,2BAA6B,WACa,OAApChD,EAAKG,4BACPtD,EAAcmD,EAAKG,4BAGrBH,EAAKG,2BAA6BlD,EAChC+C,EAAK0D,kBAngB0B,IAsgBlC,EAvc4B1D,EAyc7B0D,kBAAoB,WAClB1D,EAAKG,2BAA6B,KAElCH,EAAK2C,SAAS,CAAErC,aAAa,GAAS,WAGpCN,EAAKyB,oBAAoB,EAAG,KAC7B,EACF,EAjd4BzB,CAE5B,CA7BH9E,EAAA4E,EAAAD,GAAAC,EA+BS6D,yBAAP,SACEC,EACAhB,GAIA,OAFAiB,EAAoBD,EAAWhB,GAC/BhD,EAAcgE,GACP,IACR,EAtCH,IAAAE,EAAAhE,EAAA1E,UAAA,OAAA0I,EAwCEC,SAAA,SAASvD,GACPA,EAAeqC,KAAKC,IAAI,EAAGtC,GAE3B/D,KAAKkG,SAAS,SAAAC,GACZ,OAAIA,EAAUpC,eAAiBA,EACtB,KAEF,CACLD,gBACEqC,EAAUpC,aAAeA,EAAe,UAAY,WACtDA,aAAcA,EACdE,0BAA0B,EAE7B,EAAEjE,KAAKuG,2BACT,EAtDHc,EAwDEE,aAAA,SAAajF,EAAekF,QAAqC,IAArCA,IAAAA,EAAuB,QACjD,IAAAC,EAA8BzH,KAAKsD,MAA3BoE,EAARD,EAAQC,UAAW5C,EAAnB2C,EAAmB3C,OACXf,EAAiB/D,KAAK2D,MAAtBI,aAERzB,EAAQ8D,KAAKC,IAAI,EAAGD,KAAKE,IAAIhE,EAAOoF,EAAY,IAKhD,IAAIC,EAAgB,EACpB,GAAI3H,KAAKyD,UAAW,CAClB,IAAMsD,EAAa/G,KAAKyD,UAEtBkE,EADa,aAAX7C,EAEAiC,EAASd,YAAcc,EAASpF,YAC5BZ,IACA,EAGJgG,EAASJ,aAAeI,EAASL,aAC7B3F,IACA,CAET,CAEDf,KAAKsH,SACHxE,EACE9C,KAAKsD,MACLhB,EACAkF,EACAzD,EACA/D,KAAKwD,eACLmE,GAGL,EA3FHN,EA6FEO,kBAAA,WACE,IAAAC,EAAmD7H,KAAKsD,MAAhDrB,EAAR4F,EAAQ5F,UAAW+B,EAAnB6D,EAAmB7D,oBAAqBc,EAAxC+C,EAAwC/C,OAExC,GAAmC,kBAAxBd,GAAsD,MAAlBhE,KAAKyD,UAAmB,CACrE,IAAMsD,EAAa/G,KAAKyD,UAEN,eAAdxB,GAAyC,eAAX6C,EAChCiC,EAAS3E,WAAa4B,EAEtB+C,EAASH,UAAY5C,CAExB,CAEDhE,KAAK8H,qBACN,EA3GHT,EA6GEU,mBAAA,WACE,IAAAC,EAA8BhI,KAAKsD,MAA3BrB,EAAR+F,EAAQ/F,UAAW6C,EAAnBkD,EAAmBlD,OACnBmD,EAAmDjI,KAAK2D,MAAhDI,EAARkE,EAAQlE,aAER,GAFAkE,EAAsBhE,0BAE4B,MAAlBjE,KAAKyD,UAAmB,CACtD,IAAMsD,EAAa/G,KAAKyD,UAGxB,GAAkB,eAAdxB,GAAyC,eAAX6C,EAChC,GAAkB,QAAd7C,EAIF,OAAQH,KACN,IAAK,WACHiF,EAAS3E,YAAc2B,EACvB,MACF,IAAK,qBACHgD,EAAS3E,WAAa2B,EACtB,MACF,QACE,IAAQpC,EAA6BoF,EAA7BpF,YAAasE,EAAgBc,EAAhBd,YACrBc,EAAS3E,WAAa6D,EAActE,EAAcoC,OAItDgD,EAAS3E,WAAa2B,OAGxBgD,EAASH,UAAY7C,CAExB,CAED/D,KAAK8H,qBACN,EA/IHT,EAiJEa,qBAAA,WAC0C,OAApClI,KAAK0D,4BACPtD,EAAcJ,KAAK0D,2BAEtB,EArJH2D,EAuJEc,OAAA,WACE,IAAAC,EAiBIpI,KAAKsD,MAhBP+E,EADFD,EACEC,SACAC,EAFFF,EAEEE,UACArG,EAHFmG,EAGEnG,UACAX,EAJF8G,EAIE9G,OACAiH,EALFH,EAKEG,SACAC,EANFJ,EAMEI,iBACAC,EAPFL,EAOEK,aACAf,EARFU,EAQEV,UACAgB,EATFN,EASEM,SATFC,EAAAP,EAUEQ,QAAAA,OAVF,IAAAD,EAUYtG,EAVZsG,EAWE7D,EAXFsD,EAWEtD,OACA+D,EAZFT,EAYES,iBACAC,EAbFV,EAaEU,aACA1H,EAdFgH,EAcEhH,MACA2H,EAfFX,EAeEW,eACA1H,EAhBF+G,EAgBE/G,MAEMwC,EAAgB7D,KAAK2D,MAArBE,YAGFqB,EACU,eAAdjD,GAAyC,eAAX6C,EAE1BJ,EAAWQ,EACblF,KAAK6F,oBACL7F,KAAKwG,kBAETwC,EAAgChJ,KAAKiJ,oBAA9BC,EAAPF,EAAA,GAAmBG,EAAnBH,EAAA,GAEMI,EAAQ,GACd,GAAI1B,EAAY,EACd,IAAK,IAAI2B,EAAQH,EAAYG,GAASF,EAAWE,IAC/CD,EAAME,MACJnI,EAAAA,EAAAA,eAAckH,EAAU,CACtB9F,KAAMmG,EACNa,IAAKX,EAAQS,EAAOX,GACpBpG,MAAA+G,EACAxF,YAAakF,EAAiBlF,OAAc0B,EAC5CnE,MAAOpB,KAAK2E,cAAc0E,MAQlC,IAAMG,EAAqB5G,EACzB5C,KAAKsD,MACLtD,KAAKwD,gBAGP,OAAOrC,EAAAA,EAAAA,eACL0H,GAAoBC,GAAgB,MACpC,CACER,UAAAA,EACA5D,SAAAA,EACAoC,IAAK9G,KAAK6G,gBACVzF,MAAK7D,EAAA,CACH8H,SAAU,WACV/D,OAAAA,EACAD,MAAAA,EACAE,SAAU,OACVkI,wBAAyB,QACzBC,WAAY,YACZzH,UAAAA,GACGb,KAGPD,EAAAA,EAAAA,eAAcqH,GAAoBC,GAAgB,MAAO,CACvDJ,SAAUe,EACVtC,IAAKyB,EACLnH,MAAO,CACLE,OAAQ4D,EAAe,OAASsE,EAChCG,cAAe9F,EAAc,YAAS0B,EACtClE,MAAO6D,EAAesE,EAAqB,UAIlD,EAvOHnC,EAgRES,oBAAA,WACE,GAA0C,oBAA/B9H,KAAKsD,MAAMkB,iBACExE,KAAKsD,MAAnBoE,UACQ,EAAG,CACjB,IAAAkC,EAKI5J,KAAKiJ,oBAJPY,EADFD,EAAA,GAEEE,EAFFF,EAAA,GAGEG,EAHFH,EAAA,GAIEI,EAJFJ,EAAA,GAMA5J,KAAKkE,qBACH2F,EACAC,EACAC,EACAC,EAEH,CAGH,GAAmC,oBAAxBhK,KAAKsD,MAAMoB,SAAyB,CAC7C,IAAAuF,EAIIjK,KAAK2D,MAHPuG,EADFD,EACEnG,gBACAqG,EAFFF,EAEElG,aACAqG,EAHFH,EAGEhG,yBAEFjE,KAAKyE,cACHyF,EACAC,EACAC,EAEH,CACF,EA/SH/C,EA4VE4B,kBAAA,WACE,IAAAoB,EAAqCrK,KAAKsD,MAAlCoE,EAAR2C,EAAQ3C,UAAW4C,EAAnBD,EAAmBC,cACnBC,EAAuDvK,KAAK2D,MAApDE,EAAR0G,EAAQ1G,YAAaC,EAArByG,EAAqBzG,gBAAiBC,EAAtCwG,EAAsCxG,aAEtC,GAAkB,IAAd2D,EACF,MAAO,CAAC,EAAG,EAAG,EAAG,GAGnB,IAAMwB,EAAanG,EACjB/C,KAAKsD,MACLS,EACA/D,KAAKwD,gBAED2F,EAAYnG,EAChBhD,KAAKsD,MACL4F,EACAnF,EACA/D,KAAKwD,gBAKDgH,EACH3G,GAAmC,aAApBC,EAEZ,EADAsC,KAAKC,IAAI,EAAGiE,GAEZG,EACH5G,GAAmC,YAApBC,EAEZ,EADAsC,KAAKC,IAAI,EAAGiE,GAGlB,MAAO,CACLlE,KAAKC,IAAI,EAAG6C,EAAasB,GACzBpE,KAAKC,IAAI,EAAGD,KAAKE,IAAIoB,EAAY,EAAGyB,EAAYsB,IAChDvB,EACAC,EAEH,EAjYH9F,CAAA,EAA6BqH,EAAAA,eAA7BhI,EAKSiI,aAAe,CACpB1I,UAAW,MACXyG,cAAUnD,EACVT,OAAQ,WACRwF,cAAe,EACfvB,gBAAgB,GAVpBrG,CA8eD,CAQD,IAAM0E,EAAsB,SAAAwD,EAAAC,GAWjBD,EATPvC,SASOuC,EARP3I,UAQO2I,EAPPtJ,OAOOsJ,EANP9F,OAMO8F,EALPnC,aAKOmC,EAJP9B,aAIO8B,EAHPvJ,MAGOwJ,EADPjH,QA0EH,EChuBKkH,EAAgBtI,EAAoB,CACxCG,cAAe,SAAAF,EAA2BH,GAA3B,OACbA,EADaG,EAAGoC,QAAH,EAGfhC,YAAa,SAAA+H,EAA2BtI,GAA3B,OAAAsI,EAAG/F,QAAH,EAGbjC,sBAAuB,SAAAiI,GAAA,IAAGnD,EAAHmD,EAAGnD,UAAH,OAAAmD,EAAchG,SACP6C,CADP,EAGvB5E,8BAA+B,SAAAiI,EAE7BzI,EACAkF,EACAzD,EACAiH,EACArD,GACW,IANT1F,EAMS8I,EANT9I,UAAWX,EAMFyJ,EANEzJ,OAAQoG,EAMVqD,EANUrD,UAAW7C,EAMrBkG,EANqBlG,SAAUC,EAM/BiG,EAN+BjG,OAAQzD,EAMvC0J,EANuC1J,MAS5CP,EAD6B,eAAdmB,GAAyC,eAAX6C,EACpBzD,EAAQC,EACjC2J,EAAiB7E,KAAKC,IAC1B,EACAqB,EAAc7C,EAA0B/D,GAEpCoK,EAAY9E,KAAKE,IACrB2E,EACA3I,EAAUuC,GAENsG,EAAY/E,KAAKC,IACrB,EACA/D,EAAUuC,EACR/D,EACE+D,EACF8C,GAcJ,OAXc,UAAVH,IAKAA,EAHAzD,GAAgBoH,EAAYrK,GAC5BiD,GAAgBmH,EAAYpK,EAEpB,OAEA,UAIJ0G,GACN,IAAK,QACH,OAAO0D,EACT,IAAK,MACH,OAAOC,EACT,IAAK,SAGH,IAAMC,EAAehF,KAAKiF,MACxBF,GAAaD,EAAYC,GAAa,GAExC,OAAIC,EAAehF,KAAKkF,KAAKxK,EAAO,GAC3B,EACEsK,EAAeH,EAAiB7E,KAAKmF,MAAMzK,EAAO,GACpDmK,EAEAG,EAIX,QACE,OAAIrH,GAAgBoH,GAAapH,GAAgBmH,EACxCnH,EACEA,EAAeoH,EACjBA,EAEAD,EAGd,EAEDnI,uBAAwB,SAAAyI,EAEtBC,GAFsB,IACpB/D,EADoB8D,EACpB9D,UAAW7C,EADS2G,EACT3G,SADS,OAItBuB,KAAKC,IACH,EACAD,KAAKE,IAAIoB,EAAY,EAAGtB,KAAKmF,MAAME,EAAW5G,IAN1B,EASxB7B,0BAA2B,SAAA0I,EAEzBxC,EACAnF,GACW,IAHT9B,EAGSyJ,EAHTzJ,UAAWX,EAGFoK,EAHEpK,OAAQoG,EAGVgE,EAHUhE,UAAW7C,EAGrB6G,EAHqB7G,SAAUC,EAG/B4G,EAH+B5G,OAAQzD,EAGvCqK,EAHuCrK,MAM5CoK,EAASvC,EAAerE,EACxB/D,EAF6B,eAAdmB,GAAyC,eAAX6C,EAEpBzD,EAAQC,EACjCqK,EAAkBvF,KAAKkF,MAC1BxK,EAAOiD,EAAe0H,GAAY5G,GAErC,OAAOuB,KAAKC,IACV,EACAD,KAAKE,IACHoB,EAAY,EACZwB,EAAayC,EAAkB,GAGpC,EAED1I,kBA7GwC,SA6GtBK,GAAwB,EAI1CJ,uCAAuC,EAEvCC,cAAe,SAAAyI,GAAoCA,EAAjC/G,QAUjB,G", "sources": ["../../node_modules/@babel/runtime/helpers/esm/extends.js", "../../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "../../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../../node_modules/memoize-one/dist/memoize-one.esm.js", "../../node_modules/react-window/src/timer.js", "../../node_modules/react-window/src/domHelpers.js", "../../node_modules/react-window/src/createGridComponent.js", "../../node_modules/react-window/src/createListComponent.js", "../../node_modules/react-window/src/FixedSizeList.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "// @flow\n\n// Animation frame based implementation of setTimeout.\n// Inspired by <PERSON>, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\n\nconst hasNativePerformanceNow =\n  typeof performance === 'object' && typeof performance.now === 'function';\n\nconst now = hasNativePerformanceNow\n  ? () => performance.now()\n  : () => Date.now();\n\nexport type TimeoutID = {|\n  id: AnimationFrameID,\n|};\n\nexport function cancelTimeout(timeoutID: TimeoutID) {\n  cancelAnimationFrame(timeoutID.id);\n}\n\nexport function requestTimeout(callback: Function, delay: number): TimeoutID {\n  const start = now();\n\n  function tick() {\n    if (now() - start >= delay) {\n      callback.call(null);\n    } else {\n      timeoutID.id = requestAnimationFrame(tick);\n    }\n  }\n\n  const timeoutID: TimeoutID = {\n    id: requestAnimationFrame(tick),\n  };\n\n  return timeoutID;\n}\n", "// @flow\n\nlet size: number = -1;\n\n// This utility copied from \"dom-helpers\" package.\nexport function getScrollbarSize(recalculate?: boolean = false): number {\n  if (size === -1 || recalculate) {\n    const div = document.createElement('div');\n    const style = div.style;\n    style.width = '50px';\n    style.height = '50px';\n    style.overflow = 'scroll';\n\n    ((document.body: any): HTMLBodyElement).appendChild(div);\n\n    size = div.offsetWidth - div.clientWidth;\n\n    ((document.body: any): HTMLBodyElement).removeChild(div);\n  }\n\n  return size;\n}\n\nexport type RTLOffsetType =\n  | 'negative'\n  | 'positive-descending'\n  | 'positive-ascending';\n\nlet cachedRTLResult: RTLOffsetType | null = null;\n\n// TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// <PERSON><PERSON>'s elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nexport function getRTLOffsetType(recalculate?: boolean = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div');\n    const outerStyle = outerDiv.style;\n    outerStyle.width = '50px';\n    outerStyle.height = '50px';\n    outerStyle.overflow = 'scroll';\n    outerStyle.direction = 'rtl';\n\n    const innerDiv = document.createElement('div');\n    const innerStyle = innerDiv.style;\n    innerStyle.width = '100px';\n    innerStyle.height = '100px';\n\n    outerDiv.appendChild(innerDiv);\n\n    ((document.body: any): HTMLBodyElement).appendChild(outerDiv);\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = 'positive-descending';\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = 'negative';\n      } else {\n        cachedRTLResult = 'positive-ascending';\n      }\n    }\n\n    ((document.body: any): HTMLBodyElement).removeChild(outerDiv);\n\n    return cachedRTLResult;\n  }\n\n  return cachedRTLResult;\n}\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\ntype Direction = 'ltr' | 'rtl';\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n\ntype RenderComponentProps<T> = {|\n  columnIndex: number,\n  data: T,\n  isScrolling?: boolean,\n  rowIndex: number,\n  style: Object,\n|};\nexport type RenderComponent<T> = React$ComponentType<\n  $Shape<RenderComponentProps<T>>\n>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype OnItemsRenderedCallback = ({\n  overscanColumnStartIndex: number,\n  overscanColumnStopIndex: number,\n  overscanRowStartIndex: number,\n  overscanRowStopIndex: number,\n  visibleColumnStartIndex: number,\n  visibleColumnStopIndex: number,\n  visibleRowStartIndex: number,\n  visibleRowStopIndex: number,\n}) => void;\ntype OnScrollCallback = ({\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [key: string]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  columnCount: number,\n  columnWidth: itemSize,\n  direction: Direction,\n  height: number,\n  initialScrollLeft?: number,\n  initialScrollTop?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemData: T,\n  itemKey?: (params: {|\n    columnIndex: number,\n    data: T,\n    rowIndex: number,\n  |}) => any,\n  onItemsRendered?: OnItemsRenderedCallback,\n  onScroll?: OnScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanColumnCount?: number,\n  overscanColumnsCount?: number, // deprecated\n  overscanCount?: number, // deprecated\n  overscanRowCount?: number,\n  overscanRowsCount?: number, // deprecated\n  rowCount: number,\n  rowHeight: itemSize,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n|};\n\ntype getItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForItemAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any,\n  scrollbarSize: number\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = ({ columnIndex, data, rowIndex }) =>\n  `${rowIndex}:${columnIndex}`;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsOverscanCount = null;\nlet devWarningsOverscanRowsColumnsCount = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsOverscanCount = new WeakSet();\n    devWarningsOverscanRowsColumnsCount = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createGridComponent({\n  getColumnOffset,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getColumnWidth,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getOffsetForColumnAndAlignment,\n  getOffsetForRowAndAlignment,\n  getRowHeight,\n  getRowOffset,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getColumnOffset: getItemOffset,\n  getColumnStartIndexForOffset: GetStartIndexForOffset,\n  getColumnStopIndexForStartIndex: GetStopIndexForStartIndex,\n  getColumnWidth: getItemSize,\n  getEstimatedTotalHeight: getEstimatedTotalSize,\n  getEstimatedTotalWidth: getEstimatedTotalSize,\n  getOffsetForColumnAndAlignment: GetOffsetForItemAndAlignment,\n  getOffsetForRowAndAlignment: GetOffsetForItemAndAlignment,\n  getRowOffset: getItemOffset,\n  getRowHeight: getItemSize,\n  getRowStartIndexForOffset: GetStartIndexForOffset,\n  getRowStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class Grid<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n    _outerRef: ?HTMLDivElement;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      horizontalScrollDirection: 'forward',\n      scrollLeft:\n        typeof this.props.initialScrollLeft === 'number'\n          ? this.props.initialScrollLeft\n          : 0,\n      scrollTop:\n        typeof this.props.initialScrollTop === 'number'\n          ? this.props.initialScrollTop\n          : 0,\n      scrollUpdateWasRequested: false,\n      verticalScrollDirection: 'forward',\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo({\n      scrollLeft,\n      scrollTop,\n    }: {\n      scrollLeft: number,\n      scrollTop: number,\n    }): void {\n      if (scrollLeft !== undefined) {\n        scrollLeft = Math.max(0, scrollLeft);\n      }\n      if (scrollTop !== undefined) {\n        scrollTop = Math.max(0, scrollTop);\n      }\n\n      this.setState(prevState => {\n        if (scrollLeft === undefined) {\n          scrollLeft = prevState.scrollLeft;\n        }\n        if (scrollTop === undefined) {\n          scrollTop = prevState.scrollTop;\n        }\n\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          return null;\n        }\n\n        return {\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          scrollUpdateWasRequested: true,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem({\n      align = 'auto',\n      columnIndex,\n      rowIndex,\n    }: {\n      align: ScrollToAlign,\n      columnIndex?: number,\n      rowIndex?: number,\n    }): void {\n      const { columnCount, height, rowCount, width } = this.props;\n      const { scrollLeft, scrollTop } = this.state;\n      const scrollbarSize = getScrollbarSize();\n\n      if (columnIndex !== undefined) {\n        columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n      }\n      if (rowIndex !== undefined) {\n        rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n      }\n\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      // The scrollbar size should be considered when scrolling an item into view,\n      // to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      const horizontalScrollbarSize =\n        estimatedTotalWidth > width ? scrollbarSize : 0;\n      const verticalScrollbarSize =\n        estimatedTotalHeight > height ? scrollbarSize : 0;\n\n      this.scrollTo({\n        scrollLeft:\n          columnIndex !== undefined\n            ? getOffsetForColumnAndAlignment(\n                this.props,\n                columnIndex,\n                align,\n                scrollLeft,\n                this._instanceProps,\n                verticalScrollbarSize\n              )\n            : scrollLeft,\n        scrollTop:\n          rowIndex !== undefined\n            ? getOffsetForRowAndAlignment(\n                this.props,\n                rowIndex,\n                align,\n                scrollTop,\n                this._instanceProps,\n                horizontalScrollbarSize\n              )\n            : scrollTop,\n      });\n    }\n\n    componentDidMount() {\n      const { initialScrollLeft, initialScrollTop } = this.props;\n\n      if (this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (typeof initialScrollLeft === 'number') {\n          outerRef.scrollLeft = initialScrollLeft;\n        }\n        if (typeof initialScrollTop === 'number') {\n          outerRef.scrollTop = initialScrollTop;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction } = this.props;\n      const { scrollLeft, scrollTop, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // So we need to determine which browser behavior we're dealing with, and mimic it.\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              outerRef.scrollLeft = -scrollLeft;\n              break;\n            case 'positive-ascending':\n              outerRef.scrollLeft = scrollLeft;\n              break;\n            default:\n              const { clientWidth, scrollWidth } = outerRef;\n              outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        } else {\n          outerRef.scrollLeft = Math.max(0, scrollLeft);\n        }\n\n        outerRef.scrollTop = Math.max(0, scrollTop);\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        columnCount,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemData,\n        itemKey = defaultItemKey,\n        outerElementType,\n        outerTagName,\n        rowCount,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      const [\n        columnStartIndex,\n        columnStopIndex,\n      ] = this._getHorizontalRangeToRender();\n      const [rowStartIndex, rowStopIndex] = this._getVerticalRangeToRender();\n\n      const items = [];\n      if (columnCount > 0 && rowCount) {\n        for (\n          let rowIndex = rowStartIndex;\n          rowIndex <= rowStopIndex;\n          rowIndex++\n        ) {\n          for (\n            let columnIndex = columnStartIndex;\n            columnIndex <= columnStopIndex;\n            columnIndex++\n          ) {\n            items.push(\n              createElement(children, {\n                columnIndex,\n                data: itemData,\n                isScrolling: useIsScrolling ? isScrolling : undefined,\n                key: itemKey({ columnIndex, data: itemData, rowIndex }),\n                rowIndex,\n                style: this._getItemStyle(rowIndex, columnIndex),\n              })\n            );\n          }\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll: this._onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: estimatedTotalHeight,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: estimatedTotalWidth,\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanColumnStartIndex: number,\n      overscanColumnStopIndex: number,\n      overscanRowStartIndex: number,\n      overscanRowStopIndex: number,\n      visibleColumnStartIndex: number,\n      visibleColumnStopIndex: number,\n      visibleRowStartIndex: number,\n      visibleRowStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanColumnStartIndex: number,\n        overscanColumnStopIndex: number,\n        overscanRowStartIndex: number,\n        overscanRowStopIndex: number,\n        visibleColumnStartIndex: number,\n        visibleColumnStopIndex: number,\n        visibleRowStartIndex: number,\n        visibleRowStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): OnItemsRenderedCallback)({\n          overscanColumnStartIndex,\n          overscanColumnStopIndex,\n          overscanRowStartIndex,\n          overscanRowStopIndex,\n          visibleColumnStartIndex,\n          visibleColumnStopIndex,\n          visibleRowStartIndex,\n          visibleRowStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollLeft: number,\n      scrollTop: number,\n      horizontalScrollDirection: ScrollDirection,\n      verticalScrollDirection: ScrollDirection,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollLeft: number,\n        scrollTop: number,\n        horizontalScrollDirection: ScrollDirection,\n        verticalScrollDirection: ScrollDirection,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): OnScrollCallback)({\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          verticalScrollDirection,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      const { columnCount, onItemsRendered, onScroll, rowCount } = this.props;\n\n      if (typeof onItemsRendered === 'function') {\n        if (columnCount > 0 && rowCount > 0) {\n          const [\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n          ] = this._getHorizontalRangeToRender();\n          const [\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex,\n          ] = this._getVerticalRangeToRender();\n          this._callOnItemsRendered(\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex\n          );\n        }\n      }\n\n      if (typeof onScroll === 'function') {\n        const {\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          scrollUpdateWasRequested,\n          verticalScrollDirection,\n        } = this.state;\n        this._callOnScroll(\n          scrollLeft,\n          scrollTop,\n          horizontalScrollDirection,\n          verticalScrollDirection,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (rowIndex: number, columnIndex: number) => Object;\n    _getItemStyle = (rowIndex: number, columnIndex: number): Object => {\n      const { columnWidth, direction, rowHeight } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && columnWidth,\n        shouldResetStyleCacheOnItemSizeChange && direction,\n        shouldResetStyleCacheOnItemSizeChange && rowHeight\n      );\n\n      const key = `${rowIndex}:${columnIndex}`;\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(key)) {\n        style = itemStyleCache[key];\n      } else {\n        const offset = getColumnOffset(\n          this.props,\n          columnIndex,\n          this._instanceProps\n        );\n        const isRtl = direction === 'rtl';\n        itemStyleCache[key] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offset,\n          right: isRtl ? offset : undefined,\n          top: getRowOffset(this.props, rowIndex, this._instanceProps),\n          height: getRowHeight(this.props, rowIndex, this._instanceProps),\n          width: getColumnWidth(this.props, columnIndex, this._instanceProps),\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getHorizontalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanColumnCount,\n        overscanColumnsCount,\n        overscanCount,\n        rowCount,\n      } = this.props;\n      const { horizontalScrollDirection, isScrolling, scrollLeft } = this.state;\n\n      const overscanCountResolved: number =\n        overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getColumnStartIndexForOffset(\n        this.props,\n        scrollLeft,\n        this._instanceProps\n      );\n      const stopIndex = getColumnStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollLeft,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || horizontalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || horizontalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _getVerticalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanCount,\n        overscanRowCount,\n        overscanRowsCount,\n        rowCount,\n      } = this.props;\n      const { isScrolling, verticalScrollDirection, scrollTop } = this.state;\n\n      const overscanCountResolved: number =\n        overscanRowCount || overscanRowsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getRowStartIndexForOffset(\n        this.props,\n        scrollTop,\n        this._instanceProps\n      );\n      const stopIndex = getRowStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollTop,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || verticalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || verticalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScroll = (event: ScrollEvent): void => {\n      const {\n        clientHeight,\n        clientWidth,\n        scrollLeft,\n        scrollTop,\n        scrollHeight,\n        scrollWidth,\n      } = event.currentTarget;\n      this.setState(prevState => {\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n        let calculatedScrollLeft = scrollLeft;\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              calculatedScrollLeft = -scrollLeft;\n              break;\n            case 'positive-descending':\n              calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        calculatedScrollLeft = Math.max(\n          0,\n          Math.min(calculatedScrollLeft, scrollWidth - clientWidth)\n        );\n        const calculatedScrollTop = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: calculatedScrollTop,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1);\n      });\n    };\n  };\n}\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    innerTagName,\n    outerTagName,\n    overscanColumnsCount,\n    overscanCount,\n    overscanRowsCount,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof overscanCount === 'number') {\n      if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n        devWarningsOverscanCount.add(instance);\n        console.warn(\n          'The overscanCount prop has been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (\n      typeof overscanColumnsCount === 'number' ||\n      typeof overscanRowsCount === 'number'\n    ) {\n      if (\n        devWarningsOverscanRowsColumnsCount &&\n        !devWarningsOverscanRowsColumnsCount.has(instance)\n      ) {\n        devWarningsOverscanRowsColumnsCount.add(instance);\n        console.warn(\n          'The overscanColumnsCount and overscanRowsCount props have been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    switch (direction) {\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    if (typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Grids must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    }\n\n    if (typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Grids must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n// TODO Deprecate directions \"horizontal\" and \"vertical\"\ntype Direction = 'ltr' | 'rtl' | 'horizontal' | 'vertical';\ntype Layout = 'horizontal' | 'vertical';\n\ntype RenderComponentProps<T> = {|\n  data: T,\n  index: number,\n  isScrolling?: boolean,\n  style: Object,\n|};\ntype RenderComponent<T> = React$ComponentType<$Shape<RenderComponentProps<T>>>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype onItemsRenderedCallback = ({\n  overscanStartIndex: number,\n  overscanStopIndex: number,\n  visibleStartIndex: number,\n  visibleStopIndex: number,\n}) => void;\ntype onScrollCallback = ({\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [index: number]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  direction: Direction,\n  height: number | string,\n  initialScrollOffset?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemCount: number,\n  itemData: T,\n  itemKey?: (index: number, data: T) => any,\n  itemSize: itemSize,\n  layout: Layout,\n  onItemsRendered?: onItemsRenderedCallback,\n  onScroll?: onScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanCount: number,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number | string,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n|};\n\ntype GetItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForIndexAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = (index: number, data: any) => index;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsDirection = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsDirection = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createListComponent({\n  getItemOffset,\n  getEstimatedTotalSize,\n  getItemSize,\n  getOffsetForIndexAndAlignment,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getItemOffset: GetItemOffset,\n  getEstimatedTotalSize: GetEstimatedTotalSize,\n  getItemSize: GetItemSize,\n  getOffsetForIndexAndAlignment: GetOffsetForIndexAndAlignment,\n  getStartIndexForOffset: GetStartIndexForOffset,\n  getStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class List<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _outerRef: ?HTMLDivElement;\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      layout: 'vertical',\n      overscanCount: 2,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      scrollDirection: 'forward',\n      scrollOffset:\n        typeof this.props.initialScrollOffset === 'number'\n          ? this.props.initialScrollOffset\n          : 0,\n      scrollUpdateWasRequested: false,\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo(scrollOffset: number): void {\n      scrollOffset = Math.max(0, scrollOffset);\n\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollOffset) {\n          return null;\n        }\n        return {\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset: scrollOffset,\n          scrollUpdateWasRequested: true,\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem(index: number, align: ScrollToAlign = 'auto'): void {\n      const { itemCount, layout } = this.props;\n      const { scrollOffset } = this.state;\n\n      index = Math.max(0, Math.min(index, itemCount - 1));\n\n      // The scrollbar size should be considered when scrolling an item into view, to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      // This is an edge case for lists; normally they only scroll in the dominant direction.\n      let scrollbarSize = 0;\n      if (this._outerRef) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (layout === 'vertical') {\n          scrollbarSize =\n            outerRef.scrollWidth > outerRef.clientWidth\n              ? getScrollbarSize()\n              : 0;\n        } else {\n          scrollbarSize =\n            outerRef.scrollHeight > outerRef.clientHeight\n              ? getScrollbarSize()\n              : 0;\n        }\n      }\n\n      this.scrollTo(\n        getOffsetForIndexAndAlignment(\n          this.props,\n          index,\n          align,\n          scrollOffset,\n          this._instanceProps,\n          scrollbarSize\n        )\n      );\n    }\n\n    componentDidMount() {\n      const { direction, initialScrollOffset, layout } = this.props;\n\n      if (typeof initialScrollOffset === 'number' && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          outerRef.scrollLeft = initialScrollOffset;\n        } else {\n          outerRef.scrollTop = initialScrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction, layout } = this.props;\n      const { scrollOffset, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          if (direction === 'rtl') {\n            // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n            // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n            // So we need to determine which browser behavior we're dealing with, and mimic it.\n            switch (getRTLOffsetType()) {\n              case 'negative':\n                outerRef.scrollLeft = -scrollOffset;\n                break;\n              case 'positive-ascending':\n                outerRef.scrollLeft = scrollOffset;\n                break;\n              default:\n                const { clientWidth, scrollWidth } = outerRef;\n                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                break;\n            }\n          } else {\n            outerRef.scrollLeft = scrollOffset;\n          }\n        } else {\n          outerRef.scrollTop = scrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemCount,\n        itemData,\n        itemKey = defaultItemKey,\n        layout,\n        outerElementType,\n        outerTagName,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      // TODO Deprecate direction \"horizontal\"\n      const isHorizontal =\n        direction === 'horizontal' || layout === 'horizontal';\n\n      const onScroll = isHorizontal\n        ? this._onScrollHorizontal\n        : this._onScrollVertical;\n\n      const [startIndex, stopIndex] = this._getRangeToRender();\n\n      const items = [];\n      if (itemCount > 0) {\n        for (let index = startIndex; index <= stopIndex; index++) {\n          items.push(\n            createElement(children, {\n              data: itemData,\n              key: itemKey(index, itemData),\n              index,\n              isScrolling: useIsScrolling ? isScrolling : undefined,\n              style: this._getItemStyle(index),\n            })\n          );\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalSize = getEstimatedTotalSize(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: isHorizontal ? '100%' : estimatedTotalSize,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: isHorizontal ? estimatedTotalSize : '100%',\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanStartIndex: number,\n      overscanStopIndex: number,\n      visibleStartIndex: number,\n      visibleStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanStartIndex: number,\n        overscanStopIndex: number,\n        visibleStartIndex: number,\n        visibleStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): onItemsRenderedCallback)({\n          overscanStartIndex,\n          overscanStopIndex,\n          visibleStartIndex,\n          visibleStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollDirection: ScrollDirection,\n      scrollOffset: number,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollDirection: ScrollDirection,\n        scrollOffset: number,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): onScrollCallback)({\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      if (typeof this.props.onItemsRendered === 'function') {\n        const { itemCount } = this.props;\n        if (itemCount > 0) {\n          const [\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex,\n          ] = this._getRangeToRender();\n          this._callOnItemsRendered(\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex\n          );\n        }\n      }\n\n      if (typeof this.props.onScroll === 'function') {\n        const {\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        } = this.state;\n        this._callOnScroll(\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (index: number) => Object;\n    _getItemStyle = (index: number): Object => {\n      const { direction, itemSize, layout } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && itemSize,\n        shouldResetStyleCacheOnItemSizeChange && layout,\n        shouldResetStyleCacheOnItemSizeChange && direction\n      );\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(index)) {\n        style = itemStyleCache[index];\n      } else {\n        const offset = getItemOffset(this.props, index, this._instanceProps);\n        const size = getItemSize(this.props, index, this._instanceProps);\n\n        // TODO Deprecate direction \"horizontal\"\n        const isHorizontal =\n          direction === 'horizontal' || layout === 'horizontal';\n\n        const isRtl = direction === 'rtl';\n        const offsetHorizontal = isHorizontal ? offset : 0;\n        itemStyleCache[index] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offsetHorizontal,\n          right: isRtl ? offsetHorizontal : undefined,\n          top: !isHorizontal ? offset : 0,\n          height: !isHorizontal ? size : '100%',\n          width: isHorizontal ? size : '100%',\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getRangeToRender(): [number, number, number, number] {\n      const { itemCount, overscanCount } = this.props;\n      const { isScrolling, scrollDirection, scrollOffset } = this.state;\n\n      if (itemCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getStartIndexForOffset(\n        this.props,\n        scrollOffset,\n        this._instanceProps\n      );\n      const stopIndex = getStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollOffset,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || scrollDirection === 'backward'\n          ? Math.max(1, overscanCount)\n          : 1;\n      const overscanForward =\n        !isScrolling || scrollDirection === 'forward'\n          ? Math.max(1, overscanCount)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScrollHorizontal = (event: ScrollEvent): void => {\n      const { clientWidth, scrollLeft, scrollWidth } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollLeft) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        let scrollOffset = scrollLeft;\n        if (direction === 'rtl') {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              scrollOffset = -scrollLeft;\n              break;\n            case 'positive-descending':\n              scrollOffset = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _onScrollVertical = (event: ScrollEvent): void => {\n      const { clientHeight, scrollHeight, scrollTop } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollTop) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1, null);\n      });\n    };\n  };\n}\n\n// NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    layout,\n    innerTagName,\n    outerTagName,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n\n    switch (direction) {\n      case 'horizontal':\n      case 'vertical':\n        if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n          devWarningsDirection.add(instance);\n          console.warn(\n            'The direction prop should be either \"ltr\" (default) or \"rtl\". ' +\n              'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.'\n          );\n        }\n        break;\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    switch (layout) {\n      case 'horizontal':\n      case 'vertical':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"layout\" prop has been specified. ' +\n            'Value should be either \"horizontal\" or \"vertical\". ' +\n            `\"${layout}\" was specified.`\n        );\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    if (isHorizontal && typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Horizontal lists must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    } else if (!isHorizontal && typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Vertical lists must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Props, ScrollToAlign } from './createListComponent';\n\ntype InstanceProps = any;\n\nconst FixedSizeList = createListComponent({\n  getItemOffset: ({ itemSize }: Props<any>, index: number): number =>\n    index * ((itemSize: any): number),\n\n  getItemSize: ({ itemSize }: Props<any>, index: number): number =>\n    ((itemSize: any): number),\n\n  getEstimatedTotalSize: ({ itemCount, itemSize }: Props<any>) =>\n    ((itemSize: any): number) * itemCount,\n\n  getOffsetForIndexAndAlignment: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const lastItemOffset = Math.max(\n      0,\n      itemCount * ((itemSize: any): number) - size\n    );\n    const maxOffset = Math.min(\n      lastItemOffset,\n      index * ((itemSize: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      index * ((itemSize: any): number) -\n        size +\n        ((itemSize: any): number) +\n        scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center': {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      }\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    { itemCount, itemSize }: Props<any>,\n    offset: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(itemCount - 1, Math.floor(offset / ((itemSize: any): number)))\n    ),\n\n  getStopIndexForStartIndex: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    startIndex: number,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const offset = startIndex * ((itemSize: any): number);\n    const size = (((isHorizontal ? width : height): any): number);\n    const numVisibleItems = Math.ceil(\n      (size + scrollOffset - offset) / ((itemSize: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        itemCount - 1,\n        startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'number') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeList;\n"], "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_assertThisInitialized", "ReferenceError", "_setPrototypeOf", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "o", "prototype", "create", "constructor", "safeIsNaN", "Number", "isNaN", "value", "isEqual", "first", "second", "areInputsEqual", "newInputs", "lastInputs", "i", "resultFn", "lastThis", "lastResult", "lastArgs", "calledOnce", "newArgs", "_i", "this", "now", "performance", "Date", "cancelTimeout", "timeoutID", "cancelAnimationFrame", "id", "requestTimeout", "callback", "delay", "start", "requestAnimationFrame", "tick", "size", "getScrollbarSize", "recalculate", "div", "document", "createElement", "style", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "cachedRTLResult", "getRTLOffsetType", "outerDiv", "outerStyle", "direction", "innerDiv", "innerStyle", "scrollLeft", "defaultItemKey$1", "index", "data", "createListComponent", "_ref", "_class", "getItemOffset", "getEstimatedTotalSize", "getItemSize", "getOffsetForIndexAndAlignment", "getStartIndexForOffset", "getStopIndexForStartIndex", "initInstanceProps", "shouldResetStyleCacheOnItemSizeChange", "validateProps", "_PureComponent", "List", "props", "_this", "_instanceProps", "_outerRef", "_resetIsScrollingTimeoutId", "state", "instance", "isScrolling", "scrollDirection", "scrollOffset", "initialScrollOffset", "scrollUpdateWasRequested", "_callOnItemsRendered", "memoizeOne", "overscanStartIndex", "overscanStopIndex", "visibleStartIndex", "visibleStopIndex", "onItemsRendered", "_callOnScroll", "onScroll", "_getItemStyle", "_this$props", "itemSize", "layout", "itemStyleCache", "_getItemStyleCache", "_offset", "isHorizontal", "isRtl", "offsetHorizontal", "position", "left", "undefined", "right", "top", "_", "__", "___", "_onScrollHorizontal", "event", "_event$currentTarget", "currentTarget", "scrollWidth", "setState", "prevState", "Math", "max", "min", "_resetIsScrollingDebounced", "_onScrollVertical", "_event$currentTarget2", "clientHeight", "scrollHeight", "scrollTop", "_outerRefSetter", "ref", "outerRef", "current", "_resetIsScrolling", "getDerivedStateFromProps", "nextProps", "validateSharedProps$1", "_proto", "scrollTo", "scrollToItem", "align", "_this$props2", "itemCount", "scrollbarSize", "componentDidMount", "_this$props3", "_callPropsCallbacks", "componentDidUpdate", "_this$props4", "_this$state", "componentWillUnmount", "render", "_this$props5", "children", "className", "innerRef", "innerElementType", "innerTagName", "itemData", "_this$props5$itemKey", "itemKey", "outerElementType", "outerTagName", "useIsScrolling", "_this$_getRangeToRend", "_getRangeToRender", "startIndex", "stopIndex", "items", "_index", "push", "key", "estimatedTotalSize", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "_this$_getRangeToRend2", "_overscanStartIndex", "_overscanStopIndex", "_visibleStartIndex", "_visibleStopIndex", "_this$state2", "_scrollDirection", "_scrollOffset", "_scrollUpdateWasRequested", "_this$props6", "overscanCount", "_this$state3", "overscanBackward", "overscanForward", "PureComponent", "defaultProps", "_ref2", "_ref3", "FixedSizeList", "_ref4", "instanceProps", "lastItemOffset", "maxOffset", "minOffset", "middleOffset", "round", "ceil", "floor", "_ref5", "offset", "_ref6", "numVisibleItems", "_ref7"], "sourceRoot": ""}