{"version": 3, "file": "static/js/645.0759e18b.chunk.js", "mappings": "sKAWA,MAAMA,EAAoB,CACxB,CACEC,MAAOC,EAAAA,GAAgBC,KACvBC,MAAO,OACPC,YAAa,+BAEf,CACEJ,MAAOC,EAAAA,GAAgBI,OACvBF,MAAO,SACPC,YAAa,mCAEf,CACEJ,MAAOC,EAAAA,GAAgBK,KACvBH,MAAO,OACPC,YAAa,uCAEf,CACEJ,MAAOC,EAAAA,GAAgBM,QACvBJ,MAAO,UACPC,YAAa,kCAEf,CACEJ,MAAOC,EAAAA,GAAgBO,SACvBL,MAAO,WACPC,YAAa,2BAEf,CACEJ,MAAOC,EAAAA,GAAgBQ,IACvBN,MAAO,MACPC,YAAa,6BA0CJM,EAAwDC,IAM9D,IAN+D,MACpEX,EAAK,SACLY,EAAQ,UACRC,EAAY,GAAE,SACdC,GAAW,EAAK,MAChBX,EAAQ,oBACTQ,EACC,OACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAeH,GAAYI,SAAA,EACvCC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,8CAA6CI,SAC3Dd,KAEHe,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wCAAuCI,SACnDlB,EAAkBoB,IAAKC,IACtB,MAAMC,EAAarB,IAAUoB,EAAOpB,MAC9BsB,EAAeD,EAlCKE,KAClC,OAAQA,GACN,KAAKtB,EAAAA,GAAgBC,KACnB,MAAO,gFACT,KAAKD,EAAAA,GAAgBI,OACnB,MAAO,wFACT,KAAKJ,EAAAA,GAAgBK,KACnB,MAAO,oFACT,KAAKL,EAAAA,GAAgBM,QACnB,MAAO,oFACT,KAAKN,EAAAA,GAAgBO,SACnB,MAAO,wEACT,KAAKP,EAAAA,GAAgBQ,IACnB,MAAO,4EACT,QACE,MAAO,0FAoBCe,CAA2BJ,EAAOpB,OAtDpBuB,KAC1B,OAAQA,GACN,KAAKtB,EAAAA,GAAgBC,KACnB,MAAO,gHACT,KAAKD,EAAAA,GAAgBI,OACnB,MAAO,sHACT,KAAKJ,EAAAA,GAAgBK,KACnB,MAAO,mHACT,KAAKL,EAAAA,GAAgBM,QACnB,MAAO,mHACT,KAAKN,EAAAA,GAAgBO,SACnB,MAAO,0GACT,KAAKP,EAAAA,GAAgBQ,IACnB,MAAO,6GACT,QACE,MAAO,wHAwCCgB,CAAmBL,EAAOpB,OAE9B,OACEe,EAAAA,EAAAA,MAAA,UAEEW,KAAK,SACLC,QAASA,KAAOb,GAAYF,EAASQ,EAAOpB,OAC5Cc,SAAUA,EACVD,UAAS,qIAAAG,OAELM,EAAY,sBAAAN,OAEZF,EACI,gCACA,iCAAgC,sBAAAE,OAGpCK,EACI,uEACA,GAAE,qJAIVO,MAAOR,EAAOhB,YACd,eAAciB,EAAWJ,SAAA,EAEzBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,SAAEG,EAAOjB,SACvCe,EAAAA,EAAAA,KAAA,OACEL,UAAS,gBAAAG,OACPK,EAAa,gBAAkB,uBAC9BJ,SAEFG,EAAOhB,iBAGXiB,IACCH,EAAAA,EAAAA,KAAA,OAAKL,UAAU,yBAAwBI,UACrCC,EAAAA,EAAAA,KAAA,OACEL,UAAU,UACVgB,KAAK,eACLC,QAAQ,YAAWb,UAEnBC,EAAAA,EAAAA,KAAA,QACEa,SAAS,UACTC,EAAE,qHACFC,SAAS,kBA1CZb,EAAOpB,YAmDpBkB,EAAAA,EAAAA,KAAA,KAAGL,UAAU,0BAAyBI,SAAC,oI,gDCrItC,MAAMiB,GAAmBC,E,QAAAA,IAAuBC,IAAG,CACxDC,UAAW,GACXC,kBAAmB,IAAIC,IACvBC,WAAW,EACXC,eAAgB,CAAC,EAEjBC,eAAgBC,UACdP,EAAI,CAAEI,WAAW,IAEjB,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,iBAAkB,CAC7CC,QAAS,CACPC,cAAc,UAADlC,OAAY4B,MAI7B,IAAKG,EAASI,GACZ,MAAM,IAAIC,MAAM,6BAGlB,MAAMC,QAAeN,EAASO,OAE9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOG,OAFvBpB,EAAI,CAAEC,UAAWgB,EAAOI,KAAMjB,WAAW,GAI7C,CAAE,MAAOgB,GAGP,MAFAE,QAAQF,MAAM,yBAA0BA,GACxCpB,EAAI,CAAEI,WAAW,IACXgB,CACR,GAGFG,eAAgBhB,UACd,MAAMiB,EAAW,IAAIC,SACrBD,EAASE,OAAO,WAAYC,GAE5B,IACE,MAAMnB,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,wBAAyB,CACpDgB,OAAQ,OACRf,QAAS,CACPC,cAAc,UAADlC,OAAY4B,IAE3BqB,KAAML,IAGR,IAAKb,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,gBACvC,CAEA,MAAMH,QAAeN,EAASO,OAE9B,GAAID,EAAOE,QAOT,OALAnB,EAAK+B,IAAK,CACR9B,UAAW,CAACgB,EAAOI,QAASU,EAAM9B,WAClCI,gBAAc2B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAM1B,gBAAc,IAAE,CAACsB,EAAKM,MAAO,SAGnDhB,EAAOI,KAEd,MAAM,IAAIL,MAAMC,EAAOG,MAE3B,CAAE,MAAOA,GAEP,MADAE,QAAQF,MAAM,yBAA0BA,GAClCA,CACR,GAGFc,eAAgB3B,UACd,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADhC,OAAmBuD,GAAM,CACnDP,OAAQ,SACRf,QAAS,CACPC,cAAc,UAADlC,OAAY4B,MAI7B,IAAKG,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,gBACvC,CAGApB,EAAK+B,IAAK,CACR9B,UAAW8B,EAAM9B,UAAUmC,OAAQC,GAAQA,EAAIF,KAAOA,GACtDjC,kBAAmB,IAAIC,IACrB,IAAI4B,EAAM7B,mBAAmBkC,OAAQE,GAAUA,IAAUH,MAG/D,CAAE,MAAOf,GAEP,MADAE,QAAQF,MAAM,yBAA0BA,GAClCA,CACR,GAGFmB,gBAAiBhC,UACf,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAADhC,OACC4D,mBAAmBC,IAC9C,CACE5B,QAAS,CACPC,cAAc,UAADlC,OAAY4B,MAK/B,IAAKG,EAASI,GACZ,MAAM,IAAIC,MAAM,iBAGlB,MAAMC,QAAeN,EAASO,OAC9B,OAAOD,EAAOE,QAAUF,EAAOI,KAAO,EACxC,CAAE,MAAOD,GAEP,OADAE,QAAQF,MAAM,0BAA2BA,GAClC,EACT,GAGFsB,YAAanC,UACX,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADhC,OAAmBuD,GAAM,CACnDtB,QAAS,CACPC,cAAc,UAADlC,OAAY4B,MAI7B,IAAKG,EAASI,GACZ,OAAO,KAGT,MAAME,QAAeN,EAASO,OAC9B,OAAOD,EAAOE,QAAUF,EAAOI,KAAO,IACxC,CAAE,MAAOD,GAEP,OADAE,QAAQF,MAAM,sBAAuBA,GAC9B,IACT,GAGFuB,wBAA0BR,IACxBnC,EAAK+B,IACH,MAAMa,EAAe,IAAIzC,IAAI4B,EAAM7B,mBAMnC,OALI0C,EAAaC,IAAIV,GACnBS,EAAaE,OAAOX,GAEpBS,EAAaG,IAAIZ,GAEZ,CAAEjC,kBAAmB0C,MAIhCI,eAAgBA,KACdhD,EAAI,CAAEE,kBAAmB,IAAIC,OAG/B8C,UAAWA,KACTjD,EAAK+B,IAAK,CACR7B,kBAAmB,IAAIC,IAAI4B,EAAM9B,UAAUlB,IAAKsD,GAAQA,EAAIF,SAIhEe,kBAAmBA,CAACC,EAAkBC,KACpCpD,EAAK+B,IAAK,CACR1B,gBAAc2B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAM1B,gBAAc,IAAE,CAAC8C,GAAWC,U,gDCtLtD,MAAMC,EAAkBA,KAC7B,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAA8B,OACvDC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCpC,EAAOuC,IAAYH,EAAAA,EAAAA,UAAwB,MAE5CI,GAAgBC,EAAAA,EAAAA,aAAYtD,UAChC,IACEmD,GAAW,GACXC,EAAS,MAET,MAAMnD,EAAQC,aAAaC,QAAQ,eAAiBoD,eAAepD,QAAQ,cAC3E,IAAKF,EACH,MAAM,IAAIQ,MAAM,iCAGlB,MAAML,QAAiBC,MAAM,qBAAsB,CACjDC,QAAS,CACP,cAAgB,UAADjC,OAAY4B,GAC3B,eAAgB,sBAIpB,IAAKG,EAASI,GAAI,CAChB,MAAMgD,QAAkBpD,EAASO,OACjC,MAAM,IAAIF,MAAM+C,EAAU3C,OAAS,gCACrC,CAEA,MAAMC,QAAaV,EAASO,OAC5BqC,EAAYlC,EAAKA,KACnB,CAAE,MAAO2C,GACPL,EAASK,EAAIC,SACb3C,QAAQF,MAAM,gCAAiC4C,EACjD,CAAC,QACCN,GAAW,EACb,GACC,IAEGQ,GAAiBL,EAAAA,EAAAA,aAAYtD,UACjC,IACEoD,EAAS,MAET,MAAMnD,EAAQC,aAAaC,QAAQ,eAAiBoD,eAAepD,QAAQ,cAC3E,IAAKF,EACH,MAAM,IAAIQ,MAAM,iCAGlB,MAAML,QAAiBC,MAAM,qBAAsB,CACjDgB,OAAQ,QACRf,QAAS,CACP,cAAgB,UAADjC,OAAY4B,GAC3B,eAAgB,oBAElBqB,KAAMsC,KAAKC,UAAUC,KAGvB,IAAK1D,EAASI,GAAI,CAChB,MAAMgD,QAAkBpD,EAASO,OACjC,MAAM,IAAIF,MAAM+C,EAAU3C,OAAS,iCACrC,CAEA,MAAMC,QAAaV,EAASO,OAC5BqC,EAAYlC,EAAKA,KACnB,CAAE,MAAO2C,GAGP,MAFAL,EAASK,EAAIC,SACb3C,QAAQF,MAAM,gCAAiC4C,GACzCA,CACR,GACC,IAEGM,GAAUT,EAAAA,EAAAA,aAAYtD,gBACpBqD,KACL,CAACA,IAMJ,OAJAW,EAAAA,EAAAA,WAAU,KACRX,KACC,CAACA,IAEG,CACLN,WACAG,UACArC,QACA8C,iBACAI,W,oHC7EG,MAAME,EAAwCjG,IAU9C,IAV+C,MACpDX,EAAK,SACLY,EAAQ,MACRT,EAAK,IACL0G,EAAM,EAAC,IACPC,EAAM,IAAG,YACTC,EAAc,iBAAgB,MAC9BvD,EAAK,UACL3C,EAAY,GAAE,SACdC,GAAW,GACZH,EACC,MAAOqG,EAAYC,IAAiBrB,EAAAA,EAAAA,UAAS5F,EAAMkH,aAC5CC,EAAYC,IAAiBxB,EAAAA,EAAAA,UAAiB,KAGrDe,EAAAA,EAAAA,WAAU,KACJ3G,EAAMkH,aAAeF,GACvBC,EAAcjH,EAAMkH,aAErB,CAAClH,IAEJ,MAkFMqH,EAAe7D,GAAS2D,EAE9B,OACEpG,EAAAA,EAAAA,MAAA,OAAKF,UAAWA,EAAUI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAC5Dd,KAGHY,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,OACL4F,UAAU,UACVtH,MAAOgH,EACPpG,SA7DmB2G,IACzB,MAAMC,EAAWD,EAAEE,OAAOzH,OAGT,KAAbwH,GAAmB,QAAQE,KAAKF,KAtCXG,KAOzB,GANAV,EAAcU,GAGdP,EAAc,IAGU,KAApBO,EAASC,OACX,OAIF,IAAK,QAAQF,KAAKC,EAASC,QAEzB,YADAR,EAAc,+BAIhB,MAAMS,EAAWC,SAASH,EAASC,OAAQ,IAGvCC,EAAWhB,EACbO,EAAc,2BAADpG,OAA4B6F,IAIvCgB,EAAWf,EACbM,EAAc,0BAADpG,OAA2B8F,IAK1ClG,EAASiH,IAQPE,CAAkBP,IAyDdQ,OArDWC,MAES,KAAtBjB,EAAWY,QAAiBT,KAC9BF,EAAcjH,EAAMkH,YACpBE,EAAc,MAkDVc,UA9CeX,IAGlB,CACC,YACA,SACA,MACA,SACA,QACA,YACA,aACA,UACA,aACAY,SAASZ,EAAEa,MACXb,EAAEa,KAAO,KAAOb,EAAEa,KAAO,KACzBb,EAAEc,SAAW,CAAC,IAAK,IAAK,IAAK,IAAK,KAAKF,SAASZ,EAAEa,IAAIE,gBAExDf,EAAEgB,kBA8BExB,YAAaA,EACbjG,SAAUA,EACVD,UAAS,6LAAAG,OAILqG,EACI,4DACA,qEAAoE,kBAAArG,OAExEF,EAAW,gCAAkC,GAAE,mBAKrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CI,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QA5Ca6G,KACrB,MAAMhB,EAAWiB,KAAK5B,IAAI7G,EAAQ,EAAG8G,GACrClG,EAAS4G,IA2CD1G,SAAUA,GAAYd,GAAS8G,EAC/BjG,UAAU,+LAKV6H,UAAW,EAAEzH,UAEbC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,UAAUgB,KAAK,eAAeC,QAAQ,YAAWb,UAC9DC,EAAAA,EAAAA,KAAA,QACEa,SAAS,UACTC,EAAE,sHACFC,SAAS,iBAIff,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QA1DagH,KACrB,MAAMnB,EAAWiB,KAAK3B,IAAI9G,EAAQ,EAAG6G,GACrCjG,EAAS4G,IAyDD1G,SAAUA,GAAYd,GAAS6G,EAC/BhG,UAAU,+LAKV6H,UAAW,EAAEzH,UAEbC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,UAAUgB,KAAK,eAAeC,QAAQ,YAAWb,UAC9DC,EAAAA,EAAAA,KAAA,QACEa,SAAS,UACTC,EAAE,qHACFC,SAAS,sBAQlBoF,IACCnG,EAAAA,EAAAA,KAAA,KAAGL,UAAU,4BAA2BI,SAAEoG,KAI1CA,IACAtG,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6BAA4BI,SAAA,CAAC,0BAChB4F,EAAI,QAAMC,SC3L/B8B,EAA0CjI,IAOhD,IAPiD,SACtD6E,EAAW,EAAC,gBACZqD,GAAkB,EAAK,MACvB1I,EAAK,UACLU,EAAY,GAAE,KACdiI,EAAO,KAAI,QACXC,EAAU,WACXpI,EACC,MAAMqI,EAAc,CAClBC,GAAI,QACJC,GAAI,MACJC,GAAI,OAgBN,OACEpI,EAAAA,EAAAA,MAAA,OAAKF,UAAS,UAAAG,OAAYH,GAAYI,SAAA,CACnCd,IACCY,EAAAA,EAAAA,MAAA,OACEF,UAAS,0CAAAG,OAVO,CACtBiI,GAAI,UACJC,GAAI,UACJC,GAAI,aAOuEL,IAAQ7H,SAAA,EAE7EC,EAAAA,EAAAA,KAAA,QAAML,UAAU,4BAA2BI,SAAEd,KAC3C0I,IACA9H,EAAAA,EAAAA,MAAA,QAAMF,UAAU,gBAAeI,SAAA,CAAEwH,KAAKW,MAAM5D,GAAU,WAK5DtE,EAAAA,EAAAA,KAAA,OACEL,UAAS,mDAAAG,OAAqDgI,EAAYF,IAAQ7H,UAElFC,EAAAA,EAAAA,KAAA,OACEL,UAAS,GAAAG,OA9BM,CACrBqI,QAAS,iBACT9F,QAAS,eACT+F,QAAS,gBACT9F,MAAO,cA2BgBuF,GAAQ,uDAAA/H,OAEvBgI,EAAYF,GAAK,KAAA9H,OAEjB6H,EACI,uBACA,mCAENU,MACGV,OAEGW,EADA,CAAEC,MAAM,GAADzI,OAAKyH,KAAK5B,IAAI,IAAK4B,KAAK3B,IAAI,EAAGtB,IAAU,cAiBnDkE,EAA4DC,IAKlE,IALmE,aACxEC,EAAY,MACZC,EAAK,cACLC,EAAa,UACbjJ,EAAY,IACb8I,EACC,MAAOnE,EAAUuE,GAAeC,EAAAA,SAAe,IACxCC,EAAaC,GAAkBF,EAAAA,SAAe,GA0CrD,GAxCAA,EAAAA,UAAgB,KACd,IAAIG,EACAC,EAwBJ,OAtBIR,GACFM,EAAe,GACfH,EAAY,GAGZI,EAAWE,YAAY,KACrBH,EAAgBI,GAASA,EAAO,IAC/B,KAGHF,EAAmBC,YAAY,KAC7BN,EAAaO,IAEX,MAAMC,EAAYD,EAAO,GAAK,EAAIA,EAAO,GAAK,EAAIA,EAAO,GAAK,EAAI,GAClE,OAAO7B,KAAK5B,IAAI,GAAIyD,EAAOC,MAE5B,OAEHR,EAAY,GACZG,EAAe,IAGV,KACDC,GAAUK,cAAcL,GACxBC,GAAkBI,cAAcJ,KAErC,CAACR,IAGJI,EAAAA,UAAgB,MACTJ,GAAgBpE,EAAW,IAC9BuE,EAAY,KACZU,WAAW,IAAMV,EAAY,GAAI,OAElC,CAACH,EAAcpE,KAEboE,GAA6B,IAAbpE,EACnB,OAAO,KAGT,MAAMkF,EAAcC,IAClB,GAAIA,EAAU,GAAI,MAAM,GAAN3J,OAAU2J,EAAO,KACnC,MAAMC,EAAUnC,KAAKoC,MAAMF,EAAU,IAC/BG,EAAmBH,EAAU,GACnC,MAAM,GAAN3J,OAAU4J,EAAO,MAAA5J,OAAK8J,EAAgB,MAGxC,OACE5J,EAAAA,EAAAA,KAAA,OACEL,UAAS,iEAAAG,OAAmEH,GAAYI,UAExFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wFACfK,EAAAA,EAAAA,KAAA,QAAML,UAAU,yBAAwBI,SACrC4I,GAAS,8BAGd9I,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBI,SAAA,CACnCgJ,EAAc,GAAKS,EAAWT,GAC9BH,GACiB,IAAhBG,GAAiB,UAAAjJ,OACP0J,EAAWZ,GAAc,YAIzC5I,EAAAA,EAAAA,KAAC0H,EAAW,CACVpD,SAAUA,EACVqD,gBAA8B,IAAbrD,EACjBuD,QAAQ,UACRD,KAAK,QAGP5H,EAAAA,EAAAA,KAAA,OAAKL,UAAU,oCAAmCI,SAC/C2I,EACG,gDACA,+B,wBCpJd,MAAMmB,EAAsCpK,IAOrC,IAPsC,MAC3CX,EAAK,SACLY,EAAQ,IACRiG,EAAG,IACHC,EAAG,MACH3G,EAAK,YACL4G,GACDpG,EACC,MAAOqG,EAAYC,IAAiBrB,EAAAA,EAAAA,UAAS5F,EAAMkH,aAC5C1D,EAAOuC,IAAYH,EAAAA,EAAAA,UAAiB,KAG3Ce,EAAAA,EAAAA,WAAU,KACRM,EAAcjH,EAAMkH,aACnB,CAAClH,IA4CJ,OACEe,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,SAAOF,UAAU,0CAAyCI,SAAA,CACvDd,EAAM,QAETY,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,OACL1B,MAAOgH,EACPpG,SAAW2G,GAnDGC,KAOpB,GANAP,EAAcO,GAGVhE,GAAOuC,EAAS,IAGH,KAAbyB,EACF,OAIF,MAAMK,EAAWC,SAASN,GACtBwD,MAAMnD,GACR9B,EAAS,oBAIP8B,EAAWhB,EACbd,EAAS,cAAD/E,OAAe6F,IAIrBgB,EAAWf,EACbf,EAAS,cAAD/E,OAAe8F,IAKzBlG,EAASiH,IAsBcoD,CAAa1D,EAAEE,OAAOzH,OACvCgI,OApBWC,KAEjB,MAAMJ,EAAWC,SAASd,IACtBgE,MAAMnD,IAA4B,KAAfb,KAErBC,EAAcjH,EAAMkH,YACpBnB,EAAS,MAeLgB,YAAaA,EACblG,UAAS,gHAAAG,OACPwC,EACI,sCACA,kDAGPA,IACCtC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,4EAA2EI,SACvFuC,WAQA0H,EAAoDvB,IAM1D,IAN2D,kBAChErH,EAAiB,kBACjB6I,EAAiB,mBACjBC,EAAkB,kBAClBC,EAAiB,aACjBC,EAAe,GAChB3B,EACC,MAAM,UAAEtH,EAAS,eAAEK,EAAc,UAAEF,IAAcN,EAAAA,EAAAA,MAC1CqJ,EAAaC,IAAkB5F,EAAAA,EAAAA,UAAS,KAE/Ce,EAAAA,EAAAA,WAAU,KACiB,IAArBtE,EAAUoJ,QACZ/I,KAED,CAACL,EAAUoJ,OAAQ/I,IAEtB,MAAMgJ,EAAoBrJ,EAAUmC,OACjCC,GACCA,EAAIkH,cACJlH,EAAImH,SAAStD,cAAcH,SAASoD,EAAYjD,gBAG9CuD,EAAwBC,IAG5B,GAFmBxJ,EAAkB6F,SAAS2D,GAG5CX,EAAkB7I,EAAkBkC,OAAQD,GAAOA,IAAOuH,SACrD,GAAIxJ,EAAkBmJ,OAASH,EAAc,CAClDH,EAAkB,IAAI7I,EAAmBwJ,IAGzC,MAAMC,EAAW1J,EAAU2J,KAAMvH,GAAQA,EAAIF,KAAOuH,GACxC,OAARC,QAAQ,IAARA,GAAAA,EAAUE,YAAcF,EAASE,WAAa,GAChDZ,EAAkBS,EAAY,CAC5BI,UAAW,EACXC,QAASJ,EAASE,YAGxB,GAOIG,EAAwBA,CAC5BN,EACAO,EACArM,KAEA,MAAMsM,EAAelB,EAAmBU,IAAe,CACrDI,UAAW,EACXC,QAAS,GAELJ,EAAW1J,EAAU2J,KAAMvH,GAAQA,EAAIF,KAAOuH,GAC9CS,GAAkB,OAARR,QAAQ,IAARA,OAAQ,EAARA,EAAUE,aAAc,EAExC,IAAIO,GAAQpI,EAAAA,EAAAA,GAAA,GAAQkI,GAEN,cAAVD,EACFG,EAASN,UAAYzD,KAAK3B,IACxB,EACA2B,KAAK5B,IAAI7G,EAAOuM,EAASC,EAASL,UAGpCK,EAASL,QAAU1D,KAAK3B,IAAI0F,EAASN,UAAWzD,KAAK5B,IAAI7G,EAAOuM,IAGlElB,EAAkBS,EAAYU,IAGhC,OAAIhK,GAEAtB,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wCAAuCI,UACpDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,SAAC,2BAKZ,IAArBoB,EAAUoJ,QAEV1K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBI,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,qBAAoBI,SAAC,wBACpCC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,iEAQzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBC,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,OACLqF,YAAY,sBACZ/G,MAAOuL,EACP3K,SAAW2G,GAAMiE,EAAejE,EAAEE,OAAOzH,OACzCa,UAAU,wKAKbyB,EAAkBmJ,OAAS,IAC1B1K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DI,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BI,SAAA,CAAC,YACnCqB,EAAkBmJ,OAAO,OAAKH,EAAa,kBAEvDpK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SAnEzBoB,EAAUmC,OAAQC,GAAQnC,EAAkB6F,SAAS1D,EAAIF,KAoEhCpD,IAAKsD,IAAS,IAADgI,EACnC,MAAMC,EAAejI,EAAIwH,YAAcxH,EAAIwH,WAAa,EAClDU,EAAYvB,EAAmB3G,EAAIF,IAEzC,OACExD,EAAAA,EAAAA,MAAA,OAEEF,UAAU,4CAA2CI,SAAA,EAErDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,QAAML,UAAU,6CAA4CI,SACzDwD,EAAImH,YAEP1K,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMkK,EAAqBpH,EAAIF,IACxC1D,UAAU,kGAAiGI,SAC5G,cAKFyL,IACC3L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBI,SAAA,CAAC,gBACjCwD,EAAIwH,WAAY,IACD,SAAlBxH,EAAImI,UAAuB,SAAW,QAAS,IAAI,gBAItD7L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC6J,EAAS,CACR5K,MAAM,OACNH,OAAgB,OAAT2M,QAAS,IAATA,OAAS,EAATA,EAAWT,YAAa,EAC/BtL,SAAWZ,GACToM,EAAsB3H,EAAIF,GAAI,YAAavE,GAE7C6G,IAAK,EACLC,IAAKrC,EAAIwH,YAAc,EACvBlF,YAAY,OAGd7F,EAAAA,EAAAA,KAAC6J,EAAS,CACR5K,MAAM,KACNH,OAAgB,OAAT2M,QAAS,IAATA,OAAS,EAATA,EAAWR,UAAW1H,EAAIwH,YAAc,EAC/CrL,SAAWZ,GACToM,EAAsB3H,EAAIF,GAAI,UAAWvE,GAE3C6G,KAAc,OAAT8F,QAAS,IAATA,OAAS,EAATA,EAAWT,YAAa,EAC7BpF,IAAKrC,EAAIwH,YAAc,EACvBlF,aAA2B,QAAd0F,EAAAhI,EAAIwH,kBAAU,IAAAQ,OAAA,EAAdA,EAAgBvF,aAAc,UAI/ChG,EAAAA,EAAAA,KAAA,UACES,QAASA,IACP0J,EAAkB5G,EAAIF,GAAI,CACxB2H,UAAW,EACXC,QAAS1H,EAAIwH,YAAc,IAG/BpL,UAAU,gIAA+HI,SAC1I,eAKF0L,IACC5L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBI,SAAA,CAAC,SAC9B0L,EAAUR,QAAUQ,EAAUT,UAAY,EAAE,MAAI,IACtDzH,EAAIwH,WAAY,IACE,SAAlBxH,EAAImI,UAAuB,SAAW,eAM7CF,IACA3L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBI,SAAA,CAAC,gBACjCwD,EAAImI,UAAUC,cAAc,0CAzE/BpI,EAAIF,YAqFrBrD,EAAAA,EAAAA,KAAA,OAAKL,UAAU,qCAAoCI,SAChDyK,EAAkBvK,IAAK4K,IACtB,MAAM1K,EAAaiB,EAAkB6F,SAAS4D,EAASxH,IACjDuI,GACHzL,GAAciB,EAAkBmJ,OAASH,EACtCoB,EAAeX,EAASE,YAAcF,EAASE,WAAa,EAElE,OACE/K,EAAAA,EAAAA,KAAA,OAEEL,UAAS,0FAAAG,OAGLK,EACI,uCACAyL,EACA,gEACA,4DAA2D,oBAGnEnL,QAASA,IACPmL,GAAazL,EACTwK,EAAqBE,EAASxH,IAC9B,KACLtD,UAEDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iBAAgBI,UAC7BF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAML,UAAU,UAASI,SACC,QAAvB8K,EAASa,UACN,eACuB,SAAvBb,EAASa,UACT,eACuB,QAAvBb,EAASa,UACT,eACA,kBAEN7L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,kCAAiCI,SAC3C8K,EAASH,YAEZ7K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDI,SAAA,EAChEF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CACG8K,EAASa,UAAUC,cAAc,UAAG,IACpCpE,KAAKW,MAAM2C,EAASgB,UAAY,MAAM,SAExCL,IACC3L,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,UACD8K,EAASE,WAAY,IACA,SAAvBF,EAASa,UAAuB,SAAW,uBAQxD1L,EAAAA,EAAAA,KAAA,OACEL,UAAS,oGAAAG,OAGPK,EACI,oCACA,kBAAiB,sBAEvBJ,SAECI,IACCH,EAAAA,EAAAA,KAAA,OACEL,UAAU,qBACVgB,KAAK,eACLC,QAAQ,YAAWb,UAEnBC,EAAAA,EAAAA,KAAA,QACEa,SAAS,UACTC,EAAE,qHACFC,SAAS,oBApEd8J,EAASxH,QA+EQ,IAA7BmH,EAAkBD,QAAgBF,IACjCrK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iCAAgCI,SAAC,wC,wBCtYxD,MAAM+L,EAAuB,CAC3B,CACEhN,MAAOiN,EAAAA,GAAcC,MACrB/M,MAAO,QACPC,YAAa,kCACb+M,KAAM,gBAER,CACEnN,MAAOiN,EAAAA,GAAc5M,OACrBF,MAAO,SACPC,YAAa,kCACb+M,KAAM,gBAER,CACEnN,MAAOiN,EAAAA,GAAcG,KACrBjN,MAAO,OACPC,YAAa,wCACb+M,KAAM,iBA8BGE,EAA8D1M,IAMpE,IANqE,MAC1EX,EAAK,SACLY,EAAQ,UACRC,EAAY,GAAE,SACdC,GAAW,EAAK,MAChBX,EAAQ,kBACTQ,EACC,OACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAeH,GAAYI,SAAA,EACvCC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,8CAA6CI,SAC3Dd,KAEHe,EAAAA,EAAAA,KAAA,OAAKL,UAAU,yBAAwBI,SACpC+L,EAAqB7L,IAAKC,IACzB,MAAMC,EAAarB,IAAUoB,EAAOpB,MAC9BsB,EAAeD,EA5BQoK,KACrC,OAAQA,GACN,KAAKwB,EAAAA,GAAcC,MACjB,MAAO,wFACT,KAAKD,EAAAA,GAAc5M,OACjB,MAAO,wFACT,KAAK4M,EAAAA,GAAcG,KACjB,MAAO,oFACT,QACE,MAAO,0FAoBCE,CAA8BlM,EAAOpB,OA1CpByL,KAC7B,OAAQA,GACN,KAAKwB,EAAAA,GAAcC,MACjB,MAAO,sHACT,KAAKD,EAAAA,GAAc5M,OACjB,MAAO,sHACT,KAAK4M,EAAAA,GAAcG,KACjB,MAAO,mHACT,QACE,MAAO,wHAkCCG,CAAsBnM,EAAOpB,OAEjC,OACEe,EAAAA,EAAAA,MAAA,UAEEW,KAAK,SACLC,QAASA,KAAOb,GAAYF,EAASQ,EAAOpB,OAC5Cc,SAAUA,EACVD,UAAS,qIAAAG,OAELM,EAAY,sBAAAN,OAEZF,EACI,gCACA,iCAAgC,sBAAAE,OAGpCK,EACI,uEACA,GAAE,qJAIVO,MAAOR,EAAOhB,YACd,eAAciB,EAAWJ,SAAA,EAEzBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBI,SAAA,EACpCC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,WAAUI,SAAEG,EAAO+L,QAClCjM,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,SAAEG,EAAOjB,SACvCe,EAAAA,EAAAA,KAAA,OACEL,UAAS,WAAAG,OACPK,EAAa,gBAAkB,uBAC9BJ,SAEFG,EAAOhB,iBAGXiB,IACCH,EAAAA,EAAAA,KAAA,OAAKL,UAAU,yBAAwBI,UACrCC,EAAAA,EAAAA,KAAA,OACEL,UAAU,UACVgB,KAAK,eACLC,QAAQ,YAAWb,UAEnBC,EAAAA,EAAAA,KAAA,QACEa,SAAS,UACTC,EAAE,qHACFC,SAAS,kBA3CZb,EAAOpB,YAoDpBkB,EAAAA,EAAAA,KAAA,KAAGL,UAAU,0BAAyBI,SAAC,qH,cC9CtC,MAAMuM,GAAarL,E,QAAAA,IAA2BC,IAAG,CACtDwH,cAAc,EACd6D,mBAAoB,GACpBC,cAAe,KAEfC,mBAAoBhL,UAClBP,EAAI,CAAEwH,cAAc,EAAM6D,mBAAoB,2BAE9C,IACE,MAAM7K,EAAQC,aAAaC,QAAQ,cAEnCV,EAAI,CAAEqL,mBAAoB,qCAE1B,MAAM1K,QAAiBC,MAAM,8BAA+B,CAC1DgB,OAAQ,OACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY4B,IAE3BqB,KAAMsC,KAAKC,UAAUoH,KAGvB,IAAK7K,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,oBACvC,CAEA,MAAMH,QAAeN,EAASO,OAE9B,GAAID,EAAOE,QAAS,CAClBnB,EAAI,CACFsL,cAAe,CACbG,SAAUxK,EAAOI,KAAKoK,SACtBC,QAASzK,EAAOI,KAAKsK,WACrBrM,KAAM,cAERkI,cAAc,EACd6D,mBAAoB,KAItB,IAEE,MAAM,cAAEO,SAAwB,uCAC1BC,EAAaD,EAAcE,WACjCD,EAAWE,uBAAuB9K,EAAOI,KAAKoK,SAAStJ,IACvD0J,EAAWG,qBACb,CAAE,MAAOC,GACP3K,QAAQ4K,KAAK,qCAAsCD,EACrD,CAEA,MAAO,CACLR,SAAUxK,EAAOI,KAAKoK,SACtBE,WAAY1K,EAAOI,KAAKsK,WACxBQ,iBAAkBlL,EAAOI,KAAK8K,iBAElC,CACE,MAAM,IAAInL,MAAMC,EAAOG,MAE3B,CAAE,MAAOA,GAEP,MADApB,EAAI,CAAEwH,cAAc,EAAO6D,mBAAoB,KACzCjK,CACR,GAGFgL,aAAc7L,UACZP,EAAI,CAAEwH,cAAc,EAAM6D,mBAAoB,2BAE9C,IACE,MAAM7K,EAAQC,aAAaC,QAAQ,cAEnCV,EAAI,CAAEqL,mBAAoB,yCAE1B,MAAM1K,QAAiBC,MAAM,wBAAyB,CACpDgB,OAAQ,OACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY4B,IAE3BqB,KAAMsC,KAAKC,UAAUoH,KAGvB,IAAK7K,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,oBACvC,CAEA,MAAMH,QAAeN,EAASO,OAE9B,GAAID,EAAOE,QAAS,CAClBnB,EAAI,CACFsL,cAAe,CACbG,SAAUxK,EAAOI,KAAKoK,SACtBC,QAASzK,EAAOI,KAAKgL,UACrB/M,KAAM,QAERkI,cAAc,EACd6D,mBAAoB,KAItB,IAEE,MAAM,cAAEO,SAAwB,uCAC1BC,EAAaD,EAAcE,WACjCD,EAAWE,uBAAuB9K,EAAOI,KAAKoK,SAAStJ,IACvD0J,EAAWG,qBACb,CAAE,MAAOC,GACP3K,QAAQ4K,KAAK,qCAAsCD,EACrD,CAEA,MAAO,CACLR,SAAUxK,EAAOI,KAAKoK,SACtBY,UAAWpL,EAAOI,KAAKgL,UACvBF,iBAAkBlL,EAAOI,KAAK8K,iBAElC,CACE,MAAM,IAAInL,MAAMC,EAAOG,MAE3B,CAAE,MAAOA,GAEP,MADApB,EAAI,CAAEwH,cAAc,EAAO6D,mBAAoB,KACzCjK,CACR,GAGFkL,uBAAwB/L,UACtBP,EAAI,CAAEwH,cAAc,EAAM6D,mBAAoB,2BAE9C,IACE,MAAM7K,EAAQC,aAAaC,QAAQ,cAEnCV,EAAI,CACFqL,mBAAoB,gDAGtB,MAAM1K,QAAiBC,MAAM,mCAAoC,CAC/DgB,OAAQ,OACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY4B,IAE3BqB,KAAMsC,KAAKC,UAAUoH,KAGvB,IAAK7K,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,oBACvC,CAEA,MAAMH,QAAeN,EAASO,OAE9B,GAAID,EAAOE,QAAS,CAClBnB,EAAI,CACFwH,cAAc,EACd6D,mBAAoB,KAItB,IAEE,MAAM,cAAEO,SAAwB,uCACbA,EAAcE,WACtBC,uBAAuBP,EAAOe,WAC3C,CAAE,MAAON,GACP3K,QAAQ4K,KAAK,qCAAsCD,EACrD,CAEA,MAAO,CACLN,WAAY1K,EAAOI,KAAKsK,WACxBQ,iBAAkBlL,EAAOI,KAAK8K,iBAElC,CACE,MAAM,IAAInL,MAAMC,EAAOG,MAE3B,CAAE,MAAOA,GAEP,MADApB,EAAI,CAAEwH,cAAc,EAAO6D,mBAAoB,KACzCjK,CACR,GAGFoL,0BAA2BjM,UACzBP,EAAI,CAAEwH,cAAc,EAAM6D,mBAAoB,2BAE9C,IACE,MAAM7K,EAAQC,aAAaC,QAAQ,cAEnCV,EAAI,CACFqL,mBAAoB,oDAGtB,MAAM1K,QAAiBC,MAAM,uCAAwC,CACnEgB,OAAQ,OACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY4B,IAE3BqB,KAAMsC,KAAKC,UAAUoH,KAGvB,IAAK7K,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,oBACvC,CAEA,MAAMH,QAAeN,EAASO,OAE9B,GAAID,EAAOE,QAGT,OAFAnB,EAAI,CAAEwH,cAAc,EAAO6D,mBAAoB,KAExC,CACLgB,UAAWpL,EAAOI,KAAKgL,UACvBF,iBAAkBlL,EAAOI,KAAK8K,kBAGhC,MAAM,IAAInL,MAAMC,EAAOG,MAE3B,CAAE,MAAOA,GAEP,MADApB,EAAI,CAAEwH,cAAc,EAAO6D,mBAAoB,KACzCjK,CACR,GAGFqL,mBAAoBA,KAClBzM,EAAI,CAAEsL,cAAe,W,cChTlB,MAAMoB,EAAwDnO,IAQ9D,IAR+D,cACpEoO,EAAa,WACbC,EAAU,iBACVC,EAAgB,iBAChBC,EAAgB,UAChB1M,GAAY,EAAK,UACjB3B,EAAY,GAAE,SACdsO,EAAW,QACZxO,EACC,OAAsB,IAAlBoO,EAA4B,MAG9B7N,EAAAA,EAAAA,KAAA,OACEL,UAAS,0DAAAG,OAA4DH,GAAYI,UAEjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yBAAwBI,SAAA,CACrC8N,EAAc,OAAKC,EAAW,IAAEG,EACd,IAAlBJ,EAAsB,IAAM,GAAG,gBAGlC7N,EAAAA,EAAAA,KAAA,UACES,QAASuN,EACTrO,UAAU,mDACVC,SAAU0B,EAAUvB,SACrB,wBAKHC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,8BAA6BI,UAC1CC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QAASsN,EACTlG,QAAQ,SACRD,KAAK,KACLtG,UAAWA,EACX1B,SAAU0B,EACV3B,UAAU,YAAWI,SACtB,4B,aCNJ,MAAMoO,EAA0D1O,IAOhE,IAPiE,WACtEgO,EAAU,WACVZ,EAAU,iBACVuB,EAAgB,mBAChBC,EAAkB,mBAClBC,EAAkB,sBAClBC,GACD9O,EACC,MAAM,MAAE+O,EAAK,QAAEC,IAAYC,EAAAA,EAAAA,MACrB,KAAEC,IAASC,EAAAA,EAAAA,MACX,uBAAEpB,GAA2BlB,KAC3B9H,SAAUqK,EAAY,eAAEzJ,IAAmBb,EAAAA,EAAAA,MAC5CuK,EAAUC,IAAerK,EAAAA,EAAAA,WAAS,IAClCtD,EAAmB4N,IAAwBtK,EAAAA,EAAAA,UAAmB,KAC9DwF,EAAoB+E,IAAyBvK,EAAAA,EAAAA,UAEjD,CAAC,IACGwK,EAAgBC,IAAqBzK,EAAAA,EAAAA,UAAS,KAC9C0K,EAAcC,IAAmB3K,EAAAA,EAAAA,UAAS,KAC1C4K,EAAiBC,IAAsB7K,EAAAA,EAAAA,UAC5C3F,EAAAA,GAAgBI,SAEXqQ,EAAeC,IAAoB/K,EAAAA,EAAAA,UACxCqH,EAAAA,GAAc5M,SAETuJ,EAAcgH,IAAmBhL,EAAAA,EAAAA,WAAS,IAC1CiL,EAAaC,IAAkBlL,EAAAA,EAAAA,WAAS,IACxCmL,EAAcC,IAAmBpL,EAAAA,EAAAA,UAAuB,CAC7DqL,MAAO,GACPC,KAAM,GACNC,iBAAkB,KAEbC,EAAkBC,IAAuBzL,EAAAA,EAAAA,UAC9C,OAEK0L,EAAUC,IAAe3L,EAAAA,EAAAA,UAAuB,CACrDqL,MAAO,GACPC,KAAM,GACNC,iBAAkB,KAIbK,GAAoBC,KAAyB7L,EAAAA,EAAAA,UAAmB,KAChE8L,GAAoBC,KAAyB/L,EAAAA,EAAAA,WAAS,IACtDgM,GAAgBC,KAAqBjM,EAAAA,EAAAA,WAAS,IAG9CkM,GAAoBC,KAAyBnM,EAAAA,EAAAA,UAClD,IAAIrD,KAyDA6C,GAAiBA,KACrBqM,GAAsB,IACtBE,IAAsB,IAyClBK,GAAsBrP,UAC1BkP,IAAkB,GAElB,IACE,MAAM9O,QAAiBC,MAAM,8BAA+B,CAC1DgB,OAAQ,OACRf,QAAS,CACPC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,eAC9C,eAAgB,oBAElBmB,KAAMsC,KAAKC,UAAU,CACnByL,aAAcT,OAIlB,IAAKzO,EAASI,GAAI,CAChB,MAAMgD,QAAkBpD,EAASO,OACjC,MAAM,IAAIF,MAAM+C,EAAU3C,OAAS,8BACrC,CAEA,MAAMC,QAAaV,EAASO,QACtB,aAAE4O,GAAiBzO,EAAKA,KAG9B+N,GAAmBW,QAAS5N,GAAOiL,EAAmBjL,IACtDa,WAEMsK,EAAM,CACV9N,MAAO,UACPyE,QAAQ,GAADrF,OAAKkR,EAAY,cAAAlR,OACL,IAAjBkR,EAAqB,IAAM,GAAE,0BAE/BnJ,QAAS,WAEb,CAAE,MAAOvF,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,8BAC1B0C,QAAS,SAEb,CAAC,QACC8I,IAAkB,EACpB,GAmKIO,GAA2BzP,UAC/B,IAQE,WAPuBK,MAAM,mBAADhC,OAAoBqR,EAAU9N,IAAM,CAC9DP,OAAQ,SACRf,QAAS,CACPC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,mBAIpCK,GACZ,MAAM,IAAIC,MAAM,8BAGlBoM,EAAmB6C,EAAU9N,UAEvBmL,EAAM,CACV9N,MAAO,UACPyE,QAAS,kCACT0C,QAAS,WAEb,CAAE,MAAOvF,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,6BAC1B0C,QAAS,SAEb,GAGIuJ,GAAsBA,IAGnB7J,KAAK8J,KAAKnC,EAAiB,GAqFpC,OACErP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,uBAC/CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QAASA,IAAMmP,GAAgBD,GAC/B9H,QAAQ,YACRD,KAAK,KAAI7H,SACV,0BAKDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAML,UAAU,wBAAuBI,SAAC,aACxCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMsO,GAAaD,GAC5BnP,UAAS,gHAAAG,OAELgP,EAAW,iBAAmB,cAAa,oBAC7C/O,UAEFC,EAAAA,EAAAA,KAAA,QACEL,UAAS,oHAAAG,OAELgP,EAAW,gBAAkB,gBAAe,mCASzDa,IACC9P,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DI,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,uBAGpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,2BAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAO+Q,EAAaE,MACpBrQ,SAAW2G,GACTyJ,EAAiB1G,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBkG,GAAI,IACP2G,MAAO1J,EAAEE,OAAOzH,SAGpBa,UAAU,uJACV2R,KAAM,EACNzL,YAAY,mCAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,8BAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAO+Q,EAAaG,KACpBtQ,SAAW2G,GACTyJ,EAAiB1G,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAWkG,GAAI,IAAE4G,KAAM3J,EAAEE,OAAOzH,SAEvDa,UAAU,uJACV2R,KAAM,EACNzL,YAAY,kCAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAO+Q,EAAaI,iBACpBvQ,SAAW2G,GACTyJ,EAAiB1G,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBkG,GAAI,IACP6G,iBAAkBrJ,SAASP,EAAEE,OAAOzH,UAGxCa,UAAU,kIAAiII,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,mBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,gBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,yBAItBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QAvXOgB,UACzB,GAAKoO,EAAaE,MAAMrJ,QAAWmJ,EAAaG,KAAKtJ,OASrD,IACE,MAAM7E,QAAiBC,MAAM,6BAADhC,OAA8B2N,GAAc,CACtE3K,OAAQ,OACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,gBAEhDmB,KAAMsC,KAAKC,UAAU,CACnByK,MAAOF,EAAaE,MAAMrJ,OAC1BsJ,KAAMH,EAAaG,KAAKtJ,OACxBuJ,iBAAkBJ,EAAaI,iBAC/BsB,iBAAiB,MAIrB,IAAK1P,EAASI,GACZ,MAAM,IAAIC,MAAM,8BAGlB,MAAMC,QAAeN,EAASO,OAC9BgM,EAAiBjM,EAAOI,MAGxBuN,EAAgB,CAAEC,MAAO,GAAIC,KAAM,GAAIC,iBAAkB,IACzDL,GAAe,SAETpB,EAAM,CACV9N,MAAO,UACPyE,QAAS,gCACT0C,QAAS,WAEb,CAAE,MAAOvF,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,0BAC1B0C,QAAS,SAEb,YA7CQ2G,EAAM,CACV9N,MAAO,mBACPyE,QAAS,4CACT0C,QAAS,WAkXkCA,QAAQ,UAAS9H,SAAC,mBAGvDC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QAASA,IAAMmP,GAAe,GAAQ/H,QAAQ,YAAW9H,SAAC,oBASzEmQ,IACCrQ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DI,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,oBAGpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,2BAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsR,EAASL,MAChBrQ,SAAW2G,GACTgK,EAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAWkG,GAAI,IAAE2G,MAAO1J,EAAEE,OAAOzH,SAEpDa,UAAU,uJACV2R,KAAM,EACNzL,YAAY,mCAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,8BAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsR,EAASJ,KAChBtQ,SAAW2G,GACTgK,EAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAWkG,GAAI,IAAE4G,KAAM3J,EAAEE,OAAOzH,SAEnDa,UAAU,uJACV2R,KAAM,EACNzL,YAAY,kCAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOsR,EAASH,iBAChBvQ,SAAW2G,GACTgK,EAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACP6G,iBAAkBrJ,SAASP,EAAEE,OAAOzH,UAGxCa,UAAU,kIAAiII,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,mBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,gBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,yBAItBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QA9XGgB,UACrB,GAAKyO,EAEL,GAAKE,EAASL,MAAMrJ,QAAW0J,EAASJ,KAAKtJ,OAS7C,IACE,MAAM7E,QAAiBC,MAAM,mBAADhC,OAAoBoQ,EAAiB7M,IAAM,CACrEP,OAAQ,MACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,gBAEhDmB,KAAMsC,KAAKC,UAAU,CACnByK,MAAOK,EAASL,MAAMrJ,OACtBsJ,KAAMI,EAASJ,KAAKtJ,OACpBuJ,iBAAkBG,EAASH,qBAI/B,IAAKpO,EAASI,GACZ,MAAM,IAAIC,MAAM,8BAGlB,MAAMC,QAAeN,EAASO,OAC9BiM,EAAmBlM,EAAOI,MAE1B4N,EAAoB,MACpBE,EAAY,CAAEN,MAAO,GAAIC,KAAM,GAAIC,iBAAkB,UAE/CzB,EAAM,CACV9N,MAAO,UACPyE,QAAS,kCACT0C,QAAS,WAEb,CAAE,MAAOvF,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,6BAC1B0C,QAAS,SAEb,YA3CQ2G,EAAM,CACV9N,MAAO,mBACPyE,QAAS,4CACT0C,QAAS,WAuX8BA,QAAQ,UAAS9H,SAAC,kBAGnDC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QA/UK+Q,KACvBrB,EAAoB,MACpBE,EAAY,CAAEN,MAAO,GAAIC,KAAM,GAAIC,iBAAkB,KA6URpI,QAAQ,YAAW9H,SAAC,oBAS9D+O,IACCjP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DI,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,6BAIpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEC,EAAAA,EAAAA,KAACgK,EAAgB,CACf5I,kBAAmBA,EACnB6I,kBAAmB+E,EACnB9E,mBAAoBA,EACpBC,kBA/kBgBsH,CAC5B7G,EACA8G,KAEAzC,EAAuB7F,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACtBkG,GAAI,IACP,CAACwB,GAAa8G,MA0kBJtH,aAAc,QAKlBvK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDC,EAAAA,EAAAA,KAAC0F,EAAU,CACTzG,MAAM,uBACNH,MAAOoQ,EACPxP,SAAUyP,EACVxJ,IAAK,EACLC,IAAK,IACLC,YAAY,uBACZjG,SAAU8I,KAGZ7I,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,iBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGI,SAAA,CAC5GqR,KAAsB,qBAM7BpR,EAAAA,EAAAA,KAACR,EAAAA,EAAkB,CACjBV,MAAOwQ,EACP5P,SAAU6P,KAIZvP,EAAAA,EAAAA,KAACmM,EAAqB,CACpBrN,MAAO0Q,EACP9P,SAAU+P,KAIZ5P,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oCAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsQ,EACP1P,SAAW2G,GAAMgJ,EAAgBhJ,EAAEE,OAAOzH,OAC1Ca,UAAU,uJACV2R,KAAM,EACNzL,YAAY,8DAKhB7F,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QA9UqBgB,UAC/B,GAAiC,IAA7BL,EAAkBmJ,OAOpB,kBANMiE,EAAM,CACV9N,MAAO,wBACPyE,QACE,mEACF0C,QAAS,YAKb,MAAM8J,EAAaP,KACnB,GAAIzC,GAAQA,EAAKiD,kBAAoBD,EAMnC,kBALMnD,EAAM,CACV9N,MAAO,uBACPyE,QAAQ,YAADrF,OAAc6R,EAAU,yBAAA7R,OAAwBoP,EAAc,mCAAApP,OAAkC6O,EAAKiD,kBAAiB,uBAC7H/J,QAAS,UAYb,SAPwB4G,EAAQ,CAC9B/N,MAAO,sBACPyE,QAAQ,YAADrF,OAAcoP,EAAc,qBAAApP,OAAoBsB,EAAkBmJ,OAAM,oCAAAzK,OAAmC6R,EAAU,aAC5HE,YAAa,WACbC,WAAY,WAGd,CAEApC,GAAgB,GAChB,IACE,MAAMvN,QAAeqL,EAAuB,CAC1CC,aACAsE,YAAa3Q,EACb8I,qBACA8H,MAAO9C,EACPE,aAAcA,EAAa1I,aAAU4B,EACrCgH,kBACAE,gBACAyC,gBAAiBpF,EAAW5M,IAAKiS,GAAMA,EAAEnC,SAG3CxB,EAAsBpM,EAAO0K,YAGzB8B,GACFC,EAAAA,EAAa5B,WAAWmF,WAAW,CACjCP,kBAAmBzP,EAAOkL,mBAK9B,IACE,MAAM,cAAEP,SAAwB,uCAC1BC,EAAaD,EAAcE,iBAC3BD,EAAWE,uBAAuBQ,EAC1C,CAAE,MAAON,GACP3K,QAAQ4K,KAAK,qCAAsCD,EACrD,OAEMqB,EAAM,CACV9N,MAAO,UACPyE,QAAQ,aAADrF,OAAeqC,EAAO0K,WAAWtC,OAAM,6BAC9C1C,QAAS,YAIXmH,EAAqB,IACrBK,EAAgB,IAChBN,GAAY,EACd,CAAE,MAAOzM,SACDkM,EAAM,CACV9N,MAAO,mBACPyE,QAAS7C,EAAM6C,SAAW,gCAC1B0C,QAAS,SAEb,CAAC,QACC6H,GAAgB,EAClB,CAnDsB,GAmTZ9P,SAAuC,IAA7BwB,EAAkBmJ,QAAgB7B,EAC5C/I,UAAU,SACVkI,QAAQ,UAAS9H,SAEhB2I,EACG,gBAAe,YAAA5I,OACHoP,EAAc,wBAOtClP,EAAAA,EAAAA,KAACwI,EAAoB,CACnBE,aAAcA,EACdC,MAAOD,EAAe,wCAAqCJ,EAC3DM,cAAerB,KAAK8J,KAAKnC,EAAiB,OAI5CrP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,MAAIF,UAAU,iCAAgCI,SAAA,CAAC,uBACxB8M,EAAWtC,OAAO,OAGxCsC,EAAWtC,OAAS,IACnBvK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,8BAA6BI,UAC1CF,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oDAAmDI,SAAA,EAClEC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,WACL4R,QAAS5B,GACT9Q,SAAW2G,IAAMgM,OAnpBRD,EAmpBwB/L,EAAEE,OAAO6L,QAlpBxD3B,GAAsB2B,QAEpB7B,GADE6B,EACoBvF,EAAW5M,IAAKiS,GAAMA,EAAE7O,IAExB,IALD+O,OAopBTzS,UAAU,4GAEZK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,wBAOdC,EAAAA,EAAAA,KAAC4N,EAAkB,CACjBC,cAAeyC,GAAmB/F,OAClCuD,WAAYjB,EAAWtC,OACvBwD,iBAlpBiBtM,UACvB,GAAkC,IAA9B6O,GAAmB/F,OAAvB,CAGA,GAAiB,OAAZsE,QAAY,IAAZA,IAAAA,EAAcyD,0BAA2B,CAC5C,IAAIC,GAAuB,EAiB3B,UAfwB9D,EAAQ,CAC9B/N,MAAO,oBACPyE,QAAQ,mCAADrF,OACLwQ,GAAmB/F,OAAM,cAAAzK,OACgB,IAA9BwQ,GAAmB/F,OAAe,IAAM,GAAE,KACvD1C,QAAS,SACTgK,YAAa,SACbC,WAAY,SACZU,aAAc,UACdC,mBAAmB,EACnBC,sBAAwBN,IACtBG,EAAuBH,KAIX,OAGhB,GAAIG,EACF,UACQnN,EAAe,CAAEkN,2BAA2B,GACpD,CAAE,MAAOhQ,GACPE,QAAQF,MAAM,kCAAmCA,EAEnD,CAEJ,OAEMwO,IAlCqC,GAkpBrC9C,iBAAkB9J,GAClB5C,UAAWoP,KAIZ7D,EAAWtC,OAAS,IACnB1K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,mEAGrCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,UACEY,QA5sBckS,KAC1B,MAAMC,EAAkB,IAAIvR,IAAIwL,EAAW5M,IAAKiS,GAAMA,EAAE7O,KACxDwN,GAAsB+B,IA2sBVjT,UAAU,mLAAkLI,SAAA,EAE5LC,EAAAA,EAAAA,KAAC6S,EAAAA,IAAK,CAAClT,UAAU,aACjBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,iBAERF,EAAAA,EAAAA,MAAA,UACEY,QA7sBYqS,KACxBjC,GAAsB,IAAIxP,MA6sBd1B,UAAU,mLAAkLI,SAAA,EAE5LC,EAAAA,EAAAA,KAAC+S,EAAAA,IAAQ,CAACpT,UAAU,aACpBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,sBAMS,IAAtB8M,EAAWtC,QACVvK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iCAAgCI,SAAC,oEAIhDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvB8M,EAAW5M,IAAKkR,IACfnR,EAAAA,EAAAA,KAAA,OAEEL,UAAU,wGAAuGI,UAEjHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BI,SAAA,EAEzCC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,qBAAoBI,UACjCC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,WACL4R,QAAS9B,GAAmBrJ,SAASkK,EAAU9N,IAC/C3D,SAAW2G,IACT2M,OAztBSC,EAytBa9B,EAAU9N,QAAIgD,EAAEE,OAAO6L,QAvtB/D7B,GAAuBnH,GAAS,IAAIA,EAAM6J,KAE1C1C,GAAuBnH,GAASA,EAAK9F,OAAQD,GAAOA,IAAO4P,IAC3DxC,IAAsB,KALIuC,IAACC,GA2tBXtT,UAAU,8GAIdK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iBAAgBI,UAC7BF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OACEF,UAAU,sCACVc,QAASA,KAAMyS,OAtwBND,EAswB4B9B,EAAU9N,QArwBnEwN,GAAuBzH,IACrB,MAAM+J,EAAS,IAAI9R,IAAI+H,GAMvB,OALI+J,EAAOpP,IAAIkP,GACbE,EAAOnP,OAAOiP,GAEdE,EAAOlP,IAAIgP,GAENE,IARoBF,OAuwBTvS,MACEkQ,GAAmB7M,IAAIoN,EAAU9N,IAC7B,uBACA,yBACLtD,SAAA,EAEDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,QAAML,UAAU,gDAA+CI,SAAC,WAGhEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wEAAuEI,SACjFoR,EAAUpB,WAGda,GAAmB7M,IAAIoN,EAAU9N,KAChCxD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,QAAML,UAAU,gDAA+CI,SAAC,UAGhEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,4DAA2DI,SACrEoR,EAAUnB,WAIfhQ,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,8EAA6EI,SAAC,iCAK/FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDI,SAAA,CAC/DoR,EAAUI,kBACTvR,EAAAA,EAAAA,KAAA,QAAML,UAAU,uDAAsDI,SAAC,iBAIxEoR,EAAUlB,mBACTpQ,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,cACQ,IAC2B,kBAA/BoR,EAAUlB,kBACdmD,EAAAA,EAAAA,IACEjC,EAAUlB,mBAEZmD,EAAAA,EAAAA,KACEC,EAAAA,EAAAA,IACElC,EAAUlB,uBAKtBpQ,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,aACOoR,EAAUmC,gBAAkB,EAAE,mBAK/CzT,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAloBJ0Q,KAC3BhB,EAAoBgB,GACpBd,EAAY,CACVN,MAAOoB,EAAUpB,MACjBC,KAAMmB,EAAUnB,KAChBC,iBACwC,kBAA/BkB,EAAUlB,kBACbsD,EAAAA,EAAAA,IACEpC,EAAUlB,kBAEZkB,EAAUlB,kBAAoB,KAwnBDuD,CAAoBrC,GACnCxR,UAAU,qCACVe,MAAM,iBAAgBX,SACvB,kBAGDC,EAAAA,EAAAA,KAAA,UACES,QAASA,IApkBHgB,WAE5B,GAAgB,OAAZoN,QAAY,IAAZA,GAAAA,EAAcyD,0BAGhB,kBADMpB,GAAyBC,GAIjC,IAAIoB,GAAuB,EAkB3B,SAhBwB9D,EAAQ,CAC9B/N,MAAO,mBACPyE,QAAQ,6DAADrF,OAA+DqR,EAAUpB,MAAM0D,UACpF,EACA,KACD3T,OAAGqR,EAAUpB,MAAMxF,OAAS,GAAK,MAAQ,IAC1C1C,QAAS,SACTgK,YAAa,SACbC,WAAY,SACZU,aAAc,UACdC,mBAAmB,EACnBC,sBAAwBN,IACtBG,EAAuBH,KAI3B,CAGA,GAAIG,EACF,UACQnN,EAAe,CAAEkN,2BAA2B,GACpD,CAAE,MAAOhQ,GACPE,QAAQF,MAAM,kCAAmCA,EAEnD,OAGI4O,GAAyBC,EAZT,GA0iBeuC,CAAsBvC,GACrCxR,UAAU,uCACVe,MAAM,mBAAkBX,SACzB,mCAzFJoR,EAAU9N,c,cCnyBxB,MAAMsQ,EAAgDlU,IAOtD,IAPuD,WAC5DgO,EAAU,UACVF,EAAS,gBACTqG,EAAe,kBACfC,EAAiB,kBACjBC,EAAiB,qBACjBC,GACDtU,EACC,MAAM,MAAE+O,EAAK,QAAEC,IAAYC,EAAAA,EAAAA,MACrB,KAAEC,IAASC,EAAAA,EAAAA,MACVE,EAAUC,IAAerK,EAAAA,EAAAA,WAAS,IAClCtD,EAAmB4N,IAAwBtK,EAAAA,EAAAA,UAAmB,KAC9DwF,EAAoB+E,IAAyBvK,EAAAA,EAAAA,UAEjD,CAAC,IACGsP,EAAeC,IAAoBvP,EAAAA,EAAAA,UAAS,KAC5C0K,EAAcC,IAAmB3K,EAAAA,EAAAA,UAAS,KAC1C4K,EAAiBC,IAAsB7K,EAAAA,EAAAA,UAC5C3F,EAAAA,GAAgBI,SAEXqQ,EAAeC,IAAoB/K,EAAAA,EAAAA,UACxCqH,EAAAA,GAAc5M,SAET+U,EAAuBC,IAA4BzP,EAAAA,EAAAA,UAExD,CAAC,kBAAmB,aAAc,aAAc,kBAC3CgE,EAAcgH,IAAmBhL,EAAAA,EAAAA,WAAS,IAC1CiL,EAAaC,IAAkBlL,EAAAA,EAAAA,WAAS,IACxC0P,EAAaC,IAAkB3P,EAAAA,EAAAA,UAAsB,CAC1D4P,cAAe,GACfC,cAAe,kBACfC,QAAS,CAAC,GAAI,GAAI,GAAI,IACtBC,gBAAiB,GACjBC,YAAa,GACbzE,iBAAkB,KAEb0E,EAAiBC,IAAsBlQ,EAAAA,EAAAA,UAC5C,OAIKmQ,EAAmBC,IAAwBpQ,EAAAA,EAAAA,UAAmB,KAC9D8L,EAAoBC,IAAyB/L,EAAAA,EAAAA,WAAS,IACtDgM,GAAgBC,KAAqBjM,EAAAA,EAAAA,WAAS,IAC9C0L,GAAUC,KAAe3L,EAAAA,EAAAA,UAAsB,CACpD4P,cAAe,GACfC,cAAe,kBACfC,QAAS,CAAC,GAAI,GAAI,GAAI,IACtBC,gBAAiB,GACjBC,YAAa,GACbzE,iBAAkB,KAIb8E,GAAmBC,KAAwBtQ,EAAAA,EAAAA,UAChD,IAAIrD,KAyCA6C,GAAiBA,KACrB4Q,EAAqB,IACrBrE,GAAsB,IA0TlBW,GAAsBA,IAGnB7J,KAAK8J,KAAK2C,EAAgB,GAgJ7BiB,GAAsBzU,IAC1B2T,EAA0B/K,GACxBA,EAAKnC,SAASzG,GAAQ4I,EAAK9F,OAAQ4R,GAAMA,IAAM1U,GAAQ,IAAI4I,EAAM5I,KAIrE,OACEX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8EAA6EI,SAAA,EAC1FC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAC,yBAIjDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBI,SAAA,EACnCC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QAASA,IAAMmP,GAAgBD,GAC/B9H,QAAQ,YACRD,KAAK,KAAI7H,SAER4P,EAAc,SAAW,kBAG5B9P,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAML,UAAU,wBAAuBI,SAAC,aACxCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMsO,GAAaD,GAC5BnP,UAAS,6EAAAG,OACPgP,EAAW,iBAAmB,eAC7B/O,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPgP,EAAW,gBAAkB,8BASxCa,IACC9P,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DI,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,sBAGpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,mBAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsV,EAAYE,cACnB5U,SAAW2G,GACTgO,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACPkL,cAAejO,EAAEE,OAAOzH,SAG5Ba,UAAU,uJACV2R,KAAM,EACNzL,YAAY,+BAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,mBAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOsV,EAAYG,cACnB7U,SAAW2G,IACT8O,OA9GkB3U,EA8GO6F,EAAEE,OAAOzH,WA7GhDuV,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACPmL,cAAe/T,EACfgU,QACW,oBAAThU,GAAuC,eAATA,EAC1B,CAAC,GAAI,GAAI,GAAI,IACb,GACNiU,gBAAiB,MARajU,OAgHpBb,UAAU,kIAAiII,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,kBAAiBiB,SAAC,qBAChCC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,aAAYiB,SAAC,2BAC3BC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,aAAYiB,SAAC,gBAC3BC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,eAAciB,SAAC,wBAKD,oBAA9BqU,EAAYG,eACkB,eAA9BH,EAAYG,iBACZ1U,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oBAGhEC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvBqU,EAAYI,QAAQvU,IAAI,CAACC,EAAQkV,KAChCvV,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BI,SAAA,EACtDC,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,KAAM4U,OAlHFC,EAkH4BpV,OAjH7DmU,EAAgBjL,IACd,MAAMjJ,EAAaiJ,EAAKqL,gBAAgBxN,SAASqO,GACjD,MAA2B,oBAAvBlM,EAAKmL,eAEPrR,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAYkG,GAAI,IAAEqL,gBAAiBtU,EAAa,GAAK,CAACmV,MAGtDpS,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKkG,GAAI,IACPqL,gBAAiBtU,EACbiJ,EAAKqL,gBAAgBnR,OAAQiS,GAAMA,IAAMD,GACzC,IAAIlM,EAAKqL,gBAAiBa,OAZHA,OAmHb3V,UAAS,kCAAAG,OACuB,oBAA9BsU,EAAYG,cACR,eACA,UAAS,KAAAzU,OAEbsU,EAAYK,gBAAgBxN,SAAS/G,GACjC,oCACA,kBAAiB,qCAEvBN,UAAWM,EAAOwG,OAAO3G,SAExBqU,EAAYK,gBAAgBxN,SAAS/G,KACpCF,EAAAA,EAAAA,KAAA,QAAML,UAAU,qBAAoBI,SAAC,cAGzCC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,OACL1B,MAAOoB,EACPR,SAAW2G,GA5INmP,EAACJ,EAAetW,KACzCuV,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACPoL,QAASpL,EAAKoL,QAAQvU,IAAI,CAACwV,EAAKC,IAAOA,IAAMN,EAAQtW,EAAQ2W,OA0IzCD,CAAmBJ,EAAO/O,EAAEE,OAAOzH,OAErCa,UAAU,uJACVkG,YAAW,UAAA/F,OAAYsV,EAAQ,OA1BzBA,OA+BdpV,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SACR,oBAA9BqU,EAAYG,cACT,8CACA,qDAMqB,eAA9BH,EAAYG,gBACX1U,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IACP4T,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACPqL,gBAAiB,CAAC,WAGtB9U,UAAS,+BAAAG,OACPsU,EAAYK,gBAAgBxN,SAAS,QACjC,+CACA,uDACHlH,SACJ,UAGDC,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IACP4T,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACPqL,gBAAiB,CAAC,YAGtB9U,UAAS,+BAAAG,OACPsU,EAAYK,gBAAgBxN,SAAS,SACjC,+CACA,uDACHlH,SACJ,gBAQwB,iBAA9BqU,EAAYG,gBACX1U,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,uCAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsV,EAAYK,gBAAgBkB,KAAK,MACxCjW,SAAW2G,GACTgO,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACPqL,gBAAiBpO,EAAEE,OAAOzH,MACvB8W,MAAM,MACNtS,OAAQiS,GAAMA,EAAE7O,WAGvB/G,UAAU,uJACV2R,KAAM,EACNzL,YAAY,kDAKlBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,4BAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsV,EAAYM,YACnBhV,SAAW2G,GACTgO,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACPsL,YAAarO,EAAEE,OAAOzH,SAG1Ba,UAAU,uJACV2R,KAAM,EACNzL,YAAY,sCAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOsV,EAAYnE,iBACnBvQ,SAAW2G,GACTgO,EAAgBjL,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfkG,GAAI,IACP6G,iBAAkBrJ,SAASP,EAAEE,OAAOzH,UAGxCa,UAAU,kIAAiII,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,mBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,gBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,yBAItBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QA7nBMgB,UACxB,GAAK2S,EAAYE,cAAc5N,OAS/B,GAA2C,IAAvC0N,EAAYK,gBAAgBlK,OAAhC,CAUA,GACgC,oBAA9B6J,EAAYG,eACkB,eAA9BH,EAAYG,cACZ,CAIA,GAHqBH,EAAYI,QAAQlR,OACtCmS,GAAQA,EAAI/O,OAAO6D,OAAS,GAEdA,OAAS,EAOxB,kBANMiE,EAAM,CACV9N,MAAO,mBACPyE,QACE,uEACF0C,QAAS,SAIf,CAEA,IACE,MAAMhG,QAAiBC,MAAM,iCAADhC,OACO2N,GACjC,CACE3K,OAAQ,OACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,gBAEhDmB,KAAMsC,KAAKC,UAAU,CACnBgP,cAAeF,EAAYE,cAAc5N,OACzC6N,cAAeH,EAAYG,cAC3BC,QACgC,oBAA9BJ,EAAYG,eACkB,eAA9BH,EAAYG,cACRH,EAAYI,QAAQlR,OAAQmS,GAAQA,EAAI/O,OAAO6D,OAAS,GACxD,KACNkK,gBAAiBL,EAAYK,gBAC7BC,YAAaN,EAAYM,YAAYhO,QAAU,KAC/CuJ,iBAAkBmE,EAAYnE,qBAKpC,IAAKpO,EAASI,GACZ,MAAM,IAAIC,MAAM,6BAGlB,MAAMC,QAAeN,EAASO,OAC9BwR,EAAgBzR,EAAOI,MAGvB8R,EAAe,CACbC,cAAe,GACfC,cAAe,kBACfC,QAAS,CAAC,GAAI,GAAI,GAAI,IACtBC,gBAAiB,GACjBC,YAAa,GACbzE,iBAAkB,IAEpBL,GAAe,SAETpB,EAAM,CACV9N,MAAO,UACPyE,QAAS,+BACT0C,QAAS,WAEb,CAAE,MAAOvF,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,yBAC1B0C,QAAS,SAEb,CA1EA,YANQ2G,EAAM,CACV9N,MAAO,mBACPyE,QAAS,2CACT0C,QAAS,qBAZL2G,EAAM,CACV9N,MAAO,mBACPyE,QAAS,6BACT0C,QAAS,WAwnBiCA,QAAQ,UAAS9H,SAAC,kBAGtDC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QAASA,IAAMmP,GAAe,GAAQ/H,QAAQ,YAAW9H,SAAC,oBASzE4U,IACC9U,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DI,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,mBACpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,mBAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsR,GAASkE,cAChB5U,SAAW2G,GACTgK,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPkL,cAAejO,EAAEE,OAAOzH,SAG5Ba,UAAU,uJACV2R,KAAM,EACNzL,YAAY,+BAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,mBAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOsR,GAASmE,cAChB7U,SAAW2G,GACTgK,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPmL,cAAelO,EAAEE,OAAOzH,MACxB0V,QACqB,oBAAnBnO,EAAEE,OAAOzH,OACU,eAAnBuH,EAAEE,OAAOzH,MACL,CAAC,GAAI,GAAI,GAAI,IACb,GACN2V,gBAAiB,MAGrB9U,UAAU,kIAAiII,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,kBAAiBiB,SAAC,qBAChCC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,aAAYiB,SAAC,2BAC3BC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,aAAYiB,SAAC,gBAC3BC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,eAAciB,SAAC,wBAKJ,oBAA3BqQ,GAASmE,eACkB,eAA3BnE,GAASmE,iBACT1U,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oBAGhEC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvBqQ,GAASoE,QAAQvU,IAAI,CAACC,EAAQkV,KAC7BvV,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BI,SAAA,EACtDC,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,KACP,MAAMN,EACJiQ,GAASqE,gBAAgBxN,SAAS/G,GACL,oBAA3BkQ,GAASmE,cACXlE,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPqL,gBAAiBtU,EAAa,GAAK,CAACD,MAGtCmQ,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPqL,gBAAiBtU,EACbiJ,EAAKqL,gBAAgBnR,OAClBiS,GAAMA,IAAMrV,GAEf,IAAIkJ,EAAKqL,gBAAiBvU,OAIpCP,UAAS,kCAAAG,OACoB,oBAA3BsQ,GAASmE,cACL,eACA,UAAS,KAAAzU,OAEbsQ,GAASqE,gBAAgBxN,SAAS/G,GAC9B,oCACA,kBAAiB,qCAEvBN,UAAWM,EAAOwG,OAAO3G,SAExBqQ,GAASqE,gBAAgBxN,SAAS/G,KACjCF,EAAAA,EAAAA,KAAA,QAAML,UAAU,qBAAoBI,SAAC,cAGzCC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,OACL1B,MAAOoB,EACPR,SAAW2G,GACTgK,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPoL,QAASpL,EAAKoL,QAAQvU,IAAI,CAACwV,EAAKC,IAC9BA,IAAMN,EAAQ/O,EAAEE,OAAOzH,MAAQ2W,MAIrC9V,UAAU,uJACVkG,YAAW,UAAA/F,OAAYsV,EAAQ,OAjDzBA,OAsDdpV,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SACX,oBAA3BqQ,GAASmE,cACN,8CACA,qDAMkB,eAA3BnE,GAASmE,gBACR1U,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IACP4P,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPqL,gBAAiB,CAAC,WAGtB9U,UAAS,+BAAAG,OACPsQ,GAASqE,gBAAgBxN,SAAS,QAC9B,+CACA,uDACHlH,SACJ,UAGDC,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IACP4P,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPqL,gBAAiB,CAAC,YAGtB9U,UAAS,+BAAAG,OACPsQ,GAASqE,gBAAgBxN,SAAS,SAC9B,+CACA,uDACHlH,SACJ,gBAQqB,iBAA3BqQ,GAASmE,gBACR1U,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,uCAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsR,GAASqE,gBAAgBkB,KAAK,MACrCjW,SAAW2G,GACTgK,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPqL,gBAAiBpO,EAAEE,OAAOzH,MACvB8W,MAAM,MACNtS,OAAQiS,GAAMA,EAAE7O,WAGvB/G,UAAU,uJACV2R,KAAM,EACNzL,YAAY,kDAKlBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,4BAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsR,GAASsE,YAChBhV,SAAW2G,GACTgK,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACPsL,YAAarO,EAAEE,OAAOzH,SAG1Ba,UAAU,uJACV2R,KAAM,EACNzL,YAAY,sCAIhBhG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOsR,GAASH,iBAChBvQ,SAAW2G,GACTgK,GAAajH,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZkG,GAAI,IACP6G,iBAAkBrJ,SAASP,EAAEE,OAAOzH,UAGxCa,UAAU,kIAAiII,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,mBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,gBAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,cAClBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,yBAItBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QAntBGgB,UACrB,GAAKkT,EAEL,GAAKvE,GAASkE,cAAc5N,OAS5B,GAAwC,IAApC0J,GAASqE,gBAAgBlK,OAA7B,CAUA,GAC6B,oBAA3B6F,GAASmE,eACkB,eAA3BnE,GAASmE,cACT,CAIA,GAHqBnE,GAASoE,QAAQlR,OACnCmS,GAAQA,EAAI/O,OAAO6D,OAAS,GAEdA,OAAS,EAOxB,kBANMiE,EAAM,CACV9N,MAAO,mBACPyE,QACE,uEACF0C,QAAS,SAIf,CAEA,IACE,MAAMhG,QAAiBC,MAAM,uBAADhC,OACH6U,EAAgBtR,IACvC,CACEP,OAAQ,MACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,gBAEhDmB,KAAMsC,KAAKC,UAAU,CACnBgP,cAAelE,GAASkE,cAAc5N,OACtC6N,cAAenE,GAASmE,cACxBC,QAC6B,oBAA3BpE,GAASmE,eACkB,eAA3BnE,GAASmE,cACLnE,GAASoE,QAAQlR,OAAQmS,GAAQA,EAAI/O,OAAO6D,OAAS,GACrD,KACNkK,gBAAiBrE,GAASqE,gBAC1BC,YAAatE,GAASsE,YAAYhO,QAAU,KAC5CuJ,iBAAkBG,GAASH,qBAKjC,IAAKpO,EAASI,GACZ,MAAM,IAAIC,MAAM,6BAGlB,MAAM2T,QAAwBhU,EAASO,OACvCyR,EAAkBgC,EAAgBtT,MAClCqS,EAAmB,YAEbpG,EAAM,CACV9N,MAAO,UACPyE,QAAS,iCACT0C,QAAS,WAEb,CAAE,MAAOvF,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,4BAC1B0C,QAAS,SAEb,CAhEA,YANQ2G,EAAM,CACV9N,MAAO,mBACPyE,QAAS,2CACT0C,QAAS,qBAZL2G,EAAM,CACV9N,MAAO,mBACPyE,QAAS,6BACT0C,QAAS,WA4sB8BA,QAAQ,UAAS9H,SAAC,kBAGnDC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QAhoBK+Q,KACvBoD,EAAmB,MACnBvE,GAAY,CACViE,cAAe,GACfC,cAAe,kBACfC,QAAS,CAAC,GAAI,GAAI,GAAI,IACtBC,gBAAiB,GACjBC,YAAa,GACbzE,iBAAkB,KAwnByBpI,QAAQ,YAAW9H,SAAC,oBAS9D+O,IACCjP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DI,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,4BAIpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEC,EAAAA,EAAAA,KAACgK,EAAgB,CACf5I,kBAAmBA,EACnB6I,kBAAmB+E,EACnB9E,mBAAoBA,EACpBC,kBAl5BgBsH,CAC5B7G,EACA8G,KAEAzC,EAAuB7F,IAAIlG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACtBkG,GAAI,IACP,CAACwB,GAAa8G,MA64BJtH,aAAc,QAKlBvK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDC,EAAAA,EAAAA,KAAC0F,EAAU,CACTzG,MAAM,sBACNH,MAAOkV,EACPtU,SAAUuU,EACVtO,IAAK,EACLC,IAAK,IACLC,YAAY,uBACZjG,SAAU8I,KAGZ7I,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,iBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGI,SAAA,CAC5GqR,KAAsB,qBAM7BvR,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDC,EAAAA,EAAAA,KAAC8V,EAAAA,EAAQ,CACP7W,MAAM,kBACNmT,QAAS8B,EAAsBjN,SAAS,mBACxCvH,SAAUA,IAAMuV,GAAmB,mBACnCrN,KAAK,QAEP5H,EAAAA,EAAAA,KAAC8V,EAAAA,EAAQ,CACP7W,MAAM,aACNmT,QAAS8B,EAAsBjN,SAAS,cACxCvH,SAAUA,IAAMuV,GAAmB,cACnCrN,KAAK,QAEP5H,EAAAA,EAAAA,KAAC8V,EAAAA,EAAQ,CACP7W,MAAM,aACNmT,QAAS8B,EAAsBjN,SAAS,cACxCvH,SAAUA,IAAMuV,GAAmB,cACnCrN,KAAK,QAEP5H,EAAAA,EAAAA,KAAC8V,EAAAA,EAAQ,CACP7W,MAAM,eACNmT,QAAS8B,EAAsBjN,SAAS,gBACxCvH,SAAUA,IAAMuV,GAAmB,gBACnCrN,KAAK,cAMX5H,EAAAA,EAAAA,KAACR,EAAAA,EAAkB,CACjBV,MAAOwQ,EACP5P,SAAU6P,KAIZvP,EAAAA,EAAAA,KAACmM,EAAqB,CACpBrN,MAAO0Q,EACP9P,SAAU+P,KAIZ5P,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oCAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOsQ,EACP1P,SAAW2G,GAAMgJ,EAAgBhJ,EAAEE,OAAOzH,OAC1Ca,UAAU,uJACV2R,KAAM,EACNzL,YAAY,6DAKhB7F,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QA/tBoBgB,UAC9B,GAAiC,IAA7BL,EAAkBmJ,OAOpB,kBANMiE,EAAM,CACV9N,MAAO,wBACPyE,QACE,kEACF0C,QAAS,YAKb,GAAqC,IAAjCqM,EAAsB3J,OAMxB,kBALMiE,EAAM,CACV9N,MAAO,6BACPyE,QAAS,wDACT0C,QAAS,YAKb,MAAM8J,EAAaP,KACnB,GAAIzC,GAAQA,EAAKiD,kBAAoBD,EAMnC,kBALMnD,EAAM,CACV9N,MAAO,uBACPyE,QAAQ,YAADrF,OAAc6R,EAAU,yBAAA7R,OAAwBkU,EAAa,kCAAAlU,OAAiC6O,EAAKiD,kBAAiB,uBAC3H/J,QAAS,YAYb,SAPwB4G,EAAQ,CAC9B/N,MAAO,qBACPyE,QAAQ,YAADrF,OAAckU,EAAa,mBAAAlU,OAAkB6R,EAAU,aAC9DE,YAAa,WACbC,WAAY,WAGd,CAEApC,GAAgB,GAChB,IACE,MAAM7N,QAAiBC,MAAM,uCAAwC,CACnEgB,OAAQ,OACRf,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,gBAEhDmB,KAAMsC,KAAKC,UAAU,CACnBmI,aACAsE,YAAa3Q,EACb8I,qBACA8H,MAAOgC,EACP5E,aAAcA,EAAa1I,aAAU4B,EACrCgH,kBACAE,gBACAuG,cAAe7B,EACfjC,gBAAiB1E,EAAUtN,IAAK+V,GAAMA,EAAE1B,mBAI5C,IAAKzS,EAASI,GACZ,MAAM,IAAIC,MAAM,gCAGlB,MAAMC,QAAeN,EAASO,OAC9B2R,EAAqB5R,EAAOI,KAAKgL,WAG7BoB,GACFC,EAAAA,EAAa5B,WAAWmF,WAAW,CACjCP,kBAAmBzP,EAAOI,KAAK8K,mBAKnC,IACE,MAAM,cAAEP,SAAwB,uCAC1BC,EAAaD,EAAcE,iBAC3BD,EAAWE,uBAAuBQ,EAC1C,CAAE,MAAON,GACP3K,QAAQ4K,KAAK,qCAAsCD,EACrD,OAEMqB,EAAM,CACV9N,MAAO,UACPyE,QAAQ,aAADrF,OAAeqC,EAAOI,KAAKgL,UAAUhD,OAAM,4BAClD1C,QAAS,YAIXmH,EAAqB,IACrBK,EAAgB,IAChBN,GAAY,EACd,CAAE,MAAOzM,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,+BAC1B0C,QAAS,SAEb,CAAC,QACC6H,GAAgB,EAClB,CAhEsB,GA2rBZ9P,SAC+B,IAA7BwB,EAAkBmJ,QACe,IAAjC2J,EAAsB3J,QACtB7B,EAEF/I,UAAU,SACVkI,QAAQ,UAAS9H,SAEhB2I,EACG,gBAAe,YAAA5I,OACHkU,EAAa,uBAOrChU,EAAAA,EAAAA,KAACwI,EAAoB,CACnBE,aAAcA,EACdC,MACED,EAAe,4CAAyCJ,EAE1DM,cAAerB,KAAK8J,KAAK2C,EAAgB,MAI3ChU,EAAAA,EAAAA,KAAC4N,EAAkB,CACjBC,cAAegH,EAAkBtK,OACjCuD,WAAYP,EAAUhD,OACtBwD,iBA9hCmBtM,UACvB,GAAiC,IAA7BoT,EAAkBtK,OAAc,aACZkE,EAAQ,CAC9B/N,MAAO,mBACPyE,QAAQ,mCAADrF,OACL+U,EAAkBtK,OAAM,cAAAzK,OACgB,IAA7B+U,EAAkBtK,OAAe,IAAM,GAAE,KACtD1C,QAAS,SACTgK,YAAa,SACbC,WAAY,aAGdnB,IAAkB,QAxCQlP,WAC1B,UAEQwU,QAAQC,IACZrB,EAAkB5U,IAAKoD,GACrBvB,MAAM,uBAADhC,OAAwBuD,GAAM,CACjCP,OAAQ,SACRf,QAAS,CACPC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,oBAMtDiT,EAAkB5D,QAAS5N,GAAOyQ,EAAkBzQ,IACpDa,IACF,CAAE,MAAO5B,GACPE,QAAQF,MAAM,uBAAwBA,SAChCkM,EAAM,CACV9N,MAAO,QACPyE,QAAS,uCACT0C,QAAS,SAEb,CAAC,QACC8I,IAAkB,EACpB,GAgBMG,KAkhCF9C,iBAAkB9J,GAClB5C,UAAWoP,GACX/Q,UAAU,OACVsO,SAAS,cAIXpO,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC8V,EAAAA,EAAQ,CACP1D,QAAS5B,EACT9Q,SAAW0S,GArlCEA,KACvB3B,EAAsB2B,GAEpB0C,EADE1C,EACmB7E,EAAUtN,IAAK+V,GAAMA,EAAE3S,IAEvB,KAglCUgP,CAAgBD,MAEzCvS,EAAAA,EAAAA,MAAA,MAAIF,UAAU,iCAAgCI,SAAA,CAAC,sBACzBwN,EAAUhD,OAAO,UAGxCgD,EAAUhD,OAAS,IAClB1K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,kEAGrCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,UACEY,QAjnCW0V,KACzB,MAAMC,EAAiB,IAAI/U,IAAIkM,EAAUtN,IAAK+V,GAAMA,EAAE3S,KACtD2R,GAAqBoB,IAgnCPzW,UAAU,mLAAkLI,SAAA,EAE5LC,EAAAA,EAAAA,KAAC6S,EAAAA,IAAK,CAAClT,UAAU,aACjBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,iBAERF,EAAAA,EAAAA,MAAA,UACEY,QA9iCS4V,KACvBrB,GAAqB,IAAI3T,MA8iCX1B,UAAU,mLAAkLI,SAAA,EAE5LC,EAAAA,EAAAA,KAAC+S,EAAAA,IAAQ,CAACpT,UAAU,aACpBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,yBAOM,IAArBwN,EAAUhD,QACTvK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iCAAgCI,SAAC,mEAIhDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvBwN,EAAUtN,IAAKqW,IACdtW,EAAAA,EAAAA,KAAA,OAEEL,UAAU,wGAAuGI,UAEjHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAC8V,EAAAA,EAAQ,CACP1D,QAASyC,EAAkB5N,SAASqP,EAASjT,IAC7C3D,SAAW0S,GA3oCAmE,EAACC,EAAoBpE,KAC5CA,EACF0C,EAAsB1L,GAAS,IAAIA,EAAMoN,KAEzC1B,EAAsB1L,GAASA,EAAK9F,OAAQD,GAAOA,IAAOmT,IAC1D/F,GAAsB,KAuoCN8F,CAAqBD,EAASjT,GAAI+O,GAEpCzS,UAAU,eAGZE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,OACEF,UAAU,uBACVc,QAASA,KAAMgW,OAvqCLD,EAuqC0BF,EAASjT,QAtqC/D2R,GAAsB5L,IACpB,MAAM+J,EAAS,IAAI9R,IAAI+H,GAMvB,OALI+J,EAAOpP,IAAIyS,GACbrD,EAAOnP,OAAOwS,GAEdrD,EAAOlP,IAAIuS,GAENrD,IARmBqD,OAwqCV9V,MACEqU,GAAkBhR,IAAIuS,EAASjT,IAC3B,uBACA,yBACLtD,SAAA,EAEDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,QAAML,UAAU,gDAA+CI,SAAC,cAGhEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wEAAuEI,SACjFuW,EAAShC,oBAIdzU,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,QAAML,UAAU,gDAA+CI,SAAC,UAGhEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,uEAAsEI,SAChFuW,EAAS/B,cAAcmC,QAAQ,IAAK,UAIxC3B,GAAkBhR,IAAIuS,EAASjT,KAC9BxD,EAAAA,EAAAA,MAAA8W,EAAAA,SAAA,CAAA5W,SAAA,CAC8B,eAA3BuW,EAAS/B,gBACR1U,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,QAAML,UAAU,gDAA+CI,SAAC,oBAGhEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,qCAAoCI,SAC9CuW,EAAS7B,gBAAgB,QAK/B6B,EAAS9B,UACR3U,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,QAAML,UAAU,gDAA+CI,SAAC,aAGhEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,oEAAmEI,SAC9EuW,EAAS9B,QAAQvU,IAAI,CAACC,EAAQkV,KAC7BvV,EAAAA,EAAAA,MAAA,MAEEF,UAAS,GAAAG,OACPwW,EAAS7B,gBAAgBxN,SAAS/G,GAC9B,6BACA,IACHH,SAAA,CAEFqV,EAAQ,EAAE,KAAGlV,IAAM,GAAAJ,OAPZwW,EAASjT,GAAE,YAAAvD,OAAWsV,UAcvCkB,EAAS5B,cACR7U,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,QAAML,UAAU,gDAA+CI,SAAC,iBAGhEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oEAAmEI,SAC7EuW,EAAS5B,qBAMlB1U,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,8EAA6EI,SAAC,4CAOjGF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDI,SAAA,CAC/DuW,EAAS/E,kBACRvR,EAAAA,EAAAA,KAAA,QAAML,UAAU,uDAAsDI,SAAC,iBAIxEuW,EAASrG,mBACRpQ,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,cACQ,IAC0B,kBAA9BuW,EAASrG,kBACbmD,EAAAA,EAAAA,IACEkD,EAASrG,mBAEXmD,EAAAA,EAAAA,KACEC,EAAAA,EAAAA,IACEiD,EAASrG,uBAKrBpQ,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,cACQuW,EAASM,iBAAmB,EAAE,aAE5C/W,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,YAAUuW,EAASO,eAAiB,EAAE,mBAIhDhX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAtiCD6V,KAC1B1B,EAAmB0B,GACnBjG,GAAY,CACViE,cAAegC,EAAShC,cACxBC,cAAe+B,EAAS/B,cACxBC,QAAS8B,EAAS9B,SAAW,CAAC,GAAI,GAAI,GAAI,IAC1CC,gBAAiB6B,EAAS7B,gBAC1BC,YAAa4B,EAAS5B,aAAe,GACrCzE,iBACuC,kBAA9BqG,EAASrG,kBACZsD,EAAAA,EAAAA,IACE+C,EAASrG,kBAEXqG,EAASrG,kBAAoB,KAyhCJ6G,CAAmBR,GAClC3W,UAAU,2CACVe,MAAM,gBAAeX,SACtB,kBAGDC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAvlCAgB,WAY3B,SAXwBgN,EAAQ,CAC9B/N,MAAO,kBACPyE,QAAQ,qDAADrF,OAAuDwW,EAAShC,cAAcb,UACnF,EACA,MACD3T,OAAGwW,EAAShC,cAAc/J,OAAS,IAAM,MAAQ,IAClD1C,QAAS,SACTgK,YAAa,SACbC,WAAY,WAKd,IAQE,WAPuBhQ,MAAM,uBAADhC,OAAwBwW,EAASjT,IAAM,CACjEP,OAAQ,SACRf,QAAS,CACPC,cAAc,UAADlC,OAAY6B,aAAaC,QAAQ,mBAIpCK,GACZ,MAAM,IAAIC,MAAM,6BAGlB4R,EAAkBwC,EAASjT,UAErBmL,EAAM,CACV9N,MAAO,UACPyE,QAAS,iCACT0C,QAAS,WAEb,CAAE,MAAOvF,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,4BAC1B0C,QAAS,SAEb,GAgjCiCkP,CAAqBT,GACpC3W,UAAU,uCACVe,MAAM,kBAAiBX,SACxB,8BAtIAuW,EAASjT,cCxvCjB2T,EAA8CvX,IAOpD,IAPqD,QAC1DwX,EAAO,SACPvX,EAAQ,SACRE,GAAW,EAAK,UAChBD,EAAY,GAAE,MACdV,EAAQ,gBAAe,YACvBC,EAAc,2DACfO,EACC,MAAMyX,EAAeA,KACdtX,GACHF,GAAUuX,IAWd,OACEpX,EAAAA,EAAAA,MAAA,OAAKF,UAAS,qCAAAG,OAAuCH,GAAYI,SAAA,EAC/DC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,SAAQI,UACrBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAML,UAAU,iCAAgCI,SAC7Cd,IAEFC,IACCc,EAAAA,EAAAA,KAAA,QAAML,UAAU,wBAAuBI,SACpCb,UAMTc,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACL2W,KAAK,SACL,eAAcF,EACd,gBAAAnX,OAAemX,EAAU,UAAY,SAAQ,KAAAnX,OAAIb,EAAMmI,eACvD3G,QAASyW,EACTlQ,UA5BiBoQ,IACH,MAAdA,EAAMlQ,KAA6B,UAAdkQ,EAAMlQ,MAC7BkQ,EAAM/P,iBACN6P,MA0BEtX,SAAUA,EACVD,UAAS,8OAAAG,OAGLF,EACE,4CACAqX,EACE,sCACA,gCAA+B,cAErClX,UAEFC,EAAAA,EAAAA,KAAA,QACEL,UAAS,iIAAAG,OAELmX,EAAU,gBAAkB,gBAAe,wBClD5CI,EAAyBA,KACpC,MAAM,GAAEhU,IAAOiU,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,OACX,gBAAEC,EAAe,UAAEnW,EAAS,MAAEgB,EAAK,qBAAEoV,IAAyB5K,EAAAA,EAAAA,kBAC9D,MAAE0B,EAAK,QAAEC,EAAO,OAAEkJ,IAAWjJ,EAAAA,EAAAA,MAC3BlK,SAAUqK,EAAY,eAAEzJ,IAAmBb,EAAAA,EAAAA,MAC5CqT,EAAcC,IAAmBnT,EAAAA,EAAAA,UAAuC,OACxEoT,EAAWC,IAAgBrT,EAAAA,EAAAA,UAA6B,UACxDsT,EAAgBC,IAAqBvT,EAAAA,EAAAA,UAAgC,eACrEmI,EAAYqL,IAAiBxT,EAAAA,EAAAA,UAAsB,KACnD6I,EAAW4K,IAAgBzT,EAAAA,EAAAA,UAAyB,KACpD0T,EAAcC,IAAmB3T,EAAAA,EAAAA,UAAS,KAEjDe,EAAAA,EAAAA,WAAU,KACJpC,GACFqU,EAAqBrU,GAAIiV,MAAM9V,QAAQF,QAExC,CAACe,EAAIqU,KAERjS,EAAAA,EAAAA,WAAU,KACW,OAAfgS,QAAe,IAAfA,GAAAA,EAAiB9K,WACnB0L,EAAgBZ,EAAgB9K,SAASxJ,MACzC+U,EAAcT,EAAgB5K,YAAc,IAC5CsL,EAAaV,EAAgBlK,WAAa,MAE3C,CAACkK,IAmJJ,GAAInW,EACF,OACEtB,EAAAA,EAAAA,KAAA,OAAKL,UAAU,8CAA6CI,UAC1DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,qEACfK,EAAAA,EAAAA,KAAA,QAAML,UAAU,qBAAoBI,SAAC,8BAM7C,GAAIuC,GAAyB,OAAfmV,QAAe,IAAfA,IAAAA,EAAiB9K,SAC7B,OACE3M,EAAAA,EAAAA,KAAA,OAAKL,UAAU,8CAA6CI,UAC1DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBI,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,oBAAmBI,SAC/BuC,GAAS,yBAEZtC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CAACzN,QAASA,IAAM8W,EAAS,cAAe1P,QAAQ,YAAW9H,SAAC,4BAQ3E,MAAM,SAAE4M,GAAa8K,EACfc,EAAgB1L,GAAcA,EAAWtC,OAAS,EAClDiO,EAAejL,GAAaA,EAAUhD,OAAS,EAErD,OACE1K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CI,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAM8W,EAAS,cACxB5X,UAAU,wDAAuDI,SAClE,+BAIDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,gCAA+BI,SAAEqY,KAE/CvY,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QA/KiBgB,UAC3B,IAAK4B,GAAsB,OAAfoU,QAAe,IAAfA,IAAAA,EAAiB9K,SAAU,OAEvC,MAAM8L,QAAgBd,EAAO,CAC3BjX,MAAO,mBACPyE,QAAS,uCACTuT,aAAcjB,EAAgB9K,SAASxJ,OAGzC,GAAgB,OAAZsV,GAAoBA,EAAQ/R,SAAW+Q,EAAgB9K,SAASxJ,KAEpE,GAAKsV,EAAQ/R,OASb,IAUE,WATuB5E,MAAM,mBAADhC,OAAoBuD,GAAM,CACpDP,OAAQ,MACRf,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADjC,OAAY6B,aAAaC,QAAQ,gBAElDmB,KAAMsC,KAAKC,UAAU,CAAEnC,KAAMsV,EAAQ/R,YAGzBzE,GACZ,MAAM,IAAIC,MAAM,8BAGlBmW,EAAgBI,EAAQ/R,cAClB8H,EAAM,CACV9N,MAAO,UACPyE,QAAS,kCACT0C,QAAS,kBAIL6P,EAAqBrU,EAC7B,CAAE,MAAOf,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,6BAC1B0C,QAAS,SAEb,YArCQ2G,EAAM,CACV9N,MAAO,eACPyE,QAAS,kCACT0C,QAAS,WAiKHA,QAAQ,YACRD,KAAK,KAAI7H,SACV,yBAGDC,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QAlIiBgB,UAC3B,IAAK4B,GAAsB,OAAfoU,QAAe,IAAfA,IAAAA,EAAiB9K,SAAU,OAUvC,SARwB8B,EAAQ,CAC9B/N,MAAO,mBACPyE,QAAQ,oCAADrF,OAAsC2X,EAAgB9K,SAASxJ,KAAI,qGAC1E0E,QAAS,SACTgK,YAAa,mBACbC,WAAY,WAKd,IAQE,WAPuBhQ,MAAM,mBAADhC,OAAoBuD,GAAM,CACpDP,OAAQ,SACRf,QAAS,CACP,cAAgB,UAADjC,OAAY6B,aAAaC,QAAQ,mBAItCK,GACZ,MAAM,IAAIC,MAAM,oCAGZsM,EAAM,CACV9N,MAAO,UACPyE,QAAS,kCACT0C,QAAS,YAGX0P,EAAS,aACX,CAAE,MAAOjV,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,6BAC1B0C,QAAS,SAEb,GA6FUA,QAAQ,SACRD,KAAK,KAAI7H,SACV,qCAMLF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDI,SAAA,EAChEC,EAAAA,EAAAA,KAAA,QAAML,UAAU,aAAYI,SAAE4M,EAASnM,OACtCmM,EAAS4E,kBACRvR,EAAAA,EAAAA,KAAA,QAAML,UAAU,uDAAsDI,SAAC,kBAIzEF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,WACK,IAAI4Y,KAAKhM,EAASiM,YAAYC,+BAM7C7Y,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,2BAA0BI,UACvCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBI,SAAA,EACpCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMsX,EAAa,SAC5BpY,UAAS,iGAAAG,OAES,UAAdgY,EACE,sCACA,6EAA4E,oBAEhF/X,SACH,6BAGDC,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMsX,EAAa,UAC5BpY,UAAS,iGAAAG,OAES,WAAdgY,EACE,sCACA,6EAA4E,oBAEhF/X,SACH,uCAQQ,UAAd+X,IACC9X,EAAAA,EAAAA,KAAA2W,EAAAA,SAAA,CAAA5W,UAEEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CI,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,uBAEtDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CI,SAAA,CAExDwY,IACCvY,EAAAA,EAAAA,KAAA,OACEL,UAAS,oGAAAG,OAEY,eAAjB8X,EACE,uCACA,wCAAuC,wBAG7CnX,QAASA,IAAMoX,EAAgB,cAAc9X,UAE7CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,WAAUI,SAAC,kBAC1BF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,sBACvCF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CACvB,OAAV8M,QAAU,IAAVA,OAAU,EAAVA,EAAYtC,OAAO,mDAQ7BiO,IACCxY,EAAAA,EAAAA,KAAA,OACEL,UAAS,oGAAAG,OAEY,SAAjB8X,EACE,uCACA,wCAAuC,wBAG7CnX,QAASA,IAAMoX,EAAgB,QAAQ9X,UAEvCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,WAAUI,SAAC,kBAC1BF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,mBACvCF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CACxB,OAATwN,QAAS,IAATA,OAAS,EAATA,EAAWhD,OAAO,sDAS9BqN,GAAgB/I,IACf7O,EAAAA,EAAAA,KAAA,OAAKL,UAAU,oEAAmEI,UAChFC,EAAAA,EAAAA,KAACgX,EAAa,CACZC,QAASpI,EAAaiK,mBACtBpZ,SAAU+B,UACR,UACQ2D,EAAe,CAAE0T,mBAAoB7B,GAC7C,CAAE,MAAO3U,GACPE,QAAQF,MAAM,oCAAqCA,EACrD,GAEFrD,MAAM,gBACNC,YAAY,+DAMlBc,EAAAA,EAAAA,KAACkO,EAAAA,EAAM,CACLzN,QAzUagB,UACvB,GAAK4B,GAAOuU,EAEZ,IACE,MAAMmB,GAA6B,OAAZlK,QAAY,IAAZA,OAAY,EAAZA,EAAciK,sBAAsB,QACrDhM,EAAAA,cAAcE,WAAWgM,kBAAkB3V,EAAIuU,EAAcmB,GACnExB,EAAS,UAADzX,OAAWuD,EAAE,KAAAvD,OAAI8X,GAC3B,CAAE,MAAOtV,SACDkM,EAAM,CACV9N,MAAO,QACPyE,QAAS7C,EAAM6C,SAAW,gCAC1B0C,QAAS,SAEb,GA6TUjI,UAAWgY,EACXjY,UAAU,SACViI,KAAK,KAAI7H,SAER6X,EAAY,SAAA9X,OACiB,eAAjB8X,EAAgC,mBAAqB,iBAC9D,6BAOG,WAAdE,GAA0BzU,IACzBxD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CI,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,EAClCF,EAAAA,EAAAA,MAAA,UACEY,QAASA,IAAMwX,EAAkB,cACjCtY,UAAS,iGAAAG,OAEc,eAAnBkY,EACE,4BACA,0DAAyD,oBAE7DjY,SAAA,CACH,4BACiB8M,EAAWtC,OAAO,QAEpC1K,EAAAA,EAAAA,MAAA,UACEY,QAASA,IAAMwX,EAAkB,QACjCtY,UAAS,iGAAAG,OAEc,SAAnBkY,EACE,4BACA,0DAAyD,oBAE7DjY,SAAA,CACH,0BACoBwN,EAAUhD,OAAO,UAKpB,eAAnByN,IACChY,EAAAA,EAAAA,KAACmO,EAAmB,CAClBV,WAAYpK,EACZwJ,WAAYA,EACZuB,iBA7QkByB,IAC5BqI,EAAc9O,GAAQ,IAAIA,EAAMyG,KA6QtBxB,mBA1QoB4K,IAC9Bf,EAAc9O,GAAQA,EAAKnJ,IAAIiZ,GAC7BA,EAAK7V,KAAO4V,EAAiB5V,GAAK4V,EAAmBC,KAyQ7C5K,mBArQoB2E,IAC9BiF,EAAc9O,GAAQA,EAAK9F,OAAO4V,GAAQA,EAAK7V,KAAO4P,KAqQ5C1E,sBAlQuB4K,IACjCjB,EAAc9O,GAAQ,IAAIA,KAAS+P,OAqQT,SAAnBnB,IACChY,EAAAA,EAAAA,KAAC2T,EAAc,CACblG,WAAYpK,EACZkK,UAAWA,EACXqG,gBAtQiBQ,IAC3B+D,EAAa/O,GAAQ,IAAIA,EAAMgL,KAsQrBP,kBAnQmBgC,IAC7BsC,EAAa/O,GAAQA,EAAKnJ,IAAIqW,GAC5BA,EAASjT,KAAOwS,EAAgBxS,GAAKwS,EAAkBS,KAkQ/CxC,kBA9PmB0C,IAC7B2B,EAAa/O,GAAQA,EAAK9F,OAAOgT,GAAYA,EAASjT,KAAOmT,KA8PnDzC,qBA3PsBqF,IAChCjB,EAAa/O,GAAQ,IAAIA,KAASgQ,WAiQhCvZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,uBAEpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yCAAwCI,SAAC,aACvDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCI,SAAA,CAC7CwY,IACC1Y,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CAAM8M,EAAWtC,OAAO,iBAEzBiO,IACC3Y,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CAAe,OAATwN,QAAS,IAATA,OAAS,EAATA,EAAWhD,OAAO,sBAExBgO,IAAkBC,IAClBxY,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,SAAC,yBAKpC4M,EAAS0M,kBAAoB1M,EAAS0M,iBAAiB9O,OAAS,IAC/D1K,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yCAAwCI,SAAC,sBACvDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,kCAAiCI,SAC7C4M,EAAS0M,iBAAiBpZ,IAAI,CAACsD,EAAK6R,KACnCpV,EAAAA,EAAAA,KAAA,OAAAD,SAAkBwD,EAAImH,UAAZ0K,SAMjBzI,EAAS2M,gBACRzZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeI,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yCAAwCI,SAAC,yBACvDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAE4M,EAAS2M,4B,uEC/WtD,IAAKva,EAAe,SAAfA,GAAe,OAAfA,EAAe,YAAfA,EAAe,gBAAfA,EAAe,YAAfA,EAAe,kBAAfA,EAAe,oBAAfA,EAAe,UAAfA,CAAe,MASfgN,EAAa,SAAbA,GAAa,OAAbA,EAAa,cAAbA,EAAa,gBAAbA,EAAa,YAAbA,CAAa,MAiMlB,MAAMqH,EAA2BmG,IACU,CAC9C,CAACxa,EAAgBC,MAAO,OACxB,CAACD,EAAgBI,QAAS,SAC1B,CAACJ,EAAgBK,MAAO,OACxB,CAACL,EAAgBM,SAAU,UAC3B,CAACN,EAAgBO,UAAW,WAC5B,CAACP,EAAgBQ,KAAM,OAEXga,IA+CHhG,EAA2BgG,IACW,CAC/C,CAACxa,EAAgBC,MAAO,EACxB,CAACD,EAAgBI,QAAS,EAC1B,CAACJ,EAAgBK,MAAO,EACxB,CAACL,EAAgBM,SAAU,EAC3B,CAACN,EAAgBO,UAAW,EAC5B,CAACP,EAAgBQ,KAAM,GAEVga,IAGJlG,EAA2BmG,IACtC,OAAQA,GACN,KAAK,EAEL,KAAK,EACH,OAAOza,EAAgBC,KACzB,KAAK,EAUL,QACE,OAAOD,EAAgBI,OATzB,KAAK,EACH,OAAOJ,EAAgBK,KACzB,KAAK,EACH,OAAOL,EAAgBM,QACzB,KAAK,EACH,OAAON,EAAgBO,SACzB,KAAK,EACH,OAAOP,EAAgBQ,K,sEChY7B,MAAMka,EAAmBC,IACvB,MAAMC,EAAW,IAAID,GACrB,IAAK,IAAIhE,EAAIiE,EAASpP,OAAS,EAAGmL,EAAI,EAAGA,IAAK,CAC5C,MAAMkE,EAAIrS,KAAKoC,MAAMpC,KAAKsS,UAAYnE,EAAI,KACzCiE,EAASjE,GAAIiE,EAASC,IAAM,CAACD,EAASC,GAAID,EAASjE,GACtD,CACA,OAAOiE,GAmGI7M,GAAgB7L,EAAAA,EAAAA,IAAmB,CAACC,EAAK4Y,KAAG,CACvDC,eAAgB,KAChBtC,gBAAiB,KACjBuC,UAAW,GACXC,SAAU,GACV3Y,WAAW,EACXgB,MAAO,KAGP4X,cAAe,GACfC,oBAAqB,EACrBC,SAAS,EACTC,SAAS,EAET3C,qBAAsBjW,eAAOgM,GAA8C,IAAD6M,EAAA,IAAzBC,EAAYC,UAAAjQ,OAAA,QAAAjC,IAAAkS,UAAA,IAAAA,UAAA,GAE3D,MAAM,gBAAE/C,GAAoBqC,IAC5B,GACES,IACC9C,IACuB,QAAxB6C,EAAA7C,EAAgB9K,gBAAQ,IAAA2N,OAAA,EAAxBA,EAA0BjX,MAAOoK,EACjC,CACAvM,EAAI,CAAEI,WAAW,EAAMgB,MAAO,OAE9B,IACE,MAAMZ,EAAQC,aAAaC,QAAQ,cAE7BC,QAAiBC,MAAM,mBAADhC,OAAoB2N,EAAU,YAAY,CACpE1L,QAAS,CACPC,cAAc,UAADlC,OAAY4B,MAI7B,IAAKG,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MACRc,EAAYV,OAAS,oCAEzB,CAEA,MAAMH,QAAeN,EAASO,OAE9B,IAAID,EAAOE,QAUT,MAAM,IAAIH,MAAMC,EAAOG,OATvBpB,EAAI,CACFuW,gBAAiB,CACf9K,SAAUxK,EAAOI,KAAKoK,SACtBE,WAAY1K,EAAOI,KAAKsK,YAAc,GACtCU,UAAWpL,EAAOI,KAAKgL,WAAa,IAEtCjM,WAAW,GAKjB,CAAE,MAAOgB,GAKP,MAJApB,EAAI,CACFoB,MAAOA,EAAM6C,SAAW,oCACxB7D,WAAW,IAEPgB,CACR,CACF,CACF,EAEA0W,kBAAmBvX,eACjBgM,EACAjN,GAEI,IAADia,EAAAC,EAAAC,EAAA,IADHC,EAAOJ,UAAAjQ,OAAA,QAAAjC,IAAAkS,UAAA,IAAAA,UAAA,GAEP,MAAM,gBAAE/C,EAAe,qBAAEC,GAAyBoC,IAG7CrC,IAA2C,QAAxBgD,EAAAhD,EAAgB9K,gBAAQ,IAAA8N,OAAA,EAAxBA,EAA0BpX,MAAOoK,SACjDiK,EAAqBjK,GAG7B,MAAMb,EAAUkN,IAAMrC,gBACtB,IAAK7K,EACH,MAAM,IAAI1K,MAAM,oCAGlB,MAAM2Y,EACK,eAATra,GACsB,QAAlBka,EAAA9N,EAAQC,kBAAU,IAAA6N,OAAA,EAAlBA,EAAoBnQ,SAAU,GACb,QAAjBoQ,EAAA/N,EAAQW,iBAAS,IAAAoN,OAAA,EAAjBA,EAAmBpQ,SAAU,EAEnC,GAAmB,IAAfsQ,EACF,MAAM,IAAI3Y,MAAM,wCAIlB,IAAI4Y,EACJ,GAAIF,EAKF,GAHAE,EAAgBC,MAAMC,KAAK,CAAEzQ,OAAQsQ,GAAc,CAACI,EAAGvF,IAAMA,GAGhD,eAATlV,GAAyBoM,EAAQC,WAAY,CAC/C,MAAMqO,EAAqBzB,EAAa7M,EAAQC,YAChD3L,EAAK+B,IAAK,CACRwU,iBAAevU,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVD,EAAMwU,iBAAe,IACxB5K,WAAYqO,MAGlB,MAAO,GAAa,SAAT1a,GAAmBoM,EAAQW,UAAW,CAC/C,MAAM4N,EAAoB1B,EAAa7M,EAAQW,WAC/CrM,EAAK+B,IAAK,CACRwU,iBAAevU,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVD,EAAMwU,iBAAe,IACxBlK,UAAW4N,MAGjB,CAGFja,EAAI,CACF6Y,eAAgB,CACdtM,aACAjN,OACA4a,UAAW,IAAIzC,KACf0C,aAAc,EACdR,aACAS,cAAe,GACfC,aAAc,GACdC,eAAyB,SAAThb,EAAkB,OAAI8H,EACtCmT,UAAW,EACXC,WAAYd,EACZE,kBAGN,EAEAa,gBAAiBA,KACfza,EAAI,CAAE6Y,eAAgB,QAGxB6B,SAAUA,KACR,MAAM,eAAE7B,EAAc,aAAE8B,GAAiB/B,IACzC,IAAKC,EAAgB,OAGrB,MAAM+B,EACJ/B,EAAesB,eAAiBtB,EAAec,WAAa,EACxD,EACAd,EAAesB,aAAe,EAGpCQ,EAAa,CACXrb,KAAM,YACNub,QAAS,CAAEC,UAAWjC,EAAesB,aAAcY,QAASH,GAC5DI,cAAe,CAAEb,aAActB,EAAesB,cAC9Cc,UAAWxD,KAAKyD,QAGlBlb,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GAAc,IACjBsB,aAAcS,OAKpBO,aAAcA,KACZ,MAAM,eAAEtC,EAAc,aAAE8B,GAAiB/B,IACzC,IAAKC,EAAgB,OAGrB,MAAMuC,EAC4B,IAAhCvC,EAAesB,aACXtB,EAAec,WAAa,EAC5Bd,EAAesB,aAAe,EAGpCQ,EAAa,CACXrb,KAAM,gBACNub,QAAS,CAAEC,UAAWjC,EAAesB,aAAcY,QAASK,GAC5DJ,cAAe,CAAEb,aAActB,EAAesB,cAC9Cc,UAAWxD,KAAKyD,QAGlBlb,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GAAc,IACjBsB,aAAciB,OAKpBC,SAAWnH,IACT,MAAM,eAAE2E,GAAmBD,IAC3B,IAAKC,EAAgB,OAErB,MAAMyC,EAAejV,KAAK3B,IACxB,EACA2B,KAAK5B,IAAIyP,EAAO2E,EAAec,WAAa,IAE9C3Z,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GAAc,IACjBsB,aAAcmB,OAKpBC,WAAaC,IACX,MAAM,eAAE3C,EAAc,aAAE8B,GAAiB/B,IACzC,IAAKC,EAAgB,OAErB,MAAM4C,EAAa5C,EAAewB,aAAatU,SAASyV,GAClDnB,EAAeoB,EACjB5C,EAAewB,aAAajY,OAAQD,GAAOA,IAAOqZ,GAClD,IAAI3C,EAAewB,aAAcmB,GAGrCb,EAAa,CACXrb,KAAM,cACNub,QAAS,CAAEW,SAAQC,cACnBT,cAAe,CAAEX,aAAcxB,EAAewB,cAC9CY,UAAWxD,KAAKyD,QAGlBlb,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GAAc,IACjBwB,oBAKNqB,aAAeC,IACb,MAAM,eAAE9C,GAAmBD,IACtBC,IAEAA,EAAeuB,cAAcrU,SAAS8S,EAAesB,eACxDna,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GAAc,IACjBuB,cAAe,IACVvB,EAAeuB,cAClBvB,EAAesB,oBAOzByB,iBAAkBA,CAChBtG,EACAuG,EACAC,KAEA,MAAM,eAAEjD,EAAc,aAAE6C,GAAiB9C,IACpCC,GAA0C,SAAxBA,EAAevZ,OAEtCoc,EAAapG,GAETwG,GACF9b,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GAAc,IACjByB,gBAAiBzB,EAAeyB,gBAAkB,GAAK,QAM/DyB,gBAAkBxT,IAChB,MAAM,eAAEsQ,GAAmBD,IACtBC,GAEL7Y,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GAAc,IACjB0B,UAAW1B,EAAe0B,UAAYhS,OAM5CoS,aAAeqB,IACb,MAAM,cAAEhD,EAAa,mBAAEC,GAAuBL,IAGxCqD,EAAajD,EAAckD,MAAM,EAAGjD,EAAqB,GAC/DgD,EAAWE,KAAKH,GAGhB,MAAMI,EAAiBH,EAAWC,OAAO,IAEzClc,EAAI,CACFgZ,cAAeoD,EACfnD,mBAAoBmD,EAAe/S,OAAS,EAC5C6P,QAASkD,EAAe/S,OAAS,EACjC8P,SAAS,KAIbkD,KAAMA,KACJ,MAAM,cAAErD,EAAa,mBAAEC,EAAkB,eAAEJ,GAAmBD,IAE9D,GAAIK,EAAqB,IAAMJ,EAAgB,OAE/C,MAAMmD,EAAShD,EAAcC,GAG7BjZ,EAAI,CACF6Y,gBAAc7W,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6W,GACAmD,EAAOhB,eAEZ/B,mBAAoBA,EAAqB,EACzCC,QAASD,EAAqB,EAC9BE,SAAS,KAIbmD,KAAMA,KACJ,MAAM,cAAEtD,EAAa,mBAAEC,EAAkB,eAAEJ,GAAmBD,IAE9D,GAAIK,GAAsBD,EAAc3P,OAAS,IAAMwP,EACrD,OAEF,MAAM0D,EAAkBtD,EAAqB,EACvC+C,EAAShD,EAAcuD,GAG7B,OAAQP,EAAO1c,MACb,IAAK,YACHsZ,IAAM8B,WACN,MACF,IAAK,gBACH9B,IAAMuC,eACN,MACF,IAAK,cACHvC,IAAM2C,WAAWS,EAAOnB,QAAQW,QAChC,MACF,IAAK,gBACH5C,IAAM8C,aAAaM,EAAOnB,QAAQW,QAItCxb,EAAI,CACFiZ,mBAAoBsD,EACpBrD,SAAS,EACTC,QAASoD,EAAkBvD,EAAc3P,OAAS,KAItDmT,aAAcA,KACZxc,EAAI,CACFgZ,cAAe,GACfC,oBAAqB,EACrBC,SAAS,EACTC,SAAS,KAIbsD,eAAgBlc,UACdP,EAAI,CAAEI,WAAW,EAAMgB,MAAO,OAE9B,IACE,MAAMZ,EAAQC,aAAaC,QAAQ,cAE7BC,QAAiBC,MAAM,kBAAmB,CAC9CC,QAAS,CACPC,cAAc,UAADlC,OAAY4B,MAI7B,IAAKG,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,6BACvC,CAEA,MAAMH,QAAeN,EAASO,OAE9B,IAAID,EAAOE,QAMT,MAAM,IAAIH,MAAMC,EAAOG,OALvBpB,EAAI,CACF8Y,UAAW7X,EAAOI,KAClBjB,WAAW,GAKjB,CAAE,MAAOgB,GAKP,MAJApB,EAAI,CACFoB,MAAOA,EAAM6C,SAAW,6BACxB7D,WAAW,IAEPgB,CACR,GAGFsb,mBAAoBnc,iBAA8B,IAAvBoc,EAASrD,UAAAjQ,OAAA,QAAAjC,IAAAkS,UAAA,GAAAA,UAAA,GAAG,MACrCtZ,EAAI,CAAEI,WAAW,EAAMgB,MAAO,OAE9B,IACE,MAAMZ,EAAQC,aAAaC,QAAQ,cAE7BC,QAAiBC,MAAM,iCAADhC,OACO+d,GACjC,CACE9b,QAAS,CACPC,cAAc,UAADlC,OAAY4B,MAK/B,IAAKG,EAASI,GAAI,CAChB,MAAMe,QAAoBnB,EAASO,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,iCACvC,CAEA,MAAMH,QAAeN,EAASO,OAE9B,IAAID,EAAOE,QAaT,MAAM,IAAIH,MAAMC,EAAOG,OAbL,CAElB,MAAM2X,EAAW9X,EAAOI,KAAKtC,IAAK6d,IAAY5a,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACzC4a,GAAO,IACV1C,UAAW,IAAIzC,KAAKmF,EAAQ1C,WAC5B2C,QAASD,EAAQC,QAAU,IAAIpF,KAAKmF,EAAQC,cAAWzV,KAGzDpH,EAAI,CACF+Y,WACA3Y,WAAW,GAEf,CAGF,CAAE,MAAOgB,GAKP,MAJApB,EAAI,CACFoB,MAAOA,EAAM6C,SAAW,iCACxB7D,WAAW,IAEPgB,CACR,CACF,EAGA0b,0BAA4BvQ,IAC1B,MAAM,gBAAEgK,GAAoBqC,IAEX,IAADmE,EAAZxQ,GAEiB,OAAfgK,QAAe,IAAfA,GAAyB,QAAVwG,EAAfxG,EAAiB9K,gBAAQ,IAAAsR,OAAV,EAAfA,EAA2B5a,MAAOoK,GACpCvM,EAAI,CAAEuW,gBAAiB,OAIzBvW,EAAI,CAAEuW,gBAAiB,QAI3BxK,uBAAwBxL,gBAEhBqY,IAAMpC,qBAAqBjK,GAAY,IAG/CP,oBAAqBA,KAEnBhM,EAAI,CAAE8Y,UAAW,Q", "sources": ["components/common/DifficultySelector.tsx", "stores/documentStore.ts", "hooks/useUserSettings.ts", "components/common/CountInput.tsx", "components/common/ProgressBar.tsx", "components/ai/DocumentSelector.tsx", "components/common/ContentLengthSelector.tsx", "stores/aiStore.ts", "components/ui/BulkActionsToolbar.tsx", "components/study/FlashcardManagement.tsx", "components/study/QuizManagement.tsx", "components/ui/ShuffleToggle.tsx", "pages/StudySetPage.tsx", "shared/types.ts", "stores/studyStore.ts"], "sourcesContent": ["import React from \"react\";\nimport { DifficultyLevel } from \"../../shared/types\";\n\ninterface DifficultySelectorProps {\n  value: DifficultyLevel;\n  onChange: (difficulty: DifficultyLevel) => void;\n  className?: string;\n  disabled?: boolean;\n  label?: string;\n}\n\nconst difficultyOptions = [\n  {\n    value: DifficultyLevel.EASY,\n    label: \"Easy\",\n    description: \"Basic facts and definitions\",\n  },\n  {\n    value: DifficultyLevel.MEDIUM,\n    label: \"Medium\",\n    description: \"Moderate understanding required\",\n  },\n  {\n    value: DifficultyLevel.HARD,\n    label: \"Hard\",\n    description: \"Deep analysis and critical thinking\",\n  },\n  {\n    value: DifficultyLevel.COLLEGE,\n    label: \"College\",\n    description: \"Undergraduate level complexity\",\n  },\n  {\n    value: DifficultyLevel.GRADUATE,\n    label: \"Graduate\",\n    description: \"Advanced graduate study\",\n  },\n  {\n    value: DifficultyLevel.PHD,\n    label: \"PhD\",\n    description: \"Research-level expertise\",\n  },\n];\n\nconst getDifficultyColor = (difficulty: DifficultyLevel): string => {\n  switch (difficulty) {\n    case DifficultyLevel.EASY:\n      return \"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50\";\n    case DifficultyLevel.MEDIUM:\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\n    case DifficultyLevel.HARD:\n      return \"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50\";\n    case DifficultyLevel.COLLEGE:\n      return \"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50\";\n    case DifficultyLevel.GRADUATE:\n      return \"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50\";\n    case DifficultyLevel.PHD:\n      return \"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50\";\n    default:\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\n  }\n};\n\nconst getSelectedDifficultyColor = (difficulty: DifficultyLevel): string => {\n  switch (difficulty) {\n    case DifficultyLevel.EASY:\n      return \"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20\";\n    case DifficultyLevel.MEDIUM:\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\n    case DifficultyLevel.HARD:\n      return \"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20\";\n    case DifficultyLevel.COLLEGE:\n      return \"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20\";\n    case DifficultyLevel.GRADUATE:\n      return \"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20\";\n    case DifficultyLevel.PHD:\n      return \"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20\";\n    default:\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\n  }\n};\n\nexport const DifficultySelector: React.FC<DifficultySelectorProps> = ({\n  value,\n  onChange,\n  className = \"\",\n  disabled = false,\n  label = \"Difficulty Level\",\n}) => {\n  return (\n    <div className={`space-y-3 ${className}`}>\n      <label className=\"block text-sm font-medium text-text-primary\">\n        {label}\n      </label>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n        {difficultyOptions.map((option) => {\n          const isSelected = value === option.value;\n          const colorClasses = isSelected\n            ? getSelectedDifficultyColor(option.value)\n            : getDifficultyColor(option.value);\n\n          return (\n            <button\n              key={option.value}\n              type=\"button\"\n              onClick={() => !disabled && onChange(option.value)}\n              disabled={disabled}\n              className={`\n                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu\n                ${colorClasses}\n                ${\n                  disabled\n                    ? \"opacity-50 cursor-not-allowed\"\n                    : \"cursor-pointer hover:scale-105\"\n                }\n                ${\n                  isSelected\n                    ? \"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary\"\n                    : \"\"\n                }\n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary\n              `}\n              title={option.description}\n              aria-pressed={isSelected}\n            >\n              <div className=\"text-center\">\n                <div className=\"font-semibold\">{option.label}</div>\n                <div\n                  className={`text-xs mt-1 ${\n                    isSelected ? \"text-white/90\" : \"text-text-secondary\"\n                  }`}\n                >\n                  {option.description}\n                </div>\n              </div>\n              {isSelected && (\n                <div className=\"absolute top-2 right-2\">\n                  <svg\n                    className=\"w-4 h-4\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n              )}\n            </button>\n          );\n        })}\n      </div>\n      <p className=\"text-xs text-text-muted\">\n        Select the appropriate difficulty level for your flashcards. This\n        affects the complexity of questions and answers generated.\n      </p>\n    </div>\n  );\n};\n\nexport default DifficultySelector;\n", "import { create } from \"zustand\";\nimport { DocumentMetadata, DocumentWithContent } from \"../shared/types\";\n\ninterface DocumentState {\n  documents: DocumentMetadata[];\n  selectedDocuments: Set<string>;\n  isLoading: boolean;\n  uploadProgress: { [key: string]: number };\n\n  // Actions\n  fetchDocuments: () => Promise<void>;\n  uploadDocument: (file: File) => Promise<DocumentMetadata>;\n  deleteDocument: (id: string) => Promise<void>;\n  searchDocuments: (query: string) => Promise<DocumentMetadata[]>;\n  getDocument: (id: string) => Promise<DocumentWithContent | null>;\n  toggleDocumentSelection: (id: string) => void;\n  clearSelection: () => void;\n  selectAll: () => void;\n  setUploadProgress: (fileName: string, progress: number) => void;\n}\n\nexport const useDocumentStore = create<DocumentState>((set) => ({\n  documents: [],\n  selectedDocuments: new Set(),\n  isLoading: false,\n  uploadProgress: {},\n\n  fetchDocuments: async () => {\n    set({ isLoading: true });\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/documents\", {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch documents\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        set({ documents: result.data, isLoading: false });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error(\"Fetch documents error:\", error);\n      set({ isLoading: false });\n      throw error;\n    }\n  },\n\n  uploadDocument: async (file: File) => {\n    const formData = new FormData();\n    formData.append(\"document\", file);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/documents/upload\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Upload failed\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        // Add new document to the list\n        set((state) => ({\n          documents: [result.data, ...state.documents],\n          uploadProgress: { ...state.uploadProgress, [file.name]: 100 },\n        }));\n\n        return result.data;\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error(\"Upload document error:\", error);\n      throw error;\n    }\n  },\n\n  deleteDocument: async (id: string) => {\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(`/api/documents/${id}`, {\n        method: \"DELETE\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Delete failed\");\n      }\n\n      // Remove document from the list\n      set((state) => ({\n        documents: state.documents.filter((doc) => doc.id !== id),\n        selectedDocuments: new Set(\n          [...state.selectedDocuments].filter((docId) => docId !== id)\n        ),\n      }));\n    } catch (error) {\n      console.error(\"Delete document error:\", error);\n      throw error;\n    }\n  },\n\n  searchDocuments: async (query: string) => {\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\n        `/api/documents/search?q=${encodeURIComponent(query)}`,\n        {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(\"Search failed\");\n      }\n\n      const result = await response.json();\n      return result.success ? result.data : [];\n    } catch (error) {\n      console.error(\"Search documents error:\", error);\n      return [];\n    }\n  },\n\n  getDocument: async (id: string) => {\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(`/api/documents/${id}`, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        return null;\n      }\n\n      const result = await response.json();\n      return result.success ? result.data : null;\n    } catch (error) {\n      console.error(\"Get document error:\", error);\n      return null;\n    }\n  },\n\n  toggleDocumentSelection: (id: string) => {\n    set((state) => {\n      const newSelection = new Set(state.selectedDocuments);\n      if (newSelection.has(id)) {\n        newSelection.delete(id);\n      } else {\n        newSelection.add(id);\n      }\n      return { selectedDocuments: newSelection };\n    });\n  },\n\n  clearSelection: () => {\n    set({ selectedDocuments: new Set() });\n  },\n\n  selectAll: () => {\n    set((state) => ({\n      selectedDocuments: new Set(state.documents.map((doc) => doc.id)),\n    }));\n  },\n\n  setUploadProgress: (fileName: string, progress: number) => {\n    set((state) => ({\n      uploadProgress: { ...state.uploadProgress, [fileName]: progress },\n    }));\n  },\n}));\n", "import { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  UserSettings,\r\n  UserSettingsUpdate,\r\n  UseUserSettingsReturn\r\n} from '../types/userSettings';\r\n\r\n\r\n\r\nexport const useUserSettings = (): UseUserSettingsReturn => {\r\n  const [settings, setSettings] = useState<UserSettings | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchSettings = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await fetch('/api/user/settings', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || 'Failed to fetch user settings');\r\n      }\r\n\r\n      const data = await response.json();\r\n      setSettings(data.data);\r\n    } catch (err: any) {\r\n      setError(err.message);\r\n      console.error('Error fetching user settings:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateSettings = useCallback(async (updates: UserSettingsUpdate) => {\r\n    try {\r\n      setError(null);\r\n\r\n      const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await fetch('/api/user/settings', {\r\n        method: 'PATCH',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify(updates)\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || 'Failed to update user settings');\r\n      }\r\n\r\n      const data = await response.json();\r\n      setSettings(data.data);\r\n    } catch (err: any) {\r\n      setError(err.message);\r\n      console.error('Error updating user settings:', err);\r\n      throw err; // Re-throw to allow caller to handle\r\n    }\r\n  }, []);\r\n\r\n  const refetch = useCallback(async () => {\r\n    await fetchSettings();\r\n  }, [fetchSettings]);\r\n\r\n  useEffect(() => {\r\n    fetchSettings();\r\n  }, [fetchSettings]);\r\n\r\n  return {\r\n    settings,\r\n    loading,\r\n    error,\r\n    updateSettings,\r\n    refetch\r\n  };\r\n};\r\n", "import React, { useState, useEffect } from \"react\";\r\n\r\ninterface CountInputProps {\r\n  value: number;\r\n  onChange: (value: number) => void;\r\n  label: string;\r\n  min?: number;\r\n  max?: number;\r\n  placeholder?: string;\r\n  error?: string;\r\n  className?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const CountInput: React.FC<CountInputProps> = ({\r\n  value,\r\n  onChange,\r\n  label,\r\n  min = 1,\r\n  max = 100,\r\n  placeholder = \"Enter a number\",\r\n  error,\r\n  className = \"\",\r\n  disabled = false,\r\n}) => {\r\n  const [inputValue, setInputValue] = useState(value.toString());\r\n  const [localError, setLocalError] = useState<string>(\"\");\r\n\r\n  // Update input value when external value changes\r\n  useEffect(() => {\r\n    if (value.toString() !== inputValue) {\r\n      setInputValue(value.toString());\r\n    }\r\n  }, [value]);\r\n\r\n  const validateAndUpdate = (inputStr: string) => {\r\n    setInputValue(inputStr);\r\n\r\n    // Clear any previous local error\r\n    setLocalError(\"\");\r\n\r\n    // Allow empty input (we'll handle this as invalid but not show error immediately)\r\n    if (inputStr.trim() === \"\") {\r\n      return;\r\n    }\r\n\r\n    // Check if input contains only digits\r\n    if (!/^\\d+$/.test(inputStr.trim())) {\r\n      setLocalError(\"Please enter a whole number\");\r\n      return;\r\n    }\r\n\r\n    const numValue = parseInt(inputStr.trim(), 10);\r\n\r\n    // Check range\r\n    if (numValue < min) {\r\n      setLocalError(`Number must be at least ${min}`);\r\n      return;\r\n    }\r\n\r\n    if (numValue > max) {\r\n      setLocalError(`Number must be at most ${max}`);\r\n      return;\r\n    }\r\n\r\n    // Valid input - call onChange\r\n    onChange(numValue);\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newValue = e.target.value;\r\n\r\n    // Allow typing digits and prevent pasting non-digits\r\n    if (newValue === \"\" || /^\\d+$/.test(newValue)) {\r\n      validateAndUpdate(newValue);\r\n    }\r\n  };\r\n\r\n  const handleBlur = () => {\r\n    // On blur, if empty or invalid, reset to current valid value\r\n    if (inputValue.trim() === \"\" || localError) {\r\n      setInputValue(value.toString());\r\n      setLocalError(\"\");\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    // Prevent entering non-digit characters\r\n    if (\r\n      ![\r\n        \"Backspace\",\r\n        \"Delete\",\r\n        \"Tab\",\r\n        \"Escape\",\r\n        \"Enter\",\r\n        \"ArrowLeft\",\r\n        \"ArrowRight\",\r\n        \"ArrowUp\",\r\n        \"ArrowDown\",\r\n      ].includes(e.key) &&\r\n      !(e.key >= \"0\" && e.key <= \"9\") &&\r\n      !(e.ctrlKey && [\"a\", \"c\", \"v\", \"x\", \"z\"].includes(e.key.toLowerCase()))\r\n    ) {\r\n      e.preventDefault();\r\n    }\r\n  };\r\n\r\n  const incrementValue = () => {\r\n    const newValue = Math.min(value + 1, max);\r\n    onChange(newValue);\r\n  };\r\n\r\n  const decrementValue = () => {\r\n    const newValue = Math.max(value - 1, min);\r\n    onChange(newValue);\r\n  };\r\n\r\n  const displayError = error || localError;\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n        {label}\r\n      </label>\r\n\r\n      <div className=\"relative\">\r\n        <input\r\n          type=\"text\"\r\n          inputMode=\"numeric\"\r\n          value={inputValue}\r\n          onChange={handleInputChange}\r\n          onBlur={handleBlur}\r\n          onKeyDown={handleKeyDown}\r\n          placeholder={placeholder}\r\n          disabled={disabled}\r\n          className={`\r\n            w-full px-3 py-2 pr-20 bg-background-primary border rounded-lg text-white \r\n            placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors\r\n            ${\r\n              displayError\r\n                ? \"border-red-500 focus:border-red-500 focus:ring-red-500/50\"\r\n                : \"border-gray-600 focus:border-primary-500 focus:ring-primary-500/50\"\r\n            }\r\n            ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}\r\n          `}\r\n        />\r\n\r\n        {/* Increment/Decrement buttons */}\r\n        <div className=\"absolute right-1 top-1 bottom-1 flex flex-col\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={incrementValue}\r\n            disabled={disabled || value >= max}\r\n            className=\"\r\n              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r\n              rounded-tr-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r\n              focus:outline-none focus:bg-gray-600\r\n            \"\r\n            tabIndex={-1}\r\n          >\r\n            <svg className=\"w-3 h-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </button>\r\n          <button\r\n            type=\"button\"\r\n            onClick={decrementValue}\r\n            disabled={disabled || value <= min}\r\n            className=\"\r\n              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r\n              rounded-br-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r\n              focus:outline-none focus:bg-gray-600\r\n            \"\r\n            tabIndex={-1}\r\n          >\r\n            <svg className=\"w-3 h-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error message */}\r\n      {displayError && (\r\n        <p className=\"mt-1 text-sm text-red-400\">{displayError}</p>\r\n      )}\r\n\r\n      {/* Helper text */}\r\n      {!displayError && (\r\n        <p className=\"mt-1 text-xs text-gray-500\">\r\n          Enter a number between {min} and {max}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "import React from \"react\";\r\n\r\ninterface ProgressBarProps {\r\n  progress?: number; // 0-100 percentage\r\n  isIndeterminate?: boolean; // For loading without known progress\r\n  label?: string;\r\n  className?: string;\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  variant?: \"primary\" | \"success\" | \"warning\" | \"error\";\r\n}\r\n\r\nexport const ProgressBar: React.FC<ProgressBarProps> = ({\r\n  progress = 0,\r\n  isIndeterminate = false,\r\n  label,\r\n  className = \"\",\r\n  size = \"md\",\r\n  variant = \"primary\",\r\n}) => {\r\n  const sizeClasses = {\r\n    sm: \"h-1.5\",\r\n    md: \"h-2\",\r\n    lg: \"h-3\",\r\n  };\r\n\r\n  const variantClasses = {\r\n    primary: \"bg-primary-500\",\r\n    success: \"bg-green-500\",\r\n    warning: \"bg-yellow-500\",\r\n    error: \"bg-red-500\",\r\n  };\r\n\r\n  const textSizeClasses = {\r\n    sm: \"text-xs\",\r\n    md: \"text-sm\",\r\n    lg: \"text-base\",\r\n  };\r\n\r\n  return (\r\n    <div className={`w-full ${className}`}>\r\n      {label && (\r\n        <div\r\n          className={`flex justify-between items-center mb-2 ${textSizeClasses[size]}`}\r\n        >\r\n          <span className=\"text-gray-300 font-medium\">{label}</span>\r\n          {!isIndeterminate && (\r\n            <span className=\"text-gray-400\">{Math.round(progress)}%</span>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      <div\r\n        className={`w-full bg-gray-700 rounded-full overflow-hidden ${sizeClasses[size]}`}\r\n      >\r\n        <div\r\n          className={`${\r\n            variantClasses[variant]\r\n          } transition-all duration-300 ease-out rounded-full ${\r\n            sizeClasses[size]\r\n          } ${\r\n            isIndeterminate\r\n              ? \"animate-pulse w-full\"\r\n              : \"transition-[width] duration-500\"\r\n          }`}\r\n          style={\r\n            !isIndeterminate\r\n              ? { width: `${Math.min(100, Math.max(0, progress))}%` }\r\n              : undefined\r\n          }\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Enhanced loading progress bar specifically for AI generation\r\ninterface AIGenerationProgressProps {\r\n  isGenerating: boolean;\r\n  stage?: string;\r\n  estimatedTime?: number; // in seconds\r\n  className?: string;\r\n}\r\n\r\nexport const AIGenerationProgress: React.FC<AIGenerationProgressProps> = ({\r\n  isGenerating,\r\n  stage,\r\n  estimatedTime,\r\n  className = \"\",\r\n}) => {\r\n  const [progress, setProgress] = React.useState(0);\r\n  const [timeElapsed, setTimeElapsed] = React.useState(0);\r\n\r\n  React.useEffect(() => {\r\n    let interval: NodeJS.Timeout;\r\n    let progressInterval: NodeJS.Timeout;\r\n\r\n    if (isGenerating) {\r\n      setTimeElapsed(0);\r\n      setProgress(0);\r\n\r\n      // Update elapsed time every second\r\n      interval = setInterval(() => {\r\n        setTimeElapsed((prev) => prev + 1);\r\n      }, 1000);\r\n\r\n      // Simulate progress for better UX (not accurate, just visual feedback)\r\n      progressInterval = setInterval(() => {\r\n        setProgress((prev) => {\r\n          // Start fast, then slow down as we approach completion\r\n          const increment = prev < 30 ? 3 : prev < 60 ? 2 : prev < 85 ? 1 : 0.2;\r\n          return Math.min(90, prev + increment); // Cap at 90% until actually complete\r\n        });\r\n      }, 1000);\r\n    } else {\r\n      setProgress(0);\r\n      setTimeElapsed(0);\r\n    }\r\n\r\n    return () => {\r\n      if (interval) clearInterval(interval);\r\n      if (progressInterval) clearInterval(progressInterval);\r\n    };\r\n  }, [isGenerating]);\r\n\r\n  // Complete the progress when generation finishes\r\n  React.useEffect(() => {\r\n    if (!isGenerating && progress > 0) {\r\n      setProgress(100);\r\n      setTimeout(() => setProgress(0), 1000);\r\n    }\r\n  }, [isGenerating, progress]);\r\n\r\n  if (!isGenerating && progress === 0) {\r\n    return null;\r\n  }\r\n\r\n  const formatTime = (seconds: number) => {\r\n    if (seconds < 60) return `${seconds}s`;\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}m ${remainingSeconds}s`;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`bg-background-secondary rounded-lg p-4 border border-gray-600 ${className}`}\r\n    >\r\n      <div className=\"space-y-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"animate-spin rounded-full h-5 w-5 border-2 border-primary-500 border-t-transparent\" />\r\n            <span className=\"text-white font-medium\">\r\n              {stage || \"Generating with AI...\"}\r\n            </span>\r\n          </div>\r\n          <div className=\"text-sm text-gray-400\">\r\n            {timeElapsed > 0 && formatTime(timeElapsed)}\r\n            {estimatedTime &&\r\n              timeElapsed === 0 &&\r\n              ` (Est. ${formatTime(estimatedTime)})`}\r\n          </div>\r\n        </div>\r\n\r\n        <ProgressBar\r\n          progress={progress}\r\n          isIndeterminate={progress === 0}\r\n          variant=\"primary\"\r\n          size=\"md\"\r\n        />\r\n\r\n        <div className=\"text-xs text-gray-500 text-center\">\r\n          {isGenerating\r\n            ? \"Please wait while we generate your content...\"\r\n            : \"Generation complete!\"}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect, useState } from \"react\";\nimport { useDocumentStore } from \"../../stores/documentStore\";\n\ninterface DocumentPageRange {\n  startPage: number;\n  endPage: number;\n}\n\ninterface DocumentSelectorProps {\n  selectedDocuments: string[];\n  onSelectionChange: (documentIds: string[]) => void;\n  documentPageRanges: { [documentId: string]: DocumentPageRange };\n  onPageRangeChange: (documentId: string, range: DocumentPageRange) => void;\n  maxSelection?: number;\n}\n\ninterface PageInputProps {\n  value: number;\n  onChange: (value: number) => void;\n  min: number;\n  max: number;\n  label: string;\n  placeholder?: string;\n}\n\nconst PageInput: React.FC<PageInputProps> = ({\n  value,\n  onChange,\n  min,\n  max,\n  label,\n  placeholder,\n}) => {\n  const [inputValue, setInputValue] = useState(value.toString());\n  const [error, setError] = useState<string>(\"\");\n\n  // Update input value when prop changes\n  useEffect(() => {\n    setInputValue(value.toString());\n  }, [value]);\n\n  const handleChange = (newValue: string) => {\n    setInputValue(newValue);\n\n    // Clear error when user starts typing\n    if (error) setError(\"\");\n\n    // Allow empty input temporarily\n    if (newValue === \"\") {\n      return;\n    }\n\n    // Validate numeric input\n    const numValue = parseInt(newValue);\n    if (isNaN(numValue)) {\n      setError(\"Must be a number\");\n      return;\n    }\n\n    if (numValue < min) {\n      setError(`Minimum is ${min}`);\n      return;\n    }\n\n    if (numValue > max) {\n      setError(`Maximum is ${max}`);\n      return;\n    }\n\n    // Valid input - update parent\n    onChange(numValue);\n  };\n\n  const handleBlur = () => {\n    // On blur, ensure we have a valid value\n    const numValue = parseInt(inputValue);\n    if (isNaN(numValue) || inputValue === \"\") {\n      // Reset to current valid value\n      setInputValue(value.toString());\n      setError(\"\");\n    }\n  };\n\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <label className=\"text-xs text-gray-400 whitespace-nowrap\">\n        {label}:\n      </label>\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          value={inputValue}\n          onChange={(e) => handleChange(e.target.value)}\n          onBlur={handleBlur}\n          placeholder={placeholder}\n          className={`w-16 px-2 py-1 text-xs rounded text-white text-center focus:outline-none focus:ring-1 focus:ring-primary-500 ${\n            error\n              ? \"bg-red-900/20 border border-red-500\"\n              : \"bg-background-primary border border-gray-600\"\n          }`}\n        />\n        {error && (\n          <div className=\"absolute top-full left-0 mt-1 text-xs text-red-400 whitespace-nowrap z-10\">\n            {error}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport const DocumentSelector: React.FC<DocumentSelectorProps> = ({\n  selectedDocuments,\n  onSelectionChange,\n  documentPageRanges,\n  onPageRangeChange,\n  maxSelection = 5,\n}) => {\n  const { documents, fetchDocuments, isLoading } = useDocumentStore();\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  useEffect(() => {\n    if (documents.length === 0) {\n      fetchDocuments();\n    }\n  }, [documents.length, fetchDocuments]);\n\n  const filteredDocuments = documents.filter(\n    (doc) =>\n      doc.is_processed &&\n      doc.filename.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  const handleDocumentToggle = (documentId: string) => {\n    const isSelected = selectedDocuments.includes(documentId);\n\n    if (isSelected) {\n      onSelectionChange(selectedDocuments.filter((id) => id !== documentId));\n    } else if (selectedDocuments.length < maxSelection) {\n      onSelectionChange([...selectedDocuments, documentId]);\n\n      // Initialize page range for documents with page counts\n      const document = documents.find((doc) => doc.id === documentId);\n      if (document?.page_count && document.page_count > 0) {\n        onPageRangeChange(documentId, {\n          startPage: 1,\n          endPage: document.page_count,\n        });\n      }\n    }\n  };\n\n  const getSelectedDocuments = () => {\n    return documents.filter((doc) => selectedDocuments.includes(doc.id));\n  };\n\n  const handlePageRangeUpdate = (\n    documentId: string,\n    field: \"startPage\" | \"endPage\",\n    value: number\n  ) => {\n    const currentRange = documentPageRanges[documentId] || {\n      startPage: 1,\n      endPage: 1,\n    };\n    const document = documents.find((doc) => doc.id === documentId);\n    const maxPage = document?.page_count || 1;\n\n    let newRange = { ...currentRange };\n\n    if (field === \"startPage\") {\n      newRange.startPage = Math.max(\n        1,\n        Math.min(value, maxPage, newRange.endPage)\n      );\n    } else {\n      newRange.endPage = Math.max(newRange.startPage, Math.min(value, maxPage));\n    }\n\n    onPageRangeChange(documentId, newRange);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-gray-400\">Loading documents...</div>\n      </div>\n    );\n  }\n\n  if (documents.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"text-gray-400 mb-4\">No documents found</div>\n        <p className=\"text-sm text-gray-500\">\n          Upload some documents first to generate study materials.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Search */}\n      <div>\n        <input\n          type=\"text\"\n          placeholder=\"Search documents...\"\n          value={searchQuery}\n          onChange={(e) => setSearchQuery(e.target.value)}\n          className=\"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n        />\n      </div>\n\n      {/* Selection Summary with Page Ranges */}\n      {selectedDocuments.length > 0 && (\n        <div className=\"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3\">\n          <div className=\"text-sm text-primary-400 mb-2\">\n            Selected {selectedDocuments.length} of {maxSelection} documents:\n          </div>\n          <div className=\"space-y-3\">\n            {getSelectedDocuments().map((doc) => {\n              const hasPageCount = doc.page_count && doc.page_count > 0;\n              const pageRange = documentPageRanges[doc.id];\n\n              return (\n                <div\n                  key={doc.id}\n                  className=\"bg-background-secondary/50 rounded-lg p-3\"\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-sm text-gray-300 truncate flex-1 mr-2\">\n                      {doc.filename}\n                    </span>\n                    <button\n                      onClick={() => handleDocumentToggle(doc.id)}\n                      className=\"text-red-400 hover:text-red-300 text-sm px-2 py-1 rounded hover:bg-red-400/10 transition-colors\"\n                    >\n                      Remove\n                    </button>\n                  </div>\n\n                  {hasPageCount && (\n                    <div className=\"space-y-3\">\n                      <div className=\"text-xs text-gray-400\">\n                        📄 {doc.page_count}{\" \"}\n                        {doc.file_type === \"pptx\" ? \"slides\" : \"pages\"}{\" \"}\n                        available\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-4\">\n                          <PageInput\n                            label=\"From\"\n                            value={pageRange?.startPage || 1}\n                            onChange={(value) =>\n                              handlePageRangeUpdate(doc.id, \"startPage\", value)\n                            }\n                            min={1}\n                            max={doc.page_count || 1}\n                            placeholder=\"1\"\n                          />\n\n                          <PageInput\n                            label=\"To\"\n                            value={pageRange?.endPage || doc.page_count || 1}\n                            onChange={(value) =>\n                              handlePageRangeUpdate(doc.id, \"endPage\", value)\n                            }\n                            min={pageRange?.startPage || 1}\n                            max={doc.page_count || 1}\n                            placeholder={doc.page_count?.toString() || \"1\"}\n                          />\n                        </div>\n\n                        <button\n                          onClick={() =>\n                            onPageRangeChange(doc.id, {\n                              startPage: 1,\n                              endPage: doc.page_count || 1,\n                            })\n                          }\n                          className=\"text-xs text-primary-400 hover:text-primary-300 px-2 py-1 rounded hover:bg-primary-400/10 transition-colors whitespace-nowrap\"\n                        >\n                          Use All\n                        </button>\n                      </div>\n\n                      {pageRange && (\n                        <div className=\"text-xs text-gray-500\">\n                          Using {pageRange.endPage - pageRange.startPage + 1} of{\" \"}\n                          {doc.page_count}{\" \"}\n                          {doc.file_type === \"pptx\" ? \"slides\" : \"pages\"}\n                        </div>\n                      )}\n                    </div>\n                  )}\n\n                  {!hasPageCount && (\n                    <div className=\"text-xs text-gray-500\">\n                      📄 {doc.file_type.toUpperCase()} • Full document will be\n                      used\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      )}\n\n      {/* Document List */}\n      <div className=\"max-h-64 overflow-y-auto space-y-2\">\n        {filteredDocuments.map((document) => {\n          const isSelected = selectedDocuments.includes(document.id);\n          const canSelect =\n            !isSelected && selectedDocuments.length < maxSelection;\n          const hasPageCount = document.page_count && document.page_count > 0;\n\n          return (\n            <div\n              key={document.id}\n              className={`\n                p-3 rounded-lg border cursor-pointer transition-all\n                ${\n                  isSelected\n                    ? \"bg-primary-500/20 border-primary-500\"\n                    : canSelect\n                    ? \"bg-background-secondary border-gray-600 hover:border-gray-500\"\n                    : \"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed\"\n                }\n              `}\n              onClick={() =>\n                canSelect || isSelected\n                  ? handleDocumentToggle(document.id)\n                  : null\n              }\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-lg\">\n                      {document.file_type === \"pdf\"\n                        ? \"📄\"\n                        : document.file_type === \"docx\"\n                        ? \"📝\"\n                        : document.file_type === \"txt\"\n                        ? \"📃\"\n                        : \"📊\"}\n                    </span>\n                    <div className=\"min-w-0 flex-1\">\n                      <p className=\"text-white font-medium truncate\">\n                        {document.filename}\n                      </p>\n                      <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\n                        <span>\n                          {document.file_type.toUpperCase()} •{\" \"}\n                          {Math.round(document.file_size / 1024)} KB\n                        </span>\n                        {hasPageCount && (\n                          <span>\n                            • {document.page_count}{\" \"}\n                            {document.file_type === \"pptx\" ? \"slides\" : \"pages\"}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div\n                  className={`\n                  w-5 h-5 rounded border-2 flex items-center justify-center\n                  ${\n                    isSelected\n                      ? \"bg-primary-500 border-primary-500\"\n                      : \"border-gray-500\"\n                  }\n                `}\n                >\n                  {isSelected && (\n                    <svg\n                      className=\"w-3 h-3 text-white\"\n                      fill=\"currentColor\"\n                      viewBox=\"0 0 20 20\"\n                    >\n                      <path\n                        fillRule=\"evenodd\"\n                        d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                        clipRule=\"evenodd\"\n                      />\n                    </svg>\n                  )}\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {filteredDocuments.length === 0 && searchQuery && (\n        <div className=\"text-center py-4 text-gray-400\">\n          No documents match your search.\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React from \"react\";\nimport { ContentLength } from \"../../shared/types\";\n\ninterface ContentLengthSelectorProps {\n  value: ContentLength;\n  onChange: (length: ContentLength) => void;\n  className?: string;\n  disabled?: boolean;\n  label?: string;\n}\n\nconst contentLengthOptions = [\n  {\n    value: ContentLength.SHORT,\n    label: \"Short\",\n    description: \"Concise answers (1-2 sentences)\",\n    icon: \"📝\",\n  },\n  {\n    value: ContentLength.MEDIUM,\n    label: \"Medium\",\n    description: \"Balanced detail (2-3 sentences)\",\n    icon: \"📄\",\n  },\n  {\n    value: ContentLength.LONG,\n    label: \"Long\",\n    description: \"Comprehensive answers (3-5 sentences)\",\n    icon: \"📋\",\n  },\n];\n\nconst getContentLengthColor = (length: ContentLength): string => {\n  switch (length) {\n    case ContentLength.SHORT:\n      return \"bg-background-secondary text-text-primary border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50\";\n    case ContentLength.MEDIUM:\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\n    case ContentLength.LONG:\n      return \"bg-background-secondary text-text-primary border-indigo-500/30 hover:bg-indigo-500/10 hover:border-indigo-500/50\";\n    default:\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\n  }\n};\n\nconst getSelectedContentLengthColor = (length: ContentLength): string => {\n  switch (length) {\n    case ContentLength.SHORT:\n      return \"bg-emerald-500/20 text-emerald-300 border-emerald-500 shadow-lg shadow-emerald-500/20\";\n    case ContentLength.MEDIUM:\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\n    case ContentLength.LONG:\n      return \"bg-indigo-500/20 text-indigo-300 border-indigo-500 shadow-lg shadow-indigo-500/20\";\n    default:\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\n  }\n};\n\nexport const ContentLengthSelector: React.FC<ContentLengthSelectorProps> = ({\n  value,\n  onChange,\n  className = \"\",\n  disabled = false,\n  label = \"Content Length\",\n}) => {\n  return (\n    <div className={`space-y-3 ${className}`}>\n      <label className=\"block text-sm font-medium text-text-primary\">\n        {label}\n      </label>\n      <div className=\"grid grid-cols-3 gap-3\">\n        {contentLengthOptions.map((option) => {\n          const isSelected = value === option.value;\n          const colorClasses = isSelected\n            ? getSelectedContentLengthColor(option.value)\n            : getContentLengthColor(option.value);\n\n          return (\n            <button\n              key={option.value}\n              type=\"button\"\n              onClick={() => !disabled && onChange(option.value)}\n              disabled={disabled}\n              className={`\n                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu\n                ${colorClasses}\n                ${\n                  disabled\n                    ? \"opacity-50 cursor-not-allowed\"\n                    : \"cursor-pointer hover:scale-105\"\n                }\n                ${\n                  isSelected\n                    ? \"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary\"\n                    : \"\"\n                }\n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary\n              `}\n              title={option.description}\n              aria-pressed={isSelected}\n            >\n              <div className=\"text-center space-y-2\">\n                <div className=\"text-2xl\">{option.icon}</div>\n                <div className=\"font-semibold\">{option.label}</div>\n                <div\n                  className={`text-xs ${\n                    isSelected ? \"text-white/90\" : \"text-text-secondary\"\n                  }`}\n                >\n                  {option.description}\n                </div>\n              </div>\n              {isSelected && (\n                <div className=\"absolute top-2 right-2\">\n                  <svg\n                    className=\"w-4 h-4\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n              )}\n            </button>\n          );\n        })}\n      </div>\n      <p className=\"text-xs text-text-muted\">\n        Choose how detailed you want the flashcard answers to be. This affects\n        the length and depth of explanations.\n      </p>\n    </div>\n  );\n};\n\nexport default ContentLengthSelector;\n", "import { create } from \"zustand\";\r\nimport {\r\n  StudySet,\r\n  Flashcard,\r\n  QuizQuestion,\r\n  DifficultyLevel,\r\n  ContentLength,\r\n} from \"../../../shared/types\";\r\n\r\ninterface AIGenerationState {\r\n  isGenerating: boolean;\r\n  generationProgress: string;\r\n  lastGenerated: {\r\n    studySet?: StudySet;\r\n    content?: Flashcard[] | QuizQuestion[];\r\n    type?: \"flashcards\" | \"quiz\";\r\n  } | null;\r\n\r\n  // Actions\r\n  generateFlashcards: (params: {\r\n    documentIds: string[];\r\n    documentPageRanges?: {\r\n      [documentId: string]: { startPage: number; endPage: number };\r\n    };\r\n    name: string;\r\n    count: number;\r\n    customPrompt?: string;\r\n    difficultyLevel?: DifficultyLevel;\r\n    contentLength?: ContentLength;\r\n    existingContent?: string[];\r\n  }) => Promise<{\r\n    studySet: StudySet;\r\n    flashcards: Flashcard[];\r\n    creditsRemaining: number;\r\n  }>;\r\n\r\n  generateQuiz: (params: {\r\n    documentIds: string[];\r\n    documentPageRanges?: {\r\n      [documentId: string]: { startPage: number; endPage: number };\r\n    };\r\n    name: string;\r\n    count: number;\r\n    customPrompt?: string;\r\n    difficultyLevel?: DifficultyLevel;\r\n    contentLength?: ContentLength;\r\n    questionTypes?: string[];\r\n    existingContent?: string[];\r\n    studySetId?: string;\r\n  }) => Promise<{\r\n    studySet: StudySet;\r\n    questions: QuizQuestion[];\r\n    creditsRemaining: number;\r\n  }>;\r\n\r\n  generateMoreFlashcards: (params: {\r\n    studySetId: string;\r\n    documentIds: string[];\r\n    documentPageRanges?: {\r\n      [documentId: string]: { startPage: number; endPage: number };\r\n    };\r\n    count: number;\r\n    customPrompt?: string;\r\n    difficultyLevel?: DifficultyLevel;\r\n    contentLength?: ContentLength;\r\n    existingContent?: string[];\r\n  }) => Promise<{ flashcards: Flashcard[]; creditsRemaining: number }>;\r\n\r\n  generateMoreQuizQuestions: (params: {\r\n    studySetId: string;\r\n    documentIds: string[];\r\n    documentPageRanges?: {\r\n      [documentId: string]: { startPage: number; endPage: number };\r\n    };\r\n    count: number;\r\n    customPrompt?: string;\r\n    difficultyLevel?: DifficultyLevel;\r\n    contentLength?: ContentLength;\r\n    questionTypes?: string[];\r\n    existingContent?: string[];\r\n  }) => Promise<{ questions: QuizQuestion[]; creditsRemaining: number }>;\r\n\r\n  clearLastGenerated: () => void;\r\n}\r\n\r\nexport const useAIStore = create<AIGenerationState>((set) => ({\r\n  isGenerating: false,\r\n  generationProgress: \"\",\r\n  lastGenerated: null,\r\n\r\n  generateFlashcards: async (params) => {\r\n    set({ isGenerating: true, generationProgress: \"Preparing documents...\" });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n\r\n      set({ generationProgress: \"Generating flashcards with AI...\" });\r\n\r\n      const response = await fetch(\"/api/ai/generate-flashcards\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Generation failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        set({\r\n          lastGenerated: {\r\n            studySet: result.data.studySet,\r\n            content: result.data.flashcards,\r\n            type: \"flashcards\",\r\n          },\r\n          isGenerating: false,\r\n          generationProgress: \"\",\r\n        });\r\n\r\n        // Refresh cache for the newly created study set\r\n        try {\r\n          // Import dynamically to avoid circular dependency\r\n          const { useStudyStore } = await import(\"./studyStore\");\r\n          const studyStore = useStudyStore.getState();\r\n          studyStore.refreshStudySetContent(result.data.studySet.id);\r\n          studyStore.invalidateStudySets(); // Refresh study sets list\r\n        } catch (cacheError) {\r\n          console.warn(\"Failed to refresh study set cache:\", cacheError);\r\n        }\r\n\r\n        return {\r\n          studySet: result.data.studySet,\r\n          flashcards: result.data.flashcards,\r\n          creditsRemaining: result.data.creditsRemaining,\r\n        };\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      set({ isGenerating: false, generationProgress: \"\" });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  generateQuiz: async (params) => {\r\n    set({ isGenerating: true, generationProgress: \"Preparing documents...\" });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n\r\n      set({ generationProgress: \"Generating quiz questions with AI...\" });\r\n\r\n      const response = await fetch(\"/api/ai/generate-quiz\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Generation failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        set({\r\n          lastGenerated: {\r\n            studySet: result.data.studySet,\r\n            content: result.data.questions,\r\n            type: \"quiz\",\r\n          },\r\n          isGenerating: false,\r\n          generationProgress: \"\",\r\n        });\r\n\r\n        // Refresh cache for the newly created study set\r\n        try {\r\n          // Import dynamically to avoid circular dependency\r\n          const { useStudyStore } = await import(\"./studyStore\");\r\n          const studyStore = useStudyStore.getState();\r\n          studyStore.refreshStudySetContent(result.data.studySet.id);\r\n          studyStore.invalidateStudySets(); // Refresh study sets list\r\n        } catch (cacheError) {\r\n          console.warn(\"Failed to refresh study set cache:\", cacheError);\r\n        }\r\n\r\n        return {\r\n          studySet: result.data.studySet,\r\n          questions: result.data.questions,\r\n          creditsRemaining: result.data.creditsRemaining,\r\n        };\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      set({ isGenerating: false, generationProgress: \"\" });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  generateMoreFlashcards: async (params) => {\r\n    set({ isGenerating: true, generationProgress: \"Preparing documents...\" });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n\r\n      set({\r\n        generationProgress: \"Generating additional flashcards with AI...\",\r\n      });\r\n\r\n      const response = await fetch(\"/api/ai/generate-more-flashcards\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Generation failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        set({\r\n          isGenerating: false,\r\n          generationProgress: \"\",\r\n        });\r\n\r\n        // Refresh cache for the updated study set\r\n        try {\r\n          // Import dynamically to avoid circular dependency\r\n          const { useStudyStore } = await import(\"./studyStore\");\r\n          const studyStore = useStudyStore.getState();\r\n          studyStore.refreshStudySetContent(params.studySetId);\r\n        } catch (cacheError) {\r\n          console.warn(\"Failed to refresh study set cache:\", cacheError);\r\n        }\r\n\r\n        return {\r\n          flashcards: result.data.flashcards,\r\n          creditsRemaining: result.data.creditsRemaining,\r\n        };\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      set({ isGenerating: false, generationProgress: \"\" });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  generateMoreQuizQuestions: async (params) => {\r\n    set({ isGenerating: true, generationProgress: \"Preparing documents...\" });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n\r\n      set({\r\n        generationProgress: \"Generating additional quiz questions with AI...\",\r\n      });\r\n\r\n      const response = await fetch(\"/api/ai/generate-more-quiz-questions\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Generation failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        set({ isGenerating: false, generationProgress: \"\" });\r\n\r\n        return {\r\n          questions: result.data.questions,\r\n          creditsRemaining: result.data.creditsRemaining,\r\n        };\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      set({ isGenerating: false, generationProgress: \"\" });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  clearLastGenerated: () => {\r\n    set({ lastGenerated: null });\r\n  },\r\n}));\r\n", "import React from \"react\";\r\nimport { Button } from \"../common/Button\";\r\nimport { BulkActionsToolbarProps } from \"../../types/userSettings\";\r\n\r\nexport const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({\r\n  selectedCount,\r\n  totalCount,\r\n  onDeleteSelected,\r\n  onClearSelection,\r\n  isLoading = false,\r\n  className = \"\",\r\n  itemType = \"item\",\r\n}) => {\r\n  if (selectedCount === 0) return null;\r\n\r\n  return (\r\n    <div\r\n      className={`bg-gray-800 border border-gray-700 rounded-lg p-4 mb-4 ${className}`}\r\n    >\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <span className=\"text-white font-medium\">\r\n            {selectedCount} of {totalCount} {itemType}\r\n            {selectedCount !== 1 ? \"s\" : \"\"} selected\r\n          </span>\r\n\r\n          <button\r\n            onClick={onClearSelection}\r\n            className=\"text-gray-400 hover:text-white text-sm underline\"\r\n            disabled={isLoading}\r\n          >\r\n            Clear selection\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          <Button\r\n            onClick={onDeleteSelected}\r\n            variant=\"danger\"\r\n            size=\"sm\"\r\n            isLoading={isLoading}\r\n            disabled={isLoading}\r\n            className=\"px-4 py-2\"\r\n          >\r\n            Delete Selected\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from \"react\";\nimport { Button } from \"../common/Button\";\nimport { CountInput } from \"../common/CountInput\";\nimport { AIGenerationProgress } from \"../common/ProgressBar\";\nimport { useDialog } from \"../../contexts/DialogContext\";\nimport { DocumentSelector } from \"../ai/DocumentSelector\";\nimport { DifficultySelector } from \"../common/DifficultySelector\";\nimport { ContentLengthSelector } from \"../common/ContentLengthSelector\";\nimport useAuthStore from \"../../stores/authStore\";\nimport { useAIStore } from \"../../stores/aiStore\";\nimport { useUserSettings } from \"../../hooks/useUserSettings\";\nimport { BulkActionsToolbar } from \"../ui/BulkActionsToolbar\";\nimport { HiEye, HiEyeOff } from \"react-icons/hi\";\nimport {\n  Flashcard,\n  DifficultyLevel,\n  ContentLength,\n  difficultyLevelToNumber,\n  difficultyLevelToString,\n  numberToDifficultyLevel,\n} from \"../../shared/types\";\n\ninterface FlashcardManagementProps {\n  studySetId: string;\n  flashcards: Flashcard[];\n  onFlashcardAdded: (flashcard: Flashcard) => void;\n  onFlashcardUpdated: (flashcard: Flashcard) => void;\n  onFlashcardDeleted: (flashcardId: string) => void;\n  onFlashcardsGenerated: (flashcards: Flashcard[]) => void;\n}\n\ninterface NewFlashcard {\n  front: string;\n  back: string;\n  difficulty_level: number;\n}\n\nexport const FlashcardManagement: React.FC<FlashcardManagementProps> = ({\n  studySetId,\n  flashcards,\n  onFlashcardAdded,\n  onFlashcardUpdated,\n  onFlashcardDeleted,\n  onFlashcardsGenerated,\n}) => {\n  const { alert, confirm } = useDialog();\n  const { user } = useAuthStore();\n  const { generateMoreFlashcards } = useAIStore();\n  const { settings: userSettings, updateSettings } = useUserSettings();\n  const [isAIMode, setIsAIMode] = useState(true);\n  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);\n  const [documentPageRanges, setDocumentPageRanges] = useState<{\n    [documentId: string]: { startPage: number; endPage: number };\n  }>({});\n  const [flashcardCount, setFlashcardCount] = useState(25);\n  const [customPrompt, setCustomPrompt] = useState(\"\");\n  const [difficultyLevel, setDifficultyLevel] = useState<DifficultyLevel>(\n    DifficultyLevel.MEDIUM\n  );\n  const [contentLength, setContentLength] = useState<ContentLength>(\n    ContentLength.MEDIUM\n  );\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newFlashcard, setNewFlashcard] = useState<NewFlashcard>({\n    front: \"\",\n    back: \"\",\n    difficulty_level: 3,\n  });\n  const [editingFlashcard, setEditingFlashcard] = useState<Flashcard | null>(\n    null\n  );\n  const [editForm, setEditForm] = useState<NewFlashcard>({\n    front: \"\",\n    back: \"\",\n    difficulty_level: 3,\n  });\n\n  // Bulk selection state\n  const [selectedFlashcards, setSelectedFlashcards] = useState<string[]>([]);\n  const [isSelectAllChecked, setIsSelectAllChecked] = useState(false);\n  const [isBulkDeleting, setIsBulkDeleting] = useState(false);\n\n  // Individual flashcard reveal state - track which flashcards show their back content\n  const [revealedFlashcards, setRevealedFlashcards] = useState<Set<string>>(\n    new Set()\n  );\n\n  // Toggle individual flashcard back content\n  const toggleFlashcardReveal = (flashcardId: string) => {\n    setRevealedFlashcards((prev) => {\n      const newSet = new Set(prev);\n      if (newSet.has(flashcardId)) {\n        newSet.delete(flashcardId);\n      } else {\n        newSet.add(flashcardId);\n      }\n      return newSet;\n    });\n  };\n\n  // Reveal all flashcards\n  const revealAllFlashcards = () => {\n    const allFlashcardIds = new Set(flashcards.map((f) => f.id));\n    setRevealedFlashcards(allFlashcardIds);\n  };\n\n  // Hide all flashcards\n  const hideAllFlashcards = () => {\n    setRevealedFlashcards(new Set());\n  };\n\n  // Handle page range changes\n  const handlePageRangeChange = (\n    documentId: string,\n    range: { startPage: number; endPage: number }\n  ) => {\n    setDocumentPageRanges((prev) => ({\n      ...prev,\n      [documentId]: range,\n    }));\n  };\n\n  // Bulk selection functions\n  const handleSelectFlashcard = (flashcardId: string, checked: boolean) => {\n    if (checked) {\n      setSelectedFlashcards((prev) => [...prev, flashcardId]);\n    } else {\n      setSelectedFlashcards((prev) => prev.filter((id) => id !== flashcardId));\n      setIsSelectAllChecked(false);\n    }\n  };\n\n  const handleSelectAll = (checked: boolean) => {\n    setIsSelectAllChecked(checked);\n    if (checked) {\n      setSelectedFlashcards(flashcards.map((f) => f.id));\n    } else {\n      setSelectedFlashcards([]);\n    }\n  };\n\n  const clearSelection = () => {\n    setSelectedFlashcards([]);\n    setIsSelectAllChecked(false);\n  };\n\n  const handleBulkDelete = async () => {\n    if (selectedFlashcards.length === 0) return;\n\n    // Check if user has disabled delete confirmations\n    if (!userSettings?.skip_delete_confirmations) {\n      let neverAskAgainChecked = false;\n\n      const confirmed = await confirm({\n        title: \"Delete Flashcards\",\n        message: `Are you sure you want to delete ${\n          selectedFlashcards.length\n        } flashcard${selectedFlashcards.length !== 1 ? \"s\" : \"\"}?`,\n        variant: \"danger\",\n        confirmText: \"Delete\",\n        cancelText: \"Cancel\",\n        buttonLayout: \"corners\",\n        showNeverAskAgain: true,\n        onNeverAskAgainChange: (checked) => {\n          neverAskAgainChecked = checked;\n        },\n      });\n\n      if (!confirmed) return;\n\n      // Update user settings if \"Never ask again\" was checked\n      if (neverAskAgainChecked) {\n        try {\n          await updateSettings({ skip_delete_confirmations: true });\n        } catch (error) {\n          console.error(\"Failed to update user settings:\", error);\n          // Continue with deletion even if settings update fails\n        }\n      }\n    }\n\n    await performBulkDeletion();\n  };\n\n  const performBulkDeletion = async () => {\n    setIsBulkDeleting(true);\n\n    try {\n      const response = await fetch(\"/api/flashcards/bulk-delete\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          flashcardIds: selectedFlashcards,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || \"Failed to delete flashcards\");\n      }\n\n      const data = await response.json();\n      const { deletedCount } = data.data;\n\n      // Update UI - remove deleted flashcards\n      selectedFlashcards.forEach((id) => onFlashcardDeleted(id));\n      clearSelection();\n\n      await alert({\n        title: \"Success\",\n        message: `${deletedCount} flashcard${\n          deletedCount !== 1 ? \"s\" : \"\"\n        } deleted successfully!`,\n        variant: \"success\",\n      });\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to delete flashcards\",\n        variant: \"error\",\n      });\n    } finally {\n      setIsBulkDeleting(false);\n    }\n  };\n\n  const handleAddFlashcard = async () => {\n    if (!newFlashcard.front.trim() || !newFlashcard.back.trim()) {\n      await alert({\n        title: \"Validation Error\",\n        message: \"Both front and back content are required.\",\n        variant: \"error\",\n      });\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/flashcards/study-set/${studySetId}`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n        },\n        body: JSON.stringify({\n          front: newFlashcard.front.trim(),\n          back: newFlashcard.back.trim(),\n          difficulty_level: newFlashcard.difficulty_level,\n          is_ai_generated: false,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to create flashcard\");\n      }\n\n      const result = await response.json();\n      onFlashcardAdded(result.data);\n\n      // Reset form\n      setNewFlashcard({ front: \"\", back: \"\", difficulty_level: 3 });\n      setShowAddForm(false);\n\n      await alert({\n        title: \"Success\",\n        message: \"Flashcard added successfully!\",\n        variant: \"success\",\n      });\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to add flashcard\",\n        variant: \"error\",\n      });\n    }\n  };\n\n  const handleEditFlashcard = (flashcard: Flashcard) => {\n    setEditingFlashcard(flashcard);\n    setEditForm({\n      front: flashcard.front,\n      back: flashcard.back,\n      difficulty_level:\n        typeof flashcard.difficulty_level === \"string\"\n          ? difficultyLevelToNumber(\n              flashcard.difficulty_level as DifficultyLevel\n            )\n          : flashcard.difficulty_level || 3,\n    });\n  };\n\n  const handleSaveEdit = async () => {\n    if (!editingFlashcard) return;\n\n    if (!editForm.front.trim() || !editForm.back.trim()) {\n      await alert({\n        title: \"Validation Error\",\n        message: \"Both front and back content are required.\",\n        variant: \"error\",\n      });\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/flashcards/${editingFlashcard.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n        },\n        body: JSON.stringify({\n          front: editForm.front.trim(),\n          back: editForm.back.trim(),\n          difficulty_level: editForm.difficulty_level,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update flashcard\");\n      }\n\n      const result = await response.json();\n      onFlashcardUpdated(result.data);\n\n      setEditingFlashcard(null);\n      setEditForm({ front: \"\", back: \"\", difficulty_level: 3 });\n\n      await alert({\n        title: \"Success\",\n        message: \"Flashcard updated successfully!\",\n        variant: \"success\",\n      });\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to update flashcard\",\n        variant: \"error\",\n      });\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setEditingFlashcard(null);\n    setEditForm({ front: \"\", back: \"\", difficulty_level: 3 });\n  };\n\n  const handleDeleteFlashcard = async (flashcard: Flashcard) => {\n    // Check if user has disabled delete confirmations\n    if (userSettings?.skip_delete_confirmations) {\n      // Skip confirmation and delete directly\n      await performFlashcardDeletion(flashcard);\n      return;\n    }\n\n    let neverAskAgainChecked = false;\n\n    const confirmed = await confirm({\n      title: \"Delete Flashcard\",\n      message: `Are you sure you want to delete this flashcard?\\n\\nFront: ${flashcard.front.substring(\n        0,\n        50\n      )}${flashcard.front.length > 50 ? \"...\" : \"\"}`,\n      variant: \"danger\",\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n      buttonLayout: \"corners\",\n      showNeverAskAgain: true,\n      onNeverAskAgainChange: (checked) => {\n        neverAskAgainChecked = checked;\n      },\n    });\n\n    if (!confirmed) return;\n\n    // Update user settings if \"Never ask again\" was checked\n    if (neverAskAgainChecked) {\n      try {\n        await updateSettings({ skip_delete_confirmations: true });\n      } catch (error) {\n        console.error(\"Failed to update user settings:\", error);\n        // Continue with deletion even if settings update fails\n      }\n    }\n\n    await performFlashcardDeletion(flashcard);\n  };\n\n  const performFlashcardDeletion = async (flashcard: Flashcard) => {\n    try {\n      const response = await fetch(`/api/flashcards/${flashcard.id}`, {\n        method: \"DELETE\",\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to delete flashcard\");\n      }\n\n      onFlashcardDeleted(flashcard.id);\n\n      await alert({\n        title: \"Success\",\n        message: \"Flashcard deleted successfully!\",\n        variant: \"success\",\n      });\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to delete flashcard\",\n        variant: \"error\",\n      });\n    }\n  };\n\n  const calculateCreditCost = () => {\n    // New pricing model: 1 credit = 5 flashcards\n    // So cost = Math.ceil(flashcardCount / 5)\n    return Math.ceil(flashcardCount / 5);\n  };\n\n  const handleGenerateFlashcards = async () => {\n    if (selectedDocuments.length === 0) {\n      await alert({\n        title: \"No Documents Selected\",\n        message:\n          \"Please select at least one document to generate flashcards from.\",\n        variant: \"warning\",\n      });\n      return;\n    }\n\n    const creditCost = calculateCreditCost();\n    if (user && user.credits_remaining < creditCost) {\n      await alert({\n        title: \"Insufficient Credits\",\n        message: `You need ${creditCost} credits to generate ${flashcardCount} flashcards, but you only have ${user.credits_remaining} credits remaining.`,\n        variant: \"error\",\n      });\n      return;\n    }\n\n    const confirmed = await confirm({\n      title: \"Generate Flashcards\",\n      message: `Generate ${flashcardCount} flashcards from ${selectedDocuments.length} document(s)?\\n\\nThis will cost ${creditCost} credits.`,\n      confirmText: \"Generate\",\n      cancelText: \"Cancel\",\n    });\n\n    if (!confirmed) return;\n\n    setIsGenerating(true);\n    try {\n      const result = await generateMoreFlashcards({\n        studySetId,\n        documentIds: selectedDocuments,\n        documentPageRanges,\n        count: flashcardCount,\n        customPrompt: customPrompt.trim() || undefined,\n        difficultyLevel,\n        contentLength,\n        existingContent: flashcards.map((f) => f.front), // Pass existing flashcard fronts to prevent duplicates\n      });\n\n      onFlashcardsGenerated(result.flashcards);\n\n      // Update user credits\n      if (user) {\n        useAuthStore.getState().updateUser({\n          credits_remaining: result.creditsRemaining,\n        });\n      }\n\n      // Refresh study set cache to ensure new content is loaded\n      try {\n        const { useStudyStore } = await import(\"../../stores/studyStore\");\n        const studyStore = useStudyStore.getState();\n        await studyStore.refreshStudySetContent(studySetId);\n      } catch (cacheError) {\n        console.warn(\"Failed to refresh study set cache:\", cacheError);\n      }\n\n      await alert({\n        title: \"Success\",\n        message: `Generated ${result.flashcards.length} flashcards successfully!`,\n        variant: \"success\",\n      });\n\n      // Reset AI form\n      setSelectedDocuments([]);\n      setCustomPrompt(\"\");\n      setIsAIMode(false);\n    } catch (error: any) {\n      await alert({\n        title: \"Generation Error\",\n        message: error.message || \"Failed to generate flashcards\",\n        variant: \"error\",\n      });\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-white\">Manage Flashcards</h3>\n        <div className=\"flex items-center space-x-3\">\n          <Button\n            onClick={() => setShowAddForm(!showAddForm)}\n            variant=\"secondary\"\n            size=\"sm\"\n          >\n            ➕ Add Flashcard\n          </Button>\n\n          {/* AI Mode Toggle */}\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-400\">AI Mode</span>\n            <button\n              onClick={() => setIsAIMode(!isAIMode)}\n              className={`\n                relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                ${isAIMode ? \"bg-primary-500\" : \"bg-gray-600\"}\n              `}\n            >\n              <span\n                className={`\n                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                  ${isAIMode ? \"translate-x-6\" : \"translate-x-1\"}\n                `}\n              />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Manual Add Form */}\n      {showAddForm && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-gray-600\">\n          <h4 className=\"text-md font-medium text-white mb-4\">\n            Add New Flashcard\n          </h4>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Front (Question/Term)\n              </label>\n              <textarea\n                value={newFlashcard.front}\n                onChange={(e) =>\n                  setNewFlashcard((prev) => ({\n                    ...prev,\n                    front: e.target.value,\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Enter the front content...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Back (Answer/Definition)\n              </label>\n              <textarea\n                value={newFlashcard.back}\n                onChange={(e) =>\n                  setNewFlashcard((prev) => ({ ...prev, back: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Enter the back content...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Difficulty Level\n              </label>\n              <select\n                value={newFlashcard.difficulty_level}\n                onChange={(e) =>\n                  setNewFlashcard((prev) => ({\n                    ...prev,\n                    difficulty_level: parseInt(e.target.value),\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500\"\n              >\n                <option value={1}>1 - Very Easy</option>\n                <option value={2}>2 - Easy</option>\n                <option value={3}>3 - Medium</option>\n                <option value={4}>4 - Hard</option>\n                <option value={5}>5 - Very Hard</option>\n              </select>\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <Button onClick={handleAddFlashcard} variant=\"primary\">\n                Add Flashcard\n              </Button>\n              <Button onClick={() => setShowAddForm(false)} variant=\"secondary\">\n                Cancel\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Form */}\n      {editingFlashcard && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-gray-600\">\n          <h4 className=\"text-md font-medium text-white mb-4\">\n            Edit Flashcard\n          </h4>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Front (Question/Term)\n              </label>\n              <textarea\n                value={editForm.front}\n                onChange={(e) =>\n                  setEditForm((prev) => ({ ...prev, front: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Enter the front content...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Back (Answer/Definition)\n              </label>\n              <textarea\n                value={editForm.back}\n                onChange={(e) =>\n                  setEditForm((prev) => ({ ...prev, back: e.target.value }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Enter the back content...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Difficulty Level\n              </label>\n              <select\n                value={editForm.difficulty_level}\n                onChange={(e) =>\n                  setEditForm((prev) => ({\n                    ...prev,\n                    difficulty_level: parseInt(e.target.value),\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500\"\n              >\n                <option value={1}>1 - Very Easy</option>\n                <option value={2}>2 - Easy</option>\n                <option value={3}>3 - Medium</option>\n                <option value={4}>4 - Hard</option>\n                <option value={5}>5 - Very Hard</option>\n              </select>\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <Button onClick={handleSaveEdit} variant=\"primary\">\n                Save Changes\n              </Button>\n              <Button onClick={handleCancelEdit} variant=\"secondary\">\n                Cancel\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* AI Generation Panel */}\n      {isAIMode && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-gray-600\">\n          <h4 className=\"text-md font-medium text-white mb-4\">\n            AI Flashcard Generation\n          </h4>\n\n          <div className=\"space-y-4\">\n            {/* Document Selection */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Select Documents\n              </label>\n              <DocumentSelector\n                selectedDocuments={selectedDocuments}\n                onSelectionChange={setSelectedDocuments}\n                documentPageRanges={documentPageRanges}\n                onPageRangeChange={handlePageRangeChange}\n                maxSelection={5}\n              />\n            </div>\n\n            {/* Generation Controls */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <CountInput\n                label=\"Number of Flashcards\"\n                value={flashcardCount}\n                onChange={setFlashcardCount}\n                min={1}\n                max={100}\n                placeholder=\"Enter number (1-100)\"\n                disabled={isGenerating}\n              />\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Credit Cost\n                </label>\n                <div className=\"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium\">\n                  {calculateCreditCost()} credits\n                </div>\n              </div>\n            </div>\n\n            {/* Difficulty Level */}\n            <DifficultySelector\n              value={difficultyLevel}\n              onChange={setDifficultyLevel}\n            />\n\n            {/* Content Length */}\n            <ContentLengthSelector\n              value={contentLength}\n              onChange={setContentLength}\n            />\n\n            {/* Custom Prompt */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Custom Instructions (Optional)\n              </label>\n              <textarea\n                value={customPrompt}\n                onChange={(e) => setCustomPrompt(e.target.value)}\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Add specific instructions for flashcard generation...\"\n              />\n            </div>\n\n            {/* Generate Button */}\n            <Button\n              onClick={handleGenerateFlashcards}\n              disabled={selectedDocuments.length === 0 || isGenerating}\n              className=\"w-full\"\n              variant=\"primary\"\n            >\n              {isGenerating\n                ? \"Generating...\"\n                : `Generate ${flashcardCount} Flashcards`}\n            </Button>\n          </div>\n        </div>\n      )}\n\n      {/* AI Generation Progress */}\n      <AIGenerationProgress\n        isGenerating={isGenerating}\n        stage={isGenerating ? \"Generating flashcards with AI...\" : undefined}\n        estimatedTime={Math.ceil(flashcardCount / 10)} // Rough estimate: 1 second per 10 cards\n      />\n\n      {/* Flashcard List */}\n      <div className=\"space-y-3\">\n        <div className=\"flex items-center justify-between\">\n          <h4 className=\"text-md font-medium text-white\">\n            Current Flashcards ({flashcards.length})\n          </h4>\n\n          {flashcards.length > 0 && (\n            <div className=\"flex items-center space-x-3\">\n              <label className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <input\n                  type=\"checkbox\"\n                  checked={isSelectAllChecked}\n                  onChange={(e) => handleSelectAll(e.target.checked)}\n                  className=\"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800\"\n                />\n                <span>Select All</span>\n              </label>\n            </div>\n          )}\n        </div>\n\n        {/* Bulk Actions Toolbar */}\n        <BulkActionsToolbar\n          selectedCount={selectedFlashcards.length}\n          totalCount={flashcards.length}\n          onDeleteSelected={handleBulkDelete}\n          onClearSelection={clearSelection}\n          isLoading={isBulkDeleting}\n        />\n\n        {/* Toggle Controls */}\n        {flashcards.length > 0 && (\n          <div className=\"flex items-center justify-between mb-4\">\n            <p className=\"text-sm text-gray-400\">\n              💡 Click on any flashcard to reveal/hide its answer\n            </p>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={revealAllFlashcards}\n                className=\"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm\"\n              >\n                <HiEye className=\"w-4 h-4\" />\n                <span>Show All</span>\n              </button>\n              <button\n                onClick={hideAllFlashcards}\n                className=\"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm\"\n              >\n                <HiEyeOff className=\"w-4 h-4\" />\n                <span>Hide All</span>\n              </button>\n            </div>\n          </div>\n        )}\n\n        {flashcards.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-400\">\n            No flashcards yet. Add some manually or generate them with AI.\n          </div>\n        ) : (\n          <div className=\"space-y-2\">\n            {flashcards.map((flashcard) => (\n              <div\n                key={flashcard.id}\n                className=\"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors\"\n              >\n                <div className=\"flex items-start space-x-3\">\n                  {/* Selection Checkbox */}\n                  <div className=\"flex-shrink-0 pt-1\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedFlashcards.includes(flashcard.id)}\n                      onChange={(e) =>\n                        handleSelectFlashcard(flashcard.id, e.target.checked)\n                      }\n                      className=\"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800\"\n                    />\n                  </div>\n\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-start justify-between\">\n                      <div\n                        className=\"flex-1 min-w-0 cursor-pointer group\"\n                        onClick={() => toggleFlashcardReveal(flashcard.id)}\n                        title={\n                          revealedFlashcards.has(flashcard.id)\n                            ? \"Click to hide answer\"\n                            : \"Click to reveal answer\"\n                        }\n                      >\n                        <div className=\"mb-2\">\n                          <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                            Front\n                          </span>\n                          <p className=\"text-white font-medium group-hover:text-primary-300 transition-colors\">\n                            {flashcard.front}\n                          </p>\n                        </div>\n                        {revealedFlashcards.has(flashcard.id) ? (\n                          <div className=\"mb-2\">\n                            <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                              Back\n                            </span>\n                            <p className=\"text-gray-300 group-hover:text-gray-200 transition-colors\">\n                              {flashcard.back}\n                            </p>\n                          </div>\n                        ) : (\n                          <div className=\"mb-2\">\n                            <p className=\"text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors\">\n                              Click to reveal answer...\n                            </p>\n                          </div>\n                        )}\n                        <div className=\"flex items-center space-x-4 text-xs text-gray-400\">\n                          {flashcard.is_ai_generated && (\n                            <span className=\"bg-primary-500/20 text-primary-400 px-2 py-1 rounded\">\n                              AI Generated\n                            </span>\n                          )}\n                          {flashcard.difficulty_level && (\n                            <span>\n                              Difficulty:{\" \"}\n                              {typeof flashcard.difficulty_level === \"string\"\n                                ? difficultyLevelToString(\n                                    flashcard.difficulty_level as DifficultyLevel\n                                  )\n                                : difficultyLevelToString(\n                                    numberToDifficultyLevel(\n                                      flashcard.difficulty_level\n                                    )\n                                  )}\n                            </span>\n                          )}\n                          <span>\n                            Reviewed: {flashcard.times_reviewed || 0} times\n                          </span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <button\n                          onClick={() => handleEditFlashcard(flashcard)}\n                          className=\"text-gray-400 hover:text-white p-1\"\n                          title=\"Edit flashcard\"\n                        >\n                          ✏️\n                        </button>\n                        <button\n                          onClick={() => handleDeleteFlashcard(flashcard)}\n                          className=\"text-gray-400 hover:text-red-400 p-1\"\n                          title=\"Delete flashcard\"\n                        >\n                          🗑️\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from \"react\";\nimport { Button } from \"../common/Button\";\nimport { CountInput } from \"../common/CountInput\";\nimport { AIGenerationProgress } from \"../common/ProgressBar\";\nimport { useDialog } from \"../../contexts/DialogContext\";\nimport { DocumentSelector } from \"../ai/DocumentSelector\";\nimport { DifficultySelector } from \"../common/DifficultySelector\";\nimport { ContentLengthSelector } from \"../common/ContentLengthSelector\";\nimport useAuthStore from \"../../stores/authStore\";\nimport { HiEye, HiEyeOff } from \"react-icons/hi\";\nimport {\n  QuizQuestion,\n  QuestionType,\n  DifficultyLevel,\n  ContentLength,\n  difficultyLevelToNumber,\n  difficultyLevelToString,\n  numberToDifficultyLevel,\n} from \"../../shared/types\";\nimport { Checkbox } from \"../common/Checkbox\";\nimport { BulkActionsToolbar } from \"../ui/BulkActionsToolbar\";\n\ninterface QuizManagementProps {\n  studySetId: string;\n  questions: QuizQuestion[];\n  onQuestionAdded: (question: QuizQuestion) => void;\n  onQuestionUpdated: (question: QuizQuestion) => void;\n  onQuestionDeleted: (questionId: string) => void;\n  onQuestionsGenerated: (questions: QuizQuestion[]) => void;\n}\n\ninterface NewQuestion {\n  question_text: string;\n  question_type: QuestionType;\n  options: string[];\n  correct_answers: string[];\n  explanation: string;\n  difficulty_level: number;\n}\n\nexport const QuizManagement: React.FC<QuizManagementProps> = ({\n  studySetId,\n  questions,\n  onQuestionAdded,\n  onQuestionUpdated,\n  onQuestionDeleted,\n  onQuestionsGenerated,\n}) => {\n  const { alert, confirm } = useDialog();\n  const { user } = useAuthStore();\n  const [isAIMode, setIsAIMode] = useState(true);\n  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);\n  const [documentPageRanges, setDocumentPageRanges] = useState<{\n    [documentId: string]: { startPage: number; endPage: number };\n  }>({});\n  const [questionCount, setQuestionCount] = useState(25);\n  const [customPrompt, setCustomPrompt] = useState(\"\");\n  const [difficultyLevel, setDifficultyLevel] = useState<DifficultyLevel>(\n    DifficultyLevel.MEDIUM\n  );\n  const [contentLength, setContentLength] = useState<ContentLength>(\n    ContentLength.MEDIUM\n  );\n  const [selectedQuestionTypes, setSelectedQuestionTypes] = useState<\n    QuestionType[]\n  >([\"multiple_choice\", \"select_all\", \"true_false\", \"short_answer\"]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newQuestion, setNewQuestion] = useState<NewQuestion>({\n    question_text: \"\",\n    question_type: \"multiple_choice\",\n    options: [\"\", \"\", \"\", \"\"],\n    correct_answers: [],\n    explanation: \"\",\n    difficulty_level: 3,\n  });\n  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(\n    null\n  );\n\n  // Bulk selection state\n  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);\n  const [isSelectAllChecked, setIsSelectAllChecked] = useState(false);\n  const [isBulkDeleting, setIsBulkDeleting] = useState(false);\n  const [editForm, setEditForm] = useState<NewQuestion>({\n    question_text: \"\",\n    question_type: \"multiple_choice\",\n    options: [\"\", \"\", \"\", \"\"],\n    correct_answers: [],\n    explanation: \"\",\n    difficulty_level: 3,\n  });\n\n  // Individual question reveal state - track which questions show their answer details\n  const [revealedQuestions, setRevealedQuestions] = useState<Set<string>>(\n    new Set()\n  );\n\n  // Toggle individual question answer details\n  const toggleQuestionReveal = (questionId: string) => {\n    setRevealedQuestions((prev) => {\n      const newSet = new Set(prev);\n      if (newSet.has(questionId)) {\n        newSet.delete(questionId);\n      } else {\n        newSet.add(questionId);\n      }\n      return newSet;\n    });\n  };\n\n  // Reveal all questions\n  const revealAllQuestions = () => {\n    const allQuestionIds = new Set(questions.map((q) => q.id));\n    setRevealedQuestions(allQuestionIds);\n  };\n\n  // Bulk selection handlers\n  const handleSelectQuestion = (questionId: string, checked: boolean) => {\n    if (checked) {\n      setSelectedQuestions((prev) => [...prev, questionId]);\n    } else {\n      setSelectedQuestions((prev) => prev.filter((id) => id !== questionId));\n      setIsSelectAllChecked(false);\n    }\n  };\n\n  const handleSelectAll = (checked: boolean) => {\n    setIsSelectAllChecked(checked);\n    if (checked) {\n      setSelectedQuestions(questions.map((q) => q.id));\n    } else {\n      setSelectedQuestions([]);\n    }\n  };\n\n  const clearSelection = () => {\n    setSelectedQuestions([]);\n    setIsSelectAllChecked(false);\n  };\n\n  const performBulkDeletion = async () => {\n    try {\n      // Delete selected questions via API\n      await Promise.all(\n        selectedQuestions.map((id) =>\n          fetch(`/api/quiz-questions/${id}`, {\n            method: \"DELETE\",\n            headers: {\n              Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n            },\n          })\n        )\n      );\n      // Update parent list\n      selectedQuestions.forEach((id) => onQuestionDeleted(id));\n      clearSelection();\n    } catch (error) {\n      console.error(\"Bulk deletion failed\", error);\n      await alert({\n        title: \"Error\",\n        message: \"Failed to delete selected questions.\",\n        variant: \"error\",\n      });\n    } finally {\n      setIsBulkDeleting(false);\n    }\n  };\n\n  const handleBulkDelete = async () => {\n    if (selectedQuestions.length === 0) return;\n    const confirmed = await confirm({\n      title: \"Delete Questions\",\n      message: `Are you sure you want to delete ${\n        selectedQuestions.length\n      } question$${selectedQuestions.length !== 1 ? \"s\" : \"\"}?`,\n      variant: \"danger\",\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n    });\n    if (!confirmed) return;\n    setIsBulkDeleting(true);\n    await performBulkDeletion();\n  };\n\n  // Hide all questions\n  const hideAllQuestions = () => {\n    setRevealedQuestions(new Set());\n  };\n\n  // Handle page range changes\n  const handlePageRangeChange = (\n    documentId: string,\n    range: { startPage: number; endPage: number }\n  ) => {\n    setDocumentPageRanges((prev) => ({\n      ...prev,\n      [documentId]: range,\n    }));\n  };\n\n  const handleAddQuestion = async () => {\n    if (!newQuestion.question_text.trim()) {\n      await alert({\n        title: \"Validation Error\",\n        message: \"Question text is required.\",\n        variant: \"error\",\n      });\n      return;\n    }\n\n    if (newQuestion.correct_answers.length === 0) {\n      await alert({\n        title: \"Validation Error\",\n        message: \"At least one correct answer is required.\",\n        variant: \"error\",\n      });\n      return;\n    }\n\n    // Validate options for multiple choice and select all questions\n    if (\n      newQuestion.question_type === \"multiple_choice\" ||\n      newQuestion.question_type === \"select_all\"\n    ) {\n      const validOptions = newQuestion.options.filter(\n        (opt) => opt.trim().length > 0\n      );\n      if (validOptions.length < 2) {\n        await alert({\n          title: \"Validation Error\",\n          message:\n            \"Multiple choice and select all questions require at least 2 options.\",\n          variant: \"error\",\n        });\n        return;\n      }\n    }\n\n    try {\n      const response = await fetch(\n        `/api/quiz-questions/study-set/${studySetId}`,\n        {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n          },\n          body: JSON.stringify({\n            question_text: newQuestion.question_text.trim(),\n            question_type: newQuestion.question_type,\n            options:\n              newQuestion.question_type === \"multiple_choice\" ||\n              newQuestion.question_type === \"select_all\"\n                ? newQuestion.options.filter((opt) => opt.trim().length > 0)\n                : null,\n            correct_answers: newQuestion.correct_answers,\n            explanation: newQuestion.explanation.trim() || null,\n            difficulty_level: newQuestion.difficulty_level,\n          }),\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(\"Failed to create question\");\n      }\n\n      const result = await response.json();\n      onQuestionAdded(result.data);\n\n      // Reset form\n      setNewQuestion({\n        question_text: \"\",\n        question_type: \"multiple_choice\",\n        options: [\"\", \"\", \"\", \"\"],\n        correct_answers: [],\n        explanation: \"\",\n        difficulty_level: 3,\n      });\n      setShowAddForm(false);\n\n      await alert({\n        title: \"Success\",\n        message: \"Question added successfully!\",\n        variant: \"success\",\n      });\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to add question\",\n        variant: \"error\",\n      });\n    }\n  };\n\n  const handleDeleteQuestion = async (question: QuizQuestion) => {\n    const confirmed = await confirm({\n      title: \"Delete Question\",\n      message: `Are you sure you want to delete this question?\\n\\n${question.question_text.substring(\n        0,\n        100\n      )}${question.question_text.length > 100 ? \"...\" : \"\"}`,\n      variant: \"danger\",\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n    });\n\n    if (!confirmed) return;\n\n    try {\n      const response = await fetch(`/api/quiz-questions/${question.id}`, {\n        method: \"DELETE\",\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to delete question\");\n      }\n\n      onQuestionDeleted(question.id);\n\n      await alert({\n        title: \"Success\",\n        message: \"Question deleted successfully!\",\n        variant: \"success\",\n      });\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to delete question\",\n        variant: \"error\",\n      });\n    }\n  };\n\n  const handleEditQuestion = (question: QuizQuestion) => {\n    setEditingQuestion(question);\n    setEditForm({\n      question_text: question.question_text,\n      question_type: question.question_type,\n      options: question.options || [\"\", \"\", \"\", \"\"],\n      correct_answers: question.correct_answers,\n      explanation: question.explanation || \"\",\n      difficulty_level:\n        typeof question.difficulty_level === \"string\"\n          ? difficultyLevelToNumber(\n              question.difficulty_level as DifficultyLevel\n            )\n          : question.difficulty_level || 3,\n    });\n  };\n\n  const handleSaveEdit = async () => {\n    if (!editingQuestion) return;\n\n    if (!editForm.question_text.trim()) {\n      await alert({\n        title: \"Validation Error\",\n        message: \"Question text is required.\",\n        variant: \"error\",\n      });\n      return;\n    }\n\n    if (editForm.correct_answers.length === 0) {\n      await alert({\n        title: \"Validation Error\",\n        message: \"At least one correct answer is required.\",\n        variant: \"error\",\n      });\n      return;\n    }\n\n    // Validate options for multiple choice and select all questions\n    if (\n      editForm.question_type === \"multiple_choice\" ||\n      editForm.question_type === \"select_all\"\n    ) {\n      const validOptions = editForm.options.filter(\n        (opt) => opt.trim().length > 0\n      );\n      if (validOptions.length < 2) {\n        await alert({\n          title: \"Validation Error\",\n          message:\n            \"Multiple choice and select all questions require at least 2 options.\",\n          variant: \"error\",\n        });\n        return;\n      }\n    }\n\n    try {\n      const response = await fetch(\n        `/api/quiz-questions/${editingQuestion.id}`,\n        {\n          method: \"PUT\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n          },\n          body: JSON.stringify({\n            question_text: editForm.question_text.trim(),\n            question_type: editForm.question_type,\n            options:\n              editForm.question_type === \"multiple_choice\" ||\n              editForm.question_type === \"select_all\"\n                ? editForm.options.filter((opt) => opt.trim().length > 0)\n                : null,\n            correct_answers: editForm.correct_answers,\n            explanation: editForm.explanation.trim() || null,\n            difficulty_level: editForm.difficulty_level,\n          }),\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update question\");\n      }\n\n      const updatedQuestion = await response.json();\n      onQuestionUpdated(updatedQuestion.data);\n      setEditingQuestion(null);\n\n      await alert({\n        title: \"Success\",\n        message: \"Question updated successfully!\",\n        variant: \"success\",\n      });\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to update question\",\n        variant: \"error\",\n      });\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setEditingQuestion(null);\n    setEditForm({\n      question_text: \"\",\n      question_type: \"multiple_choice\",\n      options: [\"\", \"\", \"\", \"\"],\n      correct_answers: [],\n      explanation: \"\",\n      difficulty_level: 3,\n    });\n  };\n\n  const calculateCreditCost = () => {\n    // New pricing model: 1 credit = 5 quiz questions\n    // So cost = Math.ceil(questionCount / 5)\n    return Math.ceil(questionCount / 5);\n  };\n\n  const handleGenerateQuestions = async () => {\n    if (selectedDocuments.length === 0) {\n      await alert({\n        title: \"No Documents Selected\",\n        message:\n          \"Please select at least one document to generate questions from.\",\n        variant: \"warning\",\n      });\n      return;\n    }\n\n    if (selectedQuestionTypes.length === 0) {\n      await alert({\n        title: \"No Question Types Selected\",\n        message: \"Please select at least one question type to generate.\",\n        variant: \"warning\",\n      });\n      return;\n    }\n\n    const creditCost = calculateCreditCost();\n    if (user && user.credits_remaining < creditCost) {\n      await alert({\n        title: \"Insufficient Credits\",\n        message: `You need ${creditCost} credits to generate ${questionCount} questions, but you only have ${user.credits_remaining} credits remaining.`,\n        variant: \"warning\",\n      });\n      return;\n    }\n\n    const confirmed = await confirm({\n      title: \"Generate Questions\",\n      message: `Generate ${questionCount} questions for ${creditCost} credits?`,\n      confirmText: \"Generate\",\n      cancelText: \"Cancel\",\n    });\n\n    if (!confirmed) return;\n\n    setIsGenerating(true);\n    try {\n      const response = await fetch(\"/api/ai/generate-more-quiz-questions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"auth_token\")}`,\n        },\n        body: JSON.stringify({\n          studySetId,\n          documentIds: selectedDocuments,\n          documentPageRanges,\n          count: questionCount,\n          customPrompt: customPrompt.trim() || undefined,\n          difficultyLevel,\n          contentLength,\n          questionTypes: selectedQuestionTypes,\n          existingContent: questions.map((q) => q.question_text),\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to generate questions\");\n      }\n\n      const result = await response.json();\n      onQuestionsGenerated(result.data.questions);\n\n      // Update user credits\n      if (user) {\n        useAuthStore.getState().updateUser({\n          credits_remaining: result.data.creditsRemaining,\n        });\n      }\n\n      // Refresh study set cache to ensure new content is loaded\n      try {\n        const { useStudyStore } = await import(\"../../stores/studyStore\");\n        const studyStore = useStudyStore.getState();\n        await studyStore.refreshStudySetContent(studySetId);\n      } catch (cacheError) {\n        console.warn(\"Failed to refresh study set cache:\", cacheError);\n      }\n\n      await alert({\n        title: \"Success\",\n        message: `Generated ${result.data.questions.length} questions successfully!`,\n        variant: \"success\",\n      });\n\n      // Reset AI form\n      setSelectedDocuments([]);\n      setCustomPrompt(\"\");\n      setIsAIMode(false);\n    } catch (error: any) {\n      await alert({\n        title: \"Error\",\n        message: error.message || \"Failed to generate questions\",\n        variant: \"error\",\n      });\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  const handleQuestionTypeChange = (type: QuestionType) => {\n    setNewQuestion((prev) => ({\n      ...prev,\n      question_type: type,\n      options:\n        type === \"multiple_choice\" || type === \"select_all\"\n          ? [\"\", \"\", \"\", \"\"]\n          : [],\n      correct_answers: [],\n    }));\n  };\n\n  const handleOptionChange = (index: number, value: string) => {\n    setNewQuestion((prev) => ({\n      ...prev,\n      options: prev.options.map((opt, i) => (i === index ? value : opt)),\n    }));\n  };\n\n  const handleCorrectAnswerToggle = (answer: string) => {\n    setNewQuestion((prev) => {\n      const isSelected = prev.correct_answers.includes(answer);\n      if (prev.question_type === \"multiple_choice\") {\n        // Single selection for multiple choice\n        return { ...prev, correct_answers: isSelected ? [] : [answer] };\n      } else {\n        // Multiple selection for select all\n        return {\n          ...prev,\n          correct_answers: isSelected\n            ? prev.correct_answers.filter((a) => a !== answer)\n            : [...prev.correct_answers, answer],\n        };\n      }\n    });\n  };\n\n  const toggleQuestionType = (type: QuestionType) => {\n    setSelectedQuestionTypes((prev) =>\n      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header Controls */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-white\">\n          Question Management\n        </h3>\n\n        <div className=\"flex flex-wrap gap-2\">\n          <Button\n            onClick={() => setShowAddForm(!showAddForm)}\n            variant=\"secondary\"\n            size=\"sm\"\n          >\n            {showAddForm ? \"Cancel\" : \"Add Question\"}\n          </Button>\n\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-300\">AI Mode</span>\n            <button\n              onClick={() => setIsAIMode(!isAIMode)}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                isAIMode ? \"bg-primary-500\" : \"bg-gray-600\"\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  isAIMode ? \"translate-x-6\" : \"translate-x-1\"\n                }`}\n              />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Manual Add Form */}\n      {showAddForm && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-gray-600\">\n          <h4 className=\"text-md font-medium text-white mb-4\">\n            Add New Question\n          </h4>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Question Text\n              </label>\n              <textarea\n                value={newQuestion.question_text}\n                onChange={(e) =>\n                  setNewQuestion((prev) => ({\n                    ...prev,\n                    question_text: e.target.value,\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Enter your question...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Question Type\n              </label>\n              <select\n                value={newQuestion.question_type}\n                onChange={(e) =>\n                  handleQuestionTypeChange(e.target.value as QuestionType)\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500\"\n              >\n                <option value=\"multiple_choice\">Multiple Choice</option>\n                <option value=\"select_all\">Select All That Apply</option>\n                <option value=\"true_false\">True/False</option>\n                <option value=\"short_answer\">Short Answer</option>\n              </select>\n            </div>\n\n            {/* Options for multiple choice and select all */}\n            {(newQuestion.question_type === \"multiple_choice\" ||\n              newQuestion.question_type === \"select_all\") && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Answer Options\n                </label>\n                <div className=\"space-y-2\">\n                  {newQuestion.options.map((option, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleCorrectAnswerToggle(option)}\n                        className={`flex-shrink-0 w-5 h-5 border-2 ${\n                          newQuestion.question_type === \"multiple_choice\"\n                            ? \"rounded-full\"\n                            : \"rounded\"\n                        } ${\n                          newQuestion.correct_answers.includes(option)\n                            ? \"bg-primary-500 border-primary-500\"\n                            : \"border-gray-400\"\n                        } flex items-center justify-center`}\n                        disabled={!option.trim()}\n                      >\n                        {newQuestion.correct_answers.includes(option) && (\n                          <span className=\"text-white text-xs\">✓</span>\n                        )}\n                      </button>\n                      <input\n                        type=\"text\"\n                        value={option}\n                        onChange={(e) =>\n                          handleOptionChange(index, e.target.value)\n                        }\n                        className=\"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                        placeholder={`Option ${index + 1}`}\n                      />\n                    </div>\n                  ))}\n                </div>\n                <p className=\"text-xs text-gray-400 mt-1\">\n                  {newQuestion.question_type === \"multiple_choice\"\n                    ? \"Click the circle to mark the correct answer\"\n                    : \"Click the squares to mark all correct answers\"}\n                </p>\n              </div>\n            )}\n\n            {/* True/False options */}\n            {newQuestion.question_type === \"true_false\" && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Correct Answer\n                </label>\n                <div className=\"flex space-x-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() =>\n                      setNewQuestion((prev) => ({\n                        ...prev,\n                        correct_answers: [\"True\"],\n                      }))\n                    }\n                    className={`px-4 py-2 rounded-lg border ${\n                      newQuestion.correct_answers.includes(\"True\")\n                        ? \"bg-primary-500 border-primary-500 text-white\"\n                        : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                    }`}\n                  >\n                    True\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() =>\n                      setNewQuestion((prev) => ({\n                        ...prev,\n                        correct_answers: [\"False\"],\n                      }))\n                    }\n                    className={`px-4 py-2 rounded-lg border ${\n                      newQuestion.correct_answers.includes(\"False\")\n                        ? \"bg-primary-500 border-primary-500 text-white\"\n                        : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                    }`}\n                  >\n                    False\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Short answer */}\n            {newQuestion.question_type === \"short_answer\" && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Acceptable Answers (one per line)\n                </label>\n                <textarea\n                  value={newQuestion.correct_answers.join(\"\\n\")}\n                  onChange={(e) =>\n                    setNewQuestion((prev) => ({\n                      ...prev,\n                      correct_answers: e.target.value\n                        .split(\"\\n\")\n                        .filter((a) => a.trim()),\n                    }))\n                  }\n                  className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                  rows={3}\n                  placeholder=\"Enter acceptable answers, one per line...\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Explanation (Optional)\n              </label>\n              <textarea\n                value={newQuestion.explanation}\n                onChange={(e) =>\n                  setNewQuestion((prev) => ({\n                    ...prev,\n                    explanation: e.target.value,\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={2}\n                placeholder=\"Explain the correct answer...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Difficulty Level\n              </label>\n              <select\n                value={newQuestion.difficulty_level}\n                onChange={(e) =>\n                  setNewQuestion((prev) => ({\n                    ...prev,\n                    difficulty_level: parseInt(e.target.value),\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500\"\n              >\n                <option value={1}>1 - Very Easy</option>\n                <option value={2}>2 - Easy</option>\n                <option value={3}>3 - Medium</option>\n                <option value={4}>4 - Hard</option>\n                <option value={5}>5 - Very Hard</option>\n              </select>\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <Button onClick={handleAddQuestion} variant=\"primary\">\n                Add Question\n              </Button>\n              <Button onClick={() => setShowAddForm(false)} variant=\"secondary\">\n                Cancel\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Form */}\n      {editingQuestion && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-gray-600\">\n          <h4 className=\"text-md font-medium text-white mb-4\">Edit Question</h4>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Question Text\n              </label>\n              <textarea\n                value={editForm.question_text}\n                onChange={(e) =>\n                  setEditForm((prev) => ({\n                    ...prev,\n                    question_text: e.target.value,\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Enter your question...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Question Type\n              </label>\n              <select\n                value={editForm.question_type}\n                onChange={(e) =>\n                  setEditForm((prev) => ({\n                    ...prev,\n                    question_type: e.target.value as QuestionType,\n                    options:\n                      e.target.value === \"multiple_choice\" ||\n                      e.target.value === \"select_all\"\n                        ? [\"\", \"\", \"\", \"\"]\n                        : [],\n                    correct_answers: [],\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500\"\n              >\n                <option value=\"multiple_choice\">Multiple Choice</option>\n                <option value=\"select_all\">Select All That Apply</option>\n                <option value=\"true_false\">True/False</option>\n                <option value=\"short_answer\">Short Answer</option>\n              </select>\n            </div>\n\n            {/* Options for multiple choice and select all */}\n            {(editForm.question_type === \"multiple_choice\" ||\n              editForm.question_type === \"select_all\") && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Answer Options\n                </label>\n                <div className=\"space-y-2\">\n                  {editForm.options.map((option, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <button\n                        type=\"button\"\n                        onClick={() => {\n                          const isSelected =\n                            editForm.correct_answers.includes(option);\n                          if (editForm.question_type === \"multiple_choice\") {\n                            setEditForm((prev) => ({\n                              ...prev,\n                              correct_answers: isSelected ? [] : [option],\n                            }));\n                          } else {\n                            setEditForm((prev) => ({\n                              ...prev,\n                              correct_answers: isSelected\n                                ? prev.correct_answers.filter(\n                                    (a) => a !== option\n                                  )\n                                : [...prev.correct_answers, option],\n                            }));\n                          }\n                        }}\n                        className={`flex-shrink-0 w-5 h-5 border-2 ${\n                          editForm.question_type === \"multiple_choice\"\n                            ? \"rounded-full\"\n                            : \"rounded\"\n                        } ${\n                          editForm.correct_answers.includes(option)\n                            ? \"bg-primary-500 border-primary-500\"\n                            : \"border-gray-400\"\n                        } flex items-center justify-center`}\n                        disabled={!option.trim()}\n                      >\n                        {editForm.correct_answers.includes(option) && (\n                          <span className=\"text-white text-xs\">✓</span>\n                        )}\n                      </button>\n                      <input\n                        type=\"text\"\n                        value={option}\n                        onChange={(e) =>\n                          setEditForm((prev) => ({\n                            ...prev,\n                            options: prev.options.map((opt, i) =>\n                              i === index ? e.target.value : opt\n                            ),\n                          }))\n                        }\n                        className=\"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                        placeholder={`Option ${index + 1}`}\n                      />\n                    </div>\n                  ))}\n                </div>\n                <p className=\"text-xs text-gray-400 mt-1\">\n                  {editForm.question_type === \"multiple_choice\"\n                    ? \"Click the circle to mark the correct answer\"\n                    : \"Click the squares to mark all correct answers\"}\n                </p>\n              </div>\n            )}\n\n            {/* True/False options */}\n            {editForm.question_type === \"true_false\" && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Correct Answer\n                </label>\n                <div className=\"flex space-x-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() =>\n                      setEditForm((prev) => ({\n                        ...prev,\n                        correct_answers: [\"True\"],\n                      }))\n                    }\n                    className={`px-4 py-2 rounded-lg border ${\n                      editForm.correct_answers.includes(\"True\")\n                        ? \"bg-primary-500 border-primary-500 text-white\"\n                        : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                    }`}\n                  >\n                    True\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() =>\n                      setEditForm((prev) => ({\n                        ...prev,\n                        correct_answers: [\"False\"],\n                      }))\n                    }\n                    className={`px-4 py-2 rounded-lg border ${\n                      editForm.correct_answers.includes(\"False\")\n                        ? \"bg-primary-500 border-primary-500 text-white\"\n                        : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                    }`}\n                  >\n                    False\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Short answer */}\n            {editForm.question_type === \"short_answer\" && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Acceptable Answers (one per line)\n                </label>\n                <textarea\n                  value={editForm.correct_answers.join(\"\\n\")}\n                  onChange={(e) =>\n                    setEditForm((prev) => ({\n                      ...prev,\n                      correct_answers: e.target.value\n                        .split(\"\\n\")\n                        .filter((a) => a.trim()),\n                    }))\n                  }\n                  className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                  rows={3}\n                  placeholder=\"Enter acceptable answers, one per line...\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Explanation (Optional)\n              </label>\n              <textarea\n                value={editForm.explanation}\n                onChange={(e) =>\n                  setEditForm((prev) => ({\n                    ...prev,\n                    explanation: e.target.value,\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={2}\n                placeholder=\"Explain the correct answer...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Difficulty Level\n              </label>\n              <select\n                value={editForm.difficulty_level}\n                onChange={(e) =>\n                  setEditForm((prev) => ({\n                    ...prev,\n                    difficulty_level: parseInt(e.target.value),\n                  }))\n                }\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500\"\n              >\n                <option value={1}>1 - Very Easy</option>\n                <option value={2}>2 - Easy</option>\n                <option value={3}>3 - Medium</option>\n                <option value={4}>4 - Hard</option>\n                <option value={5}>5 - Very Hard</option>\n              </select>\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <Button onClick={handleSaveEdit} variant=\"primary\">\n                Save Changes\n              </Button>\n              <Button onClick={handleCancelEdit} variant=\"secondary\">\n                Cancel\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* AI Generation Panel */}\n      {isAIMode && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-gray-600\">\n          <h4 className=\"text-md font-medium text-white mb-4\">\n            AI Question Generation\n          </h4>\n\n          <div className=\"space-y-4\">\n            {/* Document Selection */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Select Documents\n              </label>\n              <DocumentSelector\n                selectedDocuments={selectedDocuments}\n                onSelectionChange={setSelectedDocuments}\n                documentPageRanges={documentPageRanges}\n                onPageRangeChange={handlePageRangeChange}\n                maxSelection={5}\n              />\n            </div>\n\n            {/* Generation Controls */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <CountInput\n                label=\"Number of Questions\"\n                value={questionCount}\n                onChange={setQuestionCount}\n                min={1}\n                max={100}\n                placeholder=\"Enter number (1-100)\"\n                disabled={isGenerating}\n              />\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Credit Cost\n                </label>\n                <div className=\"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium\">\n                  {calculateCreditCost()} credits\n                </div>\n              </div>\n            </div>\n\n            {/* Question Types */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Question Types\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n                <Checkbox\n                  label=\"Multiple Choice\"\n                  checked={selectedQuestionTypes.includes(\"multiple_choice\")}\n                  onChange={() => toggleQuestionType(\"multiple_choice\")}\n                  size=\"sm\"\n                />\n                <Checkbox\n                  label=\"Select All\"\n                  checked={selectedQuestionTypes.includes(\"select_all\")}\n                  onChange={() => toggleQuestionType(\"select_all\")}\n                  size=\"sm\"\n                />\n                <Checkbox\n                  label=\"True False\"\n                  checked={selectedQuestionTypes.includes(\"true_false\")}\n                  onChange={() => toggleQuestionType(\"true_false\")}\n                  size=\"sm\"\n                />\n                <Checkbox\n                  label=\"Short Answer\"\n                  checked={selectedQuestionTypes.includes(\"short_answer\")}\n                  onChange={() => toggleQuestionType(\"short_answer\")}\n                  size=\"sm\"\n                />\n              </div>\n            </div>\n\n            {/* Difficulty Level */}\n            <DifficultySelector\n              value={difficultyLevel}\n              onChange={setDifficultyLevel}\n            />\n\n            {/* Content Length */}\n            <ContentLengthSelector\n              value={contentLength}\n              onChange={setContentLength}\n            />\n\n            {/* Custom Prompt */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Custom Instructions (Optional)\n              </label>\n              <textarea\n                value={customPrompt}\n                onChange={(e) => setCustomPrompt(e.target.value)}\n                className=\"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500\"\n                rows={3}\n                placeholder=\"Add specific instructions for question generation...\"\n              />\n            </div>\n\n            {/* Generate Button */}\n            <Button\n              onClick={handleGenerateQuestions}\n              disabled={\n                selectedDocuments.length === 0 ||\n                selectedQuestionTypes.length === 0 ||\n                isGenerating\n              }\n              className=\"w-full\"\n              variant=\"primary\"\n            >\n              {isGenerating\n                ? \"Generating...\"\n                : `Generate ${questionCount} Questions`}\n            </Button>\n          </div>\n        </div>\n      )}\n\n      {/* AI Generation Progress */}\n      <AIGenerationProgress\n        isGenerating={isGenerating}\n        stage={\n          isGenerating ? \"Generating quiz questions with AI...\" : undefined\n        }\n        estimatedTime={Math.ceil(questionCount / 8)} // Rough estimate: 1 second per 8 questions\n      />\n\n      {/* Bulk Actions Toolbar */}\n      <BulkActionsToolbar\n        selectedCount={selectedQuestions.length}\n        totalCount={questions.length}\n        onDeleteSelected={handleBulkDelete}\n        onClearSelection={clearSelection}\n        isLoading={isBulkDeleting}\n        className=\"mb-4\"\n        itemType=\"question\"\n      />\n\n      {/* Question List */}\n      <div className=\"space-y-3\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              checked={isSelectAllChecked}\n              onChange={(checked) => handleSelectAll(checked)}\n            />\n            <h4 className=\"text-md font-medium text-white\">\n              Current Questions ({questions.length})\n            </h4>\n          </div>\n          {questions.length > 0 && (\n            <div className=\"flex items-center space-x-4\">\n              <p className=\"text-sm text-gray-400\">\n                💡 Click on any question to reveal/hide its answer\n              </p>\n              <div className=\"flex items-center space-x-2\">\n                <button\n                  onClick={revealAllQuestions}\n                  className=\"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm\"\n                >\n                  <HiEye className=\"w-4 h-4\" />\n                  <span>Show All</span>\n                </button>\n                <button\n                  onClick={hideAllQuestions}\n                  className=\"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm\"\n                >\n                  <HiEyeOff className=\"w-4 h-4\" />\n                  <span>Hide All</span>\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {questions.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-400\">\n            No questions yet. Add some manually or generate them with AI.\n          </div>\n        ) : (\n          <div className=\"space-y-2\">\n            {questions.map((question) => (\n              <div\n                key={question.id}\n                className=\"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <Checkbox\n                    checked={selectedQuestions.includes(question.id)}\n                    onChange={(checked) =>\n                      handleSelectQuestion(question.id, checked)\n                    }\n                    className=\"mr-3 mt-1\"\n                  />\n\n                  <div className=\"flex-1 min-w-0\">\n                    <div\n                      className=\"cursor-pointer group\"\n                      onClick={() => toggleQuestionReveal(question.id)}\n                      title={\n                        revealedQuestions.has(question.id)\n                          ? \"Click to hide answer\"\n                          : \"Click to reveal answer\"\n                      }\n                    >\n                      <div className=\"mb-2\">\n                        <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                          Question\n                        </span>\n                        <p className=\"text-white font-medium group-hover:text-primary-300 transition-colors\">\n                          {question.question_text}\n                        </p>\n                      </div>\n\n                      <div className=\"mb-2\">\n                        <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                          Type\n                        </span>\n                        <p className=\"text-gray-300 capitalize group-hover:text-gray-200 transition-colors\">\n                          {question.question_type.replace(\"_\", \" \")}\n                        </p>\n                      </div>\n\n                      {revealedQuestions.has(question.id) ? (\n                        <>\n                          {question.question_type === \"true_false\" && (\n                            <div className=\"mb-2\">\n                              <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                                Correct Answer\n                              </span>\n                              <p className=\"text-green-400 text-sm font-medium\">\n                                {question.correct_answers[0]}\n                              </p>\n                            </div>\n                          )}\n\n                          {question.options && (\n                            <div className=\"mb-2\">\n                              <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                                Options\n                              </span>\n                              <ul className=\"text-gray-300 text-sm group-hover:text-gray-200 transition-colors\">\n                                {question.options.map((option, index) => (\n                                  <li\n                                    key={`${question.id}-option-${index}`}\n                                    className={`${\n                                      question.correct_answers.includes(option)\n                                        ? \"text-green-400 font-medium\"\n                                        : \"\"\n                                    }`}\n                                  >\n                                    {index + 1}. {option}\n                                  </li>\n                                ))}\n                              </ul>\n                            </div>\n                          )}\n\n                          {question.explanation && (\n                            <div className=\"mb-2\">\n                              <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                                Explanation\n                              </span>\n                              <p className=\"text-gray-300 text-sm group-hover:text-gray-200 transition-colors\">\n                                {question.explanation}\n                              </p>\n                            </div>\n                          )}\n                        </>\n                      ) : (\n                        <div className=\"mb-2\">\n                          <p className=\"text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors\">\n                            Click to reveal answer details...\n                          </p>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"flex items-center space-x-4 text-xs text-gray-400\">\n                      {question.is_ai_generated && (\n                        <span className=\"bg-primary-500/20 text-primary-400 px-2 py-1 rounded\">\n                          AI Generated\n                        </span>\n                      )}\n                      {question.difficulty_level && (\n                        <span>\n                          Difficulty:{\" \"}\n                          {typeof question.difficulty_level === \"string\"\n                            ? difficultyLevelToString(\n                                question.difficulty_level as DifficultyLevel\n                              )\n                            : difficultyLevelToString(\n                                numberToDifficultyLevel(\n                                  question.difficulty_level\n                                )\n                              )}\n                        </span>\n                      )}\n                      <span>\n                        Attempted: {question.times_attempted || 0} times\n                      </span>\n                      <span>Correct: {question.times_correct || 0} times</span>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    <button\n                      onClick={() => handleEditQuestion(question)}\n                      className=\"text-gray-400 hover:text-primary-400 p-1\"\n                      title=\"Edit question\"\n                    >\n                      ✏️\n                    </button>\n                    <button\n                      onClick={() => handleDeleteQuestion(question)}\n                      className=\"text-gray-400 hover:text-red-400 p-1\"\n                      title=\"Delete question\"\n                    >\n                      🗑️\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default QuizManagement;\n", "import React from 'react';\r\nimport { ShuffleToggleProps } from '../../types/userSettings';\r\n\r\nexport const ShuffleToggle: React.FC<ShuffleToggleProps> = ({\r\n  enabled,\r\n  onChange,\r\n  disabled = false,\r\n  className = '',\r\n  label = 'Shuffle Cards',\r\n  description = 'Randomize the order of flashcards during study sessions'\r\n}) => {\r\n  const handleToggle = () => {\r\n    if (!disabled) {\r\n      onChange(!enabled);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (event: React.KeyboardEvent) => {\r\n    if (event.key === ' ' || event.key === 'Enter') {\r\n      event.preventDefault();\r\n      handleToggle();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`flex items-center justify-between ${className}`}>\r\n      <div className=\"flex-1\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <span className=\"text-sm font-medium text-white\">\r\n            {label}\r\n          </span>\r\n          {description && (\r\n            <span className=\"text-xs text-gray-400\">\r\n              {description}\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n      \r\n      <button\r\n        type=\"button\"\r\n        role=\"switch\"\r\n        aria-checked={enabled}\r\n        aria-label={`${enabled ? 'Disable' : 'Enable'} ${label.toLowerCase()}`}\r\n        onClick={handleToggle}\r\n        onKeyDown={handleKeyDown}\r\n        disabled={disabled}\r\n        className={`\r\n          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out\r\n          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800\r\n          ${disabled \r\n            ? 'opacity-50 cursor-not-allowed bg-gray-600' \r\n            : enabled \r\n              ? 'bg-primary-500 hover:bg-primary-600' \r\n              : 'bg-gray-600 hover:bg-gray-500'\r\n          }\r\n        `}\r\n      >\r\n        <span\r\n          className={`\r\n            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out\r\n            ${enabled ? 'translate-x-6' : 'translate-x-1'}\r\n          `}\r\n        />\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect, useState } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { useStudyStore } from '../stores/studyStore';\r\nimport { Button } from '../components/common/Button';\r\nimport { FlashcardManagement } from '../components/study/FlashcardManagement';\r\nimport { QuizManagement } from '../components/study/QuizManagement';\r\nimport { useDialog } from '../contexts/DialogContext';\r\nimport { useUserSettings } from '../hooks/useUserSettings';\r\nimport { ShuffleToggle } from '../components/ui/ShuffleToggle';\r\nimport { Flashcard, QuizQuestion } from '../../../shared/types';\r\n\r\nexport const StudySetPage: React.FC = () => {\r\n  const { id } = useParams<{ id: string }>();\r\n  const navigate = useNavigate();\r\n  const { studySetContent, isLoading, error, fetchStudySetContent } = useStudyStore();\r\n  const { alert, confirm, prompt } = useDialog();\r\n  const { settings: userSettings, updateSettings } = useUserSettings();\r\n  const [selectedMode, setSelectedMode] = useState<'flashcards' | 'quiz' | null>(null);\r\n  const [activeTab, setActiveTab] = useState<'study' | 'manage'>('study');\r\n  const [managementType, setManagementType] = useState<'flashcards' | 'quiz'>('flashcards');\r\n  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);\r\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\r\n  const [studySetName, setStudySetName] = useState('');\r\n\r\n  useEffect(() => {\r\n    if (id) {\r\n      fetchStudySetContent(id).catch(console.error);\r\n    }\r\n  }, [id, fetchStudySetContent]);\r\n\r\n  useEffect(() => {\r\n    if (studySetContent?.studySet) {\r\n      setStudySetName(studySetContent.studySet.name);\r\n      setFlashcards(studySetContent.flashcards || []);\r\n      setQuestions(studySetContent.questions || []);\r\n    }\r\n  }, [studySetContent]);\r\n\r\n  const handleStartStudy = async () => {\r\n    if (!id || !selectedMode) return;\r\n\r\n    try {\r\n      const shuffleEnabled = userSettings?.shuffle_flashcards || false;\r\n      await useStudyStore.getState().startStudySession(id, selectedMode, shuffleEnabled);\r\n      navigate(`/study/${id}/${selectedMode}`);\r\n    } catch (error: any) {\r\n      await alert({\r\n        title: 'Error',\r\n        message: error.message || 'Failed to start study session',\r\n        variant: 'error'\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleRenameStudySet = async () => {\r\n    if (!id || !studySetContent?.studySet) return;\r\n\r\n    const newName = await prompt({\r\n      title: 'Rename Study Set',\r\n      message: 'Enter a new name for this study set:',\r\n      defaultValue: studySetContent.studySet.name\r\n    });\r\n\r\n    if (newName === null || newName.trim() === studySetContent.studySet.name) return;\r\n\r\n    if (!newName.trim()) {\r\n      await alert({\r\n        title: 'Invalid Name',\r\n        message: 'Study set name cannot be empty.',\r\n        variant: 'error'\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`/api/study-sets/${id}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`\r\n        },\r\n        body: JSON.stringify({ name: newName.trim() })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to rename study set');\r\n      }\r\n\r\n      setStudySetName(newName.trim());\r\n      await alert({\r\n        title: 'Success',\r\n        message: 'Study set renamed successfully!',\r\n        variant: 'success'\r\n      });\r\n\r\n      // Refresh the study set content\r\n      await fetchStudySetContent(id);\r\n    } catch (error: any) {\r\n      await alert({\r\n        title: 'Error',\r\n        message: error.message || 'Failed to rename study set',\r\n        variant: 'error'\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleDeleteStudySet = async () => {\r\n    if (!id || !studySetContent?.studySet) return;\r\n\r\n    const confirmed = await confirm({\r\n      title: 'Delete Study Set',\r\n      message: `Are you sure you want to delete \"${studySetContent.studySet.name}\"?\\n\\nThis action cannot be undone and will delete all flashcards and quiz questions in this set.`,\r\n      variant: 'danger',\r\n      confirmText: 'Delete Study Set',\r\n      cancelText: 'Cancel'\r\n    });\r\n\r\n    if (!confirmed) return;\r\n\r\n    try {\r\n      const response = await fetch(`/api/study-sets/${id}`, {\r\n        method: 'DELETE',\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to delete study set');\r\n      }\r\n\r\n      await alert({\r\n        title: 'Success',\r\n        message: 'Study set deleted successfully!',\r\n        variant: 'success'\r\n      });\r\n\r\n      navigate('/dashboard');\r\n    } catch (error: any) {\r\n      await alert({\r\n        title: 'Error',\r\n        message: error.message || 'Failed to delete study set',\r\n        variant: 'error'\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleFlashcardAdded = (newFlashcard: Flashcard) => {\r\n    setFlashcards(prev => [...prev, newFlashcard]);\r\n  };\r\n\r\n  const handleFlashcardUpdated = (updatedFlashcard: Flashcard) => {\r\n    setFlashcards(prev => prev.map(card =>\r\n      card.id === updatedFlashcard.id ? updatedFlashcard : card\r\n    ));\r\n  };\r\n\r\n  const handleFlashcardDeleted = (flashcardId: string) => {\r\n    setFlashcards(prev => prev.filter(card => card.id !== flashcardId));\r\n  };\r\n\r\n  const handleFlashcardsGenerated = (newFlashcards: Flashcard[]) => {\r\n    setFlashcards(prev => [...prev, ...newFlashcards]);\r\n  };\r\n\r\n  const handleQuestionAdded = (newQuestion: QuizQuestion) => {\r\n    setQuestions(prev => [...prev, newQuestion]);\r\n  };\r\n\r\n  const handleQuestionUpdated = (updatedQuestion: QuizQuestion) => {\r\n    setQuestions(prev => prev.map(question =>\r\n      question.id === updatedQuestion.id ? updatedQuestion : question\r\n    ));\r\n  };\r\n\r\n  const handleQuestionDeleted = (questionId: string) => {\r\n    setQuestions(prev => prev.filter(question => question.id !== questionId));\r\n  };\r\n\r\n  const handleQuestionsGenerated = (newQuestions: QuizQuestion[]) => {\r\n    setQuestions(prev => [...prev, ...newQuestions]);\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"flex items-center justify-center py-12\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500\"></div>\r\n          <span className=\"ml-3 text-gray-400\">Loading study set...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !studySetContent?.studySet) {\r\n    return (\r\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"text-center py-12\">\r\n          <div className=\"text-red-400 mb-4\">\r\n            {error || 'Study set not found'}\r\n          </div>\r\n          <Button onClick={() => navigate('/dashboard')} variant=\"secondary\">\r\n            Back to Study Sets\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const { studySet } = studySetContent;\r\n  const hasFlashcards = flashcards && flashcards.length > 0;\r\n  const hasQuestions = questions && questions.length > 0;\r\n\r\n  return (\r\n    <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n      {/* Header */}\r\n      <div className=\"mb-8\">\r\n        <button\r\n          onClick={() => navigate('/dashboard')}\r\n          className=\"text-gray-400 hover:text-white mb-4 flex items-center\"\r\n        >\r\n          ← Back to Study Sets\r\n        </button>\r\n\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <h1 className=\"text-3xl font-bold text-white\">{studySetName}</h1>\r\n\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Button\r\n              onClick={handleRenameStudySet}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n            >\r\n              ✏️ Rename\r\n            </Button>\r\n            <Button\r\n              onClick={handleDeleteStudySet}\r\n              variant=\"danger\"\r\n              size=\"sm\"\r\n            >\r\n              🗑️ Delete\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\r\n          <span className=\"capitalize\">{studySet.type}</span>\r\n          {studySet.is_ai_generated && (\r\n            <span className=\"bg-primary-500/20 text-primary-400 px-2 py-1 rounded\">\r\n              AI Generated\r\n            </span>\r\n          )}\r\n          <span>\r\n            Created {new Date(studySet.created_at).toLocaleDateString()}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tab Navigation */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"border-b border-gray-600\">\r\n          <nav className=\"-mb-px flex space-x-8\">\r\n            <button\r\n              onClick={() => setActiveTab('study')}\r\n              className={`\r\n                py-2 px-1 border-b-2 font-medium text-sm transition-colors\r\n                ${activeTab === 'study'\r\n                  ? 'border-primary-500 text-primary-400'\r\n                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'\r\n                }\r\n              `}\r\n            >\r\n              📚 Study Mode\r\n            </button>\r\n            <button\r\n              onClick={() => setActiveTab('manage')}\r\n              className={`\r\n                py-2 px-1 border-b-2 font-medium text-sm transition-colors\r\n                ${activeTab === 'manage'\r\n                  ? 'border-primary-500 text-primary-400'\r\n                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'\r\n                }\r\n              `}\r\n            >\r\n              ⚙️ Manage Content\r\n            </button>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      {activeTab === 'study' && (\r\n        <>\r\n          {/* Study Mode Selection */}\r\n          <div className=\"bg-background-secondary rounded-lg p-6 mb-6\">\r\n            <h2 className=\"text-xl font-semibold text-white mb-4\">Choose Study Mode</h2>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\r\n              {/* Flashcard Mode */}\r\n              {hasFlashcards && (\r\n                <div\r\n                  className={`\r\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\r\n                    ${selectedMode === 'flashcards'\r\n                      ? 'border-primary-500 bg-primary-500/10'\r\n                      : 'border-gray-600 hover:border-gray-500'\r\n                    }\r\n                  `}\r\n                  onClick={() => setSelectedMode('flashcards')}\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"text-2xl\">🃏</div>\r\n                    <div>\r\n                      <h3 className=\"font-medium text-white\">Flashcard Review</h3>\r\n                      <p className=\"text-sm text-gray-400\">\r\n                        {flashcards?.length} flashcards • Interactive review\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Quiz Mode */}\r\n              {hasQuestions && (\r\n                <div\r\n                  className={`\r\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\r\n                    ${selectedMode === 'quiz'\r\n                      ? 'border-primary-500 bg-primary-500/10'\r\n                      : 'border-gray-600 hover:border-gray-500'\r\n                    }\r\n                  `}\r\n                  onClick={() => setSelectedMode('quiz')}\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"text-2xl\">📝</div>\r\n                    <div>\r\n                      <h3 className=\"font-medium text-white\">Quiz Practice</h3>\r\n                      <p className=\"text-sm text-gray-400\">\r\n                        {questions?.length} questions • Test your knowledge\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Shuffle Toggle */}\r\n            {selectedMode && userSettings && (\r\n              <div className=\"mb-6 p-4 bg-background-tertiary rounded-lg border border-gray-600\">\r\n                <ShuffleToggle\r\n                  enabled={userSettings.shuffle_flashcards}\r\n                  onChange={async (enabled) => {\r\n                    try {\r\n                      await updateSettings({ shuffle_flashcards: enabled });\r\n                    } catch (error) {\r\n                      console.error('Failed to update shuffle setting:', error);\r\n                    }\r\n                  }}\r\n                  label=\"Shuffle Cards\"\r\n                  description=\"Randomize the order of flashcards during study sessions\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* Start Button */}\r\n            <Button\r\n              onClick={handleStartStudy}\r\n              disabled={!selectedMode}\r\n              className=\"w-full\"\r\n              size=\"lg\"\r\n            >\r\n              {selectedMode\r\n                ? `Start ${selectedMode === 'flashcards' ? 'Flashcard Review' : 'Quiz Practice'}`\r\n                : 'Select a study mode'\r\n              }\r\n            </Button>\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      {activeTab === 'manage' && id && (\r\n        <div className=\"bg-background-secondary rounded-lg p-6 mb-6\">\r\n          {/* Management Type Tabs */}\r\n          <div className=\"flex space-x-1 mb-6\">\r\n            <button\r\n              onClick={() => setManagementType('flashcards')}\r\n              className={`\r\n                py-2 px-4 rounded-lg font-medium text-sm transition-colors\r\n                ${managementType === 'flashcards'\r\n                  ? 'bg-primary-500 text-white'\r\n                  : 'bg-background-primary text-gray-400 hover:text-gray-300'\r\n                }\r\n              `}\r\n            >\r\n              📚 Flashcards ({flashcards.length})\r\n            </button>\r\n            <button\r\n              onClick={() => setManagementType('quiz')}\r\n              className={`\r\n                py-2 px-4 rounded-lg font-medium text-sm transition-colors\r\n                ${managementType === 'quiz'\r\n                  ? 'bg-primary-500 text-white'\r\n                  : 'bg-background-primary text-gray-400 hover:text-gray-300'\r\n                }\r\n              `}\r\n            >\r\n              ❓ Quiz Questions ({questions.length})\r\n            </button>\r\n          </div>\r\n\r\n          {/* Management Content */}\r\n          {managementType === 'flashcards' && (\r\n            <FlashcardManagement\r\n              studySetId={id}\r\n              flashcards={flashcards}\r\n              onFlashcardAdded={handleFlashcardAdded}\r\n              onFlashcardUpdated={handleFlashcardUpdated}\r\n              onFlashcardDeleted={handleFlashcardDeleted}\r\n              onFlashcardsGenerated={handleFlashcardsGenerated}\r\n            />\r\n          )}\r\n\r\n          {managementType === 'quiz' && (\r\n            <QuizManagement\r\n              studySetId={id}\r\n              questions={questions}\r\n              onQuestionAdded={handleQuestionAdded}\r\n              onQuestionUpdated={handleQuestionUpdated}\r\n              onQuestionDeleted={handleQuestionDeleted}\r\n              onQuestionsGenerated={handleQuestionsGenerated}\r\n            />\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Study Set Info */}\r\n      <div className=\"bg-background-secondary rounded-lg p-6\">\r\n        <h3 className=\"text-lg font-medium text-white mb-4\">Study Set Details</h3>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div>\r\n            <h4 className=\"text-sm font-medium text-gray-300 mb-2\">Content</h4>\r\n            <div className=\"space-y-1 text-sm text-gray-400\">\r\n              {hasFlashcards && (\r\n                <div>{flashcards.length} flashcards</div>\r\n              )}\r\n              {hasQuestions && (\r\n                <div>{questions?.length} quiz questions</div>\r\n              )}\r\n              {!hasFlashcards && !hasQuestions && (\r\n                <div className=\"text-gray-500\">No content yet</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {studySet.source_documents && studySet.source_documents.length > 0 && (\r\n            <div>\r\n              <h4 className=\"text-sm font-medium text-gray-300 mb-2\">Source Documents</h4>\r\n              <div className=\"space-y-1 text-sm text-gray-400\">\r\n                {studySet.source_documents.map((doc, index) => (\r\n                  <div key={index}>{doc.filename}</div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {studySet.custom_prompt && (\r\n            <div className=\"md:col-span-2\">\r\n              <h4 className=\"text-sm font-medium text-gray-300 mb-2\">Custom Instructions</h4>\r\n              <p className=\"text-sm text-gray-400\">{studySet.custom_prompt}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "// ----------------------------------------\n// User & Authentication Types\n// ----------------------------------------\n\nexport interface UserProfile {\n  id: string; // Corresponds to Supabase auth.uid()\n  email: string;\n  name?: string;\n  subscription_tier: \"Free\" | \"Study Starter\" | \"Study Pro\" | \"Study Master\" | \"Study Elite\";\n  credits_remaining: number;\n  is_active: boolean;\n  last_login?: string;\n  last_credit_reset?: string; // ISO 8601 date string for freemium monthly resets\n  subscription_expires_at?: string; // ISO 8601 date string\n  stripe_customer_id?: string;\n  stripe_subscription_id?: string;\n  created_at: string; // ISO 8601 date string\n  updated_at: string; // ISO 8601 date string\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: UserProfile;\n  token?: string;\n  error?: string;\n}\n\n// ----------------------------------------\n// Subscription & Pricing Types\n// ----------------------------------------\n\nexport type SubscriptionTier = \"Free\" | \"Study Starter\" | \"Study Pro\" | \"Study Master\" | \"Study Elite\";\n\nexport interface PricingTier {\n  id: SubscriptionTier;\n  name: string;\n  price: number; // Monthly price in USD\n  credits: number; // Credits included per month\n  features: string[];\n  isPopular?: boolean;\n  stripePriceId?: string; // Stripe Price ID for subscriptions\n}\n\nexport interface SubscriptionStatus {\n  tier: SubscriptionTier;\n  status: \"active\" | \"canceled\" | \"past_due\" | \"incomplete\" | \"trialing\";\n  current_period_start: string;\n  current_period_end: string;\n  cancel_at_period_end: boolean;\n  credits_remaining: number;\n  credits_reset_date: string;\n}\n\nexport interface PaymentIntent {\n  id: string;\n  amount: number;\n  currency: string;\n  status: string;\n  client_secret: string;\n}\n\nexport interface CreditPurchase {\n  id: string;\n  user_id: string;\n  credits_purchased: number;\n  amount_paid: number;\n  stripe_payment_intent_id: string;\n  created_at: string;\n}\n\n// ----------------------------------------\n// Document Management Types\n// ----------------------------------------\n\nexport type DocumentFileType = \"pdf\" | \"docx\" | \"txt\" | \"pptx\";\n\nexport interface DocumentMetadata {\n  id: string;\n  user_id: string;\n  filename: string;\n  file_type: DocumentFileType;\n  file_size: number; // in bytes\n  supabase_storage_path: string;\n  uploaded_at: string; // ISO 8601 date string\n  is_processed: boolean;\n  processing_error?: string;\n  page_count?: number; // Total number of pages (for PDFs, PPTx, etc.)\n}\n\nexport interface DocumentWithContent extends DocumentMetadata {\n  content_text: string;\n}\n\n// ----------------------------------------\n// Study Sets & Content Types\n// ----------------------------------------\n\nexport type StudySetType = \"flashcards\" | \"quiz\";\n\n// ----------------------------------------\n// Difficulty and Content Length Enums\n// ----------------------------------------\n\nexport enum DifficultyLevel {\n  EASY = \"easy\",\n  MEDIUM = \"medium\",\n  HARD = \"hard\",\n  COLLEGE = \"college\",\n  GRADUATE = \"graduate\",\n  PHD = \"phd\",\n}\n\nexport enum ContentLength {\n  SHORT = \"short\",\n  MEDIUM = \"medium\",\n  LONG = \"long\",\n}\n\n// Type aliases for database compatibility\nexport type DifficultyLevelType = keyof typeof DifficultyLevel;\nexport type ContentLengthType = keyof typeof ContentLength;\n\nexport interface StudySet {\n  id: string;\n  user_id: string;\n  name: string;\n  type: StudySetType;\n  is_ai_generated: boolean;\n  source_documents?: { id: string; filename: string }[]; // Array of document IDs and names\n  custom_prompt?: string;\n  created_at: string; // ISO 8601 date string\n  updated_at: string; // ISO 8601 date string\n  flashcard_count?: number;\n  quiz_question_count?: number;\n  last_studied_at?: string; // ISO 8601 date string\n}\n\nexport interface Flashcard {\n  id: string;\n  study_set_id: string;\n  front: string;\n  back: string;\n  is_flagged: boolean;\n  is_ai_generated: boolean;\n  difficulty_level?: DifficultyLevel;\n  content_length?: ContentLength;\n  times_reviewed: number;\n  last_reviewed_at?: string; // ISO 8601 date string\n}\n\nexport type QuestionType =\n  | \"multiple_choice\"\n  | \"select_all\"\n  | \"true_false\"\n  | \"short_answer\";\n\nexport interface QuizQuestion {\n  id: string;\n  study_set_id: string;\n  question_text: string;\n  question_type: QuestionType;\n  options?: string[]; // For multiple_choice, select_all\n  correct_answers: string[]; // Can be ['True'] or ['False'] for true_false\n  explanation?: string;\n  is_ai_generated: boolean;\n  difficulty_level?: DifficultyLevel;\n  content_length?: ContentLength;\n  times_attempted: number;\n  times_correct: number;\n}\n\n// ----------------------------------------\n// Credit System Types\n// ----------------------------------------\n\nexport interface CreditTransaction {\n  id: string;\n  user_id: string;\n  credits_used: number; // Negative for additions\n  operation_type: string;\n  description: string;\n  metadata?: Record<string, any>;\n  study_set_id?: string;\n  created_at: string; // ISO 8601 date string\n}\n\nexport interface AIOperationCost {\n  operation_type: string;\n  credits_required: number;\n  operations_per_credit: number;\n  is_active: boolean;\n}\n\n// ----------------------------------------\n// API Response Types\n// ----------------------------------------\n\nexport interface APIResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> extends APIResponse<T[]> {\n  total: number;\n  page: number;\n  limit: number;\n  hasMore: boolean;\n}\n\n// ----------------------------------------\n// Component Props Types\n// ----------------------------------------\n\nexport interface BaseComponentProps {\n  className?: string;\n  \"data-testid\"?: string;\n}\n\n// Button component props\nexport interface ButtonProps extends BaseComponentProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  variant?: \"primary\" | \"secondary\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  isLoading?: boolean;\n  disabled?: boolean;\n  type?: \"button\" | \"submit\" | \"reset\";\n}\n\n// Input component props\nexport interface InputProps extends BaseComponentProps {\n  label?: string;\n  placeholder?: string;\n  value: string;\n  onChange: (value: string) => void;\n  type?: \"text\" | \"email\" | \"password\" | \"number\";\n  error?: string;\n  required?: boolean;\n  disabled?: boolean;\n}\n\n// Modal component props\nexport interface ModalProps extends BaseComponentProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  children: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n}\n\n// ----------------------------------------\n// Study Session Types\n// ----------------------------------------\n\nexport interface StudySession {\n  id: string;\n  user_id: string;\n  study_set_id: string;\n  session_type: StudySetType;\n  started_at: string;\n  ended_at?: string;\n  total_items: number;\n  completed_items: number;\n  correct_answers?: number; // For quiz sessions\n  session_data?: Record<string, any>; // Store session-specific data\n}\n\nexport interface StudyProgress {\n  study_set_id: string;\n  total_flashcards?: number;\n  reviewed_flashcards?: number;\n  flagged_flashcards?: number;\n  total_quiz_questions?: number;\n  answered_questions?: number;\n  correct_answers?: number;\n  last_studied_at?: string;\n  study_streak?: number;\n}\n\n// ----------------------------------------\n// AI Generation Types\n// ----------------------------------------\n\nexport interface AIGenerationRequest {\n  documents: string[]; // Document IDs\n  documentPageRanges?: {\n    [documentId: string]: { startPage: number; endPage: number };\n  }; // Page ranges for documents\n  studySetType: StudySetType;\n  customPrompt?: string;\n  itemCount?: number;\n  difficultyLevel?: DifficultyLevel;\n  contentLength?: ContentLength;\n}\n\nexport interface AIGenerationResult {\n  studySetId: string;\n  itemsGenerated: number;\n  creditsUsed: number;\n  processingTime: number;\n}\n\n// Helper functions for enum conversions\nexport const difficultyLevelToString = (level: DifficultyLevel): string => {\n  const labels: Record<DifficultyLevel, string> = {\n    [DifficultyLevel.EASY]: \"Easy\",\n    [DifficultyLevel.MEDIUM]: \"Medium\",\n    [DifficultyLevel.HARD]: \"Hard\",\n    [DifficultyLevel.COLLEGE]: \"College\",\n    [DifficultyLevel.GRADUATE]: \"Graduate\",\n    [DifficultyLevel.PHD]: \"PhD\",\n  };\n  return labels[level];\n};\n\nexport const contentLengthToString = (length: ContentLength): string => {\n  const labels: Record<ContentLength, string> = {\n    [ContentLength.SHORT]: \"Short\",\n    [ContentLength.MEDIUM]: \"Medium\",\n    [ContentLength.LONG]: \"Long\",\n  };\n  return labels[length];\n};\n\nexport const stringToDifficultyLevel = (str: string): DifficultyLevel => {\n  const normalized = str.toLowerCase();\n  switch (normalized) {\n    case \"easy\":\n      return DifficultyLevel.EASY;\n    case \"medium\":\n      return DifficultyLevel.MEDIUM;\n    case \"hard\":\n      return DifficultyLevel.HARD;\n    case \"college\":\n      return DifficultyLevel.COLLEGE;\n    case \"graduate\":\n      return DifficultyLevel.GRADUATE;\n    case \"phd\":\n      return DifficultyLevel.PHD;\n    default:\n      return DifficultyLevel.MEDIUM;\n  }\n};\n\nexport const stringToContentLength = (str: string): ContentLength => {\n  const normalized = str.toLowerCase();\n  switch (normalized) {\n    case \"short\":\n      return ContentLength.SHORT;\n    case \"medium\":\n      return ContentLength.MEDIUM;\n    case \"long\":\n      return ContentLength.LONG;\n    default:\n      return ContentLength.MEDIUM;\n  }\n};\n\n// Helper functions for number conversions (for backward compatibility)\nexport const difficultyLevelToNumber = (level: DifficultyLevel): number => {\n  const mapping: Record<DifficultyLevel, number> = {\n    [DifficultyLevel.EASY]: 1,\n    [DifficultyLevel.MEDIUM]: 3,\n    [DifficultyLevel.HARD]: 4,\n    [DifficultyLevel.COLLEGE]: 5,\n    [DifficultyLevel.GRADUATE]: 6,\n    [DifficultyLevel.PHD]: 7,\n  };\n  return mapping[level];\n};\n\nexport const numberToDifficultyLevel = (num: number): DifficultyLevel => {\n  switch (num) {\n    case 1:\n      return DifficultyLevel.EASY;\n    case 2:\n      return DifficultyLevel.EASY; // Map 2 to easy for backward compatibility\n    case 3:\n      return DifficultyLevel.MEDIUM;\n    case 4:\n      return DifficultyLevel.HARD;\n    case 5:\n      return DifficultyLevel.COLLEGE;\n    case 6:\n      return DifficultyLevel.GRADUATE;\n    case 7:\n      return DifficultyLevel.PHD;\n    default:\n      return DifficultyLevel.MEDIUM;\n  }\n};\n\n// ----------------------------------------\n// Utility Types\n// ----------------------------------------\n\nexport type LoadingState = \"idle\" | \"loading\" | \"success\" | \"error\";\n\nexport interface ErrorState {\n  message: string;\n  code?: string;\n  details?: Record<string, any>;\n}\n\nexport type SortDirection = \"asc\" | \"desc\";\n\nexport interface SortConfig {\n  field: string;\n  direction: SortDirection;\n}\n\nexport interface FilterConfig {\n  field: string;\n  value: any;\n  operator?: \"eq\" | \"ne\" | \"gt\" | \"lt\" | \"gte\" | \"lte\" | \"in\" | \"like\";\n}\n", "import { create } from \"zustand\";\nimport { StudySet, Flashcard, QuizQuestion } from \"../shared/types\";\n\n// Utility function for shuffling arrays using Fisher-Yates algorithm\nconst shuffleArray = <T>(array: T[]): T[] => {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n};\n\ninterface StudyAction {\n  type:\n    | \"NEXT_ITEM\"\n    | \"PREVIOUS_ITEM\"\n    | \"TOGGLE_FLAG\"\n    | \"MARK_REVIEWED\"\n    | \"SUBMIT_ANSWER\";\n  payload: any;\n  previousState: Partial<StudySession>;\n  timestamp: number;\n}\n\ninterface StudySession {\n  studySetId: string;\n  type: \"flashcards\" | \"quiz\";\n  startTime: Date;\n  currentIndex: number;\n  totalItems: number;\n  reviewedItems: number[];\n  flaggedItems: string[];\n  correctAnswers?: number; // For quizzes\n  timeSpent: number; // in seconds\n  isShuffled: boolean; // Whether the session uses shuffled order\n  originalOrder?: number[]; // Original indices for unshuffling if needed\n}\n\ninterface StudySessionData {\n  id: string;\n  studySetId: string;\n  type: \"flashcards\" | \"quiz\";\n  startTime: Date;\n  endTime?: Date;\n  totalItems: number;\n  reviewedItems: number;\n  flaggedItems: number;\n  correctAnswers?: number;\n  timeSpent: number;\n}\n\ninterface StudyState {\n  currentSession: StudySession | null;\n  studySetContent: {\n    studySet?: StudySet;\n    flashcards?: Flashcard[];\n    questions?: QuizQuestion[];\n  } | null;\n  studySets: StudySet[];\n  sessions: StudySessionData[];\n  isLoading: boolean;\n  error: string | null;\n\n  // Undo/Redo functionality\n  actionHistory: StudyAction[];\n  currentActionIndex: number;\n  canUndo: boolean;\n  canRedo: boolean;\n\n  // Actions\n  startStudySession: (\n    studySetId: string,\n    type: \"flashcards\" | \"quiz\",\n    shuffle?: boolean\n  ) => Promise<void>;\n  endStudySession: () => void;\n  nextItem: () => void;\n  previousItem: () => void;\n  goToItem: (index: number) => void;\n  toggleFlag: (itemId: string) => void;\n  markReviewed: (itemId: string) => void;\n  submitQuizAnswer: (\n    questionId: string,\n    answer: string[],\n    isCorrect: boolean\n  ) => void;\n  updateTimeSpent: (seconds: number) => void;\n  fetchStudySetContent: (\n    studySetId: string,\n    forceRefresh?: boolean\n  ) => Promise<void>;\n  fetchStudySets: () => Promise<void>;\n  fetchStudySessions: (\n    timeRange?: \"7d\" | \"30d\" | \"90d\" | \"all\"\n  ) => Promise<void>;\n\n  // Cache management methods\n  invalidateStudySetContent: (studySetId?: string) => void;\n  refreshStudySetContent: (studySetId: string) => Promise<void>;\n  invalidateStudySets: () => void;\n\n  // Undo/Redo actions\n  undo: () => void;\n  redo: () => void;\n  clearHistory: () => void;\n  addToHistory: (action: StudyAction) => void;\n}\n\nexport const useStudyStore = create<StudyState>((set, get) => ({\n  currentSession: null,\n  studySetContent: null,\n  studySets: [],\n  sessions: [],\n  isLoading: false,\n  error: null,\n\n  // Undo/Redo state\n  actionHistory: [],\n  currentActionIndex: -1,\n  canUndo: false,\n  canRedo: false,\n\n  fetchStudySetContent: async (studySetId: string, forceRefresh = false) => {\n    // If forceRefresh is true or content doesn't exist, refetch\n    const { studySetContent } = get();\n    if (\n      forceRefresh ||\n      !studySetContent ||\n      studySetContent.studySet?.id !== studySetId\n    ) {\n      set({ isLoading: true, error: null });\n\n      try {\n        const token = localStorage.getItem(\"auth_token\");\n\n        const response = await fetch(`/api/study-sets/${studySetId}/content`, {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (!response.ok) {\n          const errorResult = await response.json();\n          throw new Error(\n            errorResult.error || \"Failed to fetch study set content\"\n          );\n        }\n\n        const result = await response.json();\n\n        if (result.success) {\n          set({\n            studySetContent: {\n              studySet: result.data.studySet,\n              flashcards: result.data.flashcards || [],\n              questions: result.data.questions || [],\n            },\n            isLoading: false,\n          });\n        } else {\n          throw new Error(result.error);\n        }\n      } catch (error: any) {\n        set({\n          error: error.message || \"Failed to fetch study set content\",\n          isLoading: false,\n        });\n        throw error;\n      }\n    }\n  },\n\n  startStudySession: async (\n    studySetId: string,\n    type: \"flashcards\" | \"quiz\",\n    shuffle = false\n  ) => {\n    const { studySetContent, fetchStudySetContent } = get();\n\n    // Fetch content if not already loaded\n    if (!studySetContent || studySetContent.studySet?.id !== studySetId) {\n      await fetchStudySetContent(studySetId);\n    }\n\n    const content = get().studySetContent;\n    if (!content) {\n      throw new Error(\"Failed to load study set content\");\n    }\n\n    const totalItems =\n      type === \"flashcards\"\n        ? content.flashcards?.length || 0\n        : content.questions?.length || 0;\n\n    if (totalItems === 0) {\n      throw new Error(\"No study materials found in this set\");\n    }\n\n    // Handle shuffling\n    let originalOrder: number[] | undefined;\n    if (shuffle) {\n      // Create array of original indices\n      originalOrder = Array.from({ length: totalItems }, (_, i) => i);\n\n      // Shuffle the content arrays\n      if (type === \"flashcards\" && content.flashcards) {\n        const shuffledFlashcards = shuffleArray(content.flashcards);\n        set((state) => ({\n          studySetContent: {\n            ...state.studySetContent!,\n            flashcards: shuffledFlashcards,\n          },\n        }));\n      } else if (type === \"quiz\" && content.questions) {\n        const shuffledQuestions = shuffleArray(content.questions);\n        set((state) => ({\n          studySetContent: {\n            ...state.studySetContent!,\n            questions: shuffledQuestions,\n          },\n        }));\n      }\n    }\n\n    set({\n      currentSession: {\n        studySetId,\n        type,\n        startTime: new Date(),\n        currentIndex: 0,\n        totalItems,\n        reviewedItems: [],\n        flaggedItems: [],\n        correctAnswers: type === \"quiz\" ? 0 : undefined,\n        timeSpent: 0,\n        isShuffled: shuffle,\n        originalOrder,\n      },\n    });\n  },\n\n  endStudySession: () => {\n    set({ currentSession: null });\n  },\n\n  nextItem: () => {\n    const { currentSession, addToHistory } = get();\n    if (!currentSession) return;\n\n    // Implement circular navigation: if at last item, wrap to first item\n    const nextIndex =\n      currentSession.currentIndex === currentSession.totalItems - 1\n        ? 0\n        : currentSession.currentIndex + 1;\n\n    // Record action in history\n    addToHistory({\n      type: \"NEXT_ITEM\",\n      payload: { fromIndex: currentSession.currentIndex, toIndex: nextIndex },\n      previousState: { currentIndex: currentSession.currentIndex },\n      timestamp: Date.now(),\n    });\n\n    set({\n      currentSession: {\n        ...currentSession,\n        currentIndex: nextIndex,\n      },\n    });\n  },\n\n  previousItem: () => {\n    const { currentSession, addToHistory } = get();\n    if (!currentSession) return;\n\n    // Implement circular navigation: if at first item, wrap to last item\n    const prevIndex =\n      currentSession.currentIndex === 0\n        ? currentSession.totalItems - 1\n        : currentSession.currentIndex - 1;\n\n    // Record action in history\n    addToHistory({\n      type: \"PREVIOUS_ITEM\",\n      payload: { fromIndex: currentSession.currentIndex, toIndex: prevIndex },\n      previousState: { currentIndex: currentSession.currentIndex },\n      timestamp: Date.now(),\n    });\n\n    set({\n      currentSession: {\n        ...currentSession,\n        currentIndex: prevIndex,\n      },\n    });\n  },\n\n  goToItem: (index: number) => {\n    const { currentSession } = get();\n    if (!currentSession) return;\n\n    const clampedIndex = Math.max(\n      0,\n      Math.min(index, currentSession.totalItems - 1)\n    );\n    set({\n      currentSession: {\n        ...currentSession,\n        currentIndex: clampedIndex,\n      },\n    });\n  },\n\n  toggleFlag: (itemId: string) => {\n    const { currentSession, addToHistory } = get();\n    if (!currentSession) return;\n\n    const wasFlagged = currentSession.flaggedItems.includes(itemId);\n    const flaggedItems = wasFlagged\n      ? currentSession.flaggedItems.filter((id) => id !== itemId)\n      : [...currentSession.flaggedItems, itemId];\n\n    // Record action in history\n    addToHistory({\n      type: \"TOGGLE_FLAG\",\n      payload: { itemId, wasFlagged },\n      previousState: { flaggedItems: currentSession.flaggedItems },\n      timestamp: Date.now(),\n    });\n\n    set({\n      currentSession: {\n        ...currentSession,\n        flaggedItems,\n      },\n    });\n  },\n\n  markReviewed: (_itemId: string) => {\n    const { currentSession } = get();\n    if (!currentSession) return;\n\n    if (!currentSession.reviewedItems.includes(currentSession.currentIndex)) {\n      set({\n        currentSession: {\n          ...currentSession,\n          reviewedItems: [\n            ...currentSession.reviewedItems,\n            currentSession.currentIndex,\n          ],\n        },\n      });\n    }\n  },\n\n  submitQuizAnswer: (\n    questionId: string,\n    _answer: string[],\n    isCorrect: boolean\n  ) => {\n    const { currentSession, markReviewed } = get();\n    if (!currentSession || currentSession.type !== \"quiz\") return;\n\n    markReviewed(questionId);\n\n    if (isCorrect) {\n      set({\n        currentSession: {\n          ...currentSession,\n          correctAnswers: (currentSession.correctAnswers || 0) + 1,\n        },\n      });\n    }\n  },\n\n  updateTimeSpent: (seconds: number) => {\n    const { currentSession } = get();\n    if (!currentSession) return;\n\n    set({\n      currentSession: {\n        ...currentSession,\n        timeSpent: currentSession.timeSpent + seconds,\n      },\n    });\n  },\n\n  // Helper function to add action to history\n  addToHistory: (action: StudyAction) => {\n    const { actionHistory, currentActionIndex } = get();\n\n    // Remove any actions after current index (when undoing then doing new action)\n    const newHistory = actionHistory.slice(0, currentActionIndex + 1);\n    newHistory.push(action);\n\n    // Limit history to last 50 actions for performance\n    const limitedHistory = newHistory.slice(-50);\n\n    set({\n      actionHistory: limitedHistory,\n      currentActionIndex: limitedHistory.length - 1,\n      canUndo: limitedHistory.length > 0,\n      canRedo: false,\n    });\n  },\n\n  undo: () => {\n    const { actionHistory, currentActionIndex, currentSession } = get();\n\n    if (currentActionIndex < 0 || !currentSession) return;\n\n    const action = actionHistory[currentActionIndex];\n\n    // Restore previous state\n    set({\n      currentSession: {\n        ...currentSession,\n        ...action.previousState,\n      },\n      currentActionIndex: currentActionIndex - 1,\n      canUndo: currentActionIndex > 0,\n      canRedo: true,\n    });\n  },\n\n  redo: () => {\n    const { actionHistory, currentActionIndex, currentSession } = get();\n\n    if (currentActionIndex >= actionHistory.length - 1 || !currentSession)\n      return;\n\n    const nextActionIndex = currentActionIndex + 1;\n    const action = actionHistory[nextActionIndex];\n\n    // Re-apply the action\n    switch (action.type) {\n      case \"NEXT_ITEM\":\n        get().nextItem();\n        break;\n      case \"PREVIOUS_ITEM\":\n        get().previousItem();\n        break;\n      case \"TOGGLE_FLAG\":\n        get().toggleFlag(action.payload.itemId);\n        break;\n      case \"MARK_REVIEWED\":\n        get().markReviewed(action.payload.itemId);\n        break;\n    }\n\n    set({\n      currentActionIndex: nextActionIndex,\n      canUndo: true,\n      canRedo: nextActionIndex < actionHistory.length - 1,\n    });\n  },\n\n  clearHistory: () => {\n    set({\n      actionHistory: [],\n      currentActionIndex: -1,\n      canUndo: false,\n      canRedo: false,\n    });\n  },\n\n  fetchStudySets: async () => {\n    set({ isLoading: true, error: null });\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n\n      const response = await fetch(\"/api/study-sets\", {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Failed to fetch study sets\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        set({\n          studySets: result.data,\n          isLoading: false,\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error: any) {\n      set({\n        error: error.message || \"Failed to fetch study sets\",\n        isLoading: false,\n      });\n      throw error;\n    }\n  },\n\n  fetchStudySessions: async (timeRange = \"30d\") => {\n    set({ isLoading: true, error: null });\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n\n      const response = await fetch(\n        `/api/study-sessions?timeRange=${timeRange}`,\n        {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        const errorResult = await response.json();\n        throw new Error(errorResult.error || \"Failed to fetch study sessions\");\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        // Convert date strings to Date objects\n        const sessions = result.data.map((session: any) => ({\n          ...session,\n          startTime: new Date(session.startTime),\n          endTime: session.endTime ? new Date(session.endTime) : undefined,\n        }));\n\n        set({\n          sessions,\n          isLoading: false,\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error: any) {\n      set({\n        error: error.message || \"Failed to fetch study sessions\",\n        isLoading: false,\n      });\n      throw error;\n    }\n  },\n\n  // Cache management methods\n  invalidateStudySetContent: (studySetId?: string) => {\n    const { studySetContent } = get();\n\n    if (studySetId) {\n      // Clear cache for specific study set only if it matches\n      if (studySetContent?.studySet?.id === studySetId) {\n        set({ studySetContent: null });\n      }\n    } else {\n      // Clear all cached study set content\n      set({ studySetContent: null });\n    }\n  },\n\n  refreshStudySetContent: async (studySetId: string) => {\n    // Force a fresh fetch of study set content\n    await get().fetchStudySetContent(studySetId, true);\n  },\n\n  invalidateStudySets: () => {\n    // Clear the study sets list to force refetch\n    set({ studySets: [] });\n  },\n}));\n"], "names": ["difficultyOptions", "value", "DifficultyLevel", "EASY", "label", "description", "MEDIUM", "HARD", "COLLEGE", "GRADUATE", "PHD", "DifficultySelector", "_ref", "onChange", "className", "disabled", "_jsxs", "concat", "children", "_jsx", "map", "option", "isSelected", "colorClasses", "difficulty", "getSelectedDifficultyColor", "getDifficultyColor", "type", "onClick", "title", "fill", "viewBox", "fillRule", "d", "clipRule", "useDocumentStore", "create", "set", "documents", "selectedDocuments", "Set", "isLoading", "uploadProgress", "fetchDocuments", "async", "token", "localStorage", "getItem", "response", "fetch", "headers", "Authorization", "ok", "Error", "result", "json", "success", "error", "data", "console", "uploadDocument", "formData", "FormData", "append", "file", "method", "body", "errorResult", "state", "_objectSpread", "name", "deleteDocument", "id", "filter", "doc", "docId", "searchDocuments", "encodeURIComponent", "query", "getDocument", "toggleDocumentSelection", "newSelection", "has", "delete", "add", "clearSelection", "selectAll", "setUploadProgress", "fileName", "progress", "useUserSettings", "settings", "setSettings", "useState", "loading", "setLoading", "setError", "fetchSettings", "useCallback", "sessionStorage", "errorData", "err", "message", "updateSettings", "JSON", "stringify", "updates", "refetch", "useEffect", "CountInput", "min", "max", "placeholder", "inputValue", "setInputValue", "toString", "localError", "setLocalError", "displayError", "inputMode", "e", "newValue", "target", "test", "inputStr", "trim", "numValue", "parseInt", "validateAndUpdate", "onBlur", "handleBlur", "onKeyDown", "includes", "key", "ctrl<PERSON>ey", "toLowerCase", "preventDefault", "incrementValue", "Math", "tabIndex", "decrementValue", "ProgressBar", "isIndeterminate", "size", "variant", "sizeClasses", "sm", "md", "lg", "round", "primary", "warning", "style", "undefined", "width", "AIGenerationProgress", "_ref2", "isGenerating", "stage", "estimatedTime", "setProgress", "React", "timeElapsed", "setTimeElapsed", "interval", "progressInterval", "setInterval", "prev", "increment", "clearInterval", "setTimeout", "formatTime", "seconds", "minutes", "floor", "remainingSeconds", "PageInput", "isNaN", "handleChange", "DocumentSelector", "onSelectionChange", "documentPageRanges", "onPageRangeChange", "maxSelection", "searchQuery", "setSearch<PERSON>uery", "length", "filteredDocuments", "is_processed", "filename", "handleDocumentToggle", "documentId", "document", "find", "page_count", "startPage", "endPage", "handlePageRangeUpdate", "field", "currentRange", "maxPage", "newRange", "_doc$page_count", "hasPageCount", "pageRange", "file_type", "toUpperCase", "canSelect", "file_size", "contentLengthOptions", "ContentLength", "SHORT", "icon", "LONG", "ContentLengthSelector", "getSelectedContentLengthColor", "getContentLengthColor", "useAIStore", "generationProgress", "lastGenerated", "generateFlashcards", "params", "studySet", "content", "flashcards", "useStudyStore", "studyStore", "getState", "refreshStudySetContent", "invalidateStudySets", "cacheError", "warn", "creditsRemaining", "generateQuiz", "questions", "generateMoreFlashcards", "studySetId", "generateMoreQuizQuestions", "clearLastGenerated", "BulkActionsToolbar", "selectedCount", "totalCount", "onDeleteSelected", "onClearSelection", "itemType", "<PERSON><PERSON>", "FlashcardManagement", "onFlashcardAdded", "onFlashcardUpdated", "onFlashcardDeleted", "onFlashcardsGenerated", "alert", "confirm", "useDialog", "user", "useAuthStore", "userSettings", "isAIMode", "setIsAIMode", "setSelectedDocuments", "setDocumentPageRanges", "flashcardCount", "setFlashcardCount", "customPrompt", "setCustomPrompt", "difficultyLevel", "setDifficultyLevel", "contentLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setIsGenerating", "showAddForm", "setShowAddForm", "newFlashcard", "setNewFlashcard", "front", "back", "difficulty_level", "editingFlashcard", "setEditingFlashcard", "editForm", "setEditForm", "selectedFlashcards", "setSelectedFlashcards", "isSelectAllChecked", "setIsSelectAllChecked", "isBulkDeleting", "setIsBulkDeleting", "revealedFlashcards", "setRevealedFlashcards", "performBulkDeletion", "flashcardIds", "deletedCount", "for<PERSON>ach", "performFlashcardDeletion", "flashcard", "calculateCreditCost", "ceil", "rows", "is_ai_generated", "handleCancelEdit", "handlePageRangeChange", "range", "creditCost", "credits_remaining", "confirmText", "cancelText", "documentIds", "count", "existingContent", "f", "updateUser", "checked", "handleSelectAll", "skip_delete_confirmations", "neverAskAgainChecked", "buttonLayout", "showNeverAskAgain", "onNeverAskAgainChange", "revealAllFlashcards", "allFlashcardIds", "<PERSON><PERSON><PERSON>", "hideAllFlashcards", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSelectFlashcard", "flashcardId", "toggleFlashcardReveal", "newSet", "difficultyLevelToString", "numberToDifficultyLevel", "times_reviewed", "difficultyLevelToNumber", "handleEditFlashcard", "substring", "handleDeleteFlashcard", "QuizManagement", "onQuestionAdded", "onQuestionUpdated", "onQuestionDeleted", "onQuestionsGenerated", "questionCount", "setQuestionCount", "selectedQuestionTypes", "setSelectedQuestionTypes", "newQuestion", "setNewQuestion", "question_text", "question_type", "options", "correct_answers", "explanation", "editingQuestion", "setEditingQuestion", "selectedQuestions", "setSelectedQuestions", "revealedQuestions", "setRevealedQuestions", "toggleQuestionType", "t", "handleQuestionTypeChange", "index", "handleCorrectAnswerToggle", "answer", "a", "handleOptionChange", "opt", "i", "join", "split", "updatedQuestion", "Checkbox", "questionTypes", "q", "Promise", "all", "revealAllQuestions", "allQuestionIds", "hideAllQuestions", "question", "handleSelectQuestion", "questionId", "toggleQuestionReveal", "replace", "_Fragment", "times_attempted", "times_correct", "handleEditQuestion", "handleDeleteQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled", "handleToggle", "role", "event", "StudySetPage", "useParams", "navigate", "useNavigate", "studySetContent", "fetchStudySetContent", "prompt", "selectedMode", "setSelectedMode", "activeTab", "setActiveTab", "managementType", "setManagementType", "setFlashcards", "setQuestions", "studySetName", "setStudySetName", "catch", "hasFlashcards", "hasQuestions", "newName", "defaultValue", "Date", "created_at", "toLocaleDateString", "shuffle_flashcards", "shuffleEnabled", "startStudySession", "updatedFlashcard", "card", "newFlashcards", "newQuestions", "source_documents", "custom_prompt", "level", "num", "shuffle<PERSON><PERSON><PERSON>", "array", "shuffled", "j", "random", "get", "currentSession", "studySets", "sessions", "actionHistory", "currentActionIndex", "canUndo", "canRedo", "_studySetContent$stud", "forceRefresh", "arguments", "_studySetContent$stud2", "_content$flashcards", "_content$questions", "shuffle", "totalItems", "originalOrder", "Array", "from", "_", "shuffledFlashcards", "shuffledQuestions", "startTime", "currentIndex", "reviewedItems", "flaggedItems", "correctAnswers", "timeSpent", "isShuffled", "endStudySession", "nextItem", "addToHistory", "nextIndex", "payload", "fromIndex", "toIndex", "previousState", "timestamp", "now", "previousItem", "prevIndex", "goToItem", "clampedIndex", "toggleFlag", "itemId", "wasFlagged", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_itemId", "submitQuizAnswer", "_answer", "isCorrect", "updateTimeSpent", "action", "newHistory", "slice", "push", "limitedHistory", "undo", "redo", "nextActionIndex", "clearHistory", "fetchStudySets", "fetchStudySessions", "timeRange", "session", "endTime", "invalidateStudySetContent", "_studySetContent$stud3"], "sourceRoot": ""}