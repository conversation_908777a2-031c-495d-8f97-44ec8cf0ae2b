{"version": 3, "file": "static/js/39.3736d5aa.chunk.js", "mappings": "qKAWA,MAAMA,EAAoB,CACxB,CACEC,MAAOC,EAAAA,GAAgBC,KACvBC,MAAO,OACPC,YAAa,+BAEf,CACEJ,MAAOC,EAAAA,GAAgBI,OACvBF,MAAO,SACPC,YAAa,mCAEf,CACEJ,MAAOC,EAAAA,GAAgBK,KACvBH,MAAO,OACPC,YAAa,uCAEf,CACEJ,MAAOC,EAAAA,GAAgBM,QACvBJ,MAAO,UACPC,YAAa,kCAEf,CACEJ,MAAOC,EAAAA,GAAgBO,SACvBL,MAAO,WACPC,YAAa,2BAEf,CACEJ,MAAOC,EAAAA,GAAgBQ,IACvBN,MAAO,MACPC,YAAa,6BA0CJM,EAAwDC,IAM9D,IAN+D,MACpEX,EAAK,SACLY,EAAQ,UACRC,EAAY,GAAE,SACdC,GAAW,EAAK,MAChBX,EAAQ,oBACTQ,EACC,OACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAeH,GAAYI,SAAA,EACvCC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,8CAA6CI,SAC3Dd,KAEHe,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wCAAuCI,SACnDlB,EAAkBoB,IAAKC,IACtB,MAAMC,EAAarB,IAAUoB,EAAOpB,MAC9BsB,EAAeD,EAlCKE,KAClC,OAAQA,GACN,KAAKtB,EAAAA,GAAgBC,KACnB,MAAO,gFACT,KAAKD,EAAAA,GAAgBI,OACnB,MAAO,wFACT,KAAKJ,EAAAA,GAAgBK,KACnB,MAAO,oFACT,KAAKL,EAAAA,GAAgBM,QACnB,MAAO,oFACT,KAAKN,EAAAA,GAAgBO,SACnB,MAAO,wEACT,KAAKP,EAAAA,GAAgBQ,IACnB,MAAO,4EACT,QACE,MAAO,0FAoBCe,CAA2BJ,EAAOpB,OAtDpBuB,KAC1B,OAAQA,GACN,KAAKtB,EAAAA,GAAgBC,KACnB,MAAO,gHACT,KAAKD,EAAAA,GAAgBI,OACnB,MAAO,sHACT,KAAKJ,EAAAA,GAAgBK,KACnB,MAAO,mHACT,KAAKL,EAAAA,GAAgBM,QACnB,MAAO,mHACT,KAAKN,EAAAA,GAAgBO,SACnB,MAAO,0GACT,KAAKP,EAAAA,GAAgBQ,IACnB,MAAO,6GACT,QACE,MAAO,wHAwCCgB,CAAmBL,EAAOpB,OAE9B,OACEe,EAAAA,EAAAA,MAAA,UAEEW,KAAK,SACLC,QAASA,KAAOb,GAAYF,EAASQ,EAAOpB,OAC5Cc,SAAUA,EACVD,UAAS,qIAAAG,OAELM,EAAY,sBAAAN,OAEZF,EACI,gCACA,iCAAgC,sBAAAE,OAGpCK,EACI,uEACA,GAAE,qJAIVO,MAAOR,EAAOhB,YACd,eAAciB,EAAWJ,SAAA,EAEzBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,SAAEG,EAAOjB,SACvCe,EAAAA,EAAAA,KAAA,OACEL,UAAS,gBAAAG,OACPK,EAAa,gBAAkB,uBAC9BJ,SAEFG,EAAOhB,iBAGXiB,IACCH,EAAAA,EAAAA,KAAA,OAAKL,UAAU,yBAAwBI,UACrCC,EAAAA,EAAAA,KAAA,OACEL,UAAU,UACVgB,KAAK,eACLC,QAAQ,YAAWb,UAEnBC,EAAAA,EAAAA,KAAA,QACEa,SAAS,UACTC,EAAE,qHACFC,SAAS,kBA1CZb,EAAOpB,YAmDpBkB,EAAAA,EAAAA,KAAA,KAAGL,UAAU,0BAAyBI,SAAC,oI,wDCGzCiB,E,oEA7JAC,EAAgB,QAMhBC,EAAS,wBACTC,EAAgB,GAAGrB,OAAOoB,EAAQ,KAAKpB,OAAOmB,EAAe,cAC7DG,EAAe,4CACfC,EAAsB,8DACtBC,EAA0B,mJAE1BC,EAAgB,SAAuBC,GACzC,OAAOJ,EAAaK,KAAKD,IAAQH,EAAoBI,KAAKD,EAC5D,EAkBIE,EAAe,SAAsBC,GACvC,IAAIC,EAAcD,IAAWA,EAAOE,qBAAuB,8BAAgC,GACvFC,EAASC,SAASC,cAAc,UACpCF,EAAOG,IAAM,GAAGnC,OAAOqB,GAAerB,OAAO8B,GAC7C,IAAIM,EAAaH,SAASI,MAAQJ,SAASK,KAE3C,IAAKF,EACH,MAAM,IAAIG,MAAM,+EAIlB,OADAH,EAAWI,YAAYR,GAChBA,CACT,EAcIS,EAAkB,KAClBC,EAAkB,KAClBC,EAAiB,KAoBjBC,EAAa,SAAoBf,GAEnC,OAAwB,OAApBY,EACKA,GAGTA,EAAkB,IAAII,QAAQ,SAAUC,EAASC,GAC/C,GAAsB,qBAAXC,QAA8C,qBAAbf,SAW5C,GAJIe,OAAOC,QAAUpB,GACnBqB,QAAQC,KAAK3B,GAGXwB,OAAOC,OACTH,EAAQE,OAAOC,aAIjB,IACE,IAAIjB,EAxFO,WAGf,IAFA,IAAIoB,EAAUnB,SAASoB,iBAAiB,gBAAiBrD,OAAOoB,EAAQ,OAE/DkC,EAAI,EAAGA,EAAIF,EAAQG,OAAQD,IAAK,CACvC,IAAItB,EAASoB,EAAQE,GAErB,GAAK7B,EAAcO,EAAOG,KAI1B,OAAOH,CACT,CAEA,OAAO,IACT,CA0EmBwB,GAEb,GAAIxB,GAAUH,EACZqB,QAAQC,KAAK3B,QACR,GAAKQ,GAEL,GAAIA,GAA6B,OAAnBW,GAA+C,OAApBD,EAA0B,CACxE,IAAIe,EAGJzB,EAAO0B,oBAAoB,OAAQf,GACnCX,EAAO0B,oBAAoB,QAAShB,GAGS,QAA5Ce,EAAqBzB,EAAO2B,kBAA+C,IAAvBF,GAAyCA,EAAmBG,YAAY5B,GAC7HA,EAASJ,EAAaC,EACxB,OAXEG,EAASJ,EAAaC,GAaxBc,EApDO,SAAgBG,EAASC,GACpC,OAAO,WACDC,OAAOC,OACTH,EAAQE,OAAOC,QAEfF,EAAO,IAAIR,MAAM,2BAErB,CACF,CA4CuBsB,CAAOf,EAASC,GACjCL,EA7DQ,SAAiBK,GAC7B,OAAO,SAAUe,GACff,EAAO,IAAIR,MAAM,2BAA4B,CAC3CuB,MAAOA,IAEX,CACF,CAuDwBC,CAAQhB,GAC1Bf,EAAOgC,iBAAiB,OAAQrB,GAChCX,EAAOgC,iBAAiB,QAAStB,EACnC,CAAE,MAAOuB,GAEP,YADAlB,EAAOkB,EAET,MAvCEnB,EAAQ,KAwCZ,IAE8B,MAAE,SAAUmB,GAExC,OADAxB,EAAkB,KACXI,QAAQE,OAAOkB,EACxB,EACF,EAsBIC,GAAa,EAEbC,EAAmB,WACrB,OAAIjD,IAIJA,EAAgB0B,EAAW,MAAa,MAAE,SAAUqB,GAGlD,OADA/C,EAAgB,KACT2B,QAAQE,OAAOkB,EACxB,GAEF,EAIApB,QAAQC,UAAUsB,KAAK,WACrB,OAAOD,GACT,GAAU,MAAE,SAAUF,GACfC,GACHhB,QAAQC,KAAKc,EAEjB,GACA,I,oBCtJA,MAAMI,EAAwC,CAC5C,CACEC,GAAI,OACJC,KAAM,OACNC,MAAO,EACPC,SAAU,QACVC,SAAU,CACR,uBACA,sBACA,8BACA,oBACA,kBACA,mCAGJ,CACEJ,GAAI,gBACJC,KAAM,gBACNC,MAAO,GACPC,SAAU,QACVC,SAAU,CACR,wBACA,yBACA,+BACA,gBACA,qBACA,uBACA,4CAGJ,CACEJ,GAAI,YACJC,KAAM,YACNC,MAAO,GACPC,SAAU,QACVE,SAAS,EACTD,SAAU,CACR,wBACA,yBACA,+BACA,mBACA,qBACA,uBACA,iCAGJ,CACEJ,GAAI,eACJC,KAAM,eACNC,MAAO,IACPC,SAAU,QACVC,SAAU,CACR,wBACA,wBACA,+BACA,mBACA,qBACA,qBACA,aACA,gDAGJ,CACEJ,GAAI,cACJC,KAAM,cACNC,MAAO,IACPC,SAAU,QACVC,SAAU,CACR,0BACA,wBACA,4BACA,oBACA,qBACA,qBACA,aACA,sBACA,6CAMAxD,EDoEW,WACf,IAAK,IAAI0D,EAAOC,UAAUtB,OAAQuB,EAAO,IAAIC,MAAMH,GAAOI,EAAO,EAAGA,EAAOJ,EAAMI,IAC/EF,EAAKE,GAAQH,UAAUG,GAGzBd,GAAa,EACb,IAAIe,EAAYC,KAAKC,MAErB,OAAOhB,IAAmBC,KAAK,SAAUgB,GACvC,OAtDa,SAAoBA,EAAaN,EAAMG,GACtD,GAAoB,OAAhBG,EACF,OAAO,KAGT,IACIC,EADKP,EAAK,GACKQ,MAAM,YAErBC,EA/I2B,SAAoCA,GACnE,OAAmB,IAAZA,EAAgB,KAAOA,CAChC,CA6IgBC,CAA2BJ,EAAYG,SACjDE,EAAkBtE,EAElBkE,GAAaE,IAAYE,GAC3BvC,QAAQC,KAAK,aAAanD,OAAOuF,EAAS,mDAAmDvF,OAAO,QAAS,wBAAwBA,OAAOyF,EAAiB,yHAG/J,IAAIC,EAASN,EAAYO,WAAMC,EAAWd,GAE1C,OA5GoB,SAAyBY,EAAQT,GAChDS,GAAWA,EAAOG,kBAIvBH,EAAOG,iBAAiB,CACtBtB,KAAM,YACNgB,QAAS,QACTN,UAAWA,GAEf,CAiGEa,CAAgBJ,EAAQT,GACjBS,CACT,CAoCWK,CAAWX,EAAaN,EAAMG,EACvC,EACF,CC/EsBe,CAAWC,+GAEpBC,EAAmCA,KAAO,IAADC,EAAAC,EACpD,MAAOC,EAAkBC,IACvBC,EAAAA,EAAAA,UAAkC,OAC7BC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAYC,IAAiBJ,EAAAA,EAAAA,WAAS,IACtCtC,EAAO2C,IAAYL,EAAAA,EAAAA,UAAwB,OAC3CM,EAAYC,IAAiBP,EAAAA,EAAAA,UAAS,KACtCQ,EAAeC,IAAoBT,EAAAA,EAAAA,UAIhC,OAEVU,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAAwBC,UAC5BV,GAAa,GACbG,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,oBAAqB,CAChDC,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,MAI7B,IAAKG,EAASI,GACZ,MAAM,IAAIpF,MAAM,qCAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,OAFvBqC,EAAoBsB,EAAOG,KAI/B,CAAE,MAAOC,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,oCAGvC3B,EAAoB,CAClB4B,YAAa7D,EAAkB,GAC/B8D,OAAQ,SACRC,gBAAiB,IAAIlD,KACnBA,KAAKC,MAAQ,QACbkD,eAEN,CAAC,QACC5B,GAAa,EACf,GAGI6B,EAAqBnB,UACzB,GAAKoB,EAAKC,OAKV,IACE,MAAMpB,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,8BAA+B,CAC1DiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,GACzB,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAU,CAAEC,OAAQL,EAAKC,OAAOK,kBAGvCjB,QAAeL,EAASM,OAC1BD,EAAOE,SAAWF,EAAOG,KAAKe,MAChC9B,EAAiB,CACfuB,KAAMX,EAAOG,KAAKQ,KAClBQ,SAAUnB,EAAOG,KAAKgB,SACtBrI,KAAMkH,EAAOG,KAAKrH,OAGpBsG,EAAiB,KAErB,CAAE,MAAOgB,GACPhB,EAAiB,KACnB,MA3BEA,EAAiB,OAkFfgC,EAAwB7B,UAC5B,GACG8B,QACC,8GAFJ,CAQAtC,GAAc,GACdC,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAA4B,CACvDiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,GACzB,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAU,CAAEO,WAAW,MAGpC,IAAK3B,EAASI,GACZ,MAAM,IAAIpF,MAAM,oCAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,aAFjBiD,GAIV,CAAE,MAAOc,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,mCAEzC,CAAC,QACCtB,GAAc,EAChB,CAhCA,GAgJF,OAAIH,GAEAtG,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeI,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,uCACfK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wCAAuCI,SACnD,CAAC,EAAG,EAAG,GAAGE,IAAKmD,IACdpD,EAAAA,EAAAA,KAAA,OAAaL,UAAU,+BAAbyD,YASpBpD,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,4BAIrDgE,IACClE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEgE,OAKrCoC,IACCtG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,kBAC/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,UACG,QAA5BkG,EAAAE,EAAiB6B,mBAAW,IAAA/B,OAAA,EAA5BA,EAA8B5B,OAAQ,uBAG3CrE,EAAAA,EAAAA,KAAA,OAAKL,UAAU,aAAYI,UACzBC,EAAAA,EAAAA,KAAA,OACEL,UAAS,uEAAAG,OACqB,WAA5BqG,EAAiB8B,OACb,iCAC4B,aAA5B9B,EAAiB8B,OACjB,6BAC4B,aAA5B9B,EAAiB8B,OACjB,mCACA,gCACHlI,SAEFoG,EAAiB8B,OAAOiB,OAAO,GAAGP,cACjCxC,EAAiB8B,OAAOkB,MAAM,UAKrChD,EAAiB+B,kBAChBrI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDI,SAAA,EAChEC,EAAAA,EAAAA,KAACoJ,EAAAA,IAAmB,CAACzJ,UAAU,aAC/BK,EAAAA,EAAAA,KAAA,QAAAD,SACGoG,EAAiBkD,kBAAiB,kBAAAvJ,OACb,IAAIkF,KACpBmB,EAAiB+B,iBACjBoB,sBAAoB,sBAAAxJ,OACA,IAAIkF,KACxBmB,EAAiB+B,iBACjBoB,2BAK0B,UAAT,QAA5BpD,EAAAC,EAAiB6B,mBAAW,IAAA9B,OAAA,EAA5BA,EAA8B9B,MAC7BvE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,CACjCoG,EAAiBkD,mBAChBrJ,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QApJmBwG,UACnCR,GAAc,GACdC,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,+BAAgC,CAC3DiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,MAI7B,IAAKG,EAASI,GACZ,MAAM,IAAIpF,MAAM,qCAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,aAFjBiD,GAIV,CAAE,MAAOc,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,oCAEzC,CAAC,QACCtB,GAAc,EAChB,GAwHgBH,UAAWE,EACXgD,QAAQ,UACRC,KAAK,KAAI1J,SACV,6BAIDC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QArMewG,UAC/B,GACG8B,QACC,8HAFJ,CAQAtC,GAAc,GACdC,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAA4B,CACvDiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,MAI7B,IAAKG,EAASI,GACZ,MAAM,IAAIpF,MAAM,iCAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,aAFjBiD,GAIV,CAAE,MAAOc,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,gCAEzC,CAAC,QACCtB,GAAc,EAChB,CA9BA,GA+LgBH,UAAWE,EACXgD,QAAQ,SACRC,KAAK,KAAI1J,SACV,yBAIHF,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACL9I,QAASuG,EACTwC,QAAQ,YACRC,KAAK,KAAI1J,SAAA,EAETC,EAAAA,EAAAA,KAAC0J,EAAAA,IAAS,CAAC/J,UAAU,iBAAiB,oBAShDE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,MAAIF,UAAU,wDAAuDI,SAAA,EACnEC,EAAAA,EAAAA,KAAC2J,EAAAA,IAAK,CAAChK,UAAU,kCAAkC,kBAGrDE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,SAAQI,UACrBC,EAAAA,EAAAA,KAAA,SACEQ,KAAK,OACL1B,MAAO6H,EACPjH,SAAWkK,IACThD,EAAcgD,EAAEC,OAAO/K,OACvBsJ,EAAmBwB,EAAEC,OAAO/K,QAE9BgL,YAAY,oBACZnK,UAAU,+KAGdK,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAM2H,EAAmBzB,GAClC6C,QAAQ,YACRC,KAAK,KACL7J,UAAW+G,EAAW2B,OAAOvI,SAC9B,aAIF8G,IACChH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iEAAgEI,SAAA,EAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC+J,EAAAA,IAAO,CAACpK,UAAU,4BACnBE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6BAA4BI,SAAA,CAAC,mBAC1B8G,EAAcwB,YAGnCrI,EAAAA,EAAAA,KAAA,KAAGL,UAAU,8BAA6BI,SAChB,YAAvB8G,EAAcrG,KAAkB,GAAAV,OAC1B+G,EAAcgC,SAAQ,gCAAA/I,OACrB+G,EAAcgC,SAAQ,oCAOtChJ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,mBACpDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sEAAqEI,SACjFoE,EAAkBlE,IAAK+J,IAAU,IAADC,EAC/B,MAAMC,GAA4B,OAAhB/D,QAAgB,IAAhBA,GAA6B,QAAb8D,EAAhB9D,EAAkB6B,mBAAW,IAAAiC,OAAb,EAAhBA,EAA+B7F,MAAO4F,EAAK5F,GACvD+F,EACJtD,GAAiBmD,EAAK1F,MAAQ,EACH,YAAvBuC,EAAcrG,KACZwJ,EAAK1F,OAAS,EAAIuC,EAAcgC,SAAW,KAC3CuB,KAAKC,IAAI,EAAGL,EAAK1F,MAAQuC,EAAcgC,UACzCmB,EAAK1F,MAEX,OACEzE,EAAAA,EAAAA,MAACyK,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBlL,UAAS,sFAAAG,OACPkK,EAAKvF,QACD,gDACAyF,EACA,4CACA,+CACHnK,SAAA,CAEFiK,EAAKvF,UACJzE,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sDAAqDI,UAClEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mGAAkGI,SAAA,EAC/GC,EAAAA,EAAAA,KAAC8K,EAAAA,IAAM,CAACnL,UAAU,aAClBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,sBAKXmK,IACClK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,0BAAyBI,UACtCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGI,SAAA,EAC7GC,EAAAA,EAAAA,KAAC+J,EAAAA,IAAO,CAACpK,UAAU,aACnBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBAKZF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBI,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAClDiK,EAAK3F,QAERxE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BI,SAAA,CAC3C8G,GACDmD,EAAK1F,MAAQ,GACb6F,IAAeH,EAAK1F,OAClBzE,EAAAA,EAAAA,MAAAkL,EAAAA,SAAA,CAAAhL,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,0CAAyCI,SAAA,CAAC,IACtDiK,EAAK1F,SACF,IACL6F,EAAWa,QAAQ,MACpB,IAAAlL,OAECkK,EAAK1F,QAEXzE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,wBAAuBI,SAAA,CAAC,IACpCiK,EAAKzF,eAGVsC,GAAiBmD,EAAK1F,MAAQ,GAAoB,IAAf6F,IAClCtK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0CAAyCI,SAAA,CAAC,aAC5C8G,EAAcwB,KAAK,WAKpCrI,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iBAAgBI,SAC3BiK,EAAKxF,SAASvE,IAAI,CAACgL,EAASC,KAC3BrL,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,8BAA6BI,SAAA,EACrDC,EAAAA,EAAAA,KAAC+J,EAAAA,IAAO,CAACpK,UAAU,0CACnBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,wBAAuBI,SAAEkL,MAFlCC,OAOblL,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAxbEwG,WAC3B,GAAe,SAAXkE,EAAJ,CAKA1E,GAAc,GACdC,EAAS,MAET,IACE,MAAMlB,QAAexE,EACrB,IAAKwE,EACH,MAAM,IAAInD,MAAM,yBAGlB,MAAM6E,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,sCAAuC,CAClEiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,GACzB,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAU,CACnB0C,SACAzC,QAAqB,OAAb7B,QAAa,IAAbA,OAAa,EAAbA,EAAewB,OAAQ,KAC/B+C,WAAW,GAADtL,OAAKgD,OAAOuI,SAASC,OAAM,2CACrCC,UAAU,GAADzL,OAAKgD,OAAOuI,SAASC,OAAM,gDAIlC5D,QAAeL,EAASM,OAC9B,IAAKD,EAAOE,QACV,MAAM,IAAIvF,MAAMqF,EAAO3D,OAAS,qCAIlC,MAAM,MAAEA,SAAgByB,EAAOgG,mBAAmB,CAChDC,UAAW/D,EAAOG,KAAK4D,YAGzB,GAAI1H,EACF,MAAM,IAAI1B,MAAM0B,EAAMgE,SAAW,yBAErC,CAAE,MAAOD,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,mCAEzC,CAAC,QACCtB,GAAc,EAChB,CA7CA,YAFQqC,KAsbuB4C,CAAqB1B,EAAK5F,IACzCxE,SAAUsK,GAAa1D,EACvBF,UAAWE,EACXgD,QACEQ,EAAKvF,QACD,UAEA,YAGN9E,UAAU,SAAQI,SAEjBmK,EACG,eACY,SAAZF,EAAK5F,GACL,oBAAmB,cAAAtE,OACLkK,EAAK3F,UApFpB2F,EAAK5F,YA6FpBvE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,8BAGpDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,qBAAoBI,SAAC,0FAIlCC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,uDAAsDI,SAClE4L,EAAAA,EAAgB1L,IAAK2L,IACpB/L,EAAAA,EAAAA,MAACyK,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBlL,UAAU,wHAAuHI,SAAA,EAEjIF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBI,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAClD6L,EAAIvH,QAEPxE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BI,SAAA,CAAC,IAC3C6L,EAAItH,UAERzE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBI,SAAA,CACnC6L,EAAIC,QAAQ,eAEfhM,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BI,SAAA,CAAC,IAC3C6L,EAAIE,eAAed,QAAQ,GAAG,qBAIpChL,EAAAA,EAAAA,KAAA,KAAGL,UAAU,yCAAwCI,SAClD6L,EAAI1M,eAGPW,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACL9I,QAASA,IAhVKwG,WAC5BR,GAAc,GACdC,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,wBAAyB,CACpDiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,GACzB,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAU,CAAEsD,gBAGzB,IAAK1E,EAASI,GACZ,MAAM,IAAIpF,MAAM,8BAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAMT,MAAM,IAAIvF,MAAMqF,EAAO3D,OAJnB2D,EAAOsE,aACTlJ,OAAOuI,SAASY,KAAOvE,EAAOsE,WAKpC,CAAE,MAAOlE,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,6BAEzC,CAAC,QACCtB,GAAc,EAChB,GA8S6ByF,CAAsBN,EAAIxH,IACzCxE,SAAU4G,EACVF,UAAWE,EACXgD,QAAQ,YACRC,KAAK,KACL9J,UAAU,SAAQI,SAAA,EAElBC,EAAAA,EAAAA,KAACmM,EAAAA,IAAc,CAACxM,UAAU,iBAAiB,gBAjCxCiM,EAAIxH,gBC9oBZgI,EAA2BA,KACtC,MAAOC,EAAaC,IAAkBjG,EAAAA,EAAAA,WAAS,IACxCkG,EAAYC,IAAiBnG,EAAAA,EAAAA,WAAS,IACtCtC,EAAO2C,IAAYL,EAAAA,EAAAA,UAAwB,OAC3CuB,EAAS6E,IAAcpG,EAAAA,EAAAA,UAAwB,OAC/CqG,EAAWC,IAAgBtG,EAAAA,EAAAA,UAAoB,CACpDuG,UAAW,GACXC,WAAY,IACZC,QAAS,GACTC,UAAW,EACXC,UAAW,YAGNC,EAAYC,IAAiB7G,EAAAA,EAAAA,UAAqB,CACvDuG,WAAW,EACXC,YAAY,EACZC,SAAS,EACTK,WAAW,EACXC,aAAa,IAyCTC,EAAkBpG,UACtB,GAAK8B,QAAQ,sCAADjJ,OAAuCwN,EAAQ,oCAA3D,CAIAd,GAAc,GACd9F,EAAS,MACT+F,EAAW,MAEX,IACE,MAAMvF,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,mBAADxH,OAAoBwN,GAAY,CAC1D/E,OAAQ,SACRhB,QAAS,CACP,cAAgB,UAADzH,OAAYoH,MAI/B,IAAKG,EAASI,GACZ,MAAM,IAAIpF,MAAM,mBAADvC,OAAoBwN,IAGrC,MAAM5F,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAIT,MAAM,IAAIvF,MAAMqF,EAAO3D,OAHvB0I,EAAW,GAAD3M,OAAIwN,EAAQ,iCAChBC,GAIV,CAAE,MAAOzF,GACPpB,EAASoB,aAAezF,MAAQyF,EAAIC,QAAO,mBAAAjI,OAAsBwN,GACnE,CAAC,QACCd,GAAc,EAChB,CA9BA,GAiCIe,EAAiBtG,UACrB,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAAmB,CAC9CC,QAAS,CACP,cAAgB,UAADzH,OAAYoH,MAI/B,GAAIG,EAASI,GAAI,CACf,MAAMC,QAAeL,EAASM,OAC1BD,EAAOE,SACT+E,EAAajF,EAAOG,KAExB,CACF,CAAE,MAAOC,GACP,GAIJ,OACE9H,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,oBAErDgE,IACClE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEgE,OAIrC6D,IACC/H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iEAAgEI,SAAA,EAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACwN,EAAAA,IAAa,CAAC7N,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SAAC,gBAE/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,sBAAqBI,SAAE6H,QAKxC/H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,wBAC/CF,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACL9I,QAAS8M,EACT/D,QAAQ,YACRC,KAAK,KAAI1J,SAAA,EAETC,EAAAA,EAAAA,KAAC0J,EAAAA,IAAS,CAAC/J,UAAU,iBAAiB,iBAK1CE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAE2M,EAAUE,aAChE5M,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,mBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAE2M,EAAUG,cAChE7M,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,mBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAE2M,EAAUI,WAChE9M,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,gBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAE2M,EAAUK,aAChE/M,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,kBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAE2M,EAAUM,aAChEhN,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,yBAM7CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAACyN,EAAAA,IAAU,CAAC9N,UAAU,2BACtBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,yBAGjDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,0HAI1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,2BACtC2N,OAAOC,QAAQV,GAAYhN,IAAIR,IAAA,IAAEmO,EAAK9O,GAAMW,EAAA,OAC3CI,EAAAA,EAAAA,MAAA,OAAeF,UAAU,oCAAmCI,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,mCAAkCI,SAChD6N,EAAIC,QAAQ,WAAY,OAAOvF,UAElCtI,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMyM,GAAaY,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIb,GAAU,IAAE,CAACW,IAAO9O,KACtDa,UAAS,6EAAAG,OACPhB,EAAQ,iBAAmB,eAC1BiB,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPhB,EAAQ,gBAAkB,uBAZxB8O,SAoBd/N,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACL9I,QAhMewG,UACvBqF,GAAe,GACf5F,EAAS,MACT+F,EAAW,MAEX,IACE,MAAMvF,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,mBAAoB,CAC/CiB,OAAQ,OACRhB,QAAS,CACP,cAAgB,UAADzH,OAAYoH,GAC3B,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAU,CAAEwE,iBAGzB,IAAK5F,EAASI,GACZ,MAAM,IAAIpF,MAAM,yBAGlB,MAAM0L,QAAa1G,EAAS0G,OACtBvM,EAAMsB,OAAOkL,IAAIC,gBAAgBF,GACjCG,EAAInM,SAASC,cAAc,KACjCkM,EAAEjC,KAAOzK,EACT0M,EAAEC,SAAQ,uBAAArO,QAA0B,IAAIkF,MAAOmD,cAAciG,MAAM,KAAK,GAAE,SAC1EF,EAAEG,QACFvL,OAAOkL,IAAIM,gBAAgB9M,GAE3BiL,EAAW,8BACb,CAAE,MAAO3E,GACPpB,EAASoB,aAAezF,MAAQyF,EAAIC,QAAU,wBAChD,CAAC,QACCuE,GAAe,EACjB,GAgKQhG,UAAW+F,EACX7C,QAAQ,UAASzJ,SAAA,EAEjBC,EAAAA,EAAAA,KAACyN,EAAAA,IAAU,CAAC9N,UAAU,iBAAiB,qBAQ3CE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wDAAuDI,SAAA,EACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAACuO,EAAAA,IAAO,CAAC5O,UAAU,0BACnBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,mBAGjDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,gGAI1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAM4M,EAAgB,cAC/B/G,UAAWiG,EACX/C,QAAQ,SACRC,KAAK,KACL9J,UAAU,SAAQI,SACnB,0BAGDC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAM4M,EAAgB,cAC/B/G,UAAWiG,EACX/C,QAAQ,SACRC,KAAK,KACL9J,UAAU,SAAQI,SACnB,6BAIHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAM4M,EAAgB,WAC/B/G,UAAWiG,EACX/C,QAAQ,SACRC,KAAK,KACL9J,UAAU,SAAQI,SACnB,uBAGDC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAM4M,EAAgB,aAC/B/G,UAAWiG,EACX/C,QAAQ,SACRC,KAAK,KACL9J,UAAU,SAAQI,SACnB,wCC7PFyO,EAA4BA,KACvC,MAAOC,EAAaC,IAAkBrI,EAAAA,EAAAA,UAA6B,OAC5DC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCsI,EAAYC,IAAiBvI,EAAAA,EAAAA,WAAS,IACtCtC,EAAO2C,IAAYL,EAAAA,EAAAA,UAAwB,OAElDU,EAAAA,EAAAA,WAAU,KACR8H,KACC,IAEH,MAAMA,EAAmB5H,UACvBV,GAAa,GACbG,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,eAAgB,CAC3CC,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,MAI7B,IAAKG,EAASI,GACZ,MAAM,IAAIpF,MAAM,gCAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,OAFvB2K,EAAehH,EAAOG,KAI1B,CAAE,MAAOC,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,+BAGvC2G,EAAe,CACbI,eAAgB,CACd,CACE1K,GAAI,IACJ5D,KAAM,OACNuO,MAAO,OACPC,MAAO,OACPC,YAAa,GACbC,WAAY,KACZC,WAAW,IAGfC,SAAU,CACR,CACEhL,GAAI,IACJiL,OAAQ,UACRC,OAAQ,GACRC,SAAU,MACVtH,OAAQ,OACRuH,KAAM,IAAIxK,KAAKA,KAAKC,MAAQ,QAA0BkD,cACtDjJ,YAAa,uBAEf,CACEkF,GAAI,IACJiL,OAAQ,UACRC,OAAQ,GACRC,SAAU,MACVtH,OAAQ,UACRuH,MAAM,IAAIxK,MAAOmD,cACjBsH,QAAS,IAAIzK,KACXA,KAAKC,MAAQ,QACbkD,cACFjJ,YAAa,wBAGjBwQ,YAAa,CACXJ,OAAQ,GACRC,SAAU,MACVC,KAAM,IAAIxK,KAAKA,KAAKC,MAAQ,QAA0BkD,gBAG5D,CAAC,QACC5B,GAAa,EACf,GAwEIoJ,EAAiB1H,IACrB,OAAQA,GACN,IAAK,OACH,OAAOjI,EAAAA,EAAAA,KAACwN,EAAAA,IAAa,CAAC7N,UAAU,2BAClC,IAAK,UACH,OAAOK,EAAAA,EAAAA,KAAC4P,EAAAA,IAAO,CAACjQ,UAAU,4BAC5B,IAAK,SACH,OAAOK,EAAAA,EAAAA,KAAC6P,EAAAA,IAAG,CAAClQ,UAAU,yBACxB,QACE,OAAOK,EAAAA,EAAAA,KAAC4P,EAAAA,IAAO,CAACjQ,UAAU,4BAI1BmQ,EAAkB7H,IACtB,OAAQA,GACN,IAAK,OACH,MAAO,iBACT,IAAK,UACH,MAAO,kBACT,IAAK,SACH,MAAO,eACT,QACE,MAAO,kBAIb,OAAI3B,GAEAtG,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeI,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,uCACfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iCACfK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,yCAQvBK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAC,wBAGjDF,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CAAC9I,QAASoO,EAAkBrF,QAAQ,YAAYC,KAAK,KAAI1J,SAAA,EAC9DC,EAAAA,EAAAA,KAAC0J,EAAAA,IAAS,CAAC/J,UAAU,iBAAiB,gBAKzCoE,IACClE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEgE,QAKtClE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,qBAC/CC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QA3GmBwG,UAE7BP,EAAS,0CA0GC8C,QAAQ,YACRC,KAAK,KAAI1J,SACV,0BAKqC,KAA5B,OAAX0O,QAAW,IAAXA,OAAW,EAAXA,EAAaK,eAAezL,SAC3BxD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBI,SAAA,EAC/BC,EAAAA,EAAAA,KAAC+P,EAAAA,IAAY,CAACpQ,UAAU,0CACxBK,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,SAAC,iCAG/BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACZ,OAAX0O,QAAW,IAAXA,OAAW,EAAXA,EAAaK,eAAe7O,IAAKsI,IAAM,IAAAyH,EAAA,OACtCnQ,EAAAA,EAAAA,MAAA,OAEEF,UAAU,wGAAuGI,SAAA,EAEjHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC+P,EAAAA,IAAY,CAACpQ,UAAU,2BACxBE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yBAAwBI,SAAA,CACzB,QADyBiQ,EACrCzH,EAAOyG,aAAK,IAAAgB,OAAA,EAAZA,EAAcrH,cAAc,6BAAOJ,EAAOwG,SAE5CxG,EAAO4G,YACNnP,EAAAA,EAAAA,KAAA,QAAML,UAAU,2EAA0EI,SAAC,eAK9FwI,EAAO0G,aAAe1G,EAAO2G,aAC5BrP,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CAAC,UAC3B,IACPwI,EAAO0G,YAAYgB,WAAWC,SAAS,EAAG,KAAK,IAC/C3H,EAAO2G,qBAMd3G,EAAO4G,YACPnP,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAnJSwG,WACpC2H,GAAc,GACdlI,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,uCAAwC,CACnEiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,GACzB,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAU,CAAE0H,sBAGzB,IAAK9I,EAASI,GACZ,MAAM,IAAIpF,MAAM,2CAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,aAFjB8K,GAIV,CAAE,MAAO/G,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,kCAEzC,CAAC,QACC6G,GAAc,EAChB,GAoHiCwB,CAA8B7H,EAAOnE,IACpDkC,UAAWqI,EACXnF,QAAQ,YACRC,KAAK,KAAI1J,SACV,qBAhCEwI,EAAOnE,YA2CV,OAAXqK,QAAW,IAAXA,OAAW,EAAXA,EAAaiB,eACZ7P,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+DAA8DI,SAAA,EAC3EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,sBAGpDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,oCAAmCI,UAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,gBAAeI,SAAA,CAAC,IACzB0O,EAAYiB,YAAYJ,OAAQ,IACjCb,EAAYiB,YAAYH,SAAS5G,kBAEpC9I,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CAAC,SAC5B,IACN,IAAIiF,KAAKyJ,EAAYiB,YAAYF,MAAMlG,iCAQlDzJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,oBAIlB,KAAtB,OAAX0O,QAAW,IAAXA,OAAW,EAAXA,EAAaW,SAAS/L,SACrBrD,EAAAA,EAAAA,KAAA,OAAKL,UAAU,mBAAkBI,UAC/BC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,SAAC,yBAG/BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACZ,OAAX0O,QAAW,IAAXA,OAAW,EAAXA,EAAaW,SAASnP,IAAKoQ,IAC1BxQ,EAAAA,EAAAA,MAACyK,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1B/K,UAAU,wGAAuGI,SAAA,EAEjHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,CACzC4P,EAAcU,EAAQpI,SACvBpI,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAML,UAAU,yBAAwBI,SACrCsQ,EAAQhB,UAEXrP,EAAAA,EAAAA,KAAA,QACEL,UAAS,uBAAAG,OAAyBgQ,EAChCO,EAAQpI,SACNlI,SAEHsQ,EAAQpI,OAAOiB,OAAO,GAAGP,cACxB0H,EAAQpI,OAAOkB,MAAM,SAG3BnJ,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SACjCsQ,EAAQnR,eAEXW,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CACjC,IAAIiF,KAAKqL,EAAQb,MAAMlG,qBACvB+G,EAAQZ,SAA8B,YAAnBY,EAAQpI,SAC1BpI,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CACG,IAAI,aACC,IACL,IAAIiF,KAAKqL,EAAQZ,SAASnG,kCAOrCzJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yBAAwBI,SAAA,CAAC,IACrCsQ,EAAQf,OAAO,IAAEe,EAAQd,SAAS5G,iBAElB,SAAnB0H,EAAQpI,SACPpI,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACL9I,QAASA,IAlRDwG,WAC5B,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,yBAADxH,OACDwQ,EAAS,aAClC,CACE/I,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,MAK/B,IAAKG,EAASI,GACZ,MAAM,IAAIpF,MAAM,8BAGlB,MAAM0L,QAAa1G,EAAS0G,OACtBvM,EAAMsB,OAAOkL,IAAIC,gBAAgBF,GACjCG,EAAInM,SAASC,cAAc,KACjCkM,EAAEjC,KAAOzK,EACT0M,EAAEC,SAAQ,WAAArO,OAAcwQ,EAAS,QACjCpC,EAAEG,QACFvL,OAAOkL,IAAIM,gBAAgB9M,EAC7B,CAAE,MAAOsG,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,6BAEzC,GAuPmCwI,CAAsBF,EAAQjM,IAC7CoF,QAAQ,YACRC,KAAK,KAAI1J,SAAA,EAETC,EAAAA,EAAAA,KAACyN,EAAAA,IAAU,CAAC9N,UAAU,iBAAiB,mBA/CxC0Q,EAAQjM,gBC9UlBoM,EAA8C/Q,IAGpD,IAHqD,QAC1DgR,EAAO,SACPC,GACDjR,EACC,MAAOkR,EAAaC,IAAkBvK,EAAAA,EAAAA,WAAS,IACxCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCtC,EAAO2C,IAAYL,EAAAA,EAAAA,UAAwB,OAC3CuB,EAAS6E,IAAcpG,EAAAA,EAAAA,UAAwB,OAC/CwK,EAAWC,IAAgBzK,EAAAA,EAAAA,UAA2B,OACtD0K,EAAkBC,IAAuB3K,EAAAA,EAAAA,UAAS,KAClD4K,EAAYC,IAAiB7K,EAAAA,EAAAA,WAAS,IACtC8K,EAAiBC,IAAsB/K,EAAAA,EAAAA,WAAS,GA0DjDgL,EAAkBpK,UACtB,GAAK8J,GAAgD,IAA5BA,EAAiB1N,OAA1C,CAKAkD,GAAa,GACbG,EAAS,MAET,IAEE,MAAM,aAAE4K,SAAuB,6BACzBC,EAAWD,EACfvL,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,iCAAAA,+GAAYyL,uBACZzL,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,iCAAAA,+GAAY0L,6BAId,IAAKZ,EACH,MAAM,IAAIxO,MAAM,2BAIlB,MAAQwF,KAAM6J,EAAe3N,MAAO4N,SAC5BJ,EAASK,KAAKC,IAAIC,UAAU,CAChCC,SAAUlB,EAAUkB,WAGxB,GAAIJ,EACF,MAAM,IAAItP,MAAMsP,EAAe5J,SAGjC,MAAQhE,MAAOiO,SAAsBT,EAASK,KAAKC,IAAII,OAAO,CAC5DF,SAAUlB,EAAUkB,SACpBG,YAAaR,EAActN,GAC3BiE,KAAM0I,IAGR,GAAIiB,EACF,MAAM,IAAI3P,MAAM2P,EAAYjK,SAG9B0E,EAAW,mDACXmE,GAAe,GACfQ,GAAmB,GACnBV,GAAS,EACX,CAAE,MAAO5I,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,4BAEzC,CAAC,QACCxB,GAAa,EACf,CAhDA,MAFEG,EAAS,sCAqGPyL,EAAyBC,IAC7BC,UAAUC,UAAUC,UAAUH,GAC9B3F,EAAW,wBACX+F,WAAW,IAAM/F,EAAW,MAAO,MAG/BgG,EAAoBA,KACxB7B,GAAe,GACfE,EAAa,MACbE,EAAoB,IACpBtK,EAAS,MACT+F,EAAW,OAGb,OAAIkE,GAAeE,GAEfhR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAC0S,EAAAA,IAAa,CAAC/S,UAAU,8BACzBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,uCAKhDgE,IACC/D,EAAAA,EAAAA,KAAA,OAAKL,UAAU,6DAA4DI,UACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,uBAAsBI,SAAEgE,SAK7C6D,IACC5H,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iEAAgEI,UAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACwN,EAAAA,IAAa,CAAC7N,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,yBAAwBI,SAAE6H,SAK9CuJ,GAwFAtR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAACwN,EAAAA,IAAa,CAAC7N,UAAU,2CACzBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,+BAGpDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,sEAKvCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DI,SAAA,EAC1EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAC,uCAGjDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,+BAA8BI,SAAC,8HAI5CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,8BAA6BI,SACzC8Q,EAAU8B,YAAY1S,IAAI,CAACoI,EAAM6C,KAChClL,EAAAA,EAAAA,KAAA,OAEEL,UAAU,+EAA8EI,UAExFC,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SAAEsI,KAHzC6C,OAOXrL,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACL9I,QAASA,IACP0R,EAAsBtB,EAAU8B,YAAYC,KAAK,OAEnDpJ,QAAQ,YACRC,KAAK,KAAI1J,SAAA,EAETC,EAAAA,EAAAA,KAAC6S,EAAAA,IAAW,CAAClT,UAAU,iBAAiB,wBAK5CK,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAM2Q,GAAmB,GAClC5H,QAAQ,UACR7J,UAAU,SAAQI,SACnB,mCApIHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,0BAG5CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,uFAI1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,uCAAsCI,UACnDC,EAAAA,EAAAA,KAAA,OACEiC,IAAK4O,EAAUiC,OACfC,IAAI,cACJpT,UAAU,oBAKhBE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,wCAG5CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,oEAG1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,6EAA4EI,UACzFC,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SACzCkR,EAAaJ,EAAUmC,OAAS,wGAGrChT,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAMyQ,GAAeD,GAC9BzH,QAAQ,YACRC,KAAK,KAAI1J,SAERkR,GACCjR,EAAAA,EAAAA,KAACiT,EAAAA,IAAQ,CAACtT,UAAU,aAEpBK,EAAAA,EAAAA,KAACkT,EAAAA,IAAK,CAACvT,UAAU,eAGrBK,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAM0R,EAAsBtB,EAAUmC,QAC/CxJ,QAAQ,YACRC,KAAK,KAAI1J,UAETC,EAAAA,EAAAA,KAAC6S,EAAAA,IAAW,CAAClT,UAAU,qBAK7BE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,0BAG5CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,yDAG1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAACmT,EAAAA,EAAK,CACJrU,MAAOiS,EACPrR,SAAUsR,EACVlH,YAAY,SACZnK,UAAU,YAEZK,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAAS4Q,EACT/K,UAAWA,EACX1G,SAAsC,IAA5BmR,EAAiB1N,OAAatD,SACzC,kBAMLC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iBAAgBI,UAC7BC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASgS,EACTjJ,QAAQ,YACR7J,UAAU,SAAQI,SACnB,oBA6DXF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC0S,EAAAA,IAAa,CACZ/S,UAAS,WAAAG,OACP2Q,EAAU,iBAAmB,oBAGjC5Q,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,+BAG/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,0DAKzCC,EAAAA,EAAAA,KAAA,OACEL,UAAS,uEAAAG,OACP2Q,EACI,iCACA,gCACH1Q,SAEF0Q,EAAU,UAAY,gBAI1B1M,IACC/D,EAAAA,EAAAA,KAAA,OAAKL,UAAU,6DAA4DI,UACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,uBAAsBI,SAAEgE,SAK7C6D,IACC5H,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iEAAgEI,UAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACwN,EAAAA,IAAa,CAAC7N,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,yBAAwBI,SAAE6H,UAKhD/H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SACjC0Q,EACG,uGACA,8JAGNzQ,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iBAAgBI,SAC5B0Q,GACCzQ,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAjSawG,UACvB,GACG8B,QACC,wGAFJ,CAQAxC,GAAa,GACbG,EAAS,MAET,IAEE,MAAM,aAAE4K,SAAuB,6BACzBC,EAAWD,EACfvL,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,iCAAAA,+GAAYyL,uBACZzL,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,iCAAAA,+GAAY0L,8BAIN5J,KAAMuL,EAAarP,MAAOsP,SAC1B9B,EAASK,KAAKC,IAAIyB,cAE1B,GAAID,EACF,MAAM,IAAIhR,MAAMgR,EAAatL,SAI/B,IAAK,MAAMwL,KAAUH,EAAYI,KAAM,CACrC,MAAQzP,MAAO0P,SAAwBlC,EAASK,KAAKC,IAAI6B,SAAS,CAChE3B,SAAUwB,EAAOnP,KAGfqP,GACFzQ,QAAQe,MAAM,6BAA8B0P,EAEhD,CAEAhH,EAAW,mDACXiE,GAAS,EACX,CAAE,MAAO5I,GACPpB,EAASoB,aAAezF,MAAQyF,EAAIC,QAAU,wBAChD,CAAC,QACCxB,GAAa,EACf,CAtCA,GA2RUD,UAAWA,EACXkD,QAAQ,SAAQzJ,SACjB,iBAIDF,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACL9I,QAxZYwG,UACtBV,GAAa,GACbG,EAAS,MACT+F,EAAW,MAEX,IAEE,MAAM,aAAE6E,SAAuB,6BACzBC,EAAWD,EACfvL,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,iCAAAA,+GAAYyL,uBACZzL,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,iCAAAA,+GAAY0L,8BAIR,KAAE5J,EAAI,MAAE9D,SAAgBwN,EAASK,KAAKC,IAAI8B,OAAO,CACrDC,WAAY,OACZC,aAAc,0BAGhB,GAAI9P,EACF,MAAM,IAAI1B,MAAM0B,EAAMgE,SAIxB+I,EAAa,CACXgC,OAAQjL,EAAK2L,KAAKM,QAClBd,OAAQnL,EAAK2L,KAAKR,OAClBL,YAAa,GACbZ,SAAUlK,EAAKzD,KAEjBwM,GAAe,EACjB,CAAE,MAAO9I,GACPpB,EAASoB,aAAezF,MAAQyF,EAAIC,QAAU,uBAE9C+I,EAAa,CACXgC,OACE,yHACFE,OAAQ,mBACRjB,SAAU,iBACVY,YAAa,CACX,WACA,WACA,WACA,WACA,WACA,WACA,WACA,cAGJ/B,GAAe,EACjB,CAAC,QACCrK,GAAa,EACf,GAoWUD,UAAWA,EACXkD,QAAQ,UAASzJ,SAAA,EAEjBC,EAAAA,EAAAA,KAAC+T,EAAAA,IAAK,CAACpU,UAAU,iBAAiB,yBCvbnCqU,EAA0DvU,IAIhE,IAJiE,OACtEwU,EAAM,QACNC,EAAO,UACPC,GACD1U,EACC,MAAO2U,EAAUC,IAAehO,EAAAA,EAAAA,UAAS,CACvCiO,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,MAEZC,EAAeC,IAAoBrO,EAAAA,EAAAA,UAAS,CACjDsO,SAAS,EACTC,KAAK,EACL7L,SAAS,KAEJzC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCtC,EAAO2C,IAAYL,EAAAA,EAAAA,UAAwB,OAC3CuB,EAAS6E,IAAcpG,EAAAA,EAAAA,UAAwB,MAOhDwO,EAAgBC,GAAmBhW,IALfiW,EAACD,EAAehW,KACxCuV,EAAYW,IAAIlH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUkH,GAAI,IAAE,CAACF,GAAQhW,KACzC4H,EAAS,OAITqO,CAAkBD,EAAOhW,IAGrBmW,EAA4BH,IAChCJ,EAAiBM,IAAIlH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUkH,GAAI,IAAE,CAACF,IAASE,EAAKF,OAsEhDI,EAAYA,KAChBb,EAAY,CACVC,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,KAEnBE,EAAiB,CACfC,SAAS,EACTC,KAAK,EACL7L,SAAS,IAEXrC,EAAS,MACT+F,EAAW,OAGP0I,EAAcA,KAClBD,IACAhB,KAGF,OAAKD,GAGHjU,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iFAAgFI,UAC7FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFI,SAAA,EAE9FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uEAAsEI,SAAA,EACnFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC+T,EAAAA,IAAK,CAACpU,UAAU,8BACjBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAC,wBAEnDC,EAAAA,EAAAA,KAAA,UACES,QAAS0U,EACTxV,UAAU,mDAAkDI,UAE5DC,EAAAA,EAAAA,KAAC6P,EAAAA,IAAG,CAAClQ,UAAU,kBAKnBE,EAAAA,EAAAA,MAAA,QAAMuV,SAnFSnO,UAGnB,GAFA2C,EAAEyL,iBAxBGjB,EAASE,gBAITF,EAASG,YAIVH,EAASG,YAAYlR,OAAS,GAChCqD,EAAS,mDACF,GAEL0N,EAASG,cAAgBH,EAASI,iBACpC9N,EAAS,8BACF,GAEL0N,EAASE,kBAAoBF,EAASG,cACxC7N,EAAS,wDACF,IAbPA,EAAS,4BACF,IALPA,EAAS,gCACF,GAwBT,CAEAH,GAAa,GACbG,EAAS,MACT+F,EAAW,MAEX,IACE,MAAMvF,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,4BAA6B,CACxDiB,OAAQ,OACRhB,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADzH,OAAYoH,IAE7B9E,KAAMoG,KAAKC,UAAU,CACnB6L,gBAAiBF,EAASE,gBAC1BC,YAAaH,EAASG,gBAIpB1M,QAAaR,EAASM,OAE5B,IAAKN,EAASI,GACZ,MAAM,IAAIpF,MAAMwF,EAAK9D,OAAS,6BAGhC0I,EAAW,kCACX+F,WAAW,KACT2B,IACAD,IACAgB,KACC,KAEL,CAAE,MAAOpN,GACPpB,EAASoB,aAAezF,MAAQyF,EAAIC,QAAU,4BAChD,CAAC,QACCxB,GAAa,EACf,CArC2B,GAgFO5G,UAAU,gBAAeI,SAAA,EAErDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAACmT,EAAAA,EAAK,CACJ3S,KAAMiU,EAAcE,QAAU,OAAS,WACvC7V,MAAOsV,EAASE,gBAChB5U,SAAUmV,EAAa,mBACvB/K,YAAY,8BACZnK,UAAU,QACVC,SAAU0G,KAEZtG,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IAAMwU,EAAyB,WACxCtV,UAAU,qFAAoFI,SAE7F0U,EAAcE,SAAU3U,EAAAA,EAAAA,KAACiT,EAAAA,IAAQ,CAACtT,UAAU,aAAeK,EAAAA,EAAAA,KAACkT,EAAAA,IAAK,CAACvT,UAAU,qBAMnFE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,kBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAACmT,EAAAA,EAAK,CACJ3S,KAAMiU,EAAcG,IAAM,OAAS,WACnC9V,MAAOsV,EAASG,YAChB7U,SAAUmV,EAAa,eACvB/K,YAAY,0BACZnK,UAAU,QACVC,SAAU0G,KAEZtG,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IAAMwU,EAAyB,OACxCtV,UAAU,qFAAoFI,SAE7F0U,EAAcG,KAAM5U,EAAAA,EAAAA,KAACiT,EAAAA,IAAQ,CAACtT,UAAU,aAAeK,EAAAA,EAAAA,KAACkT,EAAAA,IAAK,CAACvT,UAAU,kBAG7EK,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,oDAM5CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,0BAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAACmT,EAAAA,EAAK,CACJ3S,KAAMiU,EAAc1L,QAAU,OAAS,WACvCjK,MAAOsV,EAASI,gBAChB9U,SAAUmV,EAAa,mBACvB/K,YAAY,4BACZnK,UAAU,QACVC,SAAU0G,KAEZtG,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IAAMwU,EAAyB,WACxCtV,UAAU,qFAAoFI,SAE7F0U,EAAc1L,SAAU/I,EAAAA,EAAAA,KAACiT,EAAAA,IAAQ,CAACtT,UAAU,aAAeK,EAAAA,EAAAA,KAACkT,EAAAA,IAAK,CAACvT,UAAU,oBAMlFoE,IACC/D,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,uBAAsBI,SAAEgE,MAIxC6D,IACC5H,EAAAA,EAAAA,KAAA,OAAKL,UAAU,4DAA2DI,UACxEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,yBAAwBI,SAAE6H,OAK3C/H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,EAClCC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL/I,KAAK,SACLgJ,QAAQ,YACR/I,QAAS0U,EACTvV,SAAU0G,EACV3G,UAAU,SAAQI,SACnB,YAGDC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL/I,KAAK,SACLgJ,QAAQ,UACRlD,UAAWA,EACX1G,SAAU0G,EACV3G,UAAU,SAAQI,SAEjBuG,EAAY,cAAgB,+BA/HrB,MCvHTgP,EAAkD7V,IAIxD,IAJyD,OAC9DwU,EAAM,QACNC,EAAO,OACPqB,GACD9V,EACC,MAAO+V,EAAkBC,IAAuBpP,EAAAA,EAAAA,UAAS,KAClDC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCtC,EAAO2C,IAAYL,EAAAA,EAAAA,UAAwB,OAC3CqP,EAAMC,IAAWtP,EAAAA,EAAAA,UAA8B,WAqBhDuP,EAnBe,CACnBC,WAAY,CACVnV,MAAO,qBACPxB,YAAa,wFACb4W,YAAa,aACbC,WAAY,qBACZC,YAAa,8DACbC,SAAU,gCAEZC,OAAQ,CACNxV,MAAO,iBACPxB,YAAa,mGACb4W,YAAa,iBACbC,WAAY,yBACZC,YAAa,uHACbC,SAAU,6BAIcV,GACtBY,EAAsBX,IAAqBI,EAAOE,YAElDM,EAAenP,UACnB,GAAa,YAATyO,EAKJ,GAAKS,EAAL,CAKA5P,GAAa,GACbG,EAAS,MAET,IACE,MAAMQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAMsO,EAAOK,SAAU,CAC5C1N,OAAQ,OACRhB,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADzH,OAAYoH,IAE7B9E,KAAMoG,KAAKC,UAAU,CACnB4N,aAAcb,MAIZ3N,QAAaR,EAASM,OAE5B,IAAKN,EAASI,GACZ,MAAM,IAAIpF,MAAMwF,EAAK9D,OAAK,aAAAjE,OAAiByV,EAAM,aAInDpO,aAAamP,WAAW,cACxBnP,aAAamP,WAAW,aACxBxT,OAAOuI,SAASY,KAAO,QAEzB,CAAE,MAAOnE,GACPpB,EAASoB,aAAezF,MAAQyF,EAAIC,QAAO,aAAAjI,OAAgByV,EAAM,YACnE,CAAC,QACChP,GAAa,EACf,CAjCA,MAFEG,EAAS,gBAAD5G,OAAiB8V,EAAOE,YAAW,sBAL3CH,EAAQ,UA2CNR,EAAcA,KAClBM,EAAoB,IACpB/O,EAAS,MACTiP,EAAQ,WACRzB,KAQF,OAAKD,GAGHjU,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iFAAgFI,UAC7FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8EAA6EI,SAAA,EAE1FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEI,SAAA,EAC/EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAE6V,EAAOlV,YAE3DV,EAAAA,EAAAA,KAAA,UACES,QAAS0U,EACTxV,UAAU,mDAAkDI,UAE5DC,EAAAA,EAAAA,KAAC6P,EAAAA,IAAG,CAAClQ,UAAU,kBAKnBK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,MAAKI,SACR,YAAT2V,GACC7V,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BI,SAAA,EACzCC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,+CAC/BE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,gCAA+BI,SAAC,aAC9CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAE6V,EAAOI,uBAKnDnW,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAE6V,EAAO1W,cAEtC,WAAXqW,IACC1V,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCI,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,uBACHF,EAAAA,EAAAA,MAAA,MAAIF,UAAU,uCAAsCI,SAAA,EAClDC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,mCACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,+BACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,sCACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0CACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,qCAMZF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,EAClCC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,YACR/I,QAAS0U,EACTxV,UAAU,SAAQI,SACnB,YAGDC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,SACR/I,QAAS2V,EACTzW,UAAU,SAAQI,SACnB,oBAMLF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACuO,EAAAA,IAAO,CAAC5O,UAAU,0BACnBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,2BAA0BI,SAAC,6BAI7CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6BAA4BI,SAAA,CAAC,wCACJC,EAAAA,EAAAA,KAAA,QAAML,UAAU,mCAAkCI,SAAE6V,EAAOE,cAAmB,yBAGpH9V,EAAAA,EAAAA,KAACmT,EAAAA,EAAK,CACJ3S,KAAK,OACL1B,MAAO0W,EACP9V,SAAWZ,IACT2W,EAAoB3W,GACpB4H,EAAS,OAEXoD,YAAa8L,EAAOE,YACpBnW,UAAU,YACVC,SAAU0G,OAIbvC,IACC/D,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,uBAAsBI,SAAEgE,OAIzClE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,EAClCC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,YACR/I,QA5GG8V,KACjBZ,EAAQ,WACRjP,EAAS,OA2GK9G,SAAU0G,EACV3G,UAAU,SAAQI,SACnB,UAGDC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,SACR/I,QAAS2V,EACT9P,UAAWA,EACX1G,SAAU0G,IAAc6P,EACxBxW,UAAU,SAAQI,SAEjBuG,EAAY,gBAAkBsP,EAAOG,0BApHlC,M,kCC/DtB,MAAMS,EAAsC,CAC1C,CACEpS,GAAI,UACJnF,MAAO,UACPwX,KAAMC,EAAAA,IACNxX,YAAa,mCAEf,CACEkF,GAAI,cACJnF,MAAO,cACPwX,KAAME,EAAAA,IACNzX,YAAa,6BAEf,CACEkF,GAAI,gBACJnF,MAAO,gBACPwX,KAAMG,EAAAA,IACN1X,YAAa,iCAEf,CACEkF,GAAI,WACJnF,MAAO,WACPwX,KAAM/D,EAAAA,IACNxT,YAAa,kCAEf,CACEkF,GAAI,eACJnF,MAAO,eACPwX,KAAM1G,EAAAA,IACN7Q,YAAa,iCAEf,CACEkF,GAAI,UACJnF,MAAO,UACPwX,KAAM1G,EAAAA,IACN7Q,YAAa,gCAEf,CACEkF,GAAI,OACJnF,MAAO,kBACPwX,KAAMI,EAAAA,IACN3X,YAAa,yCAIJ4X,EAAyBA,KACpC,MAAOC,EAAeC,IAAoB3Q,EAAAA,EAAAA,UAAS,YAC5CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCtC,EAAO2C,IAAYL,EAAAA,EAAAA,UAAwB,OAC3CuB,EAAS6E,IAAcpG,EAAAA,EAAAA,UAAwB,OAChD,KAAE4Q,EAAI,OAAEC,IAAWC,EAAAA,EAAAA,KACnBC,GAAWC,EAAAA,EAAAA,OAGVC,EAAaC,IAAkBlR,EAAAA,EAAAA,UAAS,CAC7ChC,MAAU,OAAJ4S,QAAI,IAAJA,OAAI,EAAJA,EAAM5S,OAAQ,GACpBmT,OAAW,OAAJP,QAAI,IAAJA,OAAI,EAAJA,EAAMO,QAAS,GACtBC,IAAK,GACLC,OAAQ,QAGHtK,EAAauK,IAAkBtR,EAAAA,EAAAA,UAAS,CAC7CuR,MAAO,OACPC,SAAU,KACVC,gBAAgB,EAChBC,UAAU,EACVC,iBAAkB,aAClBC,gBAAiB,GACjBC,gBAAiBnZ,EAAAA,GAAgBI,UAG5BgZ,EAAeC,IAAoB/R,EAAAA,EAAAA,UAAS,CACjDgS,oBAAoB,EACpBP,gBAAgB,EAChBQ,gBAAgB,EAChBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,iBAAiB,KAGZC,EAAkBC,IAAuBtS,EAAAA,EAAAA,UAAS,CACvDuS,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAgB,MAIXC,EAA2BC,IAChC3S,EAAAA,EAAAA,WAAS,IACJ4S,EAAuBC,IAA4B7S,EAAAA,EAAAA,WAAS,IAC5D8S,EAAkBC,IAAuB/S,EAAAA,EAAAA,UAE9C,eAGFU,EAAAA,EAAAA,WAAU,KACiBE,WACvB,IACEV,GAAa,GAEb,MAAMW,EAAQC,aAAaC,QAAQ,cACnC,IAAKF,EAEH,YADAR,EAAS,2BAKX,MAAMW,QAAiBC,MAAM,wBAAyB,CACpDC,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,MAI7B,IAAKG,EAASI,GACZ,MAAM,IAAIpF,MAAM,mCAGlB,MAAMqF,QAAeL,EAASM,OAC9B,GAAID,EAAOE,SAAWF,EAAOG,KAAM,CAEjC,MAAMwR,EAAe3R,EAAOG,KAC5B8P,EAAe,CACbC,MAAOyB,EAAazB,MACpBC,SAAUwB,EAAaxB,SACvBC,eAAgBuB,EAAaC,gBAC7BvB,SAAUsB,EAAaE,UACvBvB,iBAAkBqB,EAAaG,mBAC/BvB,gBAAiBoB,EAAaI,iBAC9BvB,gBAAiBmB,EAAaK,kBAElC,CACF,CAAE,MAAO5R,GACPpB,EAAS,gCACT1D,QAAQe,MAAM,4BAA6B+D,EAC7C,CAAC,QACCvB,GAAa,EACf,GAGFoT,IACC,IAEH,MAAMC,EAAoB3S,UACxBV,GAAa,GACbG,EAAS,MACT+F,EAAW,MAEX,IACE,MAAM2H,EAAW,IAAIyF,SACrBzF,EAAS0F,OAAO,OAAQxC,EAAYjT,MACpC+P,EAAS0F,OAAO,MAAOxC,EAAYG,KAC/BH,EAAYI,QACdtD,EAAS0F,OAAO,SAAUxC,EAAYI,QAGxC,MAAMxQ,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,oBAAqB,CAChDiB,OAAQ,MACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,IAE3B9E,KAAMgS,IAGR,IAAK/M,EAASI,GACZ,MAAM,IAAIpF,MAAM,4BAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,OAFvB0I,EAAW,gCAIf,CAAE,MAAO3E,GACPpB,EAASoB,aAAezF,MAAQyF,EAAIC,QAAU,2BAChD,CAAC,QACCxB,GAAa,EACf,GAGIwT,EAAwB9S,UAE5B,IAAIX,EAAJ,CACAC,GAAa,GACbG,EAAS,MACT+F,EAAW,MAEX,IACE,MAAMvF,EAAQC,aAAaC,QAAQ,cAEnC,IAAKF,EAGH,OAFAR,EAAS,gCACTH,GAAa,GAKf,MAAMyT,EAAqB,CACzBpC,MAAOxK,EAAYwK,MACnBC,SAAUzK,EAAYyK,SACtByB,gBAAiBlM,EAAY0K,eAC7ByB,UAAWnM,EAAY2K,SACvByB,mBAAoBpM,EAAY4K,iBAChCyB,iBAAkBrM,EAAY6K,gBAC9ByB,iBAAkBtM,EAAY8K,iBAG1B7Q,QAAiBC,MAAM,wBAAyB,CACpDiB,OAAQ,MACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,GACzB,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAUuR,KAGvB,IAAK3S,EAASI,GACZ,MAAM,IAAIpF,MAAM,gCAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,OAFvB0I,EAAW,oCAIf,CAAE,MAAO3E,GACPpB,EACEoB,aAAezF,MAAQyF,EAAIC,QAAU,+BAEzC,CAAC,QACCxB,GAAa,EACf,CAlDqB,GAqDjB0T,EAA0BhT,UAC9BV,GAAa,GACbG,EAAS,MACT+F,EAAW,MAEX,IACE,MAAMvF,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,0BAA2B,CACtDiB,OAAQ,MACRhB,QAAS,CACPC,cAAc,UAAD1H,OAAYoH,GACzB,eAAgB,oBAElB9E,KAAMoG,KAAKC,UAAU0P,KAGvB,IAAK9Q,EAASI,GACZ,MAAM,IAAIpF,MAAM,0CAGlB,MAAMqF,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIvF,MAAMqF,EAAO3D,OAFvB0I,EAAW,8CAIf,CAAE,MAAO3E,GACPpB,EACEoB,aAAezF,MACXyF,EAAIC,QACJ,yCAER,CAAC,QACCxB,GAAa,EACf,GAGI2T,EAAejT,UACnB,IACEV,GAAa,SACP2Q,IACNE,EAAS,SACX,CAAE,MAAOrT,GACP2C,EAAS,uCACTH,GAAa,EACf,GAGI4T,EAAuBA,KAAA,IAAAC,EAAAC,EAAA,OAC3Bra,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,yBAKtDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,qBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,0IAAyII,UACjJ,OAAJkX,QAAI,IAAJA,GAAU,QAANmD,EAAJnD,EAAM5S,YAAI,IAAA+V,GAAW,QAAXC,EAAVD,EAAYlR,OAAO,UAAE,IAAAmR,OAAjB,EAAJA,EAAuB1R,gBAAiB,OAE3C9I,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAC0J,EAAAA,EAAM,CACLC,QAAQ,YACRC,KAAK,KACLhJ,QAASA,KACP,MAAM6Z,EAAQvY,SAASC,cAAc,SACrCsY,EAAM9Z,KAAO,OACb8Z,EAAMC,OAAS,UACfD,EAAME,SAAY5Q,IAAO,IAAD6Q,EACtB,MAAMC,EAA2C,QAAvCD,EAAI7Q,EAAEC,OAA4B8Q,aAAK,IAAAF,OAAA,EAApCA,EAAuC,GAChDC,GACFnD,GAAczJ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIwJ,GAAW,IAAEI,OAAQgD,MAG7CJ,EAAMjM,SACNtO,SAAA,EAEFC,EAAAA,EAAAA,KAAC4a,EAAAA,IAAQ,CAACjb,UAAU,iBAAiB,mBAGvCK,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,iCAK3CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAACmT,EAAAA,EAAK,CACJlU,MAAM,YACNH,MAAOwY,EAAYjT,KACnB3E,SAAWZ,GACTyY,GAAczJ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIwJ,GAAW,IAAEjT,KAAMvF,KAEzCgL,YAAY,0BAEd9J,EAAAA,EAAAA,KAACmT,EAAAA,EAAK,CACJlU,MAAM,gBACNuB,KAAK,QACL1B,MAAOwY,EAAYE,MACnB9X,SAAWZ,GACTyY,GAAczJ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIwJ,GAAW,IAAEE,MAAO1Y,KAE1CgL,YAAY,mBACZlK,UAAQ,KAEVC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oBAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAOwY,EAAYG,IACnB/X,SAAWkK,GACT2N,GAAczJ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIwJ,GAAW,IAAEG,IAAK7N,EAAEC,OAAO/K,SAEjDgL,YAAY,4BACZ+Q,KAAM,EACNlb,UAAU,mLAIhBK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CAAC9I,QAASmZ,EAAmBtT,UAAWA,EAAUvG,SAAC,yBAodlE,OACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CI,SAAA,EAC5DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CI,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,qCAAoCI,SAAC,cACnDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,SAAC,2CAI9BgE,IACClE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEgE,KAClC/D,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAMiG,EAAS,MACxB8C,QAAQ,YACRC,KAAK,KACL9J,UAAU,OAAMI,SACjB,eAMJ6H,IACC/H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iEAAgEI,SAAA,EAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACwN,EAAAA,IAAa,CAAC7N,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SAAC,gBAE/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,sBAAqBI,SAAE6H,KACpC5H,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACL9I,QAASA,IAAMgM,EAAW,MAC1BjD,QAAQ,YACRC,KAAK,KACL9J,UAAU,OAAMI,SACjB,gBAMLF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EAEpDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,UAC5BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sEAAqEI,UAClFC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvByW,EAAiBvW,IAAK6a,IACrB,MAAMC,EAAOD,EAAQrE,KACfuE,EAAWjE,IAAkB+D,EAAQ1W,GAE3C,OACEvE,EAAAA,EAAAA,MAAA,UAEEY,QAASA,IAAMuW,EAAiB8D,EAAQ1W,IACxCzE,UAAS,6KAAAG,OAILkb,EACI,kEACA,8DAA6D,4BAEnEjb,SAAA,EAEFC,EAAAA,EAAAA,KAAC+a,EAAI,CAACpb,UAAU,aAChBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,QAAML,UAAU,oBAAmBI,SAChC+a,EAAQ7b,SAEXe,EAAAA,EAAAA,KAAA,QAAML,UAAU,uCAAsCI,SACnD+a,EAAQ5b,mBAlBR4b,EAAQ1W,aA6BzBpE,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,UAC5BC,EAAAA,EAAAA,KAACsK,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGwQ,EAAG,IAC1BtQ,QAAS,CAAEF,QAAS,EAAGwQ,EAAG,GAC1BrQ,WAAY,CAAEC,SAAU,IACxBlL,UAAU,sEAAqEI,SAjHrEmb,MACpB,OAAQnE,GACN,IAAK,UAcL,QACE,OAAOoD,IAbT,IAAK,cACH,OA3bJna,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,qBAGtDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,WAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEY,QAASA,IACPkX,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIV,GAAW,IAAEwK,MAAO,UAE1CjY,UAAS,6EAAAG,OACe,SAAtBsN,EAAYwK,MACR,wDACA,uDACH7X,SAAA,EAEHC,EAAAA,EAAAA,KAACmb,EAAAA,IAAM,CAACxb,UAAU,aAClBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,aAERF,EAAAA,EAAAA,MAAA,UACEY,QAASA,IACPkX,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIV,GAAW,IAAEwK,MAAO,WAE1CjY,UAAS,6EAAAG,OACe,UAAtBsN,EAAYwK,MACR,wDACA,uDAENhY,UAAQ,EAAAG,SAAA,EAERC,EAAAA,EAAAA,KAACob,EAAAA,IAAK,CAACzb,UAAU,aACjBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kCAMZF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,cAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOsO,EAAYyK,SACnBnY,SAAWkK,GACT+N,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIV,GAAW,IAAEyK,SAAUjO,EAAEC,OAAO/K,SAEtDa,UAAU,0KAAyKI,SAAA,EAEnLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,KAAIiB,SAAC,aACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,KAAKc,UAAQ,EAAAG,SAAC,2BAG5BC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,KAAKc,UAAQ,EAAAG,SAAC,gCAOhCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,wBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPkX,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXV,GAAW,IACd4K,iBAAkB,gBAGtBrY,UAAS,6EAAAG,OAC0B,eAAjCsN,EAAY4K,iBACR,wDACA,uDACHjY,UAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBAERC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPkX,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIV,GAAW,IAAE4K,iBAAkB,UAErDrY,UAAS,6EAAAG,OAC0B,SAAjCsN,EAAY4K,iBACR,wDACA,uDACHjY,UAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBAMZF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,wCAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOsO,EAAY6K,gBACnBvY,SAAWkK,GACT+N,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXV,GAAW,IACd6K,gBAAiBoD,SAASzR,EAAEC,OAAO/K,UAGvCa,UAAU,0KAAyKI,SAAA,EAEnLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,YACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,qBAKvBC,EAAAA,EAAAA,KAACR,EAAAA,EAAkB,CACjBV,MAAOsO,EAAY8K,gBACnBxY,SAAW4b,GACT3D,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIV,GAAW,IAAE8K,gBAAiBoD,KAEpDrc,MAAM,2BACNU,UAAU,4CAIZE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,oCAAmCI,SAAC,qBAGrDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,wCAIvCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPkX,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXV,GAAW,IACd0K,gBAAiB1K,EAAY0K,kBAGjCnY,UAAS,6EAAAG,OACPsN,EAAY0K,eAAiB,iBAAmB,eAC/C/X,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPsN,EAAY0K,eACR,gBACA,yBAMZjY,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,oCAAmCI,SAAC,eAGrDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,yCAIvCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPkX,GAAc7J,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXV,GAAW,IACd2K,UAAW3K,EAAY2K,YAG3BpY,UAAS,6EAAAG,OACPsN,EAAY2K,SAAW,iBAAmB,eACzChY,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPsN,EAAY2K,SAAW,gBAAkB,+BAOrD/X,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CAAC9I,QAASsZ,EAAuBzT,UAAWA,EAAUvG,SAAC,4BAyPlE,IAAK,gBACH,OAjPJC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,2BAGtDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvB2N,OAAOC,QAAQwK,GAAelY,IAAIR,IAAA,IAAEmO,EAAK9O,GAAMW,EAAA,OAC9CI,EAAAA,EAAAA,MAAA,OAAeF,UAAU,oCAAmCI,SAAA,EAC1DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAC5D6N,EAAIC,QAAQ,WAAY,OAAOvF,UAElCzI,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CACzB,uBAAR6N,GACC,sCACO,mBAARA,GACC,uCACO,mBAARA,GACC,wCACO,oBAARA,GAA6B,2BACrB,6BAARA,GACC,4CACO,oBAARA,GACC,iDAGN5N,EAAAA,EAAAA,KAAA,UACES,QAASA,IACP2X,GAAgBtK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIqK,GAAa,IAAE,CAACvK,IAAO9O,KAE/Ca,UAAS,6EAAAG,OACPhB,EAAQ,iBAAmB,eAC1BiB,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPhB,EAAQ,gBAAkB,uBA7BxB8O,QAoCd5N,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CAAC9I,QAASwZ,EAAyB3T,UAAWA,EAAUvG,SAAC,sCAsMpE,IAAK,WACH,OA9LJC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,uBAGtDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBC,EAAAA,EAAAA,KAACwQ,EAAa,CACZC,QAASiI,EAAiBE,iBAC1BlI,SAAWD,GACTkI,GAAmB7K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAChB4K,GAAgB,IACnBE,iBAAkBnI,QAMxB5Q,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,wBAC5CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,oCAAmCI,SAAC,yBAGrDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,yDAIvCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPkY,GAAmB7K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAChB4K,GAAgB,IACnBG,oBAAqBH,EAAiBG,sBAG1ClZ,UAAS,6EAAAG,OACP4Y,EAAiBG,mBACb,iBACA,eACH9Y,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACP4Y,EAAiBG,mBACb,gBACA,yBAMZhZ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,+BAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAO4Z,EAAiBI,eACxBpZ,SAAWkK,GACT+O,GAAmB7K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAChB4K,GAAgB,IACnBI,eAAgBuC,SAASzR,EAAEC,OAAO/K,UAGtCa,UAAU,0KAAyKI,SAAA,EAEnLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,YACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,IAAIiB,SAAC,aACpBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,IAAIiB,SAAC,aACpBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,KAAKiB,SAAC,WACrBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,YACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,8BAO1BF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAC+T,EAAAA,IAAK,CAACpU,UAAU,8BACjBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,wBAEzCC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,sDAG1CC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,YACR/I,QAASA,IAAMuY,GAA6B,GAAMjZ,SACnD,wBAMHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAACub,EAAAA,IAAQ,CAAC5b,UAAU,6BACpBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,iBAEzCC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,6CAG1CC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,YACR/I,QAASyZ,EACTta,SAAU0G,EAAUvG,SAEnBuG,EAAY,iBAAmB,iBAKpCzG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wDAAuDI,SAAA,EACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAACiJ,EAAAA,IAAmB,CAACtJ,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,oBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,gCAA+BI,SAAC,0BAG9CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,uEAI1CC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,YACRC,KAAK,KACLhJ,QAASA,KACP2Y,EAAoB,cACpBF,GAAyB,IACzBnZ,SACH,2BAKHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,gCAA+BI,SAAC,sBAG9CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,4FAI1CC,EAAAA,EAAAA,KAACuJ,EAAAA,EAAM,CACLC,QAAQ,SACRC,KAAK,KACLhJ,QAASA,KACP2Y,EAAoB,UACpBF,GAAyB,IACzBnZ,SACH,oCA2BX,IAAK,eACH,OAjBkCC,EAAAA,EAAAA,KAACgG,EAAsB,IAkB3D,IAAK,UACH,OAjB6BhG,EAAAA,EAAAA,KAACwO,EAAe,IAkB/C,IAAK,OACH,OAjB0BxO,EAAAA,EAAAA,KAACoM,EAAc,MAqHlC8O,IANInE,YAab/W,EAAAA,EAAAA,KAACgU,EAAmB,CAClBC,OAAQ8E,EACR7E,QAASA,IAAM8E,GAA6B,GAC5C7E,UAAWA,KAETnR,QAAQwY,IAAI,qCAIhBxb,EAAAA,EAAAA,KAACsV,EAAe,CACdrB,OAAQgF,EACR/E,QAASA,IAAMgF,GAAyB,GACxC3D,OAAQ4D,O,2CC58BT,MAAMsC,EAA+B,CAC1C,CACErX,GAAI,OACJC,KAAM,OACNC,MAAO,EACPuH,QAAS,GACTrH,SAAU,CACR,uBACA,sBACA,8BACA,oBACA,mBAEFkX,mBAAehW,GAEjB,CACEtB,GAAI,gBACJC,KAAM,gBACNC,MAAO,GACPuH,QAAS,IACTrH,SAAU,CACR,wBACA,yBACA,+BACA,gBACA,qBACA,wBAEFkX,cAAe,kCAEjB,CACEtX,GAAI,YACJC,KAAM,YACNC,MAAO,GACPuH,QAAS,IACTrH,SAAU,CACR,wBACA,wBACA,+BACA,mBACA,qBACA,oBACA,0BAEFmX,WAAW,EACXD,cAAe,kCAEjB,CACEtX,GAAI,eACJC,KAAM,eACNC,MAAO,IACPuH,QAAS,IACTrH,SAAU,CACR,wBACA,wBACA,+BACA,mBACA,qBACA,oBACA,qBACA,cAEFkX,cAAe,kCAEjB,CACEtX,GAAI,cACJC,KAAM,cACNC,MAAO,IACPuH,QAAS,KACTrH,SAAU,CACR,0BACA,wBACA,4BACA,oBACA,qBACA,oBACA,qBACA,aACA,uBAEFkX,cAAe,mCAuON/P,EAAmC,CAC9C,CACEvH,GAAI,eACJC,KAAM,eACNwH,QAAS,GACTvH,MAAO,GACPwH,eAAgB,GAChB5M,YAAa,+BACbwc,cAAe,kCAEjB,CACEtX,GAAI,aACJC,KAAM,aACNwH,QAAS,IACTvH,MAAO,GACPwH,eAAgB,IAChB5M,YAAa,6BACbwc,cAAe,kCAEjB,CACEtX,GAAI,aACJC,KAAM,aACNwH,QAAS,IACTvH,MAAO,GACPwH,eAAgB,IAChB5M,YAAa,kBACbwc,cAAe,kCAEjB,CACEtX,GAAI,YACJC,KAAM,YACNwH,QAAS,IACTvH,MAAO,IACPwH,eAAgB,GAChB5M,YAAa,kCACbwc,cAAe,kC,uECzPZ,IAAK3c,EAAe,SAAfA,GAAe,OAAfA,EAAe,YAAfA,EAAe,gBAAfA,EAAe,YAAfA,EAAe,kBAAfA,EAAe,oBAAfA,EAAe,UAAfA,CAAe,MASf6c,EAAa,SAAbA,GAAa,OAAbA,EAAa,cAAbA,EAAa,gBAAbA,EAAa,YAAbA,CAAa,MAiMlB,MAAMC,EAA2BP,IACU,CAC9C,CAACvc,EAAgBC,MAAO,OACxB,CAACD,EAAgBI,QAAS,SAC1B,CAACJ,EAAgBK,MAAO,OACxB,CAACL,EAAgBM,SAAU,UAC3B,CAACN,EAAgBO,UAAW,WAC5B,CAACP,EAAgBQ,KAAM,OAEX+b,IA+CHQ,EAA2BR,IACW,CAC/C,CAACvc,EAAgBC,MAAO,EACxB,CAACD,EAAgBI,QAAS,EAC1B,CAACJ,EAAgBK,MAAO,EACxB,CAACL,EAAgBM,SAAU,EAC3B,CAACN,EAAgBO,UAAW,EAC5B,CAACP,EAAgBQ,KAAM,GAEV+b,IAGJS,EAA2BC,IACtC,OAAQA,GACN,KAAK,EAEL,KAAK,EACH,OAAOjd,EAAgBC,KACzB,KAAK,EAUL,QACE,OAAOD,EAAgBI,OATzB,KAAK,EACH,OAAOJ,EAAgBK,KACzB,KAAK,EACH,OAAOL,EAAgBM,QACzB,KAAK,EACH,OAAON,EAAgBO,SACzB,KAAK,EACH,OAAOP,EAAgBQ,K", "sources": ["components/common/DifficultySelector.tsx", "../../node_modules/@stripe/stripe-js/dist/index.mjs", "components/settings/SubscriptionManagement.tsx", "components/settings/DataManagement.tsx", "components/settings/EnhancedBilling.tsx", "components/settings/TwoFactorAuth.tsx", "components/settings/ChangePasswordModal.tsx", "components/settings/DangerZoneModal.tsx", "pages/SettingsPage.tsx", "shared/constants.ts", "shared/types.ts"], "sourcesContent": ["import React from \"react\";\nimport { DifficultyLevel } from \"../../shared/types\";\n\ninterface DifficultySelectorProps {\n  value: DifficultyLevel;\n  onChange: (difficulty: DifficultyLevel) => void;\n  className?: string;\n  disabled?: boolean;\n  label?: string;\n}\n\nconst difficultyOptions = [\n  {\n    value: DifficultyLevel.EASY,\n    label: \"Easy\",\n    description: \"Basic facts and definitions\",\n  },\n  {\n    value: DifficultyLevel.MEDIUM,\n    label: \"Medium\",\n    description: \"Moderate understanding required\",\n  },\n  {\n    value: DifficultyLevel.HARD,\n    label: \"Hard\",\n    description: \"Deep analysis and critical thinking\",\n  },\n  {\n    value: DifficultyLevel.COLLEGE,\n    label: \"College\",\n    description: \"Undergraduate level complexity\",\n  },\n  {\n    value: DifficultyLevel.GRADUATE,\n    label: \"Graduate\",\n    description: \"Advanced graduate study\",\n  },\n  {\n    value: DifficultyLevel.PHD,\n    label: \"PhD\",\n    description: \"Research-level expertise\",\n  },\n];\n\nconst getDifficultyColor = (difficulty: DifficultyLevel): string => {\n  switch (difficulty) {\n    case DifficultyLevel.EASY:\n      return \"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50\";\n    case DifficultyLevel.MEDIUM:\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\n    case DifficultyLevel.HARD:\n      return \"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50\";\n    case DifficultyLevel.COLLEGE:\n      return \"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50\";\n    case DifficultyLevel.GRADUATE:\n      return \"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50\";\n    case DifficultyLevel.PHD:\n      return \"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50\";\n    default:\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\n  }\n};\n\nconst getSelectedDifficultyColor = (difficulty: DifficultyLevel): string => {\n  switch (difficulty) {\n    case DifficultyLevel.EASY:\n      return \"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20\";\n    case DifficultyLevel.MEDIUM:\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\n    case DifficultyLevel.HARD:\n      return \"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20\";\n    case DifficultyLevel.COLLEGE:\n      return \"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20\";\n    case DifficultyLevel.GRADUATE:\n      return \"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20\";\n    case DifficultyLevel.PHD:\n      return \"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20\";\n    default:\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\n  }\n};\n\nexport const DifficultySelector: React.FC<DifficultySelectorProps> = ({\n  value,\n  onChange,\n  className = \"\",\n  disabled = false,\n  label = \"Difficulty Level\",\n}) => {\n  return (\n    <div className={`space-y-3 ${className}`}>\n      <label className=\"block text-sm font-medium text-text-primary\">\n        {label}\n      </label>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n        {difficultyOptions.map((option) => {\n          const isSelected = value === option.value;\n          const colorClasses = isSelected\n            ? getSelectedDifficultyColor(option.value)\n            : getDifficultyColor(option.value);\n\n          return (\n            <button\n              key={option.value}\n              type=\"button\"\n              onClick={() => !disabled && onChange(option.value)}\n              disabled={disabled}\n              className={`\n                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu\n                ${colorClasses}\n                ${\n                  disabled\n                    ? \"opacity-50 cursor-not-allowed\"\n                    : \"cursor-pointer hover:scale-105\"\n                }\n                ${\n                  isSelected\n                    ? \"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary\"\n                    : \"\"\n                }\n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary\n              `}\n              title={option.description}\n              aria-pressed={isSelected}\n            >\n              <div className=\"text-center\">\n                <div className=\"font-semibold\">{option.label}</div>\n                <div\n                  className={`text-xs mt-1 ${\n                    isSelected ? \"text-white/90\" : \"text-text-secondary\"\n                  }`}\n                >\n                  {option.description}\n                </div>\n              </div>\n              {isSelected && (\n                <div className=\"absolute top-2 right-2\">\n                  <svg\n                    className=\"w-4 h-4\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n              )}\n            </button>\n          );\n        })}\n      </div>\n      <p className=\"text-xs text-text-muted\">\n        Select the appropriate difficulty level for your flashcards. This\n        affects the complexity of questions and answers generated.\n      </p>\n    </div>\n  );\n};\n\nexport default DifficultySelector;\n", "var RELEASE_TRAIN = 'basil';\n\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n  return version === 3 ? 'v3' : version;\n};\n\nvar ORIGIN = 'https://js.stripe.com';\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/\").concat(RELEASE_TRAIN, \"/stripe.js\");\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\n\nvar isStripeJSURL = function isStripeJSURL(url) {\n  return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\n\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(ORIGIN, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!isStripeJSURL(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"7.4.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise$1 = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function (cause) {\n    reject(new Error('Failed to load Stripe.js', {\n      cause: cause\n    }));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise$1 !== null) {\n    return stripePromise$1;\n  }\n\n  stripePromise$1 = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise$1[\"catch\"](function (error) {\n    stripePromise$1 = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var pk = args[0];\n  var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n\n  var version = runtimeVersionToUrlVersion(maybeStripe.version);\n  var expectedVersion = RELEASE_TRAIN;\n\n  if (isTestKey && version !== expectedVersion) {\n    console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"7.4.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar stripePromise;\nvar loadCalled = false;\n\nvar getStripePromise = function getStripePromise() {\n  if (stripePromise) {\n    return stripePromise;\n  }\n\n  stripePromise = loadScript(null)[\"catch\"](function (error) {\n    // clear cache on error\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n  return stripePromise;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\n\n\nPromise.resolve().then(function () {\n  return getStripePromise();\n})[\"catch\"](function (error) {\n  if (!loadCalled) {\n    console.warn(error);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n\n  return getStripePromise().then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\nexport { loadStripe };\n", "import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  HiExclamationCircle,\n  HiInformationCircle,\n  HiShoppingCart,\n  HiTag,\n} from \"react-icons/hi\";\nimport { loadStripe } from \"@stripe/stripe-js\";\nimport { Button } from \"../common/Button\";\nimport { CREDIT_PACKAGES, CreditPackage } from \"../../shared/constants\";\n\ninterface SubscriptionPlan {\n  id: string;\n  name: string;\n  price: number;\n  interval: \"month\" | \"year\";\n  features: string[];\n  popular?: boolean;\n  current?: boolean;\n}\n\ninterface SubscriptionData {\n  currentPlan: SubscriptionPlan | null;\n  status: \"active\" | \"canceled\" | \"past_due\" | \"trialing\";\n  nextBillingDate?: string;\n  cancelAtPeriodEnd?: boolean;\n}\n\nconst subscriptionPlans: SubscriptionPlan[] = [\n  {\n    id: \"free\",\n    name: \"Free\",\n    price: 0,\n    interval: \"month\",\n    features: [\n      \"25 credits per month\",\n      \"Basic AI generation\",\n      \"Document upload (up to 5MB)\",\n      \"Community support\",\n      \"Basic analytics\",\n      \"Perfect for trying out ChewyAI\",\n    ],\n  },\n  {\n    id: \"study_starter\",\n    name: \"Study Starter\",\n    price: 29,\n    interval: \"month\",\n    features: [\n      \"150 credits per month\",\n      \"Advanced AI generation\",\n      \"Document upload (up to 10MB)\",\n      \"Email support\",\n      \"Detailed analytics\",\n      \"Export functionality\",\n      \"Perfect for high school & early college\",\n    ],\n  },\n  {\n    id: \"study_pro\",\n    name: \"Study Pro\",\n    price: 59,\n    interval: \"month\",\n    popular: true,\n    features: [\n      \"350 credits per month\",\n      \"Advanced AI generation\",\n      \"Document upload (up to 25MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Export functionality\",\n      \"Perfect for college students\",\n    ],\n  },\n  {\n    id: \"study_master\",\n    name: \"Study Master\",\n    price: 119,\n    interval: \"month\",\n    features: [\n      \"750 credits per month\",\n      \"Premium AI generation\",\n      \"Document upload (up to 50MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Team collaboration\",\n      \"API access\",\n      \"Perfect for graduate students & researchers\",\n    ],\n  },\n  {\n    id: \"study_elite\",\n    name: \"Study Elite\",\n    price: 239,\n    interval: \"month\",\n    features: [\n      \"1,500 credits per month\",\n      \"Premium AI generation\",\n      \"Unlimited document upload\",\n      \"Dedicated support\",\n      \"Advanced analytics\",\n      \"Team collaboration\",\n      \"API access\",\n      \"White-label options\",\n      \"Perfect for study groups & institutions\",\n    ],\n  },\n];\n\n// Initialize Stripe\nconst stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY!);\n\nexport const SubscriptionManagement: React.FC = () => {\n  const [subscriptionData, setSubscriptionData] =\n    useState<SubscriptionData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isChanging, setIsChanging] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [couponCode, setCouponCode] = useState(\"\");\n  const [appliedCoupon, setAppliedCoupon] = useState<{\n    code: string;\n    discount: number;\n    type: \"percent\" | \"amount\";\n  } | null>(null);\n\n  useEffect(() => {\n    fetchSubscriptionData();\n  }, []);\n\n  const fetchSubscriptionData = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/subscription\", {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch subscription data\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        setSubscriptionData(result.data);\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to load subscription data\"\n      );\n      // Set mock data for development - default to free plan\n      setSubscriptionData({\n        currentPlan: subscriptionPlans[0], // Free plan\n        status: \"active\",\n        nextBillingDate: new Date(\n          Date.now() + 30 * 24 * 60 * 60 * 1000\n        ).toISOString(),\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const validateCouponCode = async (code: string) => {\n    if (!code.trim()) {\n      setAppliedCoupon(null);\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/stripe/validate-coupon\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ coupon: code.trim().toUpperCase() }),\n      });\n\n      const result = await response.json();\n      if (result.success && result.data.valid) {\n        setAppliedCoupon({\n          code: result.data.code,\n          discount: result.data.discount,\n          type: result.data.type,\n        });\n      } else {\n        setAppliedCoupon(null);\n      }\n    } catch (err) {\n      setAppliedCoupon(null);\n    }\n  };\n\n  const handleStripeCheckout = async (planId: string) => {\n    if (planId === \"free\") {\n      await handleDowngradeToFree();\n      return;\n    }\n\n    setIsChanging(true);\n    setError(null);\n\n    try {\n      const stripe = await stripePromise;\n      if (!stripe) {\n        throw new Error(\"Stripe failed to load\");\n      }\n\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/stripe/create-checkout-session\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          planId,\n          coupon: appliedCoupon?.code || null,\n          successUrl: `${window.location.origin}/settings?tab=subscription&success=true`,\n          cancelUrl: `${window.location.origin}/settings?tab=subscription&canceled=true`,\n        }),\n      });\n\n      const result = await response.json();\n      if (!result.success) {\n        throw new Error(result.error || \"Failed to create checkout session\");\n      }\n\n      // Redirect to Stripe Checkout\n      const { error } = await stripe.redirectToCheckout({\n        sessionId: result.data.sessionId,\n      });\n\n      if (error) {\n        throw new Error(error.message || \"Stripe checkout failed\");\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to start checkout process\"\n      );\n    } finally {\n      setIsChanging(false);\n    }\n  };\n\n  const handleDowngradeToFree = async () => {\n    if (\n      !confirm(\n        \"Are you sure you want to downgrade to the Free plan? You will lose access to premium features immediately.\"\n      )\n    ) {\n      return;\n    }\n\n    setIsChanging(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/subscription/cancel\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ immediate: true }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to downgrade to free plan\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        await fetchSubscriptionData();\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to downgrade to free plan\"\n      );\n    } finally {\n      setIsChanging(false);\n    }\n  };\n\n  const handleCancelSubscription = async () => {\n    if (\n      !confirm(\n        \"Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.\"\n      )\n    ) {\n      return;\n    }\n\n    setIsChanging(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/subscription/cancel\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to cancel subscription\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        await fetchSubscriptionData();\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to cancel subscription\"\n      );\n    } finally {\n      setIsChanging(false);\n    }\n  };\n\n  const handleReactivateSubscription = async () => {\n    setIsChanging(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/subscription/reactivate\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to reactivate subscription\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        await fetchSubscriptionData();\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to reactivate subscription\"\n      );\n    } finally {\n      setIsChanging(false);\n    }\n  };\n\n  const handlePurchaseCredits = async (packageId: string) => {\n    setIsChanging(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/credits/purchase\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ packageId }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to purchase credits\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        // Redirect to payment if needed, or handle success\n        if (result.paymentUrl) {\n          window.location.href = result.paymentUrl;\n        }\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to purchase credits\"\n      );\n    } finally {\n      setIsChanging(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 w-48 bg-gray-600 rounded mb-4\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {[1, 2, 3].map((i) => (\n              <div key={i} className=\"h-64 bg-gray-600 rounded-lg\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\n          Subscription Management\n        </h3>\n\n        {error && (\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\n              <span className=\"text-red-400 font-medium\">Error</span>\n            </div>\n            <p className=\"text-red-300 mt-1\">{error}</p>\n          </div>\n        )}\n\n        {/* Current Subscription Status */}\n        {subscriptionData && (\n          <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h4 className=\"text-lg font-medium text-white\">Current Plan</h4>\n                <p className=\"text-gray-400\">\n                  {subscriptionData.currentPlan?.name || \"No active plan\"}\n                </p>\n              </div>\n              <div className=\"text-right\">\n                <div\n                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\n                    subscriptionData.status === \"active\"\n                      ? \"bg-green-500/20 text-green-400\"\n                      : subscriptionData.status === \"canceled\"\n                      ? \"bg-red-500/20 text-red-400\"\n                      : subscriptionData.status === \"past_due\"\n                      ? \"bg-yellow-500/20 text-yellow-400\"\n                      : \"bg-blue-500/20 text-blue-400\"\n                  }`}\n                >\n                  {subscriptionData.status.charAt(0).toUpperCase() +\n                    subscriptionData.status.slice(1)}\n                </div>\n              </div>\n            </div>\n\n            {subscriptionData.nextBillingDate && (\n              <div className=\"flex items-center space-x-2 text-gray-400 text-sm\">\n                <HiInformationCircle className=\"w-4 h-4\" />\n                <span>\n                  {subscriptionData.cancelAtPeriodEnd\n                    ? `Access ends on ${new Date(\n                        subscriptionData.nextBillingDate\n                      ).toLocaleDateString()}`\n                    : `Next billing date: ${new Date(\n                        subscriptionData.nextBillingDate\n                      ).toLocaleDateString()}`}\n                </span>\n              </div>\n            )}\n\n            {subscriptionData.currentPlan?.id !== \"free\" && (\n              <div className=\"mt-4 flex space-x-3\">\n                {subscriptionData.cancelAtPeriodEnd ? (\n                  <Button\n                    onClick={handleReactivateSubscription}\n                    isLoading={isChanging}\n                    variant=\"primary\"\n                    size=\"sm\"\n                  >\n                    Reactivate Subscription\n                  </Button>\n                ) : (\n                  <Button\n                    onClick={handleCancelSubscription}\n                    isLoading={isChanging}\n                    variant=\"danger\"\n                    size=\"sm\"\n                  >\n                    Cancel Subscription\n                  </Button>\n                )}\n                <Button\n                  onClick={fetchSubscriptionData}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                >\n                  <HiRefresh className=\"w-4 h-4 mr-2\" />\n                  Refresh\n                </Button>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Coupon Code Section */}\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\n          <h4 className=\"text-lg font-medium text-white mb-4 flex items-center\">\n            <HiTag className=\"w-5 h-5 mr-2 text-primary-400\" />\n            Coupon Code\n          </h4>\n          <div className=\"flex space-x-3\">\n            <div className=\"flex-1\">\n              <input\n                type=\"text\"\n                value={couponCode}\n                onChange={(e) => {\n                  setCouponCode(e.target.value);\n                  validateCouponCode(e.target.value);\n                }}\n                placeholder=\"Enter coupon code\"\n                className=\"w-full bg-background-secondary border border-border-primary rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n              />\n            </div>\n            <Button\n              onClick={() => validateCouponCode(couponCode)}\n              variant=\"secondary\"\n              size=\"sm\"\n              disabled={!couponCode.trim()}\n            >\n              Apply\n            </Button>\n          </div>\n          {appliedCoupon && (\n            <div className=\"mt-3 p-3 bg-green-500/20 border border-green-500/30 rounded-lg\">\n              <div className=\"flex items-center space-x-2\">\n                <HiCheck className=\"w-4 h-4 text-green-400\" />\n                <span className=\"text-green-400 font-medium\">\n                  Coupon Applied: {appliedCoupon.code}\n                </span>\n              </div>\n              <p className=\"text-green-300 text-sm mt-1\">\n                {appliedCoupon.type === \"percent\"\n                  ? `${appliedCoupon.discount}% off your subscription!`\n                  : `$${appliedCoupon.discount} off your subscription!`}\n              </p>\n            </div>\n          )}\n        </div>\n\n        {/* Available Plans */}\n        <div>\n          <h4 className=\"text-lg font-medium text-white mb-4\">Monthly Plans</h4>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4\">\n            {subscriptionPlans.map((plan) => {\n              const isCurrent = subscriptionData?.currentPlan?.id === plan.id;\n              const finalPrice =\n                appliedCoupon && plan.price > 0\n                  ? appliedCoupon.type === \"percent\"\n                    ? plan.price * (1 - appliedCoupon.discount / 100)\n                    : Math.max(0, plan.price - appliedCoupon.discount)\n                  : plan.price;\n\n              return (\n                <motion.div\n                  key={plan.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3 }}\n                  className={`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${\n                    plan.popular\n                      ? \"border-primary-500 ring-2 ring-primary-500/20\"\n                      : isCurrent\n                      ? \"border-green-500 ring-2 ring-green-500/20\"\n                      : \"border-border-primary hover:border-gray-500\"\n                  }`}\n                >\n                  {plan.popular && (\n                    <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                      <div className=\"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1\">\n                        <HiStar className=\"w-3 h-3\" />\n                        <span>Most Popular</span>\n                      </div>\n                    </div>\n                  )}\n\n                  {isCurrent && (\n                    <div className=\"absolute -top-3 right-4\">\n                      <div className=\"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1\">\n                        <HiCheck className=\"w-3 h-3\" />\n                        <span>Current</span>\n                      </div>\n                    </div>\n                  )}\n\n                  <div className=\"text-center mb-6\">\n                    <h5 className=\"text-xl font-semibold text-white mb-2\">\n                      {plan.name}\n                    </h5>\n                    <div className=\"text-3xl font-bold text-white\">\n                      {appliedCoupon &&\n                      plan.price > 0 &&\n                      finalPrice !== plan.price ? (\n                        <>\n                          <span className=\"line-through text-gray-500 text-xl mr-2\">\n                            ${plan.price}\n                          </span>\n                          ${finalPrice.toFixed(0)}\n                        </>\n                      ) : (\n                        `$${plan.price}`\n                      )}\n                      <span className=\"text-lg text-gray-400\">\n                        /{plan.interval}\n                      </span>\n                    </div>\n                    {appliedCoupon && plan.price > 0 && finalPrice === 0 && (\n                      <div className=\"text-green-400 text-sm font-medium mt-1\">\n                        FREE with {appliedCoupon.code}!\n                      </div>\n                    )}\n                  </div>\n\n                  <ul className=\"space-y-3 mb-6\">\n                    {plan.features.map((feature, index) => (\n                      <li key={index} className=\"flex items-center space-x-2\">\n                        <HiCheck className=\"w-4 h-4 text-green-400 flex-shrink-0\" />\n                        <span className=\"text-gray-300 text-sm\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  <Button\n                    onClick={() => handleStripeCheckout(plan.id)}\n                    disabled={isCurrent || isChanging}\n                    isLoading={isChanging}\n                    variant={\n                      plan.popular\n                        ? \"primary\"\n                        : isCurrent\n                        ? \"secondary\"\n                        : \"secondary\"\n                    }\n                    className=\"w-full\"\n                  >\n                    {isCurrent\n                      ? \"Current Plan\"\n                      : plan.id === \"free\"\n                      ? \"Downgrade to Free\"\n                      : `Upgrade to ${plan.name}`}\n                  </Button>\n                </motion.div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* One-Time Credit Packages */}\n        <div className=\"mt-8\">\n          <h4 className=\"text-lg font-medium text-white mb-2\">\n            One-Time Credit Packages\n          </h4>\n          <p className=\"text-gray-400 mb-4\">\n            Purchase credits that never expire. Perfect for irregular usage or\n            exam preparation.\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            {CREDIT_PACKAGES.map((pkg: CreditPackage) => (\n              <motion.div\n                key={pkg.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n                className=\"bg-background-secondary rounded-lg p-5 border border-border-primary hover:border-gray-500 transition-all duration-200\"\n              >\n                <div className=\"text-center mb-4\">\n                  <h5 className=\"text-lg font-semibold text-white mb-1\">\n                    {pkg.name}\n                  </h5>\n                  <div className=\"text-2xl font-bold text-white\">\n                    ${pkg.price}\n                  </div>\n                  <div className=\"text-sm text-gray-400\">\n                    {pkg.credits} credits\n                  </div>\n                  <div className=\"text-xs text-primary-400 mt-1\">\n                    ${pkg.valuePerCredit.toFixed(2)} per credit\n                  </div>\n                </div>\n\n                <p className=\"text-gray-300 text-sm text-center mb-4\">\n                  {pkg.description}\n                </p>\n\n                <Button\n                  onClick={() => handlePurchaseCredits(pkg.id)}\n                  disabled={isChanging}\n                  isLoading={isChanging}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  className=\"w-full\"\n                >\n                  <HiShoppingCart className=\"w-4 h-4 mr-2\" />\n                  Purchase\n                </Button>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\r\nimport {\r\n  HiDownload,\r\n  HiExclamation<PERSON>ircle,\r\n  HiCheckCircle,\r\n  HiTrash,\r\n  HiRefresh\r\n} from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\n\r\ninterface ExportData {\r\n  studySets: boolean;\r\n  flashcards: boolean;\r\n  quizzes: boolean;\r\n  analytics: boolean;\r\n  preferences: boolean;\r\n}\r\n\r\ninterface DataStats {\r\n  studySets: number;\r\n  flashcards: number;\r\n  quizzes: number;\r\n  documents: number;\r\n  totalSize: string;\r\n}\r\n\r\nexport const DataManagement: React.FC = () => {\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const [isClearing, setIsClearing] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const [dataStats, setDataStats] = useState<DataStats>({\r\n    studySets: 12,\r\n    flashcards: 245,\r\n    quizzes: 18,\r\n    documents: 8,\r\n    totalSize: '2.4 MB'\r\n  });\r\n  \r\n  const [exportData, setExportData] = useState<ExportData>({\r\n    studySets: true,\r\n    flashcards: true,\r\n    quizzes: true,\r\n    analytics: true,\r\n    preferences: true\r\n  });\r\n\r\n  const handleExportData = async () => {\r\n    setIsExporting(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/data/export', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ exportData }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to export data');\r\n      }\r\n\r\n      const blob = await response.blob();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `chewyai-data-export-${new Date().toISOString().split('T')[0]}.json`;\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n\r\n      setSuccess('Data exported successfully!');\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to export data');\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleClearData = async (dataType: string) => {\r\n    if (!confirm(`Are you sure you want to clear all ${dataType}? This action cannot be undone.`)) {\r\n      return;\r\n    }\r\n\r\n    setIsClearing(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch(`/api/data/clear/${dataType}`, {\r\n        method: 'DELETE',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to clear ${dataType}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setSuccess(`${dataType} cleared successfully!`);\r\n        await fetchDataStats();\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : `Failed to clear ${dataType}`);\r\n    } finally {\r\n      setIsClearing(false);\r\n    }\r\n  };\r\n\r\n  const fetchDataStats = async () => {\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/data/stats', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.success) {\r\n          setDataStats(result.data);\r\n        }\r\n      }\r\n    } catch (err) {\r\n      // Silently fail for stats refresh\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Data Management</h3>\r\n        \r\n        {error && (\r\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <span className=\"text-red-400 font-medium\">Error</span>\r\n            </div>\r\n            <p className=\"text-red-300 mt-1\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {success && (\r\n          <div className=\"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiCheckCircle className=\"w-5 h-5 text-green-400\" />\r\n              <span className=\"text-green-400 font-medium\">Success</span>\r\n            </div>\r\n            <p className=\"text-green-300 mt-1\">{success}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Data Overview */}\r\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h4 className=\"text-lg font-medium text-white\">Your Data Overview</h4>\r\n            <Button\r\n              onClick={fetchDataStats}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n            >\r\n              <HiRefresh className=\"w-4 h-4 mr-2\" />\r\n              Refresh\r\n            </Button>\r\n          </div>\r\n          \r\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.studySets}</div>\r\n              <div className=\"text-sm text-gray-400\">Study Sets</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.flashcards}</div>\r\n              <div className=\"text-sm text-gray-400\">Flashcards</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.quizzes}</div>\r\n              <div className=\"text-sm text-gray-400\">Quizzes</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.documents}</div>\r\n              <div className=\"text-sm text-gray-400\">Documents</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.totalSize}</div>\r\n              <div className=\"text-sm text-gray-400\">Total Size</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Export Data */}\r\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\r\n          <div className=\"flex items-center space-x-3 mb-4\">\r\n            <HiDownload className=\"w-6 h-6 text-blue-400\" />\r\n            <h4 className=\"text-lg font-medium text-white\">Export Your Data</h4>\r\n          </div>\r\n          \r\n          <p className=\"text-gray-400 text-sm mb-4\">\r\n            Download a copy of your data in JSON format. You can use this to backup your data or import it into another account.\r\n          </p>\r\n\r\n          <div className=\"space-y-3 mb-6\">\r\n            <h5 className=\"font-medium text-white\">Select data to export:</h5>\r\n            {Object.entries(exportData).map(([key, value]) => (\r\n              <div key={key} className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm text-gray-300 capitalize\">\r\n                  {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                </label>\r\n                <button\r\n                  onClick={() => setExportData({ ...exportData, [key]: !value })}\r\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                    value ? 'bg-primary-500' : 'bg-gray-600'\r\n                  }`}\r\n                >\r\n                  <span\r\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                      value ? 'translate-x-6' : 'translate-x-1'\r\n                    }`}\r\n                  />\r\n                </button>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <Button\r\n            onClick={handleExportData}\r\n            isLoading={isExporting}\r\n            variant=\"primary\"\r\n          >\r\n            <HiDownload className=\"w-4 h-4 mr-2\" />\r\n            Export Data\r\n          </Button>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Clear Data */}\r\n        <div className=\"bg-red-500/10 rounded-lg p-6 border border-red-500/30\">\r\n          <div className=\"flex items-center space-x-3 mb-4\">\r\n            <HiTrash className=\"w-6 h-6 text-red-400\" />\r\n            <h4 className=\"text-lg font-medium text-white\">Clear Data</h4>\r\n          </div>\r\n          \r\n          <p className=\"text-gray-400 text-sm mb-6\">\r\n            Permanently delete specific types of data from your account. This action cannot be undone.\r\n          </p>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-3\">\r\n              <Button\r\n                onClick={() => handleClearData('study-sets')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear All Study Sets\r\n              </Button>\r\n              <Button\r\n                onClick={() => handleClearData('flashcards')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear All Flashcards\r\n              </Button>\r\n            </div>\r\n            <div className=\"space-y-3\">\r\n              <Button\r\n                onClick={() => handleClearData('quizzes')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear All Quizzes\r\n              </Button>\r\n              <Button\r\n                onClick={() => handleClearData('analytics')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear Analytics Data\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport {\n  HiCreditCard,\n  HiDownload,\n  HiRefresh,\n  HiExclamationCircle,\n  HiCheckCircle,\n  HiClock,\n  HiX,\n} from \"react-icons/hi\";\nimport { Button } from \"../common/Button\";\n\ninterface PaymentMethod {\n  id: string;\n  type: \"card\" | \"paypal\";\n  last4?: string;\n  brand?: string;\n  expiryMonth?: number;\n  expiryYear?: number;\n  isDefault: boolean;\n}\n\ninterface Invoice {\n  id: string;\n  number: string;\n  amount: number;\n  currency: string;\n  status: \"paid\" | \"pending\" | \"failed\" | \"draft\";\n  date: string;\n  dueDate?: string;\n  downloadUrl?: string;\n  description: string;\n}\n\ninterface BillingData {\n  paymentMethods: PaymentMethod[];\n  invoices: Invoice[];\n  nextInvoice?: {\n    amount: number;\n    currency: string;\n    date: string;\n  };\n}\n\nexport const EnhancedBilling: React.FC = () => {\n  const [billingData, setBillingData] = useState<BillingData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchBillingData();\n  }, []);\n\n  const fetchBillingData = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/billing\", {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch billing data\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        setBillingData(result.data);\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to load billing data\"\n      );\n      // Set mock data for development\n      setBillingData({\n        paymentMethods: [\n          {\n            id: \"1\",\n            type: \"card\",\n            last4: \"4242\",\n            brand: \"visa\",\n            expiryMonth: 12,\n            expiryYear: 2025,\n            isDefault: true,\n          },\n        ],\n        invoices: [\n          {\n            id: \"1\",\n            number: \"INV-001\",\n            amount: 59.0,\n            currency: \"USD\",\n            status: \"paid\",\n            date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\n            description: \"Study Pro - Monthly\",\n          },\n          {\n            id: \"2\",\n            number: \"INV-002\",\n            amount: 59.0,\n            currency: \"USD\",\n            status: \"pending\",\n            date: new Date().toISOString(),\n            dueDate: new Date(\n              Date.now() + 7 * 24 * 60 * 60 * 1000\n            ).toISOString(),\n            description: \"Study Pro - Monthly\",\n          },\n        ],\n        nextInvoice: {\n          amount: 59.0,\n          currency: \"USD\",\n          date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n        },\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDownloadInvoice = async (invoiceId: string) => {\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\n        `/api/billing/invoices/${invoiceId}/download`,\n        {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(\"Failed to download invoice\");\n      }\n\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = `invoice-${invoiceId}.pdf`;\n      a.click();\n      window.URL.revokeObjectURL(url);\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to download invoice\"\n      );\n    }\n  };\n\n  const handleAddPaymentMethod = async () => {\n    // This would typically open a Stripe payment method setup flow\n    setError(\"Payment method management coming soon\");\n  };\n\n  const handleSetDefaultPaymentMethod = async (paymentMethodId: string) => {\n    setIsUpdating(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/billing/payment-methods/default\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ paymentMethodId }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update default payment method\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        await fetchBillingData();\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to update payment method\"\n      );\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case \"paid\":\n        return <HiCheckCircle className=\"w-5 h-5 text-green-400\" />;\n      case \"pending\":\n        return <HiClock className=\"w-5 h-5 text-yellow-400\" />;\n      case \"failed\":\n        return <HiX className=\"w-5 h-5 text-red-400\" />;\n      default:\n        return <HiClock className=\"w-5 h-5 text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case \"paid\":\n        return \"text-green-400\";\n      case \"pending\":\n        return \"text-yellow-400\";\n      case \"failed\":\n        return \"text-red-400\";\n      default:\n        return \"text-gray-400\";\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 w-48 bg-gray-600 rounded mb-4\"></div>\n          <div className=\"space-y-4\">\n            <div className=\"h-32 bg-gray-600 rounded-lg\"></div>\n            <div className=\"h-64 bg-gray-600 rounded-lg\"></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-white\">\n            Billing & Invoices\n          </h3>\n          <Button onClick={fetchBillingData} variant=\"secondary\" size=\"sm\">\n            <HiRefresh className=\"w-4 h-4 mr-2\" />\n            Refresh\n          </Button>\n        </div>\n\n        {error && (\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\n              <span className=\"text-red-400 font-medium\">Error</span>\n            </div>\n            <p className=\"text-red-300 mt-1\">{error}</p>\n          </div>\n        )}\n\n        {/* Payment Methods */}\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h4 className=\"text-lg font-medium text-white\">Payment Methods</h4>\n            <Button\n              onClick={handleAddPaymentMethod}\n              variant=\"secondary\"\n              size=\"sm\"\n            >\n              Add Payment Method\n            </Button>\n          </div>\n\n          {billingData?.paymentMethods.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <HiCreditCard className=\"w-16 h-16 text-gray-500 mx-auto mb-4\" />\n              <p className=\"text-gray-400\">No payment methods added</p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {billingData?.paymentMethods.map((method) => (\n                <div\n                  key={method.id}\n                  className=\"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <HiCreditCard className=\"w-6 h-6 text-gray-400\" />\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-white font-medium\">\n                          {method.brand?.toUpperCase()} •••• {method.last4}\n                        </span>\n                        {method.isDefault && (\n                          <span className=\"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium\">\n                            Default\n                          </span>\n                        )}\n                      </div>\n                      {method.expiryMonth && method.expiryYear && (\n                        <p className=\"text-gray-400 text-sm\">\n                          Expires{\" \"}\n                          {method.expiryMonth.toString().padStart(2, \"0\")}/\n                          {method.expiryYear}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  {!method.isDefault && (\n                    <Button\n                      onClick={() => handleSetDefaultPaymentMethod(method.id)}\n                      isLoading={isUpdating}\n                      variant=\"secondary\"\n                      size=\"sm\"\n                    >\n                      Set as Default\n                    </Button>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Next Invoice */}\n        {billingData?.nextInvoice && (\n          <div className=\"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6\">\n            <h4 className=\"text-lg font-medium text-white mb-2\">\n              Upcoming Invoice\n            </h4>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-blue-300\">\n                  ${billingData.nextInvoice.amount}{\" \"}\n                  {billingData.nextInvoice.currency.toUpperCase()}\n                </p>\n                <p className=\"text-blue-400 text-sm\">\n                  Due on{\" \"}\n                  {new Date(billingData.nextInvoice.date).toLocaleDateString()}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Invoice History */}\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary\">\n          <h4 className=\"text-lg font-medium text-white mb-4\">\n            Invoice History\n          </h4>\n\n          {billingData?.invoices.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-400\">No invoices found</p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {billingData?.invoices.map((invoice) => (\n                <motion.div\n                  key={invoice.id}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary\"\n                >\n                  <div className=\"flex items-center space-x-4\">\n                    {getStatusIcon(invoice.status)}\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-white font-medium\">\n                          {invoice.number}\n                        </span>\n                        <span\n                          className={`text-sm font-medium ${getStatusColor(\n                            invoice.status\n                          )}`}\n                        >\n                          {invoice.status.charAt(0).toUpperCase() +\n                            invoice.status.slice(1)}\n                        </span>\n                      </div>\n                      <p className=\"text-gray-400 text-sm\">\n                        {invoice.description}\n                      </p>\n                      <p className=\"text-gray-500 text-xs\">\n                        {new Date(invoice.date).toLocaleDateString()}\n                        {invoice.dueDate && invoice.status === \"pending\" && (\n                          <span>\n                            {\" \"}\n                            • Due{\" \"}\n                            {new Date(invoice.dueDate).toLocaleDateString()}\n                          </span>\n                        )}\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <span className=\"text-white font-medium\">\n                      ${invoice.amount} {invoice.currency.toUpperCase()}\n                    </span>\n                    {invoice.status === \"paid\" && (\n                      <Button\n                        onClick={() => handleDownloadInvoice(invoice.id)}\n                        variant=\"secondary\"\n                        size=\"sm\"\n                      >\n                        <HiDownload className=\"w-4 h-4 mr-2\" />\n                        Download\n                      </Button>\n                    )}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>xclamation<PERSON><PERSON>cle,\n  <PERSON><PERSON>heckCircle,\n  HiClipboard,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>yeO<PERSON>,\n} from \"react-icons/hi\";\nimport { Button } from \"../common/Button\";\nimport { Input } from \"../common/Input\";\n\ninterface TwoFactorAuthProps {\n  enabled: boolean;\n  onToggle: (enabled: boolean) => void;\n}\n\ninterface SetupData {\n  qrCode: string;\n  secret: string;\n  backupCodes: string[];\n  factorId: string;\n}\n\nexport const TwoFactorAuth: React.FC<TwoFactorAuthProps> = ({\n  enabled,\n  onToggle,\n}) => {\n  const [isSetupMode, setIsSetupMode] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [setupData, setSetupData] = useState<SetupData | null>(null);\n  const [verificationCode, setVerificationCode] = useState(\"\");\n  const [showSecret, setShowSecret] = useState(false);\n  const [showBackupCodes, setShowBackupCodes] = useState(false);\n\n  const handleEnable2FA = async () => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      // Import supabase client\n      const { createClient } = await import(\"@supabase/supabase-js\");\n      const supabase = createClient(\n        process.env.REACT_APP_SUPABASE_URL!,\n        process.env.REACT_APP_SUPABASE_ANON_KEY!\n      );\n\n      // Enroll a new TOTP factor\n      const { data, error } = await supabase.auth.mfa.enroll({\n        factorType: \"totp\",\n        friendlyName: \"ChewyAI Authenticator\",\n      });\n\n      if (error) {\n        throw new Error(error.message);\n      }\n\n      // Extract the QR code and secret from the response\n      setSetupData({\n        qrCode: data.totp.qr_code,\n        secret: data.totp.secret,\n        backupCodes: [], // Supabase doesn't provide backup codes in this response\n        factorId: data.id,\n      });\n      setIsSetupMode(true);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"Failed to setup 2FA\");\n      // Mock data for development\n      setSetupData({\n        qrCode:\n          \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==\",\n        secret: \"JBSWY3DPEHPK3PXP\",\n        factorId: \"mock-factor-id\",\n        backupCodes: [\n          \"12345678\",\n          \"87654321\",\n          \"11111111\",\n          \"22222222\",\n          \"33333333\",\n          \"44444444\",\n          \"55555555\",\n          \"66666666\",\n        ],\n      });\n      setIsSetupMode(true);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleVerify2FA = async () => {\n    if (!verificationCode || verificationCode.length !== 6) {\n      setError(\"Please enter a valid 6-digit code\");\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Import supabase client\n      const { createClient } = await import(\"@supabase/supabase-js\");\n      const supabase = createClient(\n        process.env.REACT_APP_SUPABASE_URL!,\n        process.env.REACT_APP_SUPABASE_ANON_KEY!\n      );\n\n      // Get the factor ID from the setup data\n      if (!setupData) {\n        throw new Error(\"No setup data available\");\n      }\n\n      // Challenge and verify the TOTP factor\n      const { data: challengeData, error: challengeError } =\n        await supabase.auth.mfa.challenge({\n          factorId: setupData.factorId,\n        });\n\n      if (challengeError) {\n        throw new Error(challengeError.message);\n      }\n\n      const { error: verifyError } = await supabase.auth.mfa.verify({\n        factorId: setupData.factorId,\n        challengeId: challengeData.id,\n        code: verificationCode,\n      });\n\n      if (verifyError) {\n        throw new Error(verifyError.message);\n      }\n\n      setSuccess(\"Two-factor authentication enabled successfully!\");\n      setIsSetupMode(false);\n      setShowBackupCodes(true);\n      onToggle(true);\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to verify 2FA code\"\n      );\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDisable2FA = async () => {\n    if (\n      !confirm(\n        \"Are you sure you want to disable two-factor authentication? This will make your account less secure.\"\n      )\n    ) {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Import supabase client\n      const { createClient } = await import(\"@supabase/supabase-js\");\n      const supabase = createClient(\n        process.env.REACT_APP_SUPABASE_URL!,\n        process.env.REACT_APP_SUPABASE_ANON_KEY!\n      );\n\n      // List all factors and unenroll them\n      const { data: factorsData, error: factorsError } =\n        await supabase.auth.mfa.listFactors();\n\n      if (factorsError) {\n        throw new Error(factorsError.message);\n      }\n\n      // Unenroll all TOTP factors\n      for (const factor of factorsData.totp) {\n        const { error: unenrollError } = await supabase.auth.mfa.unenroll({\n          factorId: factor.id,\n        });\n\n        if (unenrollError) {\n          console.error(\"Failed to unenroll factor:\", unenrollError);\n        }\n      }\n\n      setSuccess(\"Two-factor authentication disabled successfully\");\n      onToggle(false);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"Failed to disable 2FA\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCopyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n    setSuccess(\"Copied to clipboard!\");\n    setTimeout(() => setSuccess(null), 2000);\n  };\n\n  const handleCancelSetup = () => {\n    setIsSetupMode(false);\n    setSetupData(null);\n    setVerificationCode(\"\");\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (isSetupMode && setupData) {\n    return (\n      <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary\">\n        <div className=\"flex items-center space-x-3 mb-6\">\n          <HiShieldCheck className=\"w-6 h-6 text-primary-400\" />\n          <h4 className=\"text-lg font-medium text-white\">\n            Setup Two-Factor Authentication\n          </h4>\n        </div>\n\n        {error && (\n          <div className=\"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3\">\n            <div className=\"flex items-center space-x-2\">\n              <HiExclamationCircle className=\"w-4 h-4 text-red-400\" />\n              <span className=\"text-red-400 text-sm\">{error}</span>\n            </div>\n          </div>\n        )}\n\n        {success && (\n          <div className=\"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3\">\n            <div className=\"flex items-center space-x-2\">\n              <HiCheckCircle className=\"w-4 h-4 text-green-400\" />\n              <span className=\"text-green-400 text-sm\">{success}</span>\n            </div>\n          </div>\n        )}\n\n        {!showBackupCodes ? (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <h5 className=\"font-medium text-white mb-2\">\n                Step 1: Scan QR Code\n              </h5>\n              <p className=\"text-gray-400 text-sm mb-4\">\n                Scan this QR code with your authenticator app (Google\n                Authenticator, Authy, etc.)\n              </p>\n              <div className=\"bg-white p-4 rounded-lg inline-block\">\n                <img\n                  src={setupData.qrCode}\n                  alt=\"2FA QR Code\"\n                  className=\"w-48 h-48\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <h5 className=\"font-medium text-white mb-2\">\n                Step 2: Manual Entry (Alternative)\n              </h5>\n              <p className=\"text-gray-400 text-sm mb-3\">\n                If you can't scan the QR code, enter this secret key manually:\n              </p>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3\">\n                  <code className=\"text-primary-400 font-mono\">\n                    {showSecret ? setupData.secret : \"••••••••••••••••\"}\n                  </code>\n                </div>\n                <Button\n                  onClick={() => setShowSecret(!showSecret)}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                >\n                  {showSecret ? (\n                    <HiEyeOff className=\"w-4 h-4\" />\n                  ) : (\n                    <HiEye className=\"w-4 h-4\" />\n                  )}\n                </Button>\n                <Button\n                  onClick={() => handleCopyToClipboard(setupData.secret)}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                >\n                  <HiClipboard className=\"w-4 h-4\" />\n                </Button>\n              </div>\n            </div>\n\n            <div>\n              <h5 className=\"font-medium text-white mb-2\">\n                Step 3: Verify Setup\n              </h5>\n              <p className=\"text-gray-400 text-sm mb-3\">\n                Enter the 6-digit code from your authenticator app:\n              </p>\n              <div className=\"flex space-x-3\">\n                <Input\n                  value={verificationCode}\n                  onChange={setVerificationCode}\n                  placeholder=\"123456\"\n                  className=\"flex-1\"\n                />\n                <Button\n                  onClick={handleVerify2FA}\n                  isLoading={isLoading}\n                  disabled={verificationCode.length !== 6}\n                >\n                  Verify\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <Button\n                onClick={handleCancelSetup}\n                variant=\"secondary\"\n                className=\"flex-1\"\n              >\n                Cancel\n              </Button>\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <HiCheckCircle className=\"w-16 h-16 text-green-400 mx-auto mb-4\" />\n              <h5 className=\"text-lg font-medium text-white mb-2\">\n                2FA Enabled Successfully!\n              </h5>\n              <p className=\"text-gray-400 text-sm\">\n                Your account is now protected with two-factor authentication.\n              </p>\n            </div>\n\n            <div className=\"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4\">\n              <h5 className=\"font-medium text-yellow-400 mb-2\">\n                Important: Save Your Backup Codes\n              </h5>\n              <p className=\"text-yellow-300 text-sm mb-4\">\n                Store these backup codes in a safe place. You can use them to\n                access your account if you lose your authenticator device.\n              </p>\n              <div className=\"grid grid-cols-2 gap-2 mb-4\">\n                {setupData.backupCodes.map((code, index) => (\n                  <div\n                    key={index}\n                    className=\"bg-background-secondary border border-border-primary rounded p-2 text-center\"\n                  >\n                    <code className=\"text-primary-400 font-mono\">{code}</code>\n                  </div>\n                ))}\n              </div>\n              <Button\n                onClick={() =>\n                  handleCopyToClipboard(setupData.backupCodes.join(\"\\n\"))\n                }\n                variant=\"secondary\"\n                size=\"sm\"\n              >\n                <HiClipboard className=\"w-4 h-4 mr-2\" />\n                Copy All Codes\n              </Button>\n            </div>\n\n            <Button\n              onClick={() => setShowBackupCodes(false)}\n              variant=\"primary\"\n              className=\"w-full\"\n            >\n              I've Saved My Backup Codes\n            </Button>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          <HiShieldCheck\n            className={`w-6 h-6 ${\n              enabled ? \"text-green-400\" : \"text-gray-400\"\n            }`}\n          />\n          <div>\n            <h4 className=\"text-lg font-medium text-white\">\n              Two-Factor Authentication\n            </h4>\n            <p className=\"text-gray-400 text-sm\">\n              Add an extra layer of security to your account\n            </p>\n          </div>\n        </div>\n        <div\n          className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\n            enabled\n              ? \"bg-green-500/20 text-green-400\"\n              : \"bg-gray-500/20 text-gray-400\"\n          }`}\n        >\n          {enabled ? \"Enabled\" : \"Disabled\"}\n        </div>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3\">\n          <div className=\"flex items-center space-x-2\">\n            <HiExclamationCircle className=\"w-4 h-4 text-red-400\" />\n            <span className=\"text-red-400 text-sm\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {success && (\n        <div className=\"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3\">\n          <div className=\"flex items-center space-x-2\">\n            <HiCheckCircle className=\"w-4 h-4 text-green-400\" />\n            <span className=\"text-green-400 text-sm\">{success}</span>\n          </div>\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        <p className=\"text-gray-300 text-sm\">\n          {enabled\n            ? \"Two-factor authentication is currently enabled for your account. You can disable it below if needed.\"\n            : \"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy.\"}\n        </p>\n\n        <div className=\"flex space-x-3\">\n          {enabled ? (\n            <Button\n              onClick={handleDisable2FA}\n              isLoading={isLoading}\n              variant=\"danger\"\n            >\n              Disable 2FA\n            </Button>\n          ) : (\n            <Button\n              onClick={handleEnable2FA}\n              isLoading={isLoading}\n              variant=\"primary\"\n            >\n              <HiKey className=\"w-4 h-4 mr-2\" />\n              Enable 2FA\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\nimport { Input } from '../common/Input';\r\n\r\ninterface ChangePasswordModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSuccess: () => void;\r\n}\r\n\r\nexport const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onSuccess\r\n}) => {\r\n  const [formData, setFormData] = useState({\r\n    currentPassword: '',\r\n    newPassword: '',\r\n    confirmPassword: ''\r\n  });\r\n  const [showPasswords, setShowPasswords] = useState({\r\n    current: false,\r\n    new: false,\r\n    confirm: false\r\n  });\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n\r\n  const handleInputChange = (field: string, value: string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n    setError(null);\r\n  };\r\n\r\n  const handleChange = (field: string) => (value: string) => {\r\n    handleInputChange(field, value);\r\n  };\r\n\r\n  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {\r\n    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    if (!formData.currentPassword) {\r\n      setError('Current password is required');\r\n      return false;\r\n    }\r\n    if (!formData.newPassword) {\r\n      setError('New password is required');\r\n      return false;\r\n    }\r\n    if (formData.newPassword.length < 8) {\r\n      setError('New password must be at least 8 characters long');\r\n      return false;\r\n    }\r\n    if (formData.newPassword !== formData.confirmPassword) {\r\n      setError('New passwords do not match');\r\n      return false;\r\n    }\r\n    if (formData.currentPassword === formData.newPassword) {\r\n      setError('New password must be different from current password');\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!validateForm()) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/auth/change-password', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${token}`\r\n        },\r\n        body: JSON.stringify({\r\n          currentPassword: formData.currentPassword,\r\n          newPassword: formData.newPassword\r\n        })\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || 'Failed to change password');\r\n      }\r\n\r\n      setSuccess('Password changed successfully!');\r\n      setTimeout(() => {\r\n        onSuccess();\r\n        onClose();\r\n        resetForm();\r\n      }, 1500);\r\n\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to change password');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setFormData({\r\n      currentPassword: '',\r\n      newPassword: '',\r\n      confirmPassword: ''\r\n    });\r\n    setShowPasswords({\r\n      current: false,\r\n      new: false,\r\n      confirm: false\r\n    });\r\n    setError(null);\r\n    setSuccess(null);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    resetForm();\r\n    onClose();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-background-secondary rounded-lg border border-border-primary w-full max-w-md\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-border-primary\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <HiKey className=\"w-5 h-5 text-primary-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Change Password</h3>\r\n          </div>\r\n          <button\r\n            onClick={handleClose}\r\n            className=\"text-gray-400 hover:text-white transition-colors\"\r\n          >\r\n            <HiX className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\r\n          {/* Current Password */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n              Current Password\r\n            </label>\r\n            <div className=\"relative\">\r\n              <Input\r\n                type={showPasswords.current ? 'text' : 'password'}\r\n                value={formData.currentPassword}\r\n                onChange={handleChange('currentPassword')}\r\n                placeholder=\"Enter your current password\"\r\n                className=\"pr-10\"\r\n                disabled={isLoading}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => togglePasswordVisibility('current')}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white\"\r\n              >\r\n                {showPasswords.current ? <HiEyeOff className=\"w-4 h-4\" /> : <HiEye className=\"w-4 h-4\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* New Password */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n              New Password\r\n            </label>\r\n            <div className=\"relative\">\r\n              <Input\r\n                type={showPasswords.new ? 'text' : 'password'}\r\n                value={formData.newPassword}\r\n                onChange={handleChange('newPassword')}\r\n                placeholder=\"Enter your new password\"\r\n                className=\"pr-10\"\r\n                disabled={isLoading}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => togglePasswordVisibility('new')}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white\"\r\n              >\r\n                {showPasswords.new ? <HiEyeOff className=\"w-4 h-4\" /> : <HiEye className=\"w-4 h-4\" />}\r\n              </button>\r\n            </div>\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Password must be at least 8 characters long\r\n            </p>\r\n          </div>\r\n\r\n          {/* Confirm Password */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n              Confirm New Password\r\n            </label>\r\n            <div className=\"relative\">\r\n              <Input\r\n                type={showPasswords.confirm ? 'text' : 'password'}\r\n                value={formData.confirmPassword}\r\n                onChange={handleChange('confirmPassword')}\r\n                placeholder=\"Confirm your new password\"\r\n                className=\"pr-10\"\r\n                disabled={isLoading}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => togglePasswordVisibility('confirm')}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white\"\r\n              >\r\n                {showPasswords.confirm ? <HiEyeOff className=\"w-4 h-4\" /> : <HiEye className=\"w-4 h-4\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Error/Success Messages */}\r\n          {error && (\r\n            <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-3\">\r\n              <p className=\"text-red-400 text-sm\">{error}</p>\r\n            </div>\r\n          )}\r\n\r\n          {success && (\r\n            <div className=\"bg-green-500/10 border border-green-500/30 rounded-lg p-3\">\r\n              <p className=\"text-green-400 text-sm\">{success}</p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex space-x-3 pt-4\">\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"secondary\"\r\n              onClick={handleClose}\r\n              disabled={isLoading}\r\n              className=\"flex-1\"\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              variant=\"primary\"\r\n              isLoading={isLoading}\r\n              disabled={isLoading}\r\n              className=\"flex-1\"\r\n            >\r\n              {isLoading ? 'Changing...' : 'Change Password'}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from 'react';\r\nimport { HiX, HiExclamationCircle, HiTrash } from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\nimport { Input } from '../common/Input';\r\n\r\ninterface DangerZoneModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  action: 'deactivate' | 'delete';\r\n}\r\n\r\nexport const DangerZoneModal: React.FC<DangerZoneModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  action\r\n}) => {\r\n  const [confirmationText, setConfirmationText] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [step, setStep] = useState<'confirm' | 'final'>('confirm');\r\n\r\n  const actionConfig = {\r\n    deactivate: {\r\n      title: 'Deactivate Account',\r\n      description: 'Your account will be temporarily disabled. You can reactivate it by logging in again.',\r\n      confirmText: 'DEACTIVATE',\r\n      buttonText: 'Deactivate Account',\r\n      warningText: 'This will temporarily disable your account and log you out.',\r\n      endpoint: '/api/auth/deactivate-account'\r\n    },\r\n    delete: {\r\n      title: 'Delete Account',\r\n      description: 'This will permanently delete your account and all associated data. This action cannot be undone.',\r\n      confirmText: 'DELETE FOREVER',\r\n      buttonText: 'Delete Account Forever',\r\n      warningText: 'This will permanently delete all your data including study sets, flashcards, progress, and subscription information.',\r\n      endpoint: '/api/auth/delete-account'\r\n    }\r\n  };\r\n\r\n  const config = actionConfig[action];\r\n  const isConfirmationValid = confirmationText === config.confirmText;\r\n\r\n  const handleSubmit = async () => {\r\n    if (step === 'confirm') {\r\n      setStep('final');\r\n      return;\r\n    }\r\n\r\n    if (!isConfirmationValid) {\r\n      setError(`Please type \"${config.confirmText}\" to confirm`);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch(config.endpoint, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${token}`\r\n        },\r\n        body: JSON.stringify({\r\n          confirmation: confirmationText\r\n        })\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || `Failed to ${action} account`);\r\n      }\r\n\r\n      // For both actions, redirect to login page\r\n      localStorage.removeItem('auth_token');\r\n      localStorage.removeItem('user_data');\r\n      window.location.href = '/login';\r\n\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : `Failed to ${action} account`);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setConfirmationText('');\r\n    setError(null);\r\n    setStep('confirm');\r\n    onClose();\r\n  };\r\n\r\n  const handleBack = () => {\r\n    setStep('confirm');\r\n    setError(null);\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-red-500/30\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">{config.title}</h3>\r\n          </div>\r\n          <button\r\n            onClick={handleClose}\r\n            className=\"text-gray-400 hover:text-white transition-colors\"\r\n          >\r\n            <HiX className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"p-6\">\r\n          {step === 'confirm' ? (\r\n            <div className=\"space-y-4\">\r\n              <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-4\">\r\n                <div className=\"flex items-start space-x-3\">\r\n                  <HiExclamationCircle className=\"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0\" />\r\n                  <div>\r\n                    <h4 className=\"font-medium text-red-400 mb-2\">Warning</h4>\r\n                    <p className=\"text-gray-300 text-sm\">{config.warningText}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <p className=\"text-gray-300 text-sm mb-4\">{config.description}</p>\r\n                \r\n                {action === 'delete' && (\r\n                  <div className=\"space-y-2 text-sm text-gray-400\">\r\n                    <p>This will delete:</p>\r\n                    <ul className=\"list-disc list-inside space-y-1 ml-4\">\r\n                      <li>All study sets and flashcards</li>\r\n                      <li>Quiz history and progress</li>\r\n                      <li>Account settings and preferences</li>\r\n                      <li>Subscription and billing information</li>\r\n                      <li>All uploaded documents</li>\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"flex space-x-3 pt-4\">\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  onClick={handleClose}\r\n                  className=\"flex-1\"\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                <Button\r\n                  variant=\"danger\"\r\n                  onClick={handleSubmit}\r\n                  className=\"flex-1\"\r\n                >\r\n                  Continue\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-4\">\r\n              <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <HiTrash className=\"w-5 h-5 text-red-400\" />\r\n                  <h4 className=\"font-medium text-red-400\">Final Confirmation</h4>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <p className=\"text-gray-300 text-sm mb-4\">\r\n                  To confirm this action, please type <span className=\"font-mono font-bold text-red-400\">{config.confirmText}</span> in the box below:\r\n                </p>\r\n                \r\n                <Input\r\n                  type=\"text\"\r\n                  value={confirmationText}\r\n                  onChange={(value) => {\r\n                    setConfirmationText(value);\r\n                    setError(null);\r\n                  }}\r\n                  placeholder={config.confirmText}\r\n                  className=\"font-mono\"\r\n                  disabled={isLoading}\r\n                />\r\n              </div>\r\n\r\n              {error && (\r\n                <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-3\">\r\n                  <p className=\"text-red-400 text-sm\">{error}</p>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex space-x-3 pt-4\">\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  onClick={handleBack}\r\n                  disabled={isLoading}\r\n                  className=\"flex-1\"\r\n                >\r\n                  Back\r\n                </Button>\r\n                <Button\r\n                  variant=\"danger\"\r\n                  onClick={handleSubmit}\r\n                  isLoading={isLoading}\r\n                  disabled={isLoading || !isConfirmationValid}\r\n                  className=\"flex-1\"\r\n                >\r\n                  {isLoading ? 'Processing...' : config.buttonText}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>eld<PERSON>he<PERSON>,\n  HiCreditCard,\n  HiMoon,\n  HiSun,\n  HiKey,\n  HiUpload,\n  HiDatabase,\n  HiExclamationCircle,\n  HiCheckCircle,\n  HiLogout,\n} from \"react-icons/hi\";\nimport { Button } from \"../components/common/Button\";\nimport { Input } from \"../components/common/Input\";\nimport { useAuthStore } from \"../stores/authStore\";\nimport { SubscriptionManagement } from \"../components/settings/SubscriptionManagement\";\nimport { DataManagement } from \"../components/settings/DataManagement\";\nimport { EnhancedBilling } from \"../components/settings/EnhancedBilling\";\nimport { TwoFactorAuth } from \"../components/settings/TwoFactorAuth\";\nimport { ChangePasswordModal } from \"../components/settings/ChangePasswordModal\";\nimport { DangerZoneModal } from \"../components/settings/DangerZoneModal\";\nimport { DifficultyLevel } from \"../shared/types\";\nimport { DifficultySelector } from \"../components/common/DifficultySelector\";\nimport { useNavigate } from \"react-router-dom\";\n\ninterface SettingsSection {\n  id: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description: string;\n}\n\nconst settingsSections: SettingsSection[] = [\n  {\n    id: \"profile\",\n    label: \"Profile\",\n    icon: HiUser,\n    description: \"Manage your account information\",\n  },\n  {\n    id: \"preferences\",\n    label: \"Preferences\",\n    icon: HiCog,\n    description: \"Customize your experience\",\n  },\n  {\n    id: \"notifications\",\n    label: \"Notifications\",\n    icon: HiBell,\n    description: \"Control notification settings\",\n  },\n  {\n    id: \"security\",\n    label: \"Security\",\n    icon: HiShieldCheck,\n    description: \"Password and security settings\",\n  },\n  {\n    id: \"subscription\",\n    label: \"Subscription\",\n    icon: HiCreditCard,\n    description: \"Manage your subscription plan\",\n  },\n  {\n    id: \"billing\",\n    label: \"Billing\",\n    icon: HiCreditCard,\n    description: \"Payment history and invoices\",\n  },\n  {\n    id: \"data\",\n    label: \"Data Management\",\n    icon: HiDatabase,\n    description: \"Export, import, and manage your data\",\n  },\n];\n\nexport const SettingsPage: React.FC = () => {\n  const [activeSection, setActiveSection] = useState(\"profile\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const { user, logout } = useAuthStore();\n  const navigate = useNavigate();\n\n  // Form states\n  const [profileData, setProfileData] = useState({\n    name: user?.name || \"\",\n    email: user?.email || \"\",\n    bio: \"\",\n    avatar: null as File | null,\n  });\n\n  const [preferences, setPreferences] = useState({\n    theme: \"dark\",\n    language: \"en\",\n    studyReminders: true,\n    autoSave: true,\n    defaultStudyMode: \"flashcards\" as \"flashcards\" | \"quiz\",\n    sessionDuration: 30,\n    difficultyLevel: DifficultyLevel.MEDIUM,\n  });\n\n  const [notifications, setNotifications] = useState({\n    emailNotifications: true,\n    studyReminders: true,\n    weeklyProgress: false,\n    marketingEmails: false,\n    achievementNotifications: true,\n    streakReminders: true,\n  });\n\n  const [securitySettings, setSecuritySettings] = useState({\n    twoFactorEnabled: false,\n    loginNotifications: true,\n    sessionTimeout: 30,\n  });\n\n  // Modal states\n  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =\n    useState(false);\n  const [isDangerZoneModalOpen, setIsDangerZoneModalOpen] = useState(false);\n  const [dangerZoneAction, setDangerZoneAction] = useState<\n    \"deactivate\" | \"delete\"\n  >(\"deactivate\");\n\n  // Load user settings on component mount\n  useEffect(() => {\n    const loadUserSettings = async () => {\n      try {\n        setIsLoading(true);\n\n        const token = localStorage.getItem(\"auth_token\");\n        if (!token) {\n          setError(\"Authentication required\");\n          return;\n        }\n\n        // Fetch user preferences from API\n        const response = await fetch(\"/api/user/preferences\", {\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (!response.ok) {\n          throw new Error(\"Failed to load user preferences\");\n        }\n\n        const result = await response.json();\n        if (result.success && result.data) {\n          // Convert snake_case from backend to camelCase for frontend\n          const backendPrefs = result.data;\n          setPreferences({\n            theme: backendPrefs.theme,\n            language: backendPrefs.language,\n            studyReminders: backendPrefs.study_reminders,\n            autoSave: backendPrefs.auto_save,\n            defaultStudyMode: backendPrefs.default_study_mode,\n            sessionDuration: backendPrefs.session_duration,\n            difficultyLevel: backendPrefs.difficulty_level,\n          });\n        }\n      } catch (err) {\n        setError(\"Failed to load user settings\");\n        console.error(\"Load user settings error:\", err);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadUserSettings();\n  }, []);\n\n  const handleSaveProfile = async () => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const formData = new FormData();\n      formData.append(\"name\", profileData.name);\n      formData.append(\"bio\", profileData.bio);\n      if (profileData.avatar) {\n        formData.append(\"avatar\", profileData.avatar);\n      }\n\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/user/profile\", {\n        method: \"PUT\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update profile\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        setSuccess(\"Profile updated successfully!\");\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"Failed to update profile\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSavePreferences = async () => {\n    // Guard against duplicate submissions\n    if (isLoading) return;\n    setIsLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      // Abort if user is not authenticated to avoid unauthenticated rate-limited requests\n      if (!token) {\n        setError(\"Authentication required\");\n        setIsLoading(false);\n        return;\n      }\n\n      // Convert camelCase to snake_case for backend API\n      const backendPreferences = {\n        theme: preferences.theme,\n        language: preferences.language,\n        study_reminders: preferences.studyReminders,\n        auto_save: preferences.autoSave,\n        default_study_mode: preferences.defaultStudyMode,\n        session_duration: preferences.sessionDuration,\n        difficulty_level: preferences.difficultyLevel,\n      };\n\n      const response = await fetch(\"/api/user/preferences\", {\n        method: \"PUT\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(backendPreferences),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update preferences\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        setSuccess(\"Preferences updated successfully!\");\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error ? err.message : \"Failed to update preferences\"\n      );\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSaveNotifications = async () => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\"/api/user/notifications\", {\n        method: \"PUT\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(notifications),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update notification settings\");\n      }\n\n      const result = await response.json();\n      if (result.success) {\n        setSuccess(\"Notification settings updated successfully!\");\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (err) {\n      setError(\n        err instanceof Error\n          ? err.message\n          : \"Failed to update notification settings\"\n      );\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      setIsLoading(true);\n      await logout();\n      navigate(\"/login\");\n    } catch (error) {\n      setError(\"Failed to logout. Please try again.\");\n      setIsLoading(false);\n    }\n  };\n\n  const renderProfileSection = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\n          Profile Information\n        </h3>\n\n        {/* Avatar Upload */}\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n            Profile Picture\n          </label>\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold\">\n              {user?.name?.charAt(0)?.toUpperCase() || \"U\"}\n            </div>\n            <div className=\"space-y-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => {\n                  const input = document.createElement(\"input\");\n                  input.type = \"file\";\n                  input.accept = \"image/*\";\n                  input.onchange = (e) => {\n                    const file = (e.target as HTMLInputElement).files?.[0];\n                    if (file) {\n                      setProfileData({ ...profileData, avatar: file });\n                    }\n                  };\n                  input.click();\n                }}\n              >\n                <HiUpload className=\"w-4 h-4 mr-2\" />\n                Upload Photo\n              </Button>\n              <p className=\"text-xs text-gray-500\">JPG, PNG up to 5MB</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          <Input\n            label=\"Full Name\"\n            value={profileData.name}\n            onChange={(value) =>\n              setProfileData({ ...profileData, name: value })\n            }\n            placeholder=\"Enter your full name\"\n          />\n          <Input\n            label=\"Email Address\"\n            type=\"email\"\n            value={profileData.email}\n            onChange={(value) =>\n              setProfileData({ ...profileData, email: value })\n            }\n            placeholder=\"Enter your email\"\n            disabled\n          />\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n              Bio (Optional)\n            </label>\n            <textarea\n              value={profileData.bio}\n              onChange={(e) =>\n                setProfileData({ ...profileData, bio: e.target.value })\n              }\n              placeholder=\"Tell us about yourself...\"\n              rows={4}\n              className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n        <div className=\"mt-6\">\n          <Button onClick={handleSaveProfile} isLoading={isLoading}>\n            Save Profile\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPreferencesSection = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\n          App Preferences\n        </h3>\n        <div className=\"space-y-6\">\n          {/* Theme Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n              Theme\n            </label>\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() =>\n                  setPreferences({ ...preferences, theme: \"dark\" })\n                }\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\n                  preferences.theme === \"dark\"\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                }`}\n              >\n                <HiMoon className=\"w-4 h-4\" />\n                <span>Dark</span>\n              </button>\n              <button\n                onClick={() =>\n                  setPreferences({ ...preferences, theme: \"light\" })\n                }\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\n                  preferences.theme === \"light\"\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                }`}\n                disabled\n              >\n                <HiSun className=\"w-4 h-4\" />\n                <span>Light (Coming Soon)</span>\n              </button>\n            </div>\n          </div>\n\n          {/* Language Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n              Language\n            </label>\n            <select\n              value={preferences.language}\n              onChange={(e) =>\n                setPreferences({ ...preferences, language: e.target.value })\n              }\n              className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            >\n              <option value=\"en\">English</option>\n              <option value=\"es\" disabled>\n                Spanish (Coming Soon)\n              </option>\n              <option value=\"fr\" disabled>\n                French (Coming Soon)\n              </option>\n            </select>\n          </div>\n\n          {/* Study Preferences */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n              Default Study Mode\n            </label>\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() =>\n                  setPreferences({\n                    ...preferences,\n                    defaultStudyMode: \"flashcards\",\n                  })\n                }\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\n                  preferences.defaultStudyMode === \"flashcards\"\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                }`}\n              >\n                <span>Flashcards</span>\n              </button>\n              <button\n                onClick={() =>\n                  setPreferences({ ...preferences, defaultStudyMode: \"quiz\" })\n                }\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\n                  preferences.defaultStudyMode === \"quiz\"\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\n                }`}\n              >\n                <span>Quiz</span>\n              </button>\n            </div>\n          </div>\n\n          {/* Session Duration */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n              Default Session Duration (minutes)\n            </label>\n            <select\n              value={preferences.sessionDuration}\n              onChange={(e) =>\n                setPreferences({\n                  ...preferences,\n                  sessionDuration: parseInt(e.target.value),\n                })\n              }\n              className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            >\n              <option value={15}>15 minutes</option>\n              <option value={30}>30 minutes</option>\n              <option value={45}>45 minutes</option>\n              <option value={60}>1 hour</option>\n              <option value={90}>1.5 hours</option>\n            </select>\n          </div>\n\n          {/* Difficulty Level */}\n          <DifficultySelector\n            value={preferences.difficultyLevel}\n            onChange={(level) =>\n              setPreferences({ ...preferences, difficultyLevel: level })\n            }\n            label=\"Default Difficulty Level\"\n            className=\"bg-background-secondary rounded-lg p-4\"\n          />\n\n          {/* Toggle Settings */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className=\"text-sm font-medium text-gray-300\">\n                  Study Reminders\n                </label>\n                <p className=\"text-xs text-gray-500\">\n                  Get reminded to study regularly\n                </p>\n              </div>\n              <button\n                onClick={() =>\n                  setPreferences({\n                    ...preferences,\n                    studyReminders: !preferences.studyReminders,\n                  })\n                }\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                  preferences.studyReminders ? \"bg-primary-500\" : \"bg-gray-600\"\n                }`}\n              >\n                <span\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                    preferences.studyReminders\n                      ? \"translate-x-6\"\n                      : \"translate-x-1\"\n                  }`}\n                />\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className=\"text-sm font-medium text-gray-300\">\n                  Auto-save\n                </label>\n                <p className=\"text-xs text-gray-500\">\n                  Automatically save your progress\n                </p>\n              </div>\n              <button\n                onClick={() =>\n                  setPreferences({\n                    ...preferences,\n                    autoSave: !preferences.autoSave,\n                  })\n                }\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                  preferences.autoSave ? \"bg-primary-500\" : \"bg-gray-600\"\n                }`}\n              >\n                <span\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                    preferences.autoSave ? \"translate-x-6\" : \"translate-x-1\"\n                  }`}\n                />\n              </button>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-6\">\n          <Button onClick={handleSavePreferences} isLoading={isLoading}>\n            Save Preferences\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderNotificationsSection = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\n          Notification Settings\n        </h3>\n        <div className=\"space-y-4\">\n          {Object.entries(notifications).map(([key, value]) => (\n            <div key={key} className=\"flex items-center justify-between\">\n              <div>\n                <label className=\"text-sm font-medium text-gray-300 capitalize\">\n                  {key.replace(/([A-Z])/g, \" $1\").trim()}\n                </label>\n                <p className=\"text-xs text-gray-500\">\n                  {key === \"emailNotifications\" &&\n                    \"Receive important updates via email\"}\n                  {key === \"studyReminders\" &&\n                    \"Get reminded when it's time to study\"}\n                  {key === \"weeklyProgress\" &&\n                    \"Weekly summary of your study progress\"}\n                  {key === \"marketingEmails\" && \"Product updates and tips\"}\n                  {key === \"achievementNotifications\" &&\n                    \"Get notified when you unlock achievements\"}\n                  {key === \"streakReminders\" &&\n                    \"Reminders to maintain your study streak\"}\n                </p>\n              </div>\n              <button\n                onClick={() =>\n                  setNotifications({ ...notifications, [key]: !value })\n                }\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                  value ? \"bg-primary-500\" : \"bg-gray-600\"\n                }`}\n              >\n                <span\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                    value ? \"translate-x-6\" : \"translate-x-1\"\n                  }`}\n                />\n              </button>\n            </div>\n          ))}\n        </div>\n        <div className=\"mt-6\">\n          <Button onClick={handleSaveNotifications} isLoading={isLoading}>\n            Save Notification Settings\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSecuritySection = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\n          Security Settings\n        </h3>\n        <div className=\"space-y-6\">\n          {/* Two-Factor Authentication */}\n          <TwoFactorAuth\n            enabled={securitySettings.twoFactorEnabled}\n            onToggle={(enabled) =>\n              setSecuritySettings({\n                ...securitySettings,\n                twoFactorEnabled: enabled,\n              })\n            }\n          />\n\n          {/* Session Settings */}\n          <div className=\"bg-background-tertiary rounded-lg p-4 border border-border-primary\">\n            <h4 className=\"font-medium text-white mb-4\">Session Management</h4>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-300\">\n                    Login Notifications\n                  </label>\n                  <p className=\"text-xs text-gray-500\">\n                    Get notified when someone logs into your account\n                  </p>\n                </div>\n                <button\n                  onClick={() =>\n                    setSecuritySettings({\n                      ...securitySettings,\n                      loginNotifications: !securitySettings.loginNotifications,\n                    })\n                  }\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                    securitySettings.loginNotifications\n                      ? \"bg-primary-500\"\n                      : \"bg-gray-600\"\n                  }`}\n                >\n                  <span\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                      securitySettings.loginNotifications\n                        ? \"translate-x-6\"\n                        : \"translate-x-1\"\n                    }`}\n                  />\n                </button>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Session Timeout (minutes)\n                </label>\n                <select\n                  value={securitySettings.sessionTimeout}\n                  onChange={(e) =>\n                    setSecuritySettings({\n                      ...securitySettings,\n                      sessionTimeout: parseInt(e.target.value),\n                    })\n                  }\n                  className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                >\n                  <option value={15}>15 minutes</option>\n                  <option value={30}>30 minutes</option>\n                  <option value={60}>1 hour</option>\n                  <option value={120}>2 hours</option>\n                  <option value={480}>8 hours</option>\n                  <option value={1440}>1 day</option>\n                  <option value={10080}>1 week</option>\n                  <option value={20160}>2 weeks</option>\n                  <option value={30240}>3 weeks</option>\n                  <option value={40320}>4 weeks</option>\n                  <option value={50400}>5 weeks</option>\n                  <option value={60480}>6 weeks</option>\n                  <option value={70560}>7 weeks</option>\n                  <option value={80640}>8 weeks</option>\n                  <option value={0}>Never expire</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Password Change */}\n          <div className=\"bg-background-tertiary rounded-lg p-4 border border-border-primary\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <HiKey className=\"w-5 h-5 text-primary-400\" />\n              <h4 className=\"font-medium text-white\">Change Password</h4>\n            </div>\n            <p className=\"text-gray-400 text-sm mb-4\">\n              Update your password to keep your account secure\n            </p>\n            <Button\n              variant=\"secondary\"\n              onClick={() => setIsChangePasswordModalOpen(true)}\n            >\n              Change Password\n            </Button>\n          </div>\n\n          {/* Logout Section */}\n          <div className=\"bg-background-tertiary rounded-lg p-4 border border-border-primary\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <HiLogout className=\"w-5 h-5 text-orange-400\" />\n              <h4 className=\"font-medium text-white\">Sign Out</h4>\n            </div>\n            <p className=\"text-gray-400 text-sm mb-4\">\n              Sign out of your account on this device\n            </p>\n            <Button\n              variant=\"secondary\"\n              onClick={handleLogout}\n              disabled={isLoading}\n            >\n              {isLoading ? \"Signing Out...\" : \"Sign Out\"}\n            </Button>\n          </div>\n\n          {/* Account Deletion */}\n          <div className=\"bg-red-500/10 rounded-lg p-4 border border-red-500/30\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\n              <h4 className=\"font-medium text-white\">Danger Zone</h4>\n            </div>\n            <div className=\"space-y-4\">\n              <div>\n                <h5 className=\"font-medium text-red-400 mb-2\">\n                  Account Deactivation\n                </h5>\n                <p className=\"text-gray-400 text-sm mb-3\">\n                  Temporarily deactivate your account. You can reactivate it\n                  later.\n                </p>\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={() => {\n                    setDangerZoneAction(\"deactivate\");\n                    setIsDangerZoneModalOpen(true);\n                  }}\n                >\n                  Deactivate Account\n                </Button>\n              </div>\n\n              <div>\n                <h5 className=\"font-medium text-red-400 mb-2\">\n                  Account Deletion\n                </h5>\n                <p className=\"text-gray-400 text-sm mb-3\">\n                  Permanently delete your account and all associated data. This\n                  action cannot be undone.\n                </p>\n                <Button\n                  variant=\"danger\"\n                  size=\"sm\"\n                  onClick={() => {\n                    setDangerZoneAction(\"delete\");\n                    setIsDangerZoneModalOpen(true);\n                  }}\n                >\n                  Delete Account\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSubscriptionSection = () => <SubscriptionManagement />;\n\n  const renderBillingSection = () => <EnhancedBilling />;\n\n  const renderDataSection = () => <DataManagement />;\n\n  const renderContent = () => {\n    switch (activeSection) {\n      case \"profile\":\n        return renderProfileSection();\n      case \"preferences\":\n        return renderPreferencesSection();\n      case \"notifications\":\n        return renderNotificationsSection();\n      case \"security\":\n        return renderSecuritySection();\n      case \"subscription\":\n        return renderSubscriptionSection();\n      case \"billing\":\n        return renderBillingSection();\n      case \"data\":\n        return renderDataSection();\n      default:\n        return renderProfileSection();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background-primary text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-white mb-2\">Settings</h1>\n          <p className=\"text-gray-400\">Manage your account and preferences</p>\n        </div>\n\n        {/* Error and Success Messages */}\n        {error && (\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\n              <span className=\"text-red-400 font-medium\">Error</span>\n            </div>\n            <p className=\"text-red-300 mt-1\">{error}</p>\n            <Button\n              onClick={() => setError(null)}\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"mt-2\"\n            >\n              Dismiss\n            </Button>\n          </div>\n        )}\n\n        {success && (\n          <div className=\"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <HiCheckCircle className=\"w-5 h-5 text-green-400\" />\n              <span className=\"text-green-400 font-medium\">Success</span>\n            </div>\n            <p className=\"text-green-300 mt-1\">{success}</p>\n            <Button\n              onClick={() => setSuccess(null)}\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"mt-2\"\n            >\n              Dismiss\n            </Button>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Settings Navigation */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n              <nav className=\"space-y-2\">\n                {settingsSections.map((section) => {\n                  const Icon = section.icon;\n                  const isActive = activeSection === section.id;\n\n                  return (\n                    <button\n                      key={section.id}\n                      onClick={() => setActiveSection(section.id)}\n                      className={`\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\n                        transition-all duration-200\n                        ${\n                          isActive\n                            ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\"\n                            : \"text-gray-300 hover:bg-background-tertiary hover:text-white\"\n                        }\n                      `}\n                    >\n                      <Icon className=\"w-5 h-5\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <span className=\"font-medium block\">\n                          {section.label}\n                        </span>\n                        <span className=\"text-xs text-gray-500 block truncate\">\n                          {section.description}\n                        </span>\n                      </div>\n                    </button>\n                  );\n                })}\n              </nav>\n            </div>\n          </div>\n\n          {/* Settings Content */}\n          <div className=\"lg:col-span-3\">\n            <motion.div\n              key={activeSection}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\"\n            >\n              {renderContent()}\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      <ChangePasswordModal\n        isOpen={isChangePasswordModalOpen}\n        onClose={() => setIsChangePasswordModalOpen(false)}\n        onSuccess={() => {\n          // Could show a success toast here\n          console.log(\"Password changed successfully\");\n        }}\n      />\n\n      <DangerZoneModal\n        isOpen={isDangerZoneModalOpen}\n        onClose={() => setIsDangerZoneModalOpen(false)}\n        action={dangerZoneAction}\n      />\n    </div>\n  );\n};\n", "// ----------------------------------------\n// Pricing & Subscription Constants\n// ----------------------------------------\n\nimport { PricingTier, SubscriptionTier } from \"./types\";\n\nexport const PRICING_TIERS: PricingTier[] = [\n  {\n    id: \"Free\",\n    name: \"Free\",\n    price: 0,\n    credits: 25,\n    features: [\n      \"25 credits per month\",\n      \"Basic AI generation\",\n      \"Document upload (up to 5MB)\",\n      \"Community support\",\n      \"Basic analytics\",\n    ],\n    stripePriceId: undefined,\n  },\n  {\n    id: \"Study Starter\",\n    name: \"Study Starter\",\n    price: 29,\n    credits: 150,\n    features: [\n      \"150 credits per month\",\n      \"Advanced AI generation\",\n      \"Document upload (up to 10MB)\",\n      \"Email support\",\n      \"Detailed analytics\",\n      \"Export functionality\",\n    ],\n    stripePriceId: \"price_1RgeXe7lNlVY1bMulc5SRF9z\", // Updated to recurring price\n  },\n  {\n    id: \"Study Pro\",\n    name: \"Study Pro\",\n    price: 59,\n    credits: 350,\n    features: [\n      \"350 credits per month\",\n      \"Premium AI generation\",\n      \"Document upload (up to 25MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Collaboration features\",\n    ],\n    isPopular: true,\n    stripePriceId: \"price_1RgeXe7lNlVY1bMuWT9HTvnr\", // Updated to recurring price\n  },\n  {\n    id: \"Study Master\",\n    name: \"Study Master\",\n    price: 119,\n    credits: 750,\n    features: [\n      \"750 credits per month\",\n      \"Premium AI generation\",\n      \"Document upload (up to 50MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Team collaboration\",\n      \"API access\",\n    ],\n    stripePriceId: \"price_1RgeXe7lNlVY1bMuPU6N3CYa\", // Updated to recurring price\n  },\n  {\n    id: \"Study Elite\",\n    name: \"Study Elite\",\n    price: 239,\n    credits: 1500,\n    features: [\n      \"1,500 credits per month\",\n      \"Premium AI generation\",\n      \"Unlimited document upload\",\n      \"Dedicated support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Team collaboration\",\n      \"API access\",\n      \"White-label options\",\n    ],\n    stripePriceId: \"price_1RgeXe7lNlVY1bMuzHc8Fxmg\", // Updated to recurring price\n  },\n];\n\n// ----------------------------------------\n// Credit System Constants\n// ----------------------------------------\n\nexport const CREDIT_COSTS = {\n  FLASHCARD_GENERATION: 1, // 1 credit per 5 flashcards\n  QUIZ_GENERATION: 1, // 1 credit per 5 quiz questions\n  FLEX_GENERATION: 1, // 1 credit per 5 flex items\n  DOCUMENT_PROCESSING: 0, // Free document processing\n} as const;\n\nexport const ITEMS_PER_CREDIT = {\n  FLASHCARDS: 5,\n  QUIZ_QUESTIONS: 5,\n  FLEX_ITEMS: 5,\n} as const;\n\n// ----------------------------------------\n// Subscription Limits\n// ----------------------------------------\n\nexport const SUBSCRIPTION_LIMITS = {\n  Free: {\n    maxDocumentSize: 5 * 1024 * 1024, // 5MB\n    maxDocuments: 10,\n    maxStudySets: 5,\n    creditsPerMonth: 25,\n  },\n  \"Study Starter\": {\n    maxDocumentSize: 10 * 1024 * 1024, // 10MB\n    maxDocuments: 50,\n    maxStudySets: 25,\n    creditsPerMonth: 150,\n  },\n  \"Study Pro\": {\n    maxDocumentSize: 25 * 1024 * 1024, // 25MB\n    maxDocuments: 200,\n    maxStudySets: 100,\n    creditsPerMonth: 350,\n  },\n  \"Study Master\": {\n    maxDocumentSize: 50 * 1024 * 1024, // 50MB\n    maxDocuments: 500,\n    maxStudySets: 250,\n    creditsPerMonth: 750,\n  },\n  \"Study Elite\": {\n    maxDocumentSize: 100 * 1024 * 1024, // 100MB (effectively unlimited)\n    maxDocuments: 1000,\n    maxStudySets: 500,\n    creditsPerMonth: 1500,\n  },\n} as const;\n\n// ----------------------------------------\n// Feature Flags\n// ----------------------------------------\n\nexport const FEATURE_FLAGS = {\n  Free: {\n    advancedAnalytics: false,\n    exportFunctionality: false,\n    prioritySupport: false,\n    customStudySets: false,\n    collaboration: false,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Starter\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: false,\n    customStudySets: false,\n    collaboration: false,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Pro\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Master\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: true,\n    whiteLabelOptions: false,\n  },\n  \"Study Elite\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: true,\n    whiteLabelOptions: true,\n  },\n} as const;\n\n// ----------------------------------------\n// Helper Functions\n// ----------------------------------------\n\nexport const getPricingTier = (\n  tier: SubscriptionTier\n): PricingTier | undefined => {\n  return PRICING_TIERS.find((t) => t.id === tier);\n};\n\nexport const getSubscriptionLimits = (tier: SubscriptionTier) => {\n  return SUBSCRIPTION_LIMITS[tier];\n};\n\nexport const getFeatureFlags = (tier: SubscriptionTier) => {\n  return FEATURE_FLAGS[tier];\n};\n\nexport const hasFeature = (\n  tier: SubscriptionTier,\n  feature: keyof typeof FEATURE_FLAGS.Free\n): boolean => {\n  return FEATURE_FLAGS[tier][feature];\n};\n\nexport const canUploadDocument = (\n  tier: SubscriptionTier,\n  fileSize: number\n): boolean => {\n  const limits = getSubscriptionLimits(tier);\n  return fileSize <= limits.maxDocumentSize;\n};\n\nexport const getRemainingDocuments = (\n  tier: SubscriptionTier,\n  currentCount: number\n): number => {\n  const limits = getSubscriptionLimits(tier);\n  return Math.max(0, limits.maxDocuments - currentCount);\n};\n\nexport const getRemainingStudySets = (\n  tier: SubscriptionTier,\n  currentCount: number\n): number => {\n  const limits = getSubscriptionLimits(tier);\n  return Math.max(0, limits.maxStudySets - currentCount);\n};\n\n// ----------------------------------------\n// Stripe Configuration\n// ----------------------------------------\n\nexport const STRIPE_CONFIG = {\n  publishableKey: \"NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY\",\n  webhookSecret: \"STRIPE_WEBHOOK_SECRET\",\n  successUrl: \"FRONTEND_URL\" + \"/subscription/success\",\n  cancelUrl: \"FRONTEND_URL\" + \"/subscription/cancel\",\n} as const;\n\n// ----------------------------------------\n// API Endpoints\n// ----------------------------------------\n\nexport const API_ENDPOINTS = {\n  // Authentication\n  LOGIN: \"/api/auth/login\",\n  REGISTER: \"/api/auth/register\",\n  LOGOUT: \"/api/auth/logout\",\n  REFRESH: \"/api/auth/refresh\",\n\n  // User Management\n  PROFILE: \"/api/user/profile\",\n  UPDATE_PROFILE: \"/api/user/update\",\n\n  // Subscription Management\n  CREATE_SUBSCRIPTION: \"/api/subscription/create\",\n  CANCEL_SUBSCRIPTION: \"/api/subscription/cancel\",\n  UPDATE_SUBSCRIPTION: \"/api/subscription/update\",\n  GET_SUBSCRIPTION: \"/api/subscription/status\",\n\n  // Payment Processing\n  CREATE_PAYMENT_INTENT: \"/api/payment/create-intent\",\n  CONFIRM_PAYMENT: \"/api/payment/confirm\",\n  WEBHOOK: \"/api/payment/webhook\",\n\n  // Credit Management\n  GET_CREDITS: \"/api/credits/balance\",\n  PURCHASE_CREDITS: \"/api/credits/purchase\",\n  CREDIT_HISTORY: \"/api/credits/history\",\n\n  // Document Management\n  UPLOAD_DOCUMENT: \"/api/documents/upload\",\n  GET_DOCUMENTS: \"/api/documents\",\n  DELETE_DOCUMENT: \"/api/documents/delete\",\n\n  // Study Set Management\n  CREATE_STUDY_SET: \"/api/study-sets/create\",\n  GET_STUDY_SETS: \"/api/study-sets\",\n  DELETE_STUDY_SET: \"/api/study-sets/delete\",\n\n  // AI Generation\n  GENERATE_FLASHCARDS: \"/api/ai/generate-flashcards\",\n  GENERATE_QUIZ: \"/api/ai/generate-quiz\",\n  GENERATE_FLEX: \"/api/ai/generate-flex\",\n} as const;\n\n// ----------------------------------------\n// One-Time Credit Packages\n// ----------------------------------------\n\nexport interface CreditPackage {\n  id: string;\n  name: string;\n  credits: number;\n  price: number;\n  valuePerCredit: number;\n  description: string;\n  stripePriceId?: string;\n}\n\nexport const CREDIT_PACKAGES: CreditPackage[] = [\n  {\n    id: \"starter_pack\",\n    name: \"Starter Pack\",\n    credits: 50,\n    price: 15,\n    valuePerCredit: 0.3,\n    description: \"Perfect for testing features\",\n    stripePriceId: \"price_1Rge2c7lNlVY1bMu8AobMuTT\", // Live price ID\n  },\n  {\n    id: \"boost_pack\",\n    name: \"Boost Pack\",\n    credits: 150,\n    price: 39,\n    valuePerCredit: 0.26,\n    description: \"Great for exam preparation\",\n    stripePriceId: \"price_1Rge2m7lNlVY1bMuJywdctDG\", // Live price ID\n  },\n  {\n    id: \"power_pack\",\n    name: \"Power Pack\",\n    credits: 400,\n    price: 89,\n    valuePerCredit: 0.22,\n    description: \"For heavy users\",\n    stripePriceId: \"price_1Rge2w7lNlVY1bMufspAzvdn\", // Live price ID\n  },\n  {\n    id: \"mega_pack\",\n    name: \"Mega Pack\",\n    credits: 1000,\n    price: 199,\n    valuePerCredit: 0.2,\n    description: \"Maximum value for long-term use\",\n    stripePriceId: \"price_1Rge357lNlVY1bMuuzBtmOHM\", // Live price ID\n  },\n];\n", "// ----------------------------------------\n// User & Authentication Types\n// ----------------------------------------\n\nexport interface UserProfile {\n  id: string; // Corresponds to Supabase auth.uid()\n  email: string;\n  name?: string;\n  subscription_tier: \"Free\" | \"Study Starter\" | \"Study Pro\" | \"Study Master\" | \"Study Elite\";\n  credits_remaining: number;\n  is_active: boolean;\n  last_login?: string;\n  last_credit_reset?: string; // ISO 8601 date string for freemium monthly resets\n  subscription_expires_at?: string; // ISO 8601 date string\n  stripe_customer_id?: string;\n  stripe_subscription_id?: string;\n  created_at: string; // ISO 8601 date string\n  updated_at: string; // ISO 8601 date string\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: UserProfile;\n  token?: string;\n  error?: string;\n}\n\n// ----------------------------------------\n// Subscription & Pricing Types\n// ----------------------------------------\n\nexport type SubscriptionTier = \"Free\" | \"Study Starter\" | \"Study Pro\" | \"Study Master\" | \"Study Elite\";\n\nexport interface PricingTier {\n  id: SubscriptionTier;\n  name: string;\n  price: number; // Monthly price in USD\n  credits: number; // Credits included per month\n  features: string[];\n  isPopular?: boolean;\n  stripePriceId?: string; // Stripe Price ID for subscriptions\n}\n\nexport interface SubscriptionStatus {\n  tier: SubscriptionTier;\n  status: \"active\" | \"canceled\" | \"past_due\" | \"incomplete\" | \"trialing\";\n  current_period_start: string;\n  current_period_end: string;\n  cancel_at_period_end: boolean;\n  credits_remaining: number;\n  credits_reset_date: string;\n}\n\nexport interface PaymentIntent {\n  id: string;\n  amount: number;\n  currency: string;\n  status: string;\n  client_secret: string;\n}\n\nexport interface CreditPurchase {\n  id: string;\n  user_id: string;\n  credits_purchased: number;\n  amount_paid: number;\n  stripe_payment_intent_id: string;\n  created_at: string;\n}\n\n// ----------------------------------------\n// Document Management Types\n// ----------------------------------------\n\nexport type DocumentFileType = \"pdf\" | \"docx\" | \"txt\" | \"pptx\";\n\nexport interface DocumentMetadata {\n  id: string;\n  user_id: string;\n  filename: string;\n  file_type: DocumentFileType;\n  file_size: number; // in bytes\n  supabase_storage_path: string;\n  uploaded_at: string; // ISO 8601 date string\n  is_processed: boolean;\n  processing_error?: string;\n  page_count?: number; // Total number of pages (for PDFs, PPTx, etc.)\n}\n\nexport interface DocumentWithContent extends DocumentMetadata {\n  content_text: string;\n}\n\n// ----------------------------------------\n// Study Sets & Content Types\n// ----------------------------------------\n\nexport type StudySetType = \"flashcards\" | \"quiz\";\n\n// ----------------------------------------\n// Difficulty and Content Length Enums\n// ----------------------------------------\n\nexport enum DifficultyLevel {\n  EASY = \"easy\",\n  MEDIUM = \"medium\",\n  HARD = \"hard\",\n  COLLEGE = \"college\",\n  GRADUATE = \"graduate\",\n  PHD = \"phd\",\n}\n\nexport enum ContentLength {\n  SHORT = \"short\",\n  MEDIUM = \"medium\",\n  LONG = \"long\",\n}\n\n// Type aliases for database compatibility\nexport type DifficultyLevelType = keyof typeof DifficultyLevel;\nexport type ContentLengthType = keyof typeof ContentLength;\n\nexport interface StudySet {\n  id: string;\n  user_id: string;\n  name: string;\n  type: StudySetType;\n  is_ai_generated: boolean;\n  source_documents?: { id: string; filename: string }[]; // Array of document IDs and names\n  custom_prompt?: string;\n  created_at: string; // ISO 8601 date string\n  updated_at: string; // ISO 8601 date string\n  flashcard_count?: number;\n  quiz_question_count?: number;\n  last_studied_at?: string; // ISO 8601 date string\n}\n\nexport interface Flashcard {\n  id: string;\n  study_set_id: string;\n  front: string;\n  back: string;\n  is_flagged: boolean;\n  is_ai_generated: boolean;\n  difficulty_level?: DifficultyLevel;\n  content_length?: ContentLength;\n  times_reviewed: number;\n  last_reviewed_at?: string; // ISO 8601 date string\n}\n\nexport type QuestionType =\n  | \"multiple_choice\"\n  | \"select_all\"\n  | \"true_false\"\n  | \"short_answer\";\n\nexport interface QuizQuestion {\n  id: string;\n  study_set_id: string;\n  question_text: string;\n  question_type: QuestionType;\n  options?: string[]; // For multiple_choice, select_all\n  correct_answers: string[]; // Can be ['True'] or ['False'] for true_false\n  explanation?: string;\n  is_ai_generated: boolean;\n  difficulty_level?: DifficultyLevel;\n  content_length?: ContentLength;\n  times_attempted: number;\n  times_correct: number;\n}\n\n// ----------------------------------------\n// Credit System Types\n// ----------------------------------------\n\nexport interface CreditTransaction {\n  id: string;\n  user_id: string;\n  credits_used: number; // Negative for additions\n  operation_type: string;\n  description: string;\n  metadata?: Record<string, any>;\n  study_set_id?: string;\n  created_at: string; // ISO 8601 date string\n}\n\nexport interface AIOperationCost {\n  operation_type: string;\n  credits_required: number;\n  operations_per_credit: number;\n  is_active: boolean;\n}\n\n// ----------------------------------------\n// API Response Types\n// ----------------------------------------\n\nexport interface APIResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> extends APIResponse<T[]> {\n  total: number;\n  page: number;\n  limit: number;\n  hasMore: boolean;\n}\n\n// ----------------------------------------\n// Component Props Types\n// ----------------------------------------\n\nexport interface BaseComponentProps {\n  className?: string;\n  \"data-testid\"?: string;\n}\n\n// Button component props\nexport interface ButtonProps extends BaseComponentProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  variant?: \"primary\" | \"secondary\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  isLoading?: boolean;\n  disabled?: boolean;\n  type?: \"button\" | \"submit\" | \"reset\";\n}\n\n// Input component props\nexport interface InputProps extends BaseComponentProps {\n  label?: string;\n  placeholder?: string;\n  value: string;\n  onChange: (value: string) => void;\n  type?: \"text\" | \"email\" | \"password\" | \"number\";\n  error?: string;\n  required?: boolean;\n  disabled?: boolean;\n}\n\n// Modal component props\nexport interface ModalProps extends BaseComponentProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  children: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n}\n\n// ----------------------------------------\n// Study Session Types\n// ----------------------------------------\n\nexport interface StudySession {\n  id: string;\n  user_id: string;\n  study_set_id: string;\n  session_type: StudySetType;\n  started_at: string;\n  ended_at?: string;\n  total_items: number;\n  completed_items: number;\n  correct_answers?: number; // For quiz sessions\n  session_data?: Record<string, any>; // Store session-specific data\n}\n\nexport interface StudyProgress {\n  study_set_id: string;\n  total_flashcards?: number;\n  reviewed_flashcards?: number;\n  flagged_flashcards?: number;\n  total_quiz_questions?: number;\n  answered_questions?: number;\n  correct_answers?: number;\n  last_studied_at?: string;\n  study_streak?: number;\n}\n\n// ----------------------------------------\n// AI Generation Types\n// ----------------------------------------\n\nexport interface AIGenerationRequest {\n  documents: string[]; // Document IDs\n  documentPageRanges?: {\n    [documentId: string]: { startPage: number; endPage: number };\n  }; // Page ranges for documents\n  studySetType: StudySetType;\n  customPrompt?: string;\n  itemCount?: number;\n  difficultyLevel?: DifficultyLevel;\n  contentLength?: ContentLength;\n}\n\nexport interface AIGenerationResult {\n  studySetId: string;\n  itemsGenerated: number;\n  creditsUsed: number;\n  processingTime: number;\n}\n\n// Helper functions for enum conversions\nexport const difficultyLevelToString = (level: DifficultyLevel): string => {\n  const labels: Record<DifficultyLevel, string> = {\n    [DifficultyLevel.EASY]: \"Easy\",\n    [DifficultyLevel.MEDIUM]: \"Medium\",\n    [DifficultyLevel.HARD]: \"Hard\",\n    [DifficultyLevel.COLLEGE]: \"College\",\n    [DifficultyLevel.GRADUATE]: \"Graduate\",\n    [DifficultyLevel.PHD]: \"PhD\",\n  };\n  return labels[level];\n};\n\nexport const contentLengthToString = (length: ContentLength): string => {\n  const labels: Record<ContentLength, string> = {\n    [ContentLength.SHORT]: \"Short\",\n    [ContentLength.MEDIUM]: \"Medium\",\n    [ContentLength.LONG]: \"Long\",\n  };\n  return labels[length];\n};\n\nexport const stringToDifficultyLevel = (str: string): DifficultyLevel => {\n  const normalized = str.toLowerCase();\n  switch (normalized) {\n    case \"easy\":\n      return DifficultyLevel.EASY;\n    case \"medium\":\n      return DifficultyLevel.MEDIUM;\n    case \"hard\":\n      return DifficultyLevel.HARD;\n    case \"college\":\n      return DifficultyLevel.COLLEGE;\n    case \"graduate\":\n      return DifficultyLevel.GRADUATE;\n    case \"phd\":\n      return DifficultyLevel.PHD;\n    default:\n      return DifficultyLevel.MEDIUM;\n  }\n};\n\nexport const stringToContentLength = (str: string): ContentLength => {\n  const normalized = str.toLowerCase();\n  switch (normalized) {\n    case \"short\":\n      return ContentLength.SHORT;\n    case \"medium\":\n      return ContentLength.MEDIUM;\n    case \"long\":\n      return ContentLength.LONG;\n    default:\n      return ContentLength.MEDIUM;\n  }\n};\n\n// Helper functions for number conversions (for backward compatibility)\nexport const difficultyLevelToNumber = (level: DifficultyLevel): number => {\n  const mapping: Record<DifficultyLevel, number> = {\n    [DifficultyLevel.EASY]: 1,\n    [DifficultyLevel.MEDIUM]: 3,\n    [DifficultyLevel.HARD]: 4,\n    [DifficultyLevel.COLLEGE]: 5,\n    [DifficultyLevel.GRADUATE]: 6,\n    [DifficultyLevel.PHD]: 7,\n  };\n  return mapping[level];\n};\n\nexport const numberToDifficultyLevel = (num: number): DifficultyLevel => {\n  switch (num) {\n    case 1:\n      return DifficultyLevel.EASY;\n    case 2:\n      return DifficultyLevel.EASY; // Map 2 to easy for backward compatibility\n    case 3:\n      return DifficultyLevel.MEDIUM;\n    case 4:\n      return DifficultyLevel.HARD;\n    case 5:\n      return DifficultyLevel.COLLEGE;\n    case 6:\n      return DifficultyLevel.GRADUATE;\n    case 7:\n      return DifficultyLevel.PHD;\n    default:\n      return DifficultyLevel.MEDIUM;\n  }\n};\n\n// ----------------------------------------\n// Utility Types\n// ----------------------------------------\n\nexport type LoadingState = \"idle\" | \"loading\" | \"success\" | \"error\";\n\nexport interface ErrorState {\n  message: string;\n  code?: string;\n  details?: Record<string, any>;\n}\n\nexport type SortDirection = \"asc\" | \"desc\";\n\nexport interface SortConfig {\n  field: string;\n  direction: SortDirection;\n}\n\nexport interface FilterConfig {\n  field: string;\n  value: any;\n  operator?: \"eq\" | \"ne\" | \"gt\" | \"lt\" | \"gte\" | \"lte\" | \"in\" | \"like\";\n}\n"], "names": ["difficultyOptions", "value", "DifficultyLevel", "EASY", "label", "description", "MEDIUM", "HARD", "COLLEGE", "GRADUATE", "PHD", "DifficultySelector", "_ref", "onChange", "className", "disabled", "_jsxs", "concat", "children", "_jsx", "map", "option", "isSelected", "colorClasses", "difficulty", "getSelectedDifficultyColor", "getDifficultyColor", "type", "onClick", "title", "fill", "viewBox", "fillRule", "d", "clipRule", "stripePromise", "RELEASE_TRAIN", "ORIGIN", "STRIPE_JS_URL", "V3_URL_REGEX", "STRIPE_JS_URL_REGEX", "EXISTING_SCRIPT_MESSAGE", "isStripeJSURL", "url", "test", "injectScript", "params", "queryString", "advancedFraudSignals", "script", "document", "createElement", "src", "headOrBody", "head", "body", "Error", "append<PERSON><PERSON><PERSON>", "stripePromise$1", "onErrorListener", "onLoadListener", "loadScript", "Promise", "resolve", "reject", "window", "Stripe", "console", "warn", "scripts", "querySelectorAll", "i", "length", "findScript", "_script$parentNode", "removeEventListener", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "cause", "onError", "addEventListener", "error", "loadCalled", "getStripePromise", "then", "subscriptionPlans", "id", "name", "price", "interval", "features", "popular", "_len", "arguments", "args", "Array", "_key", "startTime", "Date", "now", "maybeStripe", "isTestKey", "match", "version", "runtimeVersionToUrlVersion", "expectedVersion", "stripe", "apply", "undefined", "_registerWrapper", "registerWrapper", "initStripe", "loadStripe", "process", "SubscriptionManagement", "_subscriptionData$cur", "_subscriptionData$cur2", "subscriptionData", "setSubscriptionData", "useState", "isLoading", "setIsLoading", "isChanging", "setIsChanging", "setError", "couponCode", "setCouponCode", "appliedCoupon", "setAppliedCoupon", "useEffect", "fetchSubscriptionData", "async", "token", "localStorage", "getItem", "response", "fetch", "headers", "Authorization", "ok", "result", "json", "success", "data", "err", "message", "currentPlan", "status", "nextBillingDate", "toISOString", "validateCouponCode", "code", "trim", "method", "JSON", "stringify", "coupon", "toUpperCase", "valid", "discount", "handleDowngradeToFree", "confirm", "immediate", "HiExclamationCircle", "char<PERSON>t", "slice", "HiInformationCircle", "cancelAtPeriodEnd", "toLocaleDateString", "<PERSON><PERSON>", "variant", "size", "HiRefresh", "HiTag", "e", "target", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "plan", "_subscriptionData$cur3", "isCurrent", "finalPrice", "Math", "max", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "HiStar", "_Fragment", "toFixed", "feature", "index", "planId", "successUrl", "location", "origin", "cancelUrl", "redirectToCheckout", "sessionId", "handleStripeCheckout", "CREDIT_PACKAGES", "pkg", "credits", "valuePerCredit", "packageId", "paymentUrl", "href", "handlePurchaseCredits", "HiShoppingCart", "DataManagement", "isExporting", "setIsExporting", "isClearing", "setIsClearing", "setSuccess", "dataStats", "setDataStats", "studySets", "flashcards", "quizzes", "documents", "totalSize", "exportData", "setExportData", "analytics", "preferences", "handleClearData", "dataType", "fetchDataStats", "HiCheckCircle", "HiDownload", "Object", "entries", "key", "replace", "_objectSpread", "blob", "URL", "createObjectURL", "a", "download", "split", "click", "revokeObjectURL", "HiTrash", "EnhancedBilling", "billingData", "setBillingData", "isUpdating", "setIsUpdating", "fetchBillingData", "paymentMethods", "last4", "brand", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "isDefault", "invoices", "number", "amount", "currency", "date", "dueDate", "nextInvoice", "getStatusIcon", "<PERSON><PERSON><PERSON>", "HiX", "getStatusColor", "HiCreditCard", "_method$brand", "toString", "padStart", "paymentMethodId", "handleSetDefaultPaymentMethod", "invoice", "invoiceId", "handleDownloadInvoice", "TwoFactorAuth", "enabled", "onToggle", "isSetupMode", "setIsSetupMode", "setupData", "setSetupData", "verificationCode", "setVerificationCode", "showSecret", "setShowSecret", "showBackupCodes", "setShowBackupCodes", "handleVerify2FA", "createClient", "supabase", "REACT_APP_SUPABASE_URL", "REACT_APP_SUPABASE_ANON_KEY", "challengeData", "challengeError", "auth", "mfa", "challenge", "factorId", "verifyError", "verify", "challengeId", "handleCopyToClipboard", "text", "navigator", "clipboard", "writeText", "setTimeout", "handleCancelSetup", "HiShieldCheck", "backupCodes", "join", "HiClipboard", "qrCode", "alt", "secret", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Input", "factorsData", "factorsError", "listFactors", "factor", "totp", "unenrollError", "unenroll", "enroll", "factorType", "friendlyName", "qr_code", "<PERSON><PERSON><PERSON>", "ChangePasswordModal", "isOpen", "onClose", "onSuccess", "formData", "setFormData", "currentPassword", "newPassword", "confirmPassword", "showPasswords", "setShowPasswords", "current", "new", "handleChange", "field", "handleInputChange", "prev", "togglePasswordVisibility", "resetForm", "handleClose", "onSubmit", "preventDefault", "DangerZoneModal", "action", "confirmationText", "setConfirmationText", "step", "setStep", "config", "deactivate", "confirmText", "buttonText", "warningText", "endpoint", "delete", "isConfirmationValid", "handleSubmit", "confirmation", "removeItem", "handleBack", "settingsSections", "icon", "HiUser", "HiCog", "<PERSON><PERSON><PERSON>", "HiDatabase", "SettingsPage", "activeSection", "setActiveSection", "user", "logout", "useAuthStore", "navigate", "useNavigate", "profileData", "setProfileData", "email", "bio", "avatar", "setPreferences", "theme", "language", "studyReminders", "autoSave", "defaultStudyMode", "sessionDuration", "difficultyLevel", "notifications", "setNotifications", "emailNotifications", "weeklyProgress", "marketingEmails", "achievementNotifications", "streak<PERSON><PERSON><PERSON><PERSON>", "securitySettings", "setSecuritySettings", "twoFactorEnabled", "loginNotifications", "sessionTimeout", "isChangePasswordModalOpen", "setIsChangePasswordModalOpen", "isDangerZoneModalOpen", "setIsDangerZoneModalOpen", "dangerZoneAction", "setDangerZoneAction", "backendPrefs", "study_reminders", "auto_save", "default_study_mode", "session_duration", "difficulty_level", "loadUserSettings", "handleSaveProfile", "FormData", "append", "handleSavePreferences", "backendPreferences", "handleSaveNotifications", "handleLogout", "renderProfileSection", "_user$name", "_user$name$charAt", "input", "accept", "onchange", "_files", "file", "files", "HiUpload", "rows", "section", "Icon", "isActive", "x", "renderContent", "HiMoon", "<PERSON><PERSON><PERSON>", "parseInt", "level", "HiLogout", "log", "PRICING_TIERS", "stripePriceId", "isPopular", "ContentLength", "difficultyLevelToString", "difficultyLevelToNumber", "numberToDifficultyLevel", "num"], "sourceRoot": ""}