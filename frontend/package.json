{"name": "chewy-ai-frontend", "private": true, "version": "0.0.0", "proxy": "http://localhost:3001", "scripts": {"start": "HOST=0.0.0.0 PORT=3000 FAST_REFRESH=false DANGEROUSLY_DISABLE_HOST_CHECK=true react-scripts start", "build": "react-scripts build", "serve": "node server.js", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^4.28.0", "@types/react-window": "^1.8.8", "express": "^4.18.2", "framer-motion": "^10.12.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-icons": "^4.8.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "react-window": "^1.8.11", "zustand": "^4.3.6"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.21", "tailwindcss": "^3.3.0", "typescript": "^5.0.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}