import React from 'react';
import { motion } from 'framer-motion';
import { 
  HiCreditCard, 
  HiTrendingUp, 
  HiClock,
  HiSparkles
} from 'react-icons/hi';

interface CreditBalanceProps {
  balance: number;
  userTier: string;
  isLoading: boolean;
}

export const CreditBalance: React.FC<CreditBalanceProps> = ({
  balance,
  userTier,
  isLoading
}) => {
  const getTierColor = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'pro':
        return 'text-purple-400';
      case 'basic':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'pro':
        return <HiSparkles className="w-5 h-5" />;
      case 'basic':
        return <HiTrendingUp className="w-5 h-5" />;
      default:
        return <HiCreditCard className="w-5 h-5" />;
    }
  };

  const getBalanceStatus = (balance: number) => {
    if (balance >= 100) return { color: 'text-green-400', status: 'Excellent' };
    if (balance >= 50) return { color: 'text-yellow-400', status: 'Good' };
    if (balance >= 10) return { color: 'text-orange-400', status: 'Low' };
    return { color: 'text-red-400', status: 'Critical' };
  };

  const balanceStatus = getBalanceStatus(balance);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Main Balance Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30"
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-primary-500/20 rounded-lg">
              <HiCreditCard className="w-6 h-6 text-primary-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">Credit Balance</h3>
              <p className="text-gray-400 text-sm">Available for AI generation</p>
            </div>
          </div>
          
          <div className={`flex items-center space-x-2 ${getTierColor(userTier)}`}>
            {getTierIcon(userTier)}
            <span className="font-medium">{userTier} Plan</span>
          </div>
        </div>

        <div className="flex items-end space-x-4">
          <div>
            {isLoading ? (
              <div className="animate-pulse">
                <div className="h-12 w-32 bg-gray-600 rounded"></div>
              </div>
            ) : (
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <span className="text-4xl font-bold text-white">{balance.toLocaleString()}</span>
                <span className="text-xl text-gray-400 ml-2">credits</span>
              </motion.div>
            )}
          </div>
          
          <div className={`flex items-center space-x-1 ${balanceStatus.color} mb-2`}>
            <div className={`w-2 h-2 rounded-full ${balanceStatus.color.replace('text-', 'bg-')}`}></div>
            <span className="text-sm font-medium">{balanceStatus.status}</span>
          </div>
        </div>

        {balance < 10 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg"
          >
            <div className="flex items-center space-x-2">
              <HiClock className="w-4 h-4 text-red-400" />
              <span className="text-red-400 text-sm font-medium">Low Balance Warning</span>
            </div>
            <p className="text-red-300 text-sm mt-1">
              Consider purchasing more credits to continue using AI features.
            </p>
          </motion.div>
        )}
      </motion.div>

      {/* Quick Stats Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="bg-background-secondary rounded-lg p-6 border border-border-primary"
      >
        <h4 className="text-lg font-semibold text-white mb-4">Quick Stats</h4>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm">Plan Type</span>
            <span className={`font-medium ${getTierColor(userTier)}`}>{userTier}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm">Status</span>
            <span className={`font-medium ${balanceStatus.color}`}>{balanceStatus.status}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm">Credits Available</span>
            <span className="text-white font-medium">{balance}</span>
          </div>

          {userTier.toLowerCase() === 'free' && (
            <div className="pt-3 border-t border-border-secondary">
              <p className="text-gray-400 text-xs">
                Upgrade to Basic or Pro for more credits and features
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};
