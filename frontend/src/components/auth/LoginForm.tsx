import React, { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import useAuthStore from "../../stores/authStore";
import { Button } from "../common/Button";
import { Input } from "../common/Input";
import { Checkbox } from "../common/Checkbox";
import { GoogleAuthButton } from "./GoogleAuthButton";

export const LoginForm: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(true);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [errors, setErrors] = useState<{
    email?: string;
    password?: string;
    general?: string;
  }>({});

  const { login, signInWithGoogle, isLoading, isAuthenticated } =
    useAuthStore();
  const navigate = useNavigate();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, navigate]);

  const validateForm = () => {
    const newErrors: typeof errors = {};
    if (!email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = "Email is invalid";
    if (!password) newErrors.password = "Password is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    const result = await login(email, password, rememberMe);

    if (result.success) {
      navigate("/dashboard");
    } else {
      setErrors({ general: result.error });
    }
  };

  const handleGoogleAuth = async () => {
    setIsGoogleLoading(true);
    setErrors({});

    try {
      const result = await signInWithGoogle();
      if (result.error) {
        console.error("Google sign-in failed:", result.error);
        setErrors({
          general: result.error.includes("not configured")
            ? "Google sign-in is not properly configured. Please contact support."
            : result.error || "Failed to sign in with Google",
        });
        setIsGoogleLoading(false);
      }
      // If successful, the user will be redirected to Google and then back to our callback
      // Loading state will be cleared when the page redirects
    } catch (error) {
      console.error("Google sign-in error:", error);
      setErrors({ general: "Network error during Google sign-in" });
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background-primary">
      <div className="max-w-md w-full space-y-8 p-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
            Sign in to ChewyAI
          </h2>
          <p className="mt-2 text-center text-sm text-gray-400">
            Or{" "}
            <Link
              to="/signup"
              className="font-medium text-primary-500 hover:text-primary-400"
            >
              create a new account
            </Link>
          </p>
        </div>

        {/* Google Sign In */}
        <div className="space-y-4">
          <GoogleAuthButton
            onGoogleAuth={handleGoogleAuth}
            isLoading={isGoogleLoading}
            disabled={isLoading}
            text="Sign in with Google"
          />

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-background-primary text-gray-400">
                Or continue with email
              </span>
            </div>
          </div>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {errors.general && (
            <div className="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded">
              {errors.general}
            </div>
          )}
          <div className="space-y-4">
            <Input
              label="Email address"
              type="email"
              value={email}
              onChange={setEmail}
              error={errors.email}
              placeholder="Enter your email"
              required
            />
            <Input
              label="Password"
              type="password"
              value={password}
              onChange={setPassword}
              error={errors.password}
              placeholder="Enter your password"
              required
            />
          </div>

          <div className="flex items-center justify-between">
            <Checkbox
              checked={rememberMe}
              onChange={setRememberMe}
              label="Stay signed in"
              description="Keep me logged in across browser sessions"
              size="sm"
            />
          </div>

          <Button
            type="submit"
            isLoading={isLoading}
            disabled={isGoogleLoading}
            className="w-full"
            size="lg"
          >
            Sign in
          </Button>
        </form>
      </div>
    </div>
  );
};
