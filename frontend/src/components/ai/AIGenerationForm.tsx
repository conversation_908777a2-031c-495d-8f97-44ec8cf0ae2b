import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAIStore } from "../../stores/aiStore";
import useAuthStore from "../../stores/authStore";
import { useDocumentStore } from "../../stores/documentStore";
import { useDialog } from "../../contexts/DialogContext";
import { DocumentSelector } from "./DocumentSelector";
import { CompactUpload } from "./CompactUpload";
import { Button } from "../common/Button";
import { Input } from "../common/Input";
import { CountInput } from "../common/CountInput";
import { AIGenerationProgress } from "../common/ProgressBar";
import { DifficultySelector } from "../common/DifficultySelector";
import { ContentLengthSelector } from "../common/ContentLengthSelector";
import {
  DifficultyLevel,
  ContentLength,
  QuestionType,
} from "../../../../shared/types";

interface AIGenerationFormProps {
  type: "flashcards" | "quiz";
  studySetId?: string; // Optional - for generating more content in existing study set
}

export const AIGenerationForm: React.FC<AIGenerationFormProps> = ({
  type,
  studySetId,
}) => {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [documentPageRanges, setDocumentPageRanges] = useState<{
    [documentId: string]: { startPage: number; endPage: number };
  }>({});
  const [studySetName, setStudySetName] = useState("");
  const [itemCount, setItemCount] = useState(25);
  const [customPrompt, setCustomPrompt] = useState("");
  const [difficultyLevel, setDifficultyLevel] = useState<DifficultyLevel>(
    DifficultyLevel.MEDIUM
  );
  const [contentLength, setContentLength] = useState<ContentLength>(
    ContentLength.MEDIUM
  );
  const [selectedQuestionTypes, setSelectedQuestionTypes] = useState<
    QuestionType[]
  >(["multiple_choice", "select_all", "true_false", "short_answer"]);
  const [existingContent, setExistingContent] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const {
    generateFlashcards,
    generateQuiz,
    generateMoreFlashcards,
    generateMoreQuizQuestions,
    isGenerating,
    generationProgress,
  } = useAIStore();
  const { user, updateUser } = useAuthStore();
  const { fetchDocuments } = useDocumentStore();
  const { alert } = useDialog();
  const navigate = useNavigate();

  // Fetch existing content to prevent duplicates
  useEffect(() => {
    if (studySetId) {
      fetchExistingContent();
    }
  }, [studySetId]);

  const fetchExistingContent = async () => {
    if (!studySetId) return;

    try {
      const token = localStorage.getItem("auth_token");
      const endpoint =
        type === "flashcards"
          ? `/api/flashcards/study-set/${studySetId}`
          : `/api/quiz-questions/study-set/${studySetId}`;

      const response = await fetch(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const content =
            type === "flashcards"
              ? result.data.map((item: any) => item.front)
              : result.data.map((item: any) => item.question_text);
          setExistingContent(content);
        }
      }
    } catch (error) {
      console.error("Failed to fetch existing content:", error);
    }
  };

  // Handle upload completion - refresh document list
  const handleUploadComplete = () => {
    fetchDocuments();
  };

  // Handle page range changes
  const handlePageRangeChange = (
    documentId: string,
    range: { startPage: number; endPage: number }
  ) => {
    setDocumentPageRanges((prev) => ({
      ...prev,
      [documentId]: range,
    }));
  };

  // Handle question type selection
  const handleQuestionTypeToggle = (questionType: QuestionType) => {
    setSelectedQuestionTypes((prev) => {
      if (prev.includes(questionType)) {
        return prev.filter((type) => type !== questionType);
      } else {
        return [...prev, questionType];
      }
    });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (selectedDocuments.length === 0) {
      newErrors.documents = "Please select at least one document";
    }

    if (!studySetName.trim() && !studySetId) {
      newErrors.name = "Study set name is required";
    }

    if (itemCount < 1 || itemCount > 100) {
      newErrors.count = "Item count must be between 1 and 100";
    }

    if (type === "quiz" && selectedQuestionTypes.length === 0) {
      newErrors.questionTypes = "Please select at least one question type";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Check credits
    const requiredCredits = Math.ceil(itemCount / 5);
    if (!user || user.credits_remaining < requiredCredits) {
      await alert({
        title: "Insufficient Credits",
        message: !user
          ? `You need ${requiredCredits} credits to generate ${itemCount} items. Please log in to check your credit balance.`
          : `You need ${requiredCredits} credits to generate ${itemCount} items, but you only have ${user.credits_remaining} credits remaining.`,
        variant: "warning",
      });
      return;
    }

    try {
      const params = {
        documentIds: selectedDocuments,
        documentPageRanges,
        name: studySetName.trim(),
        count: itemCount,
        customPrompt: customPrompt.trim() || undefined,
        difficultyLevel,
        contentLength,
        ...(type === "quiz" && { questionTypes: selectedQuestionTypes }),
        ...(existingContent.length > 0 && { existingContent }),
        ...(studySetId && { studySetId }),
      };

      let result;
      if (type === "flashcards") {
        if (studySetId) {
          // Use generateMoreFlashcards for existing study sets
          const moreFlashcardsParams = {
            studySetId,
            documentIds: selectedDocuments,
            documentPageRanges,
            count: itemCount,
            customPrompt: customPrompt.trim() || undefined,
            difficultyLevel,
            contentLength,
            existingContent:
              existingContent.length > 0 ? existingContent : undefined,
          };
          result = await generateMoreFlashcards(moreFlashcardsParams);
        } else {
          result = await generateFlashcards(params);
        }
      } else {
        if (studySetId) {
          // Use generateMoreQuizQuestions for existing study sets
          const moreQuizParams = {
            studySetId,
            documentIds: selectedDocuments,
            documentPageRanges,
            count: itemCount,
            customPrompt: customPrompt.trim() || undefined,
            difficultyLevel,
            contentLength,
            questionTypes: selectedQuestionTypes,
            existingContent:
              existingContent.length > 0 ? existingContent : undefined,
          };
          result = await generateMoreQuizQuestions(moreQuizParams);
        } else {
          result = await generateQuiz(params);
        }
      }

      // Update user credits
      updateUser({ credits_remaining: result.creditsRemaining });

      // Navigate to the study set
      if (studySetId) {
        // For existing study sets, navigate back to the study set
        navigate(`/study-sets/${studySetId}`);
      } else {
        // For new study sets, navigate to the created study set
        navigate(`/study-sets/${(result as any).studySet.id}`);
      }
    } catch (error: any) {
      console.error("Generation error:", error);
      await alert({
        title: "Generation Error",
        message:
          error.message ||
          "Failed to generate study materials. Please try again.",
        variant: "error",
      });
    }
  };

  // Calculate credit cost based on new 1:5 ratio (1 credit = 5 items)
  const creditCost = Math.ceil(itemCount / 5);
  const hasEnoughCredits = user && user.credits_remaining >= creditCost;

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">
            {studySetId ? "Add More" : "Generate"}{" "}
            {type === "flashcards" ? "Flashcards" : "Quiz Questions"}
          </h2>
          <p className="text-gray-400">
            {studySetId
              ? "Add more AI-powered study materials to your existing study set"
              : "Create AI-powered study materials from your documents"}
          </p>
        </div>

        {/* Credit Info */}
        <div className="bg-background-secondary rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-300">
                Cost:{" "}
                <span className="font-medium text-primary-400">
                  {creditCost} credit{creditCost !== 1 ? "s" : ""}
                </span>
                <span className="text-gray-500 ml-1">({itemCount} items)</span>
              </p>
              <p className="text-sm text-gray-400">
                Your balance: {user?.credits_remaining || 0} credits
              </p>
            </div>
            {!hasEnoughCredits && (
              <Button
                onClick={() => navigate("/credits")}
                variant="secondary"
                size="sm"
              >
                Buy Credits
              </Button>
            )}
          </div>
        </div>

        {/* Document Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Upload New Documents
          </label>
          <CompactUpload onUploadComplete={handleUploadComplete} />
        </div>

        {/* Document Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Select Documents *
          </label>
          <div className="bg-background-secondary rounded-lg p-4">
            <DocumentSelector
              selectedDocuments={selectedDocuments}
              onSelectionChange={setSelectedDocuments}
              documentPageRanges={documentPageRanges}
              onPageRangeChange={handlePageRangeChange}
              maxSelection={5}
            />
          </div>
          {errors.documents && (
            <p className="mt-1 text-sm text-red-500">{errors.documents}</p>
          )}
        </div>

        {/* Study Set Name - Only show for new study sets */}
        {!studySetId && (
          <Input
            label="Study Set Name"
            value={studySetName}
            onChange={setStudySetName}
            placeholder={`My ${type === "flashcards" ? "Flashcards" : "Quiz"}`}
            error={errors.name}
            required
          />
        )}

        {/* Item Count */}
        <CountInput
          label={`Number of ${
            type === "flashcards" ? "Flashcards" : "Questions"
          }`}
          value={itemCount}
          onChange={setItemCount}
          min={1}
          max={100}
          placeholder="Enter number (1-100)"
          error={errors.count}
          disabled={isGenerating}
        />

        {/* Difficulty Level */}
        <DifficultySelector
          value={difficultyLevel}
          onChange={setDifficultyLevel}
          className="bg-background-secondary rounded-lg p-4"
        />

        {/* Content Length */}
        <ContentLengthSelector
          value={contentLength}
          onChange={setContentLength}
          className="bg-background-secondary rounded-lg p-4"
        />

        {/* Question Types (Quiz only) */}
        {type === "quiz" && (
          <div className="bg-background-secondary rounded-lg p-4">
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Question Types
            </label>
            <div className="grid grid-cols-2 gap-3">
              {[
                {
                  type: "multiple_choice" as QuestionType,
                  label: "Multiple Choice",
                },
                {
                  type: "select_all" as QuestionType,
                  label: "Select All That Apply",
                },
                { type: "true_false" as QuestionType, label: "True/False" },
                { type: "short_answer" as QuestionType, label: "Short Answer" },
              ].map(({ type: questionType, label }) => (
                <label
                  key={questionType}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedQuestionTypes.includes(questionType)}
                    onChange={() => handleQuestionTypeToggle(questionType)}
                    className="w-4 h-4 text-primary-600 bg-background-primary border-gray-600 rounded focus:ring-primary-500 focus:ring-2"
                  />
                  <span className="text-sm text-gray-300">{label}</span>
                </label>
              ))}
            </div>
            {errors.questionTypes && (
              <p className="mt-2 text-sm text-red-500">
                {errors.questionTypes}
              </p>
            )}
          </div>
        )}

        {/* Existing Content Info */}
        {existingContent.length > 0 && (
          <div className="bg-background-secondary rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-300">
                Duplicate Prevention Active
              </span>
            </div>
            <p className="text-sm text-gray-400">
              Found {existingContent.length} existing{" "}
              {type === "flashcards" ? "flashcard questions" : "quiz questions"}
              . The AI will avoid creating duplicates.
            </p>
          </div>
        )}

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Custom Instructions (Optional)
          </label>
          <textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="Add specific instructions for the AI (e.g., focus on key concepts, include examples, etc.)"
            rows={3}
            className="w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* Generation Progress */}
        <AIGenerationProgress
          isGenerating={isGenerating}
          stage={
            generationProgress ||
            `Generating ${
              type === "flashcards" ? "flashcards" : "quiz questions"
            } with AI...`
          }
          estimatedTime={Math.ceil(itemCount / 8)} // Rough estimate: 1 second per 8 items
        />

        {/* Submit Button */}
        <Button
          type="submit"
          isLoading={isGenerating}
          disabled={!hasEnoughCredits}
          className="w-full"
          size="lg"
        >
          {isGenerating
            ? "Generating..."
            : `${studySetId ? "Add" : "Generate"} ${
                type === "flashcards" ? "Flashcards" : "Quiz Questions"
              } (${creditCost} credit${creditCost !== 1 ? "s" : ""})`}
        </Button>

        {!hasEnoughCredits && (
          <p className="text-center text-sm text-red-400">
            Insufficient credits. Please purchase more credits to continue.
          </p>
        )}
      </form>
    </div>
  );
};
