import React, { useEffect, useState } from "react";
import { useDocumentStore } from "../../stores/documentStore";

interface DocumentPageRange {
  startPage: number;
  endPage: number;
}

interface DocumentSelectorProps {
  selectedDocuments: string[];
  onSelectionChange: (documentIds: string[]) => void;
  documentPageRanges: { [documentId: string]: DocumentPageRange };
  onPageRangeChange: (documentId: string, range: DocumentPageRange) => void;
  maxSelection?: number;
}

interface PageInputProps {
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  label: string;
  placeholder?: string;
}

const PageInput: React.FC<PageInputProps> = ({
  value,
  onChange,
  min,
  max,
  label,
  placeholder,
}) => {
  const [inputValue, setInputValue] = useState(value.toString());
  const [error, setError] = useState<string>("");

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  const handleChange = (newValue: string) => {
    setInputValue(newValue);

    // Clear error when user starts typing
    if (error) setError("");

    // Allow empty input temporarily
    if (newValue === "") {
      return;
    }

    // Validate numeric input
    const numValue = parseInt(newValue);
    if (isNaN(numValue)) {
      setError("Must be a number");
      return;
    }

    if (numValue < min) {
      setError(`Minimum is ${min}`);
      return;
    }

    if (numValue > max) {
      setError(`Maximum is ${max}`);
      return;
    }

    // Valid input - update parent
    onChange(numValue);
  };

  const handleBlur = () => {
    // On blur, ensure we have a valid value
    const numValue = parseInt(inputValue);
    if (isNaN(numValue) || inputValue === "") {
      // Reset to current valid value
      setInputValue(value.toString());
      setError("");
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <label className="text-xs text-gray-400 whitespace-nowrap">
        {label}:
      </label>
      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-16 px-2 py-1 text-xs rounded text-white text-center focus:outline-none focus:ring-1 focus:ring-primary-500 ${
            error
              ? "bg-red-900/20 border border-red-500"
              : "bg-background-primary border border-gray-600"
          }`}
        />
        {error && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-400 whitespace-nowrap z-10">
            {error}
          </div>
        )}
      </div>
    </div>
  );
};

export const DocumentSelector: React.FC<DocumentSelectorProps> = ({
  selectedDocuments,
  onSelectionChange,
  documentPageRanges,
  onPageRangeChange,
  maxSelection = 5,
}) => {
  const { documents, fetchDocuments, isLoading } = useDocumentStore();
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (documents.length === 0) {
      fetchDocuments();
    }
  }, [documents.length, fetchDocuments]);

  const filteredDocuments = documents.filter(
    (doc) =>
      doc.is_processed &&
      doc.filename.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDocumentToggle = (documentId: string) => {
    const isSelected = selectedDocuments.includes(documentId);

    if (isSelected) {
      onSelectionChange(selectedDocuments.filter((id) => id !== documentId));
    } else if (selectedDocuments.length < maxSelection) {
      onSelectionChange([...selectedDocuments, documentId]);

      // Initialize page range for documents with page counts
      const document = documents.find((doc) => doc.id === documentId);
      if (document?.page_count && document.page_count > 0) {
        onPageRangeChange(documentId, {
          startPage: 1,
          endPage: document.page_count,
        });
      }
    }
  };

  const getSelectedDocuments = () => {
    return documents.filter((doc) => selectedDocuments.includes(doc.id));
  };

  const handlePageRangeUpdate = (
    documentId: string,
    field: "startPage" | "endPage",
    value: number
  ) => {
    const currentRange = documentPageRanges[documentId] || {
      startPage: 1,
      endPage: 1,
    };
    const document = documents.find((doc) => doc.id === documentId);
    const maxPage = document?.page_count || 1;

    let newRange = { ...currentRange };

    if (field === "startPage") {
      newRange.startPage = Math.max(
        1,
        Math.min(value, maxPage, newRange.endPage)
      );
    } else {
      newRange.endPage = Math.max(newRange.startPage, Math.min(value, maxPage));
    }

    onPageRangeChange(documentId, newRange);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-400">Loading documents...</div>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-400 mb-4">No documents found</div>
        <p className="text-sm text-gray-500">
          Upload some documents first to generate study materials.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div>
        <input
          type="text"
          placeholder="Search documents..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
        />
      </div>

      {/* Selection Summary with Page Ranges */}
      {selectedDocuments.length > 0 && (
        <div className="bg-primary-500/10 border border-primary-500/30 rounded-lg p-3">
          <div className="text-sm text-primary-400 mb-2">
            Selected {selectedDocuments.length} of {maxSelection} documents:
          </div>
          <div className="space-y-3">
            {getSelectedDocuments().map((doc) => {
              const hasPageCount = doc.page_count && doc.page_count > 0;
              const pageRange = documentPageRanges[doc.id];

              return (
                <div
                  key={doc.id}
                  className="bg-background-secondary/50 rounded-lg p-3"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-300 truncate flex-1 mr-2">
                      {doc.filename}
                    </span>
                    <button
                      onClick={() => handleDocumentToggle(doc.id)}
                      className="text-red-400 hover:text-red-300 text-sm px-2 py-1 rounded hover:bg-red-400/10 transition-colors"
                    >
                      Remove
                    </button>
                  </div>

                  {hasPageCount && (
                    <div className="space-y-3">
                      <div className="text-xs text-gray-400">
                        📄 {doc.page_count}{" "}
                        {doc.file_type === "pptx" ? "slides" : "pages"}{" "}
                        available
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <PageInput
                            label="From"
                            value={pageRange?.startPage || 1}
                            onChange={(value) =>
                              handlePageRangeUpdate(doc.id, "startPage", value)
                            }
                            min={1}
                            max={doc.page_count || 1}
                            placeholder="1"
                          />

                          <PageInput
                            label="To"
                            value={pageRange?.endPage || doc.page_count || 1}
                            onChange={(value) =>
                              handlePageRangeUpdate(doc.id, "endPage", value)
                            }
                            min={pageRange?.startPage || 1}
                            max={doc.page_count || 1}
                            placeholder={doc.page_count?.toString() || "1"}
                          />
                        </div>

                        <button
                          onClick={() =>
                            onPageRangeChange(doc.id, {
                              startPage: 1,
                              endPage: doc.page_count || 1,
                            })
                          }
                          className="text-xs text-primary-400 hover:text-primary-300 px-2 py-1 rounded hover:bg-primary-400/10 transition-colors whitespace-nowrap"
                        >
                          Use All
                        </button>
                      </div>

                      {pageRange && (
                        <div className="text-xs text-gray-500">
                          Using {pageRange.endPage - pageRange.startPage + 1} of{" "}
                          {doc.page_count}{" "}
                          {doc.file_type === "pptx" ? "slides" : "pages"}
                        </div>
                      )}
                    </div>
                  )}

                  {!hasPageCount && (
                    <div className="text-xs text-gray-500">
                      📄 {doc.file_type.toUpperCase()} • Full document will be
                      used
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Document List */}
      <div className="max-h-64 overflow-y-auto space-y-2">
        {filteredDocuments.map((document) => {
          const isSelected = selectedDocuments.includes(document.id);
          const canSelect =
            !isSelected && selectedDocuments.length < maxSelection;
          const hasPageCount = document.page_count && document.page_count > 0;

          return (
            <div
              key={document.id}
              className={`
                p-3 rounded-lg border cursor-pointer transition-all
                ${
                  isSelected
                    ? "bg-primary-500/20 border-primary-500"
                    : canSelect
                    ? "bg-background-secondary border-gray-600 hover:border-gray-500"
                    : "bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"
                }
              `}
              onClick={() =>
                canSelect || isSelected
                  ? handleDocumentToggle(document.id)
                  : null
              }
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">
                      {document.file_type === "pdf"
                        ? "📄"
                        : document.file_type === "docx"
                        ? "📝"
                        : document.file_type === "txt"
                        ? "📃"
                        : "📊"}
                    </span>
                    <div className="min-w-0 flex-1">
                      <p className="text-white font-medium truncate">
                        {document.filename}
                      </p>
                      <div className="flex items-center space-x-2 text-sm text-gray-400">
                        <span>
                          {document.file_type.toUpperCase()} •{" "}
                          {Math.round(document.file_size / 1024)} KB
                        </span>
                        {hasPageCount && (
                          <span>
                            • {document.page_count}{" "}
                            {document.file_type === "pptx" ? "slides" : "pages"}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${
                    isSelected
                      ? "bg-primary-500 border-primary-500"
                      : "border-gray-500"
                  }
                `}
                >
                  {isSelected && (
                    <svg
                      className="w-3 h-3 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredDocuments.length === 0 && searchQuery && (
        <div className="text-center py-4 text-gray-400">
          No documents match your search.
        </div>
      )}
    </div>
  );
};
