import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useDocumentStore } from '../../stores/documentStore';
import { useNavigate } from 'react-router-dom';

export const DashboardUpload: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [uploadSuccess, setUploadSuccess] = useState<string[]>([]);
  const { uploadDocument, setUploadProgress } = useDocumentStore();
  const navigate = useNavigate();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsUploading(true);
    setUploadErrors([]);
    setUploadSuccess([]);

    const errors: string[] = [];
    const successes: string[] = [];

    for (const file of acceptedFiles) {
      try {
        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
          errors.push(`${file.name}: File size exceeds 50MB limit`);
          continue;
        }

        // Validate file type
        const allowedTypes = [
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ];

        if (!allowedTypes.includes(file.type)) {
          errors.push(`${file.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);
          continue;
        }

        // Set initial progress
        setUploadProgress(file.name, 0);

        // Upload file
        await uploadDocument(file);
        
        // Set completion progress
        setUploadProgress(file.name, 100);
        successes.push(file.name);
      } catch (error) {
        errors.push(`${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    setUploadErrors(errors);
    setUploadSuccess(successes);
    setIsUploading(false);

    // Auto-clear success messages after 3 seconds
    if (successes.length > 0) {
      setTimeout(() => setUploadSuccess([]), 3000);
    }
  }, [uploadDocument, setUploadProgress]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']
    },
    multiple: true,
    maxFiles: 10,
    disabled: isUploading
  });

  return (
    <div className="mb-8">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-xl p-12 text-center cursor-pointer transition-all duration-200
          ${isDragActive 
            ? 'border-primary-500 bg-primary-500/10 scale-[1.02]' 
            : 'border-gray-600 hover:border-primary-500 hover:bg-primary-500/5'
          }
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
          bg-background-secondary/50
        `}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-4">
          {/* Upload Icon */}
          <div className="flex justify-center">
            <div className={`
              p-4 rounded-full transition-colors
              ${isDragActive ? 'bg-primary-500/20' : 'bg-gray-800/50'}
            `}>
              <svg
                className={`h-16 w-16 transition-colors ${
                  isDragActive ? 'text-primary-400' : 'text-gray-400'
                }`}
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
          
          {/* Upload Text */}
          {isDragActive ? (
            <div>
              <h3 className="text-xl font-semibold text-primary-400 mb-2">
                Drop your documents here
              </h3>
              <p className="text-gray-300">
                Release to upload your files
              </p>
            </div>
          ) : (
            <div>
              <h3 className="text-2xl font-bold text-white mb-2">
                Upload Your Documents
              </h3>
              <p className="text-lg text-gray-300 mb-2">
                Drag & drop files here, or{' '}
                <span className="text-primary-500 font-semibold hover:text-primary-400 transition-colors">
                  browse to select
                </span>
              </p>
              <p className="text-sm text-gray-500">
                Supports PDF, DOCX, TXT, PPTX files (max 50MB each)
              </p>
            </div>
          )}

          {/* Quick Actions */}
          {!isDragActive && !isUploading && (
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  navigate('/documents');
                }}
                className="text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2"
              >
                📄 Manage existing documents
              </button>
              <span className="hidden sm:block text-gray-600">•</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  navigate('/create-study-set');
                }}
                className="text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2"
              >
                📚 Create study set
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="mt-4 bg-background-secondary rounded-lg p-4 border border-gray-700">
          <div className="flex items-center gap-3 mb-2">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
            <p className="text-sm font-medium text-gray-300">Uploading files...</p>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div className="bg-primary-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>
        </div>
      )}

      {/* Success Messages */}
      {uploadSuccess.length > 0 && (
        <div className="mt-4 bg-green-900/20 border border-green-700 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <h4 className="text-green-400 font-medium">Successfully uploaded:</h4>
          </div>
          <ul className="text-sm text-green-300 space-y-1">
            {uploadSuccess.map((filename, index) => (
              <li key={index}>• {filename}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Error Messages */}
      {uploadErrors.length > 0 && (
        <div className="mt-4 bg-red-900/20 border border-red-700 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <h4 className="text-red-400 font-medium">Upload errors:</h4>
          </div>
          <ul className="text-sm text-red-300 space-y-1">
            {uploadErrors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
