import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  HiDocumentText,
  HiAcademicCap,
  HiCog,
  HiChartBar,
  HiMenu,
  HiX,
  HiCollection,
  HiCreditCard,
} from "react-icons/hi";
import useAuthStore from "../../stores/authStore";
import { CreditBalance } from "../common/CreditBalance";
import { ConnectionIndicator } from "../ui/ConnectionStatus";

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: "study-sets",
    label: "Study Sets",
    path: "/dashboard",
    icon: HiCollection,
    description: "Manage and study your flashcard sets",
  },
  {
    id: "documents",
    label: "Documents",
    path: "/documents",
    icon: HiDocumentText,
    description: "Manage your documents",
  },
  {
    id: "analytics",
    label: "Analytics",
    path: "/analytics",
    icon: HiChartBar,
    description: "Study progress and insights",
  },
  {
    id: "credits",
    label: "Credits",
    path: "/credits",
    icon: HiCreditCard,
    description: "Manage your AI credits",
  },
  {
    id: "help",
    label: "Help",
    path: "/help",
    icon: HiAcademicCap,
    description: "Get help and support",
  },
  {
    id: "settings",
    label: "Settings",
    path: "/settings",
    icon: HiCog,
    description: "Account and app settings",
  },
];

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(false);
  const { user } = useAuthStore();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const isActiveRoute = (path: string) => {
    if (path === "/dashboard") {
      return location.pathname === "/dashboard" || location.pathname === "/";
    }
    return location.pathname.startsWith(path);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      onToggle(); // Close sidebar on mobile after navigation
    }
  };

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
      },
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
      },
    },
  };

  const overlayVariants = {
    open: {
      opacity: 1,
      transition: { duration: 0.2 },
    },
    closed: {
      opacity: 0,
      transition: { duration: 0.2 },
    },
  };

  const itemVariants = {
    open: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
      },
    },
    closed: {
      opacity: 0,
      x: -20,
      transition: { duration: 0.2 },
    },
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isMobile && isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden"
            variants={overlayVariants}
            initial="closed"
            animate="open"
            exit="closed"
            onClick={onToggle}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        className={`
          fixed top-0 left-0 h-full bg-background-secondary border-r border-border-primary z-50
          ${isMobile ? "w-80" : "w-64"}
          md:relative md:translate-x-0
        `}
        variants={sidebarVariants}
        initial={isMobile ? "closed" : "open"}
        animate={isOpen ? "open" : "closed"}
        aria-label="Main navigation"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border-primary">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <h1 className="text-xl font-bold text-white">ChewyAI</h1>
            </div>

            {/* Close button for mobile */}
            {isMobile && (
              <button
                onClick={onToggle}
                className="p-2 rounded-lg hover:bg-background-tertiary transition-colors"
                aria-label="Close navigation"
              >
                <HiX className="w-5 h-5 text-gray-400" />
              </button>
            )}
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 overflow-y-auto">
            <ul className="space-y-2" role="list">
              {navigationItems.map((item, index) => {
                const Icon = item.icon;
                const isActive = isActiveRoute(item.path);

                return (
                  <motion.li
                    key={item.id}
                    variants={itemVariants}
                    initial="closed"
                    animate={isOpen ? "open" : "closed"}
                    transition={{ delay: index * 0.05 }}
                  >
                    <button
                      onClick={() => handleNavigation(item.path)}
                      className={`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200 group
                        ${
                          isActive
                            ? "bg-primary-500/20 text-primary-400 border border-primary-500/30"
                            : "text-gray-300 hover:bg-background-tertiary hover:text-white"
                        }
                      `}
                      aria-current={isActive ? "page" : undefined}
                      aria-describedby={`${item.id}-description`}
                    >
                      <Icon
                        className={`
                          w-5 h-5 transition-colors
                          ${
                            isActive
                              ? "text-primary-400"
                              : "text-gray-400 group-hover:text-white"
                          }
                        `}
                      />
                      <div className="flex-1 min-w-0">
                        <span className="font-medium truncate block">
                          {item.label}
                        </span>
                        <span
                          id={`${item.id}-description`}
                          className="text-xs text-gray-500 group-hover:text-gray-400 truncate block"
                        >
                          {item.description}
                        </span>
                      </div>

                      {/* Active indicator */}
                      {isActive && (
                        <motion.div
                          className="w-2 h-2 bg-primary-400 rounded-full"
                          layoutId="activeIndicator"
                          transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                          }}
                        />
                      )}
                    </button>
                  </motion.li>
                );
              })}
            </ul>
          </nav>

          {/* User Profile Section */}
          {user && (
            <div className="px-4 py-3 border-t border-border-primary">
              <div className="flex items-center space-x-3">
                {/* User Avatar */}
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  {user.name?.charAt(0)?.toUpperCase() ||
                    user.email?.charAt(0)?.toUpperCase() ||
                    "U"}
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {user.name || user.email?.split("@")[0] || "User"}
                  </p>
                  <p className="text-xs text-gray-400 truncate">
                    {user.subscription_tier || "Free"}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="p-4 border-t border-border-primary space-y-3">
            {/* Credit Balance */}
            {user && (
              <CreditBalance
                credits={user.credits_remaining || 0}
                variant="sidebar"
                size="sm"
                showDetails={true}
              />
            )}

            {/* Connection Status */}
            <ConnectionIndicator className="justify-center" />

            {/* Version Info */}
            <div className="text-xs text-gray-500 text-center">
              <p>ChewyAI v1.0.0</p>
              <p className="mt-1">AI-Powered Study Materials</p>
            </div>
          </div>
        </div>
      </motion.aside>

      {/* Mobile Menu Button */}
      {isMobile && !isOpen && (
        <button
          onClick={onToggle}
          className="fixed top-4 left-4 z-40 p-3 bg-background-secondary border border-border-primary rounded-lg shadow-lg md:hidden"
          aria-label="Open navigation menu"
        >
          <HiMenu className="w-6 h-6 text-white" />
        </button>
      )}
    </>
  );
};
