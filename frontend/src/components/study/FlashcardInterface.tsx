import React, { useState, useEffect, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudyStore } from '../../stores/studyStore';
import { Button } from '../common/Button';
import { useScreenReaderAnnouncements } from '../accessibility/ScreenReaderAnnouncements';
import { useDialog } from '../../contexts/DialogContext';

export const FlashcardInterface: React.FC = memo(() => {
  const navigate = useNavigate();
  const { alert } = useDialog();
  const {
    currentSession,
    studySetContent,
    nextItem,
    previousItem,
    toggleFlag,
    markReviewed,
    endStudySession,
    updateTimeSpent,
    undo,
    redo,
    canUndo,
    canRedo
  } = useStudyStore();

  const [isFlipped, setIsFlipped] = useState(false);
  const [startTime, setStartTime] = useState(Date.now());
  const { announce, AnnouncementComponent } = useScreenReaderAnnouncements();

  useEffect(() => {
    const interval = setInterval(() => {
      updateTimeSpent(1);
    }, 1000);

    return () => clearInterval(interval);
  }, [updateTimeSpent]);

  useEffect(() => {
    // Reset flip state when moving to new card
    setIsFlipped(false);
    setStartTime(Date.now());
  }, [currentSession?.currentIndex]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        e.target instanceof HTMLSelectElement ||
        (e.target as HTMLElement)?.contentEditable === 'true'
      ) {
        return;
      }

      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        // Call store function directly to avoid stale closure - now supports circular navigation
        if (currentSession) {
          previousItem();
          // Calculate the new index for announcement (with wraparound)
          const newIndex = currentSession.currentIndex === 0
            ? currentSession.totalItems
            : currentSession.currentIndex;
          announce(`Card ${newIndex} of ${currentSession.totalItems}`);
        }
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        // Call store function directly to avoid stale closure - now supports circular navigation
        if (currentSession) {
          nextItem();
          // Calculate the new index for announcement (with wraparound)
          const newIndex = currentSession.currentIndex === currentSession.totalItems - 1
            ? 1
            : currentSession.currentIndex + 2;
          announce(`Card ${newIndex} of ${currentSession.totalItems}`);
        }
      } else if (e.key === ' ' || e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        e.preventDefault();
        setIsFlipped(!isFlipped);
      } else if (e.key === 'f' || e.key === 'F') {
        e.preventDefault();
        if (currentSession && studySetContent?.flashcards) {
          const currentFlashcard = studySetContent.flashcards[currentSession.currentIndex];
          if (currentFlashcard) {
            toggleFlag(currentFlashcard.id);
            const isFlagged = currentSession.flaggedItems.includes(currentFlashcard.id);
            announce(isFlagged ? 'Card unflagged' : 'Card flagged for review');
          }
        }
      } else if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        if (canUndo) undo();
      } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
        e.preventDefault();
        if (canRedo) redo();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [currentSession, studySetContent, isFlipped, canUndo, canRedo, previousItem, nextItem, toggleFlag, undo, redo, announce]);

  if (!currentSession || !studySetContent?.flashcards) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No flashcard session found</div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Study Sets
          </Button>
        </div>
      </div>
    );
  }

  const flashcards = studySetContent.flashcards;
  const currentFlashcard = flashcards[currentSession.currentIndex];
  const progress = ((currentSession.currentIndex + 1) / currentSession.totalItems) * 100;
  const isFirstCard = currentSession.currentIndex === 0;
  const isLastCard = currentSession.currentIndex === currentSession.totalItems - 1;
  const isFlagged = currentSession.flaggedItems.includes(currentFlashcard.id);

  const handleNext = () => {
    if (isFlipped) {
      markReviewed(currentFlashcard.id);
    }

    // With circular navigation, we always move to next item (wraps around)
    nextItem();
    // Calculate the new index for announcement (with wraparound)
    const newIndex = isLastCard ? 1 : currentSession.currentIndex + 2;
    announce(`Card ${newIndex} of ${currentSession.totalItems}`);
  };

  const handlePrevious = () => {
    // With circular navigation, we always move to previous item (wraps around)
    previousItem();
    // Calculate the new index for announcement (with wraparound)
    const newIndex = isFirstCard ? currentSession.totalItems : currentSession.currentIndex;
    announce(`Card ${newIndex} of ${currentSession.totalItems}`);
  };

  const handleToggleFlag = () => {
    toggleFlag(currentFlashcard.id);
    const flagStatus = isFlagged ? 'unflagged' : 'flagged';
    announce(`Card ${flagStatus}`);
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
    announce(isFlipped ? 'Showing front of card' : 'Showing back of card');
  };

  const handleFinishStudy = async () => {
    const timeSpent = Math.floor((Date.now() - startTime) / 1000);
    const reviewedCount = currentSession.reviewedItems.length;
    const flaggedCount = currentSession.flaggedItems.length;

    endStudySession();

    // Show completion modal or navigate with results
    await alert({
      title: 'Study Session Complete!',
      message: `Reviewed: ${reviewedCount}/${currentSession.totalItems} cards\nFlagged: ${flaggedCount} cards\nTime spent: ${Math.floor(timeSpent / 60)}m ${timeSpent % 60}s`,
      variant: 'success',
      confirmText: 'Continue'
    });

    navigate(`/study-sets/${currentSession.studySetId}`);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate(`/study-sets/${currentSession.studySetId}`)}
          className="text-gray-400 hover:text-white flex items-center"
        >
          ← Back to Study Set
        </button>
        
        <div className="text-center">
          <h1 className="text-xl font-semibold text-white">
            {studySetContent.studySet?.name}
          </h1>
          <p className="text-sm text-gray-400">
            Card {currentSession.currentIndex + 1} of {currentSession.totalItems}
          </p>
        </div>

        <Button
          onClick={handleFinishStudy}
          variant="secondary"
          size="sm"
        >
          Finish
        </Button>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>Progress: {Math.round(progress)}%</span>
          <span>Time: {Math.floor(currentSession.timeSpent / 60)}:{(currentSession.timeSpent % 60).toString().padStart(2, '0')}</span>
        </div>
      </div>

      {/* Flashcard */}
      <div className="mb-8 flashcard-container">
        <div
          className={`
            relative w-full min-h-[24rem] max-h-[32rem] cursor-pointer transition-transform duration-500 transform-style-preserve-3d
            ${isFlipped ? 'rotate-y-180' : ''}
          `}
          onClick={handleFlip}
          role="button"
          tabIndex={0}
          aria-label={`Flashcard ${currentSession.currentIndex + 1} of ${currentSession.totalItems}. ${isFlipped ? 'Showing back' : 'Showing front'}. Click or press space to flip.`}
          onKeyDown={(e) => {
            if (e.key === ' ' || e.key === 'Enter') {
              e.preventDefault();
              handleFlip();
            }
          }}
        >
          {/* Front of card */}
          <div className={`
            absolute inset-0 w-full h-full backface-hidden
            bg-background-secondary border border-gray-600 rounded-lg p-6 sm:p-8
            flex flex-col justify-center text-center overflow-y-auto
          `}>
            <div className="flex-1 flex flex-col justify-center min-h-0">
              <div className="text-sm text-gray-400 mb-4 flex-shrink-0">FRONT</div>
              <div className="text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center">
                <div className="max-w-full">
                  {currentFlashcard.front}
                </div>
              </div>
              {!isFlipped && (
                <div className="text-sm text-gray-500 mt-6 flex-shrink-0">
                  Click to reveal answer
                </div>
              )}
            </div>
          </div>

          {/* Back of card */}
          <div className={`
            absolute inset-0 w-full h-full backface-hidden rotate-y-180
            bg-primary-500/10 border border-primary-500/30 rounded-lg p-6 sm:p-8
            flex flex-col justify-center text-center overflow-y-auto
          `}>
            <div className="flex-1 flex flex-col justify-center min-h-0">
              <div className="text-sm text-primary-400 mb-4 flex-shrink-0">BACK</div>
              <div className="text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center">
                <div className="max-w-full">
                  {currentFlashcard.back}
                </div>
              </div>
              {isFlipped && (
                <div className="text-sm text-gray-500 mt-6 flex-shrink-0">
                  Click to flip back
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <Button
          onClick={handlePrevious}
          variant="secondary"
        >
          ← Previous
        </Button>

        <div className="flex items-center space-x-4">
          <Button
            onClick={handleToggleFlag}
            variant={isFlagged ? "primary" : "secondary"}
            size="sm"
          >
            {isFlagged ? '🚩 Flagged' : '🏳️ Flag'}
          </Button>

          <Button
            onClick={() => setIsFlipped(!isFlipped)}
            variant="secondary"
          >
            {isFlipped ? 'Show Front' : 'Show Back'}
          </Button>
        </div>

        <Button
          onClick={handleNext}
          variant="primary"
        >
          Next →
        </Button>
      </div>

      {/* Keyboard shortcuts */}
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>Keyboard shortcuts: ← → (navigate) • Space (flip) • F (flag)</p>
      </div>

      {/* Screen reader announcements */}
      <AnnouncementComponent />
    </div>
  );
});
