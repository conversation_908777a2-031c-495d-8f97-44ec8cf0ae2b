import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HiExclamationCircle,
  HiInformationCircle,
  HiShoppingCart,
  HiTag,
} from "react-icons/hi";
import { loadStripe } from "@stripe/stripe-js";
import { Button } from "../common/Button";
import { CREDIT_PACKAGES, CreditPackage } from "../../shared/constants";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  interval: "month" | "year";
  features: string[];
  popular?: boolean;
  current?: boolean;
}

interface SubscriptionData {
  currentPlan: SubscriptionPlan | null;
  status: "active" | "canceled" | "past_due" | "trialing";
  nextBillingDate?: string;
  cancelAtPeriodEnd?: boolean;
}

const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: "free",
    name: "Free",
    price: 0,
    interval: "month",
    features: [
      "25 credits per month",
      "Basic AI generation",
      "Document upload (up to 5MB)",
      "Community support",
      "Basic analytics",
      "Perfect for trying out ChewyAI",
    ],
  },
  {
    id: "study_starter",
    name: "Study Starter",
    price: 29,
    interval: "month",
    features: [
      "150 credits per month",
      "Advanced AI generation",
      "Document upload (up to 10MB)",
      "Email support",
      "Detailed analytics",
      "Export functionality",
      "Perfect for high school & early college",
    ],
  },
  {
    id: "study_pro",
    name: "Study Pro",
    price: 59,
    interval: "month",
    popular: true,
    features: [
      "350 credits per month",
      "Advanced AI generation",
      "Document upload (up to 25MB)",
      "Priority support",
      "Advanced analytics",
      "Export functionality",
      "Perfect for college students",
    ],
  },
  {
    id: "study_master",
    name: "Study Master",
    price: 119,
    interval: "month",
    features: [
      "750 credits per month",
      "Premium AI generation",
      "Document upload (up to 50MB)",
      "Priority support",
      "Advanced analytics",
      "Team collaboration",
      "API access",
      "Perfect for graduate students & researchers",
    ],
  },
  {
    id: "study_elite",
    name: "Study Elite",
    price: 239,
    interval: "month",
    features: [
      "1,500 credits per month",
      "Premium AI generation",
      "Unlimited document upload",
      "Dedicated support",
      "Advanced analytics",
      "Team collaboration",
      "API access",
      "White-label options",
      "Perfect for study groups & institutions",
    ],
  },
];

// Initialize Stripe
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY!);

export const SubscriptionManagement: React.FC = () => {
  const [subscriptionData, setSubscriptionData] =
    useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isChanging, setIsChanging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [couponCode, setCouponCode] = useState("");
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    type: "percent" | "amount";
  } | null>(null);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/subscription", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch subscription data");
      }

      const result = await response.json();
      if (result.success) {
        setSubscriptionData(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to load subscription data"
      );
      // Set mock data for development - default to free plan
      setSubscriptionData({
        currentPlan: subscriptionPlans[0], // Free plan
        status: "active",
        nextBillingDate: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const validateCouponCode = async (code: string) => {
    if (!code.trim()) {
      setAppliedCoupon(null);
      return;
    }

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/stripe/validate-coupon", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ coupon: code.trim().toUpperCase() }),
      });

      const result = await response.json();
      if (result.success && result.data.valid) {
        setAppliedCoupon({
          code: result.data.code,
          discount: result.data.discount,
          type: result.data.type,
        });
      } else {
        setAppliedCoupon(null);
      }
    } catch (err) {
      setAppliedCoupon(null);
    }
  };

  const handleStripeCheckout = async (planId: string) => {
    if (planId === "free") {
      await handleDowngradeToFree();
      return;
    }

    setIsChanging(true);
    setError(null);

    try {
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Stripe failed to load");
      }

      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/stripe/create-checkout-session", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          planId,
          coupon: appliedCoupon?.code || null,
          successUrl: `${window.location.origin}/settings?tab=subscription&success=true`,
          cancelUrl: `${window.location.origin}/settings?tab=subscription&canceled=true`,
        }),
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to create checkout session");
      }

      // Redirect to Stripe Checkout
      const { error } = await stripe.redirectToCheckout({
        sessionId: result.data.sessionId,
      });

      if (error) {
        throw new Error(error.message || "Stripe checkout failed");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to start checkout process"
      );
    } finally {
      setIsChanging(false);
    }
  };

  const handleDowngradeToFree = async () => {
    if (
      !confirm(
        "Are you sure you want to downgrade to the Free plan? You will lose access to premium features immediately."
      )
    ) {
      return;
    }

    setIsChanging(true);
    setError(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/subscription/cancel", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ immediate: true }),
      });

      if (!response.ok) {
        throw new Error("Failed to downgrade to free plan");
      }

      const result = await response.json();
      if (result.success) {
        await fetchSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to downgrade to free plan"
      );
    } finally {
      setIsChanging(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (
      !confirm(
        "Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period."
      )
    ) {
      return;
    }

    setIsChanging(true);
    setError(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/subscription/cancel", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to cancel subscription");
      }

      const result = await response.json();
      if (result.success) {
        await fetchSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to cancel subscription"
      );
    } finally {
      setIsChanging(false);
    }
  };

  const handleReactivateSubscription = async () => {
    setIsChanging(true);
    setError(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/subscription/reactivate", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to reactivate subscription");
      }

      const result = await response.json();
      if (result.success) {
        await fetchSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to reactivate subscription"
      );
    } finally {
      setIsChanging(false);
    }
  };

  const handlePurchaseCredits = async (packageId: string) => {
    setIsChanging(true);
    setError(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/credits/purchase", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ packageId }),
      });

      if (!response.ok) {
        throw new Error("Failed to purchase credits");
      }

      const result = await response.json();
      if (result.success) {
        // Redirect to payment if needed, or handle success
        if (result.paymentUrl) {
          window.location.href = result.paymentUrl;
        }
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to purchase credits"
      );
    } finally {
      setIsChanging(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 w-48 bg-gray-600 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-gray-600 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">
          Subscription Management
        </h3>

        {error && (
          <div className="mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
          </div>
        )}

        {/* Current Subscription Status */}
        {subscriptionData && (
          <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-lg font-medium text-white">Current Plan</h4>
                <p className="text-gray-400">
                  {subscriptionData.currentPlan?.name || "No active plan"}
                </p>
              </div>
              <div className="text-right">
                <div
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    subscriptionData.status === "active"
                      ? "bg-green-500/20 text-green-400"
                      : subscriptionData.status === "canceled"
                      ? "bg-red-500/20 text-red-400"
                      : subscriptionData.status === "past_due"
                      ? "bg-yellow-500/20 text-yellow-400"
                      : "bg-blue-500/20 text-blue-400"
                  }`}
                >
                  {subscriptionData.status.charAt(0).toUpperCase() +
                    subscriptionData.status.slice(1)}
                </div>
              </div>
            </div>

            {subscriptionData.nextBillingDate && (
              <div className="flex items-center space-x-2 text-gray-400 text-sm">
                <HiInformationCircle className="w-4 h-4" />
                <span>
                  {subscriptionData.cancelAtPeriodEnd
                    ? `Access ends on ${new Date(
                        subscriptionData.nextBillingDate
                      ).toLocaleDateString()}`
                    : `Next billing date: ${new Date(
                        subscriptionData.nextBillingDate
                      ).toLocaleDateString()}`}
                </span>
              </div>
            )}

            {subscriptionData.currentPlan?.id !== "free" && (
              <div className="mt-4 flex space-x-3">
                {subscriptionData.cancelAtPeriodEnd ? (
                  <Button
                    onClick={handleReactivateSubscription}
                    isLoading={isChanging}
                    variant="primary"
                    size="sm"
                  >
                    Reactivate Subscription
                  </Button>
                ) : (
                  <Button
                    onClick={handleCancelSubscription}
                    isLoading={isChanging}
                    variant="danger"
                    size="sm"
                  >
                    Cancel Subscription
                  </Button>
                )}
                <Button
                  onClick={fetchSubscriptionData}
                  variant="secondary"
                  size="sm"
                >
                  <HiRefresh className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Coupon Code Section */}
        <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6">
          <h4 className="text-lg font-medium text-white mb-4 flex items-center">
            <HiTag className="w-5 h-5 mr-2 text-primary-400" />
            Coupon Code
          </h4>
          <div className="flex space-x-3">
            <div className="flex-1">
              <input
                type="text"
                value={couponCode}
                onChange={(e) => {
                  setCouponCode(e.target.value);
                  validateCouponCode(e.target.value);
                }}
                placeholder="Enter coupon code"
                className="w-full bg-background-secondary border border-border-primary rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <Button
              onClick={() => validateCouponCode(couponCode)}
              variant="secondary"
              size="sm"
              disabled={!couponCode.trim()}
            >
              Apply
            </Button>
          </div>
          {appliedCoupon && (
            <div className="mt-3 p-3 bg-green-500/20 border border-green-500/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <HiCheck className="w-4 h-4 text-green-400" />
                <span className="text-green-400 font-medium">
                  Coupon Applied: {appliedCoupon.code}
                </span>
              </div>
              <p className="text-green-300 text-sm mt-1">
                {appliedCoupon.type === "percent"
                  ? `${appliedCoupon.discount}% off your subscription!`
                  : `$${appliedCoupon.discount} off your subscription!`}
              </p>
            </div>
          )}
        </div>

        {/* Available Plans */}
        <div>
          <h4 className="text-lg font-medium text-white mb-4">Monthly Plans</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {subscriptionPlans.map((plan) => {
              const isCurrent = subscriptionData?.currentPlan?.id === plan.id;
              const finalPrice =
                appliedCoupon && plan.price > 0
                  ? appliedCoupon.type === "percent"
                    ? plan.price * (1 - appliedCoupon.discount / 100)
                    : Math.max(0, plan.price - appliedCoupon.discount)
                  : plan.price;

              return (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${
                    plan.popular
                      ? "border-primary-500 ring-2 ring-primary-500/20"
                      : isCurrent
                      ? "border-green-500 ring-2 ring-green-500/20"
                      : "border-border-primary hover:border-gray-500"
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                        <HiStar className="w-3 h-3" />
                        <span>Most Popular</span>
                      </div>
                    </div>
                  )}

                  {isCurrent && (
                    <div className="absolute -top-3 right-4">
                      <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                        <HiCheck className="w-3 h-3" />
                        <span>Current</span>
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h5 className="text-xl font-semibold text-white mb-2">
                      {plan.name}
                    </h5>
                    <div className="text-3xl font-bold text-white">
                      {appliedCoupon &&
                      plan.price > 0 &&
                      finalPrice !== plan.price ? (
                        <>
                          <span className="line-through text-gray-500 text-xl mr-2">
                            ${plan.price}
                          </span>
                          ${finalPrice.toFixed(0)}
                        </>
                      ) : (
                        `$${plan.price}`
                      )}
                      <span className="text-lg text-gray-400">
                        /{plan.interval}
                      </span>
                    </div>
                    {appliedCoupon && plan.price > 0 && finalPrice === 0 && (
                      <div className="text-green-400 text-sm font-medium mt-1">
                        FREE with {appliedCoupon.code}!
                      </div>
                    )}
                  </div>

                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <HiCheck className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-gray-300 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    onClick={() => handleStripeCheckout(plan.id)}
                    disabled={isCurrent || isChanging}
                    isLoading={isChanging}
                    variant={
                      plan.popular
                        ? "primary"
                        : isCurrent
                        ? "secondary"
                        : "secondary"
                    }
                    className="w-full"
                  >
                    {isCurrent
                      ? "Current Plan"
                      : plan.id === "free"
                      ? "Downgrade to Free"
                      : `Upgrade to ${plan.name}`}
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* One-Time Credit Packages */}
        <div className="mt-8">
          <h4 className="text-lg font-medium text-white mb-2">
            One-Time Credit Packages
          </h4>
          <p className="text-gray-400 mb-4">
            Purchase credits that never expire. Perfect for irregular usage or
            exam preparation.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {CREDIT_PACKAGES.map((pkg: CreditPackage) => (
              <motion.div
                key={pkg.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-background-secondary rounded-lg p-5 border border-border-primary hover:border-gray-500 transition-all duration-200"
              >
                <div className="text-center mb-4">
                  <h5 className="text-lg font-semibold text-white mb-1">
                    {pkg.name}
                  </h5>
                  <div className="text-2xl font-bold text-white">
                    ${pkg.price}
                  </div>
                  <div className="text-sm text-gray-400">
                    {pkg.credits} credits
                  </div>
                  <div className="text-xs text-primary-400 mt-1">
                    ${pkg.valuePerCredit.toFixed(2)} per credit
                  </div>
                </div>

                <p className="text-gray-300 text-sm text-center mb-4">
                  {pkg.description}
                </p>

                <Button
                  onClick={() => handlePurchaseCredits(pkg.id)}
                  disabled={isChanging}
                  isLoading={isChanging}
                  variant="secondary"
                  size="sm"
                  className="w-full"
                >
                  <HiShoppingCart className="w-4 h-4 mr-2" />
                  Purchase
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
