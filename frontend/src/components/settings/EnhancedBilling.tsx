import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  HiCreditCard,
  HiDownload,
  HiRefresh,
  HiExclamationCircle,
  HiCheckCircle,
  HiClock,
  HiX,
} from "react-icons/hi";
import { Button } from "../common/Button";

interface PaymentMethod {
  id: string;
  type: "card" | "paypal";
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

interface Invoice {
  id: string;
  number: string;
  amount: number;
  currency: string;
  status: "paid" | "pending" | "failed" | "draft";
  date: string;
  dueDate?: string;
  downloadUrl?: string;
  description: string;
}

interface BillingData {
  paymentMethods: PaymentMethod[];
  invoices: Invoice[];
  nextInvoice?: {
    amount: number;
    currency: string;
    date: string;
  };
}

export const EnhancedBilling: React.FC = () => {
  const [billingData, setBillingData] = useState<BillingData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/billing", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch billing data");
      }

      const result = await response.json();
      if (result.success) {
        setBillingData(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to load billing data"
      );
      // Set mock data for development
      setBillingData({
        paymentMethods: [
          {
            id: "1",
            type: "card",
            last4: "4242",
            brand: "visa",
            expiryMonth: 12,
            expiryYear: 2025,
            isDefault: true,
          },
        ],
        invoices: [
          {
            id: "1",
            number: "INV-001",
            amount: 59.0,
            currency: "USD",
            status: "paid",
            date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            description: "Study Pro - Monthly",
          },
          {
            id: "2",
            number: "INV-002",
            amount: 59.0,
            currency: "USD",
            status: "pending",
            date: new Date().toISOString(),
            dueDate: new Date(
              Date.now() + 7 * 24 * 60 * 60 * 1000
            ).toISOString(),
            description: "Study Pro - Monthly",
          },
        ],
        nextInvoice: {
          amount: 59.0,
          currency: "USD",
          date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadInvoice = async (invoiceId: string) => {
    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch(
        `/api/billing/invoices/${invoiceId}/download`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to download invoice");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `invoice-${invoiceId}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to download invoice"
      );
    }
  };

  const handleAddPaymentMethod = async () => {
    // This would typically open a Stripe payment method setup flow
    setError("Payment method management coming soon");
  };

  const handleSetDefaultPaymentMethod = async (paymentMethodId: string) => {
    setIsUpdating(true);
    setError(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/billing/payment-methods/default", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ paymentMethodId }),
      });

      if (!response.ok) {
        throw new Error("Failed to update default payment method");
      }

      const result = await response.json();
      if (result.success) {
        await fetchBillingData();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to update payment method"
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return <HiCheckCircle className="w-5 h-5 text-green-400" />;
      case "pending":
        return <HiClock className="w-5 h-5 text-yellow-400" />;
      case "failed":
        return <HiX className="w-5 h-5 text-red-400" />;
      default:
        return <HiClock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "text-green-400";
      case "pending":
        return "text-yellow-400";
      case "failed":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 w-48 bg-gray-600 rounded mb-4"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-600 rounded-lg"></div>
            <div className="h-64 bg-gray-600 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">
            Billing & Invoices
          </h3>
          <Button onClick={fetchBillingData} variant="secondary" size="sm">
            <HiRefresh className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>

        {error && (
          <div className="mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
          </div>
        )}

        {/* Payment Methods */}
        <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-white">Payment Methods</h4>
            <Button
              onClick={handleAddPaymentMethod}
              variant="secondary"
              size="sm"
            >
              Add Payment Method
            </Button>
          </div>

          {billingData?.paymentMethods.length === 0 ? (
            <div className="text-center py-8">
              <HiCreditCard className="w-16 h-16 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No payment methods added</p>
            </div>
          ) : (
            <div className="space-y-3">
              {billingData?.paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary"
                >
                  <div className="flex items-center space-x-3">
                    <HiCreditCard className="w-6 h-6 text-gray-400" />
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="text-white font-medium">
                          {method.brand?.toUpperCase()} •••• {method.last4}
                        </span>
                        {method.isDefault && (
                          <span className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium">
                            Default
                          </span>
                        )}
                      </div>
                      {method.expiryMonth && method.expiryYear && (
                        <p className="text-gray-400 text-sm">
                          Expires{" "}
                          {method.expiryMonth.toString().padStart(2, "0")}/
                          {method.expiryYear}
                        </p>
                      )}
                    </div>
                  </div>

                  {!method.isDefault && (
                    <Button
                      onClick={() => handleSetDefaultPaymentMethod(method.id)}
                      isLoading={isUpdating}
                      variant="secondary"
                      size="sm"
                    >
                      Set as Default
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Next Invoice */}
        {billingData?.nextInvoice && (
          <div className="bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6">
            <h4 className="text-lg font-medium text-white mb-2">
              Upcoming Invoice
            </h4>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300">
                  ${billingData.nextInvoice.amount}{" "}
                  {billingData.nextInvoice.currency.toUpperCase()}
                </p>
                <p className="text-blue-400 text-sm">
                  Due on{" "}
                  {new Date(billingData.nextInvoice.date).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Invoice History */}
        <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary">
          <h4 className="text-lg font-medium text-white mb-4">
            Invoice History
          </h4>

          {billingData?.invoices.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No invoices found</p>
            </div>
          ) : (
            <div className="space-y-3">
              {billingData?.invoices.map((invoice) => (
                <motion.div
                  key={invoice.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary"
                >
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(invoice.status)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="text-white font-medium">
                          {invoice.number}
                        </span>
                        <span
                          className={`text-sm font-medium ${getStatusColor(
                            invoice.status
                          )}`}
                        >
                          {invoice.status.charAt(0).toUpperCase() +
                            invoice.status.slice(1)}
                        </span>
                      </div>
                      <p className="text-gray-400 text-sm">
                        {invoice.description}
                      </p>
                      <p className="text-gray-500 text-xs">
                        {new Date(invoice.date).toLocaleDateString()}
                        {invoice.dueDate && invoice.status === "pending" && (
                          <span>
                            {" "}
                            • Due{" "}
                            {new Date(invoice.dueDate).toLocaleDateString()}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <span className="text-white font-medium">
                      ${invoice.amount} {invoice.currency.toUpperCase()}
                    </span>
                    {invoice.status === "paid" && (
                      <Button
                        onClick={() => handleDownloadInvoice(invoice.id)}
                        variant="secondary"
                        size="sm"
                      >
                        <HiDownload className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
