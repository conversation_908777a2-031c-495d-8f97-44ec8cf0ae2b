import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'react-icons/hi';

interface StudySession {
  id: string;
  studySetId: string;
  type: 'flashcards' | 'quiz';
  startTime: Date;
  endTime?: Date;
  totalItems: number;
  reviewedItems: number;
  flaggedItems: number;
  correctAnswers?: number;
  timeSpent: number;
}

interface StudyTrendsProps {
  sessions: StudySession[];
  timeRange: '7d' | '30d' | '90d' | 'all';
  isLoading: boolean;
}

interface TrendData {
  studyStreak: number;
  longestStreak: number;
  averageSessionsPerDay: number;
  mostProductiveHour: number;
  mostProductiveDay: string;
  weeklyPattern: Array<{ day: string; sessions: number; time: number }>;
  hourlyPattern: Array<{ hour: number; sessions: number; time: number }>;
  monthlyTrend: Array<{ month: string; sessions: number; time: number }>;
}

const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return `${seconds}s`;
  }
};

const TrendCard: React.FC<{
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  isLoading?: boolean;
  color?: string;
}> = ({ title, value, subtitle, icon: Icon, isLoading, color = 'text-primary-400' }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
    className="bg-background-secondary rounded-lg p-6 border border-border-primary"
  >
    <div className="flex items-center space-x-3 mb-4">
      <div className={`p-3 bg-primary-500/20 rounded-lg`}>
        <Icon className={`w-6 h-6 ${color}`} />
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-400">{title}</h3>
        {isLoading ? (
          <div className="animate-pulse">
            <div className="h-6 w-16 bg-gray-600 rounded"></div>
          </div>
        ) : (
          <p className="text-xl font-bold text-white">{value}</p>
        )}
      </div>
    </div>
    {subtitle && (
      <p className="text-xs text-gray-500">{subtitle}</p>
    )}
  </motion.div>
);

const PatternChart: React.FC<{
  title: string;
  data: Array<{ label: string; value: number; time?: number }>;
  type: 'bar' | 'line';
  color: string;
}> = ({ title, data, color }) => {
  const maxValue = Math.max(...data.map(d => d.value), 1);
  
  return (
    <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
      <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-gray-300 text-sm w-20">{item.label}</span>
            <div className="flex-1 mx-4">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${(item.value / maxValue) * 100}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className={`h-2 rounded-full ${color}`}
                />
              </div>
            </div>
            <div className="text-right w-20">
              <span className="text-white font-medium text-sm">{item.value}</span>
              {item.time && (
                <div className="text-xs text-gray-400">{formatTime(item.time)}</div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const StudyTrends: React.FC<StudyTrendsProps> = ({
  sessions,
  timeRange,
  isLoading
}) => {
  const trendData = useMemo((): TrendData => {
    const now = new Date();
    
    // Calculate study streak
    let studyStreak = 0;
    let longestStreak = 0;
    let currentStreak = 0;
    
    for (let i = 0; i < 365; i++) {
      const checkDate = new Date(now);
      checkDate.setDate(now.getDate() - i);
      
      const hasSessionOnDate = sessions.some(session => {
        const sessionDate = new Date(session.startTime);
        return sessionDate.toDateString() === checkDate.toDateString();
      });
      
      if (hasSessionOnDate) {
        if (i === 0 || studyStreak === i) {
          studyStreak++;
        }
        currentStreak++;
        longestStreak = Math.max(longestStreak, currentStreak);
      } else {
        if (i === 0) {
          // Check yesterday for current streak
          const yesterday = new Date(now);
          yesterday.setDate(now.getDate() - 1);
          const hasYesterdaySession = sessions.some(session => {
            const sessionDate = new Date(session.startTime);
            return sessionDate.toDateString() === yesterday.toDateString();
          });
          if (!hasYesterdaySession) {
            studyStreak = 0;
          }
        }
        currentStreak = 0;
      }
    }

    // Calculate average sessions per day
    const timeRangeMs = {
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
      'all': Infinity
    }[timeRange];

    const filteredSessions = sessions.filter(session => {
      const sessionTime = new Date(session.startTime).getTime();
      return now.getTime() - sessionTime <= timeRangeMs;
    });

    const days = timeRangeMs === Infinity ? 
      Math.max(1, Math.ceil((now.getTime() - Math.min(...sessions.map(s => new Date(s.startTime).getTime()))) / (24 * 60 * 60 * 1000))) :
      Math.ceil(timeRangeMs / (24 * 60 * 60 * 1000));
    
    const averageSessionsPerDay = filteredSessions.length / days;

    // Calculate hourly pattern
    const hourlyPattern = Array.from({ length: 24 }, (_, hour) => {
      const hourSessions = filteredSessions.filter(session => {
        const sessionHour = new Date(session.startTime).getHours();
        return sessionHour === hour;
      });
      
      return {
        hour,
        sessions: hourSessions.length,
        time: hourSessions.reduce((sum, session) => sum + session.timeSpent, 0)
      };
    });

    const mostProductiveHour = hourlyPattern.reduce((max, current) => 
      current.sessions > max.sessions ? current : max
    ).hour;

    // Calculate weekly pattern
    const weeklyPattern = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => {
      const daySessions = filteredSessions.filter(session => {
        const sessionDay = new Date(session.startTime).getDay();
        return sessionDay === index;
      });
      
      return {
        day,
        sessions: daySessions.length,
        time: daySessions.reduce((sum, session) => sum + session.timeSpent, 0)
      };
    });

    const mostProductiveDay = weeklyPattern.reduce((max, current) => 
      current.sessions > max.sessions ? current : max
    ).day;

    // Calculate monthly trend (last 6 months)
    const monthlyTrend = Array.from({ length: 6 }, (_, i) => {
      const date = new Date(now);
      date.setMonth(now.getMonth() - (5 - i));
      const monthName = date.toLocaleDateString('en-US', { month: 'short' });
      
      const monthSessions = sessions.filter(session => {
        const sessionDate = new Date(session.startTime);
        return sessionDate.getMonth() === date.getMonth() && 
               sessionDate.getFullYear() === date.getFullYear();
      });
      
      return {
        month: monthName,
        sessions: monthSessions.length,
        time: monthSessions.reduce((sum, session) => sum + session.timeSpent, 0)
      };
    });

    return {
      studyStreak,
      longestStreak,
      averageSessionsPerDay,
      mostProductiveHour,
      mostProductiveDay,
      weeklyPattern,
      hourlyPattern: hourlyPattern.filter(h => h.sessions > 0), // Only show active hours
      monthlyTrend
    };
  }, [sessions, timeRange]);

  const formatHour = (hour: number): string => {
    if (hour === 0) return '12 AM';
    if (hour < 12) return `${hour} AM`;
    if (hour === 12) return '12 PM';
    return `${hour - 12} PM`;
  };

  return (
    <div className="space-y-6">
      {/* Streak and Habit Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <TrendCard
          title="Current Streak"
          value={`${trendData.studyStreak} days`}
          subtitle="Consecutive study days"
          icon={HiFire}
          isLoading={isLoading}
          color="text-orange-400"
        />
        
        <TrendCard
          title="Longest Streak"
          value={`${trendData.longestStreak} days`}
          subtitle="Personal best"
          icon={HiTrendingUp}
          isLoading={isLoading}
          color="text-green-400"
        />
        
        <TrendCard
          title="Daily Average"
          value={trendData.averageSessionsPerDay.toFixed(1)}
          subtitle="Sessions per day"
          icon={HiCalendar}
          isLoading={isLoading}
          color="text-blue-400"
        />
        
        <TrendCard
          title="Peak Hour"
          value={formatHour(trendData.mostProductiveHour)}
          subtitle={`Most active time`}
          icon={trendData.mostProductiveHour < 12 ? HiSun : HiMoon}
          isLoading={isLoading}
          color="text-yellow-400"
        />
      </div>

      {/* Pattern Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PatternChart
          title="Weekly Study Pattern"
          data={trendData.weeklyPattern.map(day => ({
            label: day.day,
            value: day.sessions,
            time: day.time
          }))}
          type="bar"
          color="bg-primary-500"
        />
        
        <PatternChart
          title="Active Study Hours"
          data={trendData.hourlyPattern.slice(0, 8).map(hour => ({
            label: formatHour(hour.hour),
            value: hour.sessions,
            time: hour.time
          }))}
          type="bar"
          color="bg-yellow-500"
        />
      </div>

      {/* Monthly Trend */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h3 className="text-lg font-semibold text-white mb-4">6-Month Study Trend</h3>
        <div className="flex items-end justify-between h-40 space-x-2">
          {trendData.monthlyTrend.map((month, index) => {
            const maxSessions = Math.max(...trendData.monthlyTrend.map(m => m.sessions), 1);
            const height = (month.sessions / maxSessions) * 100;
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full bg-gray-700 rounded-t relative" style={{ height: '120px' }}>
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: `${height}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-gradient-to-t from-purple-500 to-purple-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0"
                    title={`${month.sessions} sessions, ${formatTime(month.time)}`}
                  />
                </div>
                <div className="text-xs text-gray-400 mt-2">{month.month}</div>
                <div className="text-xs text-white font-medium">{month.sessions}</div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Study Insights */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h3 className="text-lg font-semibold text-white mb-4">Study Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-white mb-2">Best Study Day</h4>
            <p className="text-gray-300">{trendData.mostProductiveDay}</p>
            <p className="text-xs text-gray-500 mt-1">
              Most sessions completed on this day of the week
            </p>
          </div>
          
          <div>
            <h4 className="font-medium text-white mb-2">Optimal Study Time</h4>
            <p className="text-gray-300">{formatHour(trendData.mostProductiveHour)}</p>
            <p className="text-xs text-gray-500 mt-1">
              Peak productivity hour based on session frequency
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
