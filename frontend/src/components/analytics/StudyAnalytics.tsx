import React, { memo, useMemo } from 'react';
import { StudySet } from '../../../../shared/types';

interface StudySession {
  id: string;
  studySetId: string;
  type: 'flashcards' | 'quiz';
  startTime: Date;
  endTime?: Date;
  totalItems: number;
  reviewedItems: number;
  flaggedItems: number;
  correctAnswers?: number;
  timeSpent: number; // in seconds
}

interface StudyAnalyticsProps {
  studySets: StudySet[];
  sessions: StudySession[];
}

interface AnalyticsData {
  totalStudyTime: number;
  totalSessions: number;
  averageSessionTime: number;
  totalItemsReviewed: number;
  averageAccuracy: number;
  studyStreak: number;
  mostStudiedSet: string;
  recentActivity: StudySession[];
}

const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
};

const StatCard: React.FC<{
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: string;
}> = memo(({ title, value, subtitle, icon }) => (
  <div className="bg-background-secondary border border-gray-600 rounded-lg p-6">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm text-gray-400">{title}</p>
        <p className="text-2xl font-semibold text-white mt-1">{value}</p>
        {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
      </div>
      {icon && <div className="text-2xl">{icon}</div>}
    </div>
  </div>
));

StatCard.displayName = 'StatCard';

const ProgressChart: React.FC<{
  sessions: StudySession[];
}> = memo(({ sessions }) => {
  const chartData = useMemo(() => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return {
        date: date.toLocaleDateString('en-US', { weekday: 'short' }),
        sessions: 0,
        timeSpent: 0
      };
    });

    sessions.forEach(session => {
      const sessionDate = new Date(session.startTime);
      const dayIndex = Math.floor((Date.now() - sessionDate.getTime()) / (1000 * 60 * 60 * 24));

      if (dayIndex >= 0 && dayIndex < 7) {
        const chartIndex = 6 - dayIndex;
        last7Days[chartIndex].sessions += 1;
        last7Days[chartIndex].timeSpent += session.timeSpent;
      }
    });

    return last7Days;
  }, [sessions]);

  const maxTime = Math.max(...chartData.map(d => d.timeSpent));

  return (
    <div className="bg-background-secondary border border-gray-600 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Study Activity (Last 7 Days)</h3>
      <div className="flex items-end justify-between h-32 space-x-2">
        {chartData.map((day, index) => (
          <div key={index} className="flex-1 flex flex-col items-center">
            <div className="w-full bg-gray-700 rounded-t relative" style={{ height: '100px' }}>
              <div
                className="bg-primary-500 rounded-t transition-all duration-300"
                style={{
                  height: maxTime > 0 ? `${(day.timeSpent / maxTime) * 100}%` : '0%',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0
                }}
                title={`${day.sessions} sessions, ${formatTime(day.timeSpent)}`}
              />
            </div>
            <div className="text-xs text-gray-400 mt-2">{day.date}</div>
          </div>
        ))}
      </div>
    </div>
  );
});

ProgressChart.displayName = 'ProgressChart';

export const StudyAnalytics: React.FC<StudyAnalyticsProps> = memo(({
  studySets,
  sessions
}) => {
  const analytics = useMemo((): AnalyticsData => {
    const totalStudyTime = sessions.reduce((sum, session) => sum + session.timeSpent, 0);
    const totalSessions = sessions.length;
    const averageSessionTime = totalSessions > 0 ? totalStudyTime / totalSessions : 0;
    const totalItemsReviewed = sessions.reduce((sum, session) => sum + session.reviewedItems, 0);
    
    const quizSessions = sessions.filter(s => s.type === 'quiz' && s.correctAnswers !== undefined);
    const totalQuizItems = quizSessions.reduce((sum, session) => sum + session.totalItems, 0);
    const totalCorrect = quizSessions.reduce((sum, session) => sum + (session.correctAnswers || 0), 0);
    const averageAccuracy = totalQuizItems > 0 ? (totalCorrect / totalQuizItems) * 100 : 0;

    // Calculate study streak (consecutive days with sessions)
    const today = new Date();
    let studyStreak = 0;
    for (let i = 0; i < 365; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(today.getDate() - i);
      
      const hasSessionOnDate = sessions.some(session => {
        const sessionDate = new Date(session.startTime);
        return sessionDate.toDateString() === checkDate.toDateString();
      });
      
      if (hasSessionOnDate) {
        studyStreak++;
      } else if (i > 0) { // Don't break on first day (today) if no session
        break;
      }
    }

    // Find most studied set
    const setStudyCount = sessions.reduce((acc, session) => {
      acc[session.studySetId] = (acc[session.studySetId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const mostStudiedSetId = Object.entries(setStudyCount)
      .sort(([,a], [,b]) => b - a)[0]?.[0];
    
    const mostStudiedSet = studySets.find(set => set.id === mostStudiedSetId)?.name || 'None';

    const recentActivity = sessions
      .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
      .slice(0, 5);

    return {
      totalStudyTime,
      totalSessions,
      averageSessionTime,
      totalItemsReviewed,
      averageAccuracy,
      studyStreak,
      mostStudiedSet,
      recentActivity
    };
  }, [sessions, studySets]);

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Study Analytics</h1>
        <p className="text-gray-400">Track your learning progress and performance</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Study Time"
          value={formatTime(analytics.totalStudyTime)}
          icon="⏱️"
        />
        <StatCard
          title="Study Sessions"
          value={analytics.totalSessions}
          subtitle="All time"
          icon="📚"
        />
        <StatCard
          title="Average Session"
          value={formatTime(Math.round(analytics.averageSessionTime))}
          icon="⏰"
        />
        <StatCard
          title="Study Streak"
          value={`${analytics.studyStreak} days`}
          icon="🔥"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <StatCard
          title="Items Reviewed"
          value={analytics.totalItemsReviewed}
          subtitle="Flashcards & questions"
          icon="✅"
        />
        <StatCard
          title="Quiz Accuracy"
          value={`${Math.round(analytics.averageAccuracy)}%`}
          subtitle="Average across all quizzes"
          icon="🎯"
        />
      </div>

      {/* Progress Chart */}
      <div className="mb-8">
        <ProgressChart sessions={sessions} />
      </div>

      {/* Recent Activity */}
      <div className="bg-background-secondary border border-gray-600 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
        {analytics.recentActivity.length === 0 ? (
          <p className="text-gray-400">No recent study sessions</p>
        ) : (
          <div className="space-y-3">
            {analytics.recentActivity.map((session, index) => {
              const studySet = studySets.find(set => set.id === session.studySetId);
              return (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-700 last:border-b-0">
                  <div>
                    <p className="text-white font-medium">{studySet?.name || 'Unknown Set'}</p>
                    <p className="text-sm text-gray-400">
                      {session.type === 'quiz' ? 'Quiz' : 'Flashcards'} • {session.reviewedItems}/{session.totalItems} items
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-300">{formatTime(session.timeSpent)}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(session.startTime).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
});

StudyAnalytics.displayName = 'StudyAnalytics';
