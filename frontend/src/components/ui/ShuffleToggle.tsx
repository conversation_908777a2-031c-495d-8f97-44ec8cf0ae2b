import React from 'react';
import { ShuffleToggleProps } from '../../types/userSettings';

export const ShuffleToggle: React.FC<ShuffleToggleProps> = ({
  enabled,
  onChange,
  disabled = false,
  className = '',
  label = 'Shuffle Cards',
  description = 'Randomize the order of flashcards during study sessions'
}) => {
  const handleToggle = () => {
    if (!disabled) {
      onChange(!enabled);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === ' ' || event.key === 'Enter') {
      event.preventDefault();
      handleToggle();
    }
  };

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="flex-1">
        <div className="flex items-center space-x-3">
          <span className="text-sm font-medium text-white">
            {label}
          </span>
          {description && (
            <span className="text-xs text-gray-400">
              {description}
            </span>
          )}
        </div>
      </div>
      
      <button
        type="button"
        role="switch"
        aria-checked={enabled}
        aria-label={`${enabled ? 'Disable' : 'Enable'} ${label.toLowerCase()}`}
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        className={`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800
          ${disabled 
            ? 'opacity-50 cursor-not-allowed bg-gray-600' 
            : enabled 
              ? 'bg-primary-500 hover:bg-primary-600' 
              : 'bg-gray-600 hover:bg-gray-500'
          }
        `}
      >
        <span
          className={`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out
            ${enabled ? 'translate-x-6' : 'translate-x-1'}
          `}
        />
      </button>
    </div>
  );
};
