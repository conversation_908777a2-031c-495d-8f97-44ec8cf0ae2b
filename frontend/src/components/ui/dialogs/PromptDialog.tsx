import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { BaseDialog } from './BaseDialog';
import { Button } from '../../common/Button';

interface PromptDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (value: string) => void;
  title?: string;
  message: string;
  placeholder?: string;
  defaultValue?: string;
  confirmText?: string;
  cancelText?: string;
  inputType?: 'text' | 'email' | 'password' | 'number';
  validation?: (value: string) => string | null; // Returns error message or null
  isLoading?: boolean;
}

export const PromptDialog: React.FC<PromptDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  placeholder = '',
  defaultValue = '',
  confirmText = 'OK',
  cancelText = 'Cancel',
  inputType = 'text',
  validation,
  isLoading = false
}) => {
  const [value, setValue] = useState(defaultValue);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      setValue(defaultValue);
      setError(null);
      // Focus the input after dialog animation
      setTimeout(() => {
        inputRef.current?.focus();
        inputRef.current?.select();
      }, 150);
    }
  }, [isOpen, defaultValue]);

  const handleConfirm = () => {
    const trimmedValue = value.trim();
    
    // Run validation if provided
    if (validation) {
      const validationError = validation(trimmedValue);
      if (validationError) {
        setError(validationError);
        return;
      }
    }
    
    onConfirm(trimmedValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleConfirm();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  const isValid = !error && value.trim().length > 0;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  return (
    <BaseDialog
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="md"
      closeOnOverlayClick={false}
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-4" variants={itemVariants}>
          <p className="text-white leading-relaxed whitespace-pre-line">
            {message}
          </p>
        </motion.div>

        <motion.div className="mb-4" variants={itemVariants}>
          <motion.input
            ref={inputRef}
            type={inputType}
            value={value}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={isLoading}
            className={`
              w-full px-3 py-2 bg-background-primary border rounded-md text-white 
              placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors
              ${error 
                ? 'border-red-500 focus:ring-red-500' 
                : 'border-gray-600 focus:ring-primary-500'
              }
              disabled:opacity-50 disabled:cursor-not-allowed
            `}
            aria-describedby={error ? 'input-error' : undefined}
            aria-invalid={!!error}
            aria-required="true"
            aria-label={placeholder || 'Input field'}
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.2 }}
          />

          {error && (
            <motion.p
              id="input-error"
              className="mt-2 text-sm text-red-400"
              role="alert"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
            >
              {error}
            </motion.p>
          )}
        </motion.div>

        <motion.div className="flex flex-col-reverse sm:flex-row sm:gap-3" variants={itemVariants}>
          <Button
            onClick={onClose}
            variant="secondary"
            className="w-full sm:w-auto mt-3 sm:mt-0"
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          
          <Button
            onClick={handleConfirm}
            variant="primary"
            className="w-full sm:w-auto"
            disabled={!isValid}
            isLoading={isLoading}
          >
            {confirmText}
          </Button>
        </motion.div>
      </motion.div>
    </BaseDialog>
  );
};
