import React from "react";
import { Button } from "../common/Button";
import { BulkActionsToolbarProps } from "../../types/userSettings";

export const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedCount,
  totalCount,
  onDeleteSelected,
  onClearSelection,
  isLoading = false,
  className = "",
  itemType = "item",
}) => {
  if (selectedCount === 0) return null;

  return (
    <div
      className={`bg-gray-800 border border-gray-700 rounded-lg p-4 mb-4 ${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-white font-medium">
            {selectedCount} of {totalCount} {itemType}
            {selectedCount !== 1 ? "s" : ""} selected
          </span>

          <button
            onClick={onClearSelection}
            className="text-gray-400 hover:text-white text-sm underline"
            disabled={isLoading}
          >
            Clear selection
          </button>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            onClick={onDeleteSelected}
            variant="danger"
            size="sm"
            isLoading={isLoading}
            disabled={isLoading}
            className="px-4 py-2"
          >
            Delete Selected
          </Button>
        </div>
      </div>
    </div>
  );
};
