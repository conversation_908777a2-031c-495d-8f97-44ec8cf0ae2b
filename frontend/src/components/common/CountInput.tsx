import React, { useState, useEffect } from "react";

interface CountInputProps {
  value: number;
  onChange: (value: number) => void;
  label: string;
  min?: number;
  max?: number;
  placeholder?: string;
  error?: string;
  className?: string;
  disabled?: boolean;
}

export const CountInput: React.FC<CountInputProps> = ({
  value,
  onChange,
  label,
  min = 1,
  max = 100,
  placeholder = "Enter a number",
  error,
  className = "",
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState(value.toString());
  const [localError, setLocalError] = useState<string>("");

  // Update input value when external value changes
  useEffect(() => {
    if (value.toString() !== inputValue) {
      setInputValue(value.toString());
    }
  }, [value]);

  const validateAndUpdate = (inputStr: string) => {
    setInputValue(inputStr);

    // Clear any previous local error
    setLocalError("");

    // Allow empty input (we'll handle this as invalid but not show error immediately)
    if (inputStr.trim() === "") {
      return;
    }

    // Check if input contains only digits
    if (!/^\d+$/.test(inputStr.trim())) {
      setLocalError("Please enter a whole number");
      return;
    }

    const numValue = parseInt(inputStr.trim(), 10);

    // Check range
    if (numValue < min) {
      setLocalError(`Number must be at least ${min}`);
      return;
    }

    if (numValue > max) {
      setLocalError(`Number must be at most ${max}`);
      return;
    }

    // Valid input - call onChange
    onChange(numValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Allow typing digits and prevent pasting non-digits
    if (newValue === "" || /^\d+$/.test(newValue)) {
      validateAndUpdate(newValue);
    }
  };

  const handleBlur = () => {
    // On blur, if empty or invalid, reset to current valid value
    if (inputValue.trim() === "" || localError) {
      setInputValue(value.toString());
      setLocalError("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Prevent entering non-digit characters
    if (
      ![
        "Backspace",
        "Delete",
        "Tab",
        "Escape",
        "Enter",
        "ArrowLeft",
        "ArrowRight",
        "ArrowUp",
        "ArrowDown",
      ].includes(e.key) &&
      !(e.key >= "0" && e.key <= "9") &&
      !(e.ctrlKey && ["a", "c", "v", "x", "z"].includes(e.key.toLowerCase()))
    ) {
      e.preventDefault();
    }
  };

  const incrementValue = () => {
    const newValue = Math.min(value + 1, max);
    onChange(newValue);
  };

  const decrementValue = () => {
    const newValue = Math.max(value - 1, min);
    onChange(newValue);
  };

  const displayError = error || localError;

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-300 mb-2">
        {label}
      </label>

      <div className="relative">
        <input
          type="text"
          inputMode="numeric"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            w-full px-3 py-2 pr-20 bg-background-primary border rounded-lg text-white 
            placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors
            ${
              displayError
                ? "border-red-500 focus:border-red-500 focus:ring-red-500/50"
                : "border-gray-600 focus:border-primary-500 focus:ring-primary-500/50"
            }
            ${disabled ? "opacity-50 cursor-not-allowed" : ""}
          `}
        />

        {/* Increment/Decrement buttons */}
        <div className="absolute right-1 top-1 bottom-1 flex flex-col">
          <button
            type="button"
            onClick={incrementValue}
            disabled={disabled || value >= max}
            className="
              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 
              rounded-tr-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed
              focus:outline-none focus:bg-gray-600
            "
            tabIndex={-1}
          >
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <button
            type="button"
            onClick={decrementValue}
            disabled={disabled || value <= min}
            className="
              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 
              rounded-br-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed
              focus:outline-none focus:bg-gray-600
            "
            tabIndex={-1}
          >
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Error message */}
      {displayError && (
        <p className="mt-1 text-sm text-red-400">{displayError}</p>
      )}

      {/* Helper text */}
      {!displayError && (
        <p className="mt-1 text-xs text-gray-500">
          Enter a number between {min} and {max}
        </p>
      )}
    </div>
  );
};
