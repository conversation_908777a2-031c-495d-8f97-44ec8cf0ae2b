import React from "react";

interface ProgressBarProps {
  progress?: number; // 0-100 percentage
  isIndeterminate?: boolean; // For loading without known progress
  label?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "primary" | "success" | "warning" | "error";
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress = 0,
  isIndeterminate = false,
  label,
  className = "",
  size = "md",
  variant = "primary",
}) => {
  const sizeClasses = {
    sm: "h-1.5",
    md: "h-2",
    lg: "h-3",
  };

  const variantClasses = {
    primary: "bg-primary-500",
    success: "bg-green-500",
    warning: "bg-yellow-500",
    error: "bg-red-500",
  };

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  };

  return (
    <div className={`w-full ${className}`}>
      {label && (
        <div
          className={`flex justify-between items-center mb-2 ${textSizeClasses[size]}`}
        >
          <span className="text-gray-300 font-medium">{label}</span>
          {!isIndeterminate && (
            <span className="text-gray-400">{Math.round(progress)}%</span>
          )}
        </div>
      )}

      <div
        className={`w-full bg-gray-700 rounded-full overflow-hidden ${sizeClasses[size]}`}
      >
        <div
          className={`${
            variantClasses[variant]
          } transition-all duration-300 ease-out rounded-full ${
            sizeClasses[size]
          } ${
            isIndeterminate
              ? "animate-pulse w-full"
              : "transition-[width] duration-500"
          }`}
          style={
            !isIndeterminate
              ? { width: `${Math.min(100, Math.max(0, progress))}%` }
              : undefined
          }
        />
      </div>
    </div>
  );
};

// Enhanced loading progress bar specifically for AI generation
interface AIGenerationProgressProps {
  isGenerating: boolean;
  stage?: string;
  estimatedTime?: number; // in seconds
  className?: string;
}

export const AIGenerationProgress: React.FC<AIGenerationProgressProps> = ({
  isGenerating,
  stage,
  estimatedTime,
  className = "",
}) => {
  const [progress, setProgress] = React.useState(0);
  const [timeElapsed, setTimeElapsed] = React.useState(0);

  React.useEffect(() => {
    let interval: NodeJS.Timeout;
    let progressInterval: NodeJS.Timeout;

    if (isGenerating) {
      setTimeElapsed(0);
      setProgress(0);

      // Update elapsed time every second
      interval = setInterval(() => {
        setTimeElapsed((prev) => prev + 1);
      }, 1000);

      // Simulate progress for better UX (not accurate, just visual feedback)
      progressInterval = setInterval(() => {
        setProgress((prev) => {
          // Start fast, then slow down as we approach completion
          const increment = prev < 30 ? 3 : prev < 60 ? 2 : prev < 85 ? 1 : 0.2;
          return Math.min(90, prev + increment); // Cap at 90% until actually complete
        });
      }, 1000);
    } else {
      setProgress(0);
      setTimeElapsed(0);
    }

    return () => {
      if (interval) clearInterval(interval);
      if (progressInterval) clearInterval(progressInterval);
    };
  }, [isGenerating]);

  // Complete the progress when generation finishes
  React.useEffect(() => {
    if (!isGenerating && progress > 0) {
      setProgress(100);
      setTimeout(() => setProgress(0), 1000);
    }
  }, [isGenerating, progress]);

  if (!isGenerating && progress === 0) {
    return null;
  }

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div
      className={`bg-background-secondary rounded-lg p-4 border border-gray-600 ${className}`}
    >
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary-500 border-t-transparent" />
            <span className="text-white font-medium">
              {stage || "Generating with AI..."}
            </span>
          </div>
          <div className="text-sm text-gray-400">
            {timeElapsed > 0 && formatTime(timeElapsed)}
            {estimatedTime &&
              timeElapsed === 0 &&
              ` (Est. ${formatTime(estimatedTime)})`}
          </div>
        </div>

        <ProgressBar
          progress={progress}
          isIndeterminate={progress === 0}
          variant="primary"
          size="md"
        />

        <div className="text-xs text-gray-500 text-center">
          {isGenerating
            ? "Please wait while we generate your content..."
            : "Generation complete!"}
        </div>
      </div>
    </div>
  );
};
