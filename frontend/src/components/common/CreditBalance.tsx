import React from 'react';
import { useNavigate } from 'react-router-dom';
import { HiSparkles, HiPlus } from 'react-icons/hi';

interface CreditBalanceProps {
  credits: number;
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'sidebar' | 'inline' | 'card';
}

export const CreditBalance: React.FC<CreditBalanceProps> = ({ 
  credits, 
  showDetails = false, 
  size = 'md',
  variant = 'inline'
}) => {
  const navigate = useNavigate();

  const handleBuyCredits = () => {
    navigate('/credits');
  };

  // Determine styling based on variant and size
  const getContainerClasses = () => {
    const baseClasses = "flex items-center justify-between transition-all duration-200";
    
    switch (variant) {
      case 'sidebar':
        return `${baseClasses} bg-background-tertiary rounded-lg p-3 border border-gray-700 hover:border-primary-500/50`;
      case 'card':
        return `${baseClasses} bg-background-secondary rounded-lg p-4 border border-gray-600`;
      case 'inline':
      default:
        return `${baseClasses} bg-gray-800/50 rounded-lg p-2`;
    }
  };

  const getTextSizes = () => {
    switch (size) {
      case 'sm':
        return {
          primary: 'text-sm',
          secondary: 'text-xs',
          icon: 'w-4 h-4'
        };
      case 'lg':
        return {
          primary: 'text-lg',
          secondary: 'text-sm',
          icon: 'w-6 h-6'
        };
      case 'md':
      default:
        return {
          primary: 'text-sm',
          secondary: 'text-xs',
          icon: 'w-5 h-5'
        };
    }
  };

  const textSizes = getTextSizes();
  const isLowCredits = credits < 5;
  const isOutOfCredits = credits === 0;

  return (
    <div className={getContainerClasses()}>
      <div className="flex items-center space-x-2 flex-1 min-w-0">
        <div className={`
          flex items-center justify-center rounded-full p-1.5
          ${isOutOfCredits 
            ? 'bg-red-500/20 text-red-400' 
            : isLowCredits 
              ? 'bg-yellow-500/20 text-yellow-400'
              : 'bg-primary-500/20 text-primary-400'
          }
        `}>
          <HiSparkles className={textSizes.icon} />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1">
            <span className={`${textSizes.primary} font-medium text-white`}>
              {credits}
            </span>
            <span className={`${textSizes.secondary} text-gray-400`}>
              credit{credits !== 1 ? 's' : ''}
            </span>
          </div>
          
          {variant === 'sidebar' && (
            <div className={`${textSizes.secondary} text-gray-500 truncate`}>
              {isOutOfCredits 
                ? 'No credits remaining'
                : isLowCredits 
                  ? 'Running low'
                  : 'Available for AI generation'
              }
            </div>
          )}
        </div>
      </div>

      {/* Action Button */}
      {(isLowCredits || showDetails) && (
        <button
          onClick={handleBuyCredits}
          className={`
            flex items-center justify-center rounded-lg transition-colors
            ${variant === 'sidebar' 
              ? 'p-2 hover:bg-primary-500/20 text-primary-400 hover:text-primary-300' 
              : 'p-1.5 hover:bg-gray-700 text-gray-400 hover:text-white'
            }
          `}
          title="Buy more credits"
          aria-label="Buy more credits"
        >
          <HiPlus className={textSizes.icon} />
        </button>
      )}
    </div>
  );
};
