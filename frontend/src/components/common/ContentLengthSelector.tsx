import React from "react";
import { ContentLength } from "../../shared/types";

interface ContentLengthSelectorProps {
  value: ContentLength;
  onChange: (length: ContentLength) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
}

const contentLengthOptions = [
  {
    value: ContentLength.SHORT,
    label: "Short",
    description: "Concise answers (1-2 sentences)",
    icon: "📝",
  },
  {
    value: ContentLength.MEDIUM,
    label: "Medium",
    description: "Balanced detail (2-3 sentences)",
    icon: "📄",
  },
  {
    value: ContentLength.LONG,
    label: "Long",
    description: "Comprehensive answers (3-5 sentences)",
    icon: "📋",
  },
];

const getContentLengthColor = (length: ContentLength): string => {
  switch (length) {
    case ContentLength.SHORT:
      return "bg-background-secondary text-text-primary border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50";
    case ContentLength.MEDIUM:
      return "bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";
    case ContentLength.LONG:
      return "bg-background-secondary text-text-primary border-indigo-500/30 hover:bg-indigo-500/10 hover:border-indigo-500/50";
    default:
      return "bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";
  }
};

const getSelectedContentLengthColor = (length: ContentLength): string => {
  switch (length) {
    case ContentLength.SHORT:
      return "bg-emerald-500/20 text-emerald-300 border-emerald-500 shadow-lg shadow-emerald-500/20";
    case ContentLength.MEDIUM:
      return "bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";
    case ContentLength.LONG:
      return "bg-indigo-500/20 text-indigo-300 border-indigo-500 shadow-lg shadow-indigo-500/20";
    default:
      return "bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";
  }
};

export const ContentLengthSelector: React.FC<ContentLengthSelectorProps> = ({
  value,
  onChange,
  className = "",
  disabled = false,
  label = "Content Length",
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-text-primary">
        {label}
      </label>
      <div className="grid grid-cols-3 gap-3">
        {contentLengthOptions.map((option) => {
          const isSelected = value === option.value;
          const colorClasses = isSelected
            ? getSelectedContentLengthColor(option.value)
            : getContentLengthColor(option.value);

          return (
            <button
              key={option.value}
              type="button"
              onClick={() => !disabled && onChange(option.value)}
              disabled={disabled}
              className={`
                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${colorClasses}
                ${
                  disabled
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer hover:scale-105"
                }
                ${
                  isSelected
                    ? "ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary"
                    : ""
                }
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `}
              title={option.description}
              aria-pressed={isSelected}
            >
              <div className="text-center space-y-2">
                <div className="text-2xl">{option.icon}</div>
                <div className="font-semibold">{option.label}</div>
                <div
                  className={`text-xs ${
                    isSelected ? "text-white/90" : "text-text-secondary"
                  }`}
                >
                  {option.description}
                </div>
              </div>
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>
      <p className="text-xs text-text-muted">
        Choose how detailed you want the flashcard answers to be. This affects
        the length and depth of explanations.
      </p>
    </div>
  );
};

export default ContentLengthSelector;
