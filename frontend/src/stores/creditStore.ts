import { create } from "zustand";
import { CreditTransaction, AIOperationCost } from "../shared/types";

interface CreditStats {
  totalUsed: number;
  totalPurchased: number;
  usageByOperation: Record<string, number>;
  dailyUsage: Array<{ date: string; credits: number }>;
}

interface CreditState {
  balance: number;
  transactions: CreditTransaction[];
  operationCosts: AIOperationCost[];
  stats: CreditStats | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchBalance: () => Promise<void>;
  fetchTransactions: (limit?: number, offset?: number) => Promise<void>;
  fetchOperationCosts: () => Promise<void>;
  fetchStats: (days?: number) => Promise<void>;
  purchaseCredits: (packageData: {
    amount: number;
    credits: number;
    price: number;
    email: string;
    name?: string;
    discountCode?: string;
  }) => Promise<{
    success: boolean;
    error?: string;
    clientSecret?: string;
    paymentIntentId?: string;
  }>;
  refreshAfterPurchase: () => Promise<void>;
  clearError: () => void;
}

export const useCreditStore = create<CreditState>((set, get) => ({
  balance: 0,
  transactions: [],
  operationCosts: [],
  stats: null,
  isLoading: false,
  error: null,

  fetchBalance: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await fetch("/api/credits/balance", {
        headers: {
          Authorization: `Bearer ${
            localStorage.getItem("auth_token") ||
            sessionStorage.getItem("auth_token")
          }`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch credit balance");
      }

      const data = await response.json();
      if (data.success) {
        set({ balance: data.data.credits, isLoading: false });
      } else {
        throw new Error(data.error || "Failed to fetch balance");
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : "Unknown error",
        isLoading: false,
      });
    }
  },

  fetchTransactions: async (limit = 50, offset = 0) => {
    set({ isLoading: true, error: null });
    try {
      const response = await fetch(
        `/api/credits/history?limit=${limit}&offset=${offset}`,
        {
          headers: {
            Authorization: `Bearer ${
              localStorage.getItem("auth_token") ||
              sessionStorage.getItem("auth_token")
            }`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch credit history");
      }

      const data = await response.json();
      if (data.success) {
        set({
          transactions:
            offset === 0 ? data.data : [...get().transactions, ...data.data],
          isLoading: false,
        });
      } else {
        throw new Error(data.error || "Failed to fetch transactions");
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : "Unknown error",
        isLoading: false,
      });
    }
  },

  fetchOperationCosts: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await fetch("/api/credits/pricing", {
        headers: {
          Authorization: `Bearer ${
            localStorage.getItem("auth_token") ||
            sessionStorage.getItem("auth_token")
          }`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch operation costs");
      }

      const data = await response.json();
      if (data.success) {
        set({ operationCosts: data.data, isLoading: false });
      } else {
        throw new Error(data.error || "Failed to fetch operation costs");
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : "Unknown error",
        isLoading: false,
      });
    }
  },

  fetchStats: async (days = 30) => {
    set({ isLoading: true, error: null });
    try {
      const response = await fetch(`/api/credits/stats?days=${days}`, {
        headers: {
          Authorization: `Bearer ${
            localStorage.getItem("auth_token") ||
            sessionStorage.getItem("auth_token")
          }`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch credit stats");
      }

      const data = await response.json();
      if (data.success) {
        set({ stats: data.data, isLoading: false });
      } else {
        throw new Error(data.error || "Failed to fetch stats");
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : "Unknown error",
        isLoading: false,
      });
    }
  },

  purchaseCredits: async (packageData: {
    amount: number;
    credits: number;
    price: number;
    email: string;
    name?: string;
    discountCode?: string;
  }) => {
    set({ isLoading: true, error: null });
    try {
      const response = await fetch("/api/credits/purchase", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${
            localStorage.getItem("auth_token") ||
            sessionStorage.getItem("auth_token")
          }`,
        },
        body: JSON.stringify(packageData),
      });

      if (!response.ok) {
        throw new Error("Failed to initiate credit purchase");
      }

      const data = await response.json();
      if (data.success) {
        set({ isLoading: false });
        return {
          success: true,
          clientSecret: data.data.clientSecret,
          paymentIntentId: data.data.paymentIntentId,
        };
      } else {
        throw new Error(data.error || "Failed to purchase credits");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      set({
        error: errorMessage,
        isLoading: false,
      });
      return { success: false, error: errorMessage };
    }
  },

  clearError: () => set({ error: null }),

  refreshAfterPurchase: async () => {
    try {
      await Promise.all([get().fetchBalance(), get().fetchTransactions()]);
    } catch (error) {
      console.error("Failed to refresh data after purchase:", error);
    }
  },
}));
