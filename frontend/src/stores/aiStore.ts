import { create } from "zustand";
import {
  StudySet,
  Flashcard,
  QuizQuestion,
  DifficultyLevel,
  ContentLength,
} from "../../../shared/types";

interface AIGenerationState {
  isGenerating: boolean;
  generationProgress: string;
  lastGenerated: {
    studySet?: StudySet;
    content?: Flashcard[] | QuizQuestion[];
    type?: "flashcards" | "quiz";
  } | null;

  // Actions
  generateFlashcards: (params: {
    documentIds: string[];
    documentPageRanges?: {
      [documentId: string]: { startPage: number; endPage: number };
    };
    name: string;
    count: number;
    customPrompt?: string;
    difficultyLevel?: DifficultyLevel;
    contentLength?: ContentLength;
    existingContent?: string[];
  }) => Promise<{
    studySet: StudySet;
    flashcards: Flashcard[];
    creditsRemaining: number;
  }>;

  generateQuiz: (params: {
    documentIds: string[];
    documentPageRanges?: {
      [documentId: string]: { startPage: number; endPage: number };
    };
    name: string;
    count: number;
    customPrompt?: string;
    difficultyLevel?: DifficultyLevel;
    contentLength?: ContentLength;
    questionTypes?: string[];
    existingContent?: string[];
    studySetId?: string;
  }) => Promise<{
    studySet: StudySet;
    questions: QuizQuestion[];
    creditsRemaining: number;
  }>;

  generateMoreFlashcards: (params: {
    studySetId: string;
    documentIds: string[];
    documentPageRanges?: {
      [documentId: string]: { startPage: number; endPage: number };
    };
    count: number;
    customPrompt?: string;
    difficultyLevel?: DifficultyLevel;
    contentLength?: ContentLength;
    existingContent?: string[];
  }) => Promise<{ flashcards: Flashcard[]; creditsRemaining: number }>;

  generateMoreQuizQuestions: (params: {
    studySetId: string;
    documentIds: string[];
    documentPageRanges?: {
      [documentId: string]: { startPage: number; endPage: number };
    };
    count: number;
    customPrompt?: string;
    difficultyLevel?: DifficultyLevel;
    contentLength?: ContentLength;
    questionTypes?: string[];
    existingContent?: string[];
  }) => Promise<{ questions: QuizQuestion[]; creditsRemaining: number }>;

  clearLastGenerated: () => void;
}

export const useAIStore = create<AIGenerationState>((set) => ({
  isGenerating: false,
  generationProgress: "",
  lastGenerated: null,

  generateFlashcards: async (params) => {
    set({ isGenerating: true, generationProgress: "Preparing documents..." });

    try {
      const token = localStorage.getItem("auth_token");

      set({ generationProgress: "Generating flashcards with AI..." });

      const response = await fetch("/api/ai/generate-flashcards", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || "Generation failed");
      }

      const result = await response.json();

      if (result.success) {
        set({
          lastGenerated: {
            studySet: result.data.studySet,
            content: result.data.flashcards,
            type: "flashcards",
          },
          isGenerating: false,
          generationProgress: "",
        });

        // Refresh cache for the newly created study set
        try {
          // Import dynamically to avoid circular dependency
          const { useStudyStore } = await import("./studyStore");
          const studyStore = useStudyStore.getState();
          studyStore.refreshStudySetContent(result.data.studySet.id);
          studyStore.invalidateStudySets(); // Refresh study sets list
        } catch (cacheError) {
          console.warn("Failed to refresh study set cache:", cacheError);
        }

        return {
          studySet: result.data.studySet,
          flashcards: result.data.flashcards,
          creditsRemaining: result.data.creditsRemaining,
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: "" });
      throw error;
    }
  },

  generateQuiz: async (params) => {
    set({ isGenerating: true, generationProgress: "Preparing documents..." });

    try {
      const token = localStorage.getItem("auth_token");

      set({ generationProgress: "Generating quiz questions with AI..." });

      const response = await fetch("/api/ai/generate-quiz", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || "Generation failed");
      }

      const result = await response.json();

      if (result.success) {
        set({
          lastGenerated: {
            studySet: result.data.studySet,
            content: result.data.questions,
            type: "quiz",
          },
          isGenerating: false,
          generationProgress: "",
        });

        // Refresh cache for the newly created study set
        try {
          // Import dynamically to avoid circular dependency
          const { useStudyStore } = await import("./studyStore");
          const studyStore = useStudyStore.getState();
          studyStore.refreshStudySetContent(result.data.studySet.id);
          studyStore.invalidateStudySets(); // Refresh study sets list
        } catch (cacheError) {
          console.warn("Failed to refresh study set cache:", cacheError);
        }

        return {
          studySet: result.data.studySet,
          questions: result.data.questions,
          creditsRemaining: result.data.creditsRemaining,
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: "" });
      throw error;
    }
  },

  generateMoreFlashcards: async (params) => {
    set({ isGenerating: true, generationProgress: "Preparing documents..." });

    try {
      const token = localStorage.getItem("auth_token");

      set({
        generationProgress: "Generating additional flashcards with AI...",
      });

      const response = await fetch("/api/ai/generate-more-flashcards", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || "Generation failed");
      }

      const result = await response.json();

      if (result.success) {
        set({
          isGenerating: false,
          generationProgress: "",
        });

        // Refresh cache for the updated study set
        try {
          // Import dynamically to avoid circular dependency
          const { useStudyStore } = await import("./studyStore");
          const studyStore = useStudyStore.getState();
          studyStore.refreshStudySetContent(params.studySetId);
        } catch (cacheError) {
          console.warn("Failed to refresh study set cache:", cacheError);
        }

        return {
          flashcards: result.data.flashcards,
          creditsRemaining: result.data.creditsRemaining,
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: "" });
      throw error;
    }
  },

  generateMoreQuizQuestions: async (params) => {
    set({ isGenerating: true, generationProgress: "Preparing documents..." });

    try {
      const token = localStorage.getItem("auth_token");

      set({
        generationProgress: "Generating additional quiz questions with AI...",
      });

      const response = await fetch("/api/ai/generate-more-quiz-questions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || "Generation failed");
      }

      const result = await response.json();

      if (result.success) {
        set({ isGenerating: false, generationProgress: "" });

        return {
          questions: result.data.questions,
          creditsRemaining: result.data.creditsRemaining,
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: "" });
      throw error;
    }
  },

  clearLastGenerated: () => {
    set({ lastGenerated: null });
  },
}));
