import React from 'react';
import { DocumentUpload } from '../components/documents/DocumentUpload';
import { DocumentList } from '../components/documents/DocumentList';

export const DocumentsPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">Documents</h1>
          <p className="mt-2 text-gray-400">
            Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX
          </p>
        </div>

        {/* Upload Section */}
        <div className="bg-background-secondary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Upload Documents</h2>
          <DocumentUpload />
        </div>

        {/* Document List */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Your Documents</h2>
          <DocumentList />
        </div>
      </div>
    </div>
  );
};
