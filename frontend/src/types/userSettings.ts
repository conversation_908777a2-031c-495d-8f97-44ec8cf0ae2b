// User Settings Types
export interface UserSettings {
  id: string;
  user_id: string;
  skip_delete_confirmations: boolean;
  shuffle_flashcards: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserSettingsUpdate {
  skip_delete_confirmations?: boolean;
  shuffle_flashcards?: boolean;
}

export interface UseUserSettingsReturn {
  settings: UserSettings | null;
  loading: boolean;
  error: string | null;
  updateSettings: (updates: UserSettingsUpdate) => Promise<void>;
  refetch: () => Promise<void>;
}

// API Request/Response Types
export interface GetUserSettingsResponse {
  success: boolean;
  data: UserSettings;
  error?: string;
}

export interface UpdateUserSettingsRequest {
  skip_delete_confirmations?: boolean;
  shuffle_flashcards?: boolean;
}

export interface UpdateUserSettingsResponse {
  success: boolean;
  data: UserSettings;
  error?: string;
}

// Bulk Delete Types
export interface BulkDeleteRequest {
  flashcardIds: string[];
}

export interface BulkDeleteResponse {
  success: boolean;
  data: {
    deletedCount: number;
    requestedCount: number;
  };
  error?: string;
}

// Enhanced Dialog Types
export interface ConfirmOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "danger" | "warning" | "success";
  buttonLayout?: "default" | "corners";
  additionalContent?: React.ReactNode;
}

export interface DialogContextValue {
  alert: (options: Omit<ConfirmOptions, "cancelText">) => Promise<void>;
  confirm: (options: ConfirmOptions) => Promise<boolean>;
  prompt: (options: {
    title: string;
    message: string;
    defaultValue?: string;
    placeholder?: string;
    confirmText?: string;
    cancelText?: string;
  }) => Promise<string | null>;
}

// Component Props Types
export interface ShuffleToggleProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  disabled?: boolean;
  className?: string;
  label?: string;
  description?: string;
}

export interface NeverAskAgainCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export interface BulkActionsToolbarProps {
  selectedCount: number;
  totalCount: number;
  onDeleteSelected: () => void;
  onClearSelection: () => void;
  isLoading?: boolean;
  className?: string;
  itemType?: string;
}

// Study Session Types (extending existing)
export interface StudySessionExtended {
  studySetId: string;
  type: "flashcards" | "quiz";
  startTime: Date;
  currentIndex: number;
  totalItems: number;
  reviewedItems: number[];
  flaggedItems: string[];
  correctAnswers?: number;
  timeSpent: number;
  isShuffled: boolean;
  originalOrder?: number[];
}
