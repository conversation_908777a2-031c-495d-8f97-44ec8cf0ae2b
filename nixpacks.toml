[phases.setup]
nixPkgs = ["nodejs-18_x", "npm-9_x"]

[phases.install]
cmds = [
  "cd backend",
  "npm ci --only=production --no-audit --no-fund"
]

[phases.build]
cmds = [
  "cd backend", 
  "npm run build"
]

[start]
cmd = "cd backend && npm start"

[variables]
NODE_ENV = "production"
NPM_CONFIG_PRODUCTION = "true"

# Optimize for cost and performance
[build]
cache = true
buildCommand = "cd backend && npm run build"

# Health check configuration
[deploy]
healthCheck = "/api/health"

