{"name": "chewy-ai-backend", "version": "1.0.0", "description": "ChewyAI Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "axios": "^1.10.0", "bcrypt": "^5.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "form-data": "^4.0.3", "helmet": "^6.1.5", "mammoth": "^1.9.1", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "stripe": "^12.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.0", "@types/multer": "^1.4.13", "@types/pdf-parse": "^1.1.5", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "eslint": "^8.38.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.2"}, "keywords": ["ai", "study", "flashcards", "quiz", "education"], "author": "ChewyAI Team", "license": "MIT"}