-- Add last_credit_reset column to users table
-- This column tracks when credits were last reset for freemium users

-- Add the column with a default value of the current timestamp
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_credit_reset TIMESTAMPTZ DEFAULT NOW();

-- Update existing users to have their last_credit_reset set to their created_at date
-- This ensures existing freemium users get proper credit reset tracking
UPDATE users 
SET last_credit_reset = created_at 
WHERE last_credit_reset IS NULL;

-- Make the column NOT NULL after setting default values
ALTER TABLE users 
ALTER COLUMN last_credit_reset SET NOT NULL;

-- Add a comment to document the column purpose
COMMENT ON COLUMN users.last_credit_reset IS 'Timestamp of when credits were last reset for freemium users (monthly reset)'; 