import "dotenv/config";
import { createClient } from "@supabase/supabase-js";
import * as fs from "fs";
import * as path from "path";

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

console.log("🔧 Environment check:");
console.log("SUPABASE_URL:", supabaseUrl ? "✅ Set" : "❌ Missing");
console.log(
  "SUPABASE_SERVICE_ROLE_KEY:",
  supabaseServiceKey ? "✅ Set" : "❌ Missing"
);

if (!supabaseUrl || !supabaseServiceKey) {
  console.log(
    "❌ Missing required environment variables. Please check your .env file."
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addLastCreditResetColumn() {
  try {
    console.log(
      "🔄 Starting migration: Adding last_credit_reset column to users table..."
    );

    // Read the SQL file
    const sqlPath = path.join(__dirname, "addLastCreditResetColumn.sql");
    const sqlContent = fs.readFileSync(sqlPath, "utf8");

    // Execute the SQL
    const { error } = await supabase.rpc("exec_sql", { query: sqlContent });

    if (error) {
      // If RPC doesn't work, try direct SQL execution
      console.log("⚠️  RPC failed, trying direct SQL execution...");

      // Split the SQL into individual commands
      const commands = sqlContent
        .split(";")
        .map((cmd) => cmd.trim())
        .filter((cmd) => cmd && !cmd.startsWith("--"));

      for (const command of commands) {
        if (command) {
          const { error: cmdError } = await supabase
            .from("users")
            .select("1")
            .limit(1); // Test connection first

          if (
            cmdError &&
            cmdError.message.includes('relation "users" does not exist')
          ) {
            console.log(
              "❌ Users table does not exist. Please create it first."
            );
            return;
          }

          console.log(`Executing: ${command.substring(0, 50)}...`);

          // For ALTER TABLE commands, we need to use the SQL editor approach
          if (command.includes("ALTER TABLE")) {
            console.log(
              "⚠️  Please run the following SQL commands manually in your Supabase SQL editor:"
            );
            console.log("---");
            console.log(sqlContent);
            console.log("---");
            return;
          }
        }
      }
    }

    console.log("✅ Migration completed successfully!");
    console.log(
      "📋 The last_credit_reset column has been added to the users table."
    );
  } catch (error) {
    console.error("❌ Migration failed:", error);
    console.log("\n🔧 Manual Fix:");
    console.log("Please run the following SQL in your Supabase SQL editor:");
    console.log("---");
    const sqlPath = path.join(__dirname, "addLastCreditResetColumn.sql");
    const sqlContent = fs.readFileSync(sqlPath, "utf8");
    console.log(sqlContent);
    console.log("---");
  }
}

// Run the migration
addLastCreditResetColumn();
