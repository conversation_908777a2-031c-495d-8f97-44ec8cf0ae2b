-- Add page_count column to documents table for page range functionality
-- This column stores the total number of pages for PDF files and slides for PPTX files

ALTER TABLE documents 
ADD COLUMN page_count INTEGER CHECK (page_count > 0);

-- Add a comment to document the purpose
COMMENT ON COLUMN documents.page_count IS 'Total number of pages for PDF files or slides for PPTX files';

-- Create index for better performance when filtering documents with page counts
CREATE INDEX idx_documents_page_count ON documents(page_count) WHERE page_count IS NOT NULL;

-- Update existing documents to have NULL page_count (will be populated when reprocessed)
-- This is safe since the column is nullable and existing functionality will continue to work 