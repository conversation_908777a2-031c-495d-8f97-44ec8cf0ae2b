import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupStoragePolicies() {
  try {
    console.log('🔧 Setting up storage policies...');

    // First, let's make the bucket public for now to test the upload flow
    const { error: bucketError } = await supabase
      .storage
      .updateBucket('documents', { public: true });

    if (bucketError) {
      console.log('⚠️  Could not make bucket public:', bucketError.message);
    } else {
      console.log('✅ Documents bucket is now public');
    }

    // Try to create RLS policies using SQL
    const policies = [
      {
        name: 'Users can upload their own files',
        sql: `
          CREATE POLICY "Users can upload their own files" ON storage.objects
          FOR INSERT WITH CHECK (
            bucket_id = 'documents' AND 
            auth.uid()::text = (storage.foldername(name))[1]
          );
        `
      },
      {
        name: 'Users can view their own files',
        sql: `
          CREATE POLICY "Users can view their own files" ON storage.objects
          FOR SELECT USING (
            bucket_id = 'documents' AND 
            auth.uid()::text = (storage.foldername(name))[1]
          );
        `
      },
      {
        name: 'Users can delete their own files',
        sql: `
          CREATE POLICY "Users can delete their own files" ON storage.objects
          FOR DELETE USING (
            bucket_id = 'documents' AND 
            auth.uid()::text = (storage.foldername(name))[1]
          );
        `
      }
    ];

    // Enable RLS first
    const { error: rlsError } = await supabase.rpc('sql', {
      query: 'ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;'
    });

    if (rlsError) {
      console.log('⚠️  Could not enable RLS:', rlsError.message);
    } else {
      console.log('✅ RLS enabled on storage.objects');
    }

    // Create policies
    for (const policy of policies) {
      const { error } = await supabase.rpc('sql', { query: policy.sql });
      if (error) {
        console.log(`⚠️  Could not create policy "${policy.name}":`, error.message);
      } else {
        console.log(`✅ Created policy: ${policy.name}`);
      }
    }

    console.log('🎉 Storage setup complete!');

  } catch (error: any) {
    console.error('❌ Storage setup failed:', error.message);
    process.exit(1);
  }
}

setupStoragePolicies();
