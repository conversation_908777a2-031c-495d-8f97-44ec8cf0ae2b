import { Request, Response } from "express";
import multer from "multer";
import {
  DocumentProcessor,
  FileUploadService,
} from "../services/documentService";
import { documentDbService } from "../services/documentDbService";
import { DocumentFileType } from "../../../shared/types";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (_req, file, cb) => {
    const allowedTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/plain",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Unsupported file type") as any, false);
    }
  },
});

const processor = new DocumentProcessor();
const fileUploadService = new FileUploadService();

export const uploadDocument = [
  upload.single("document"),
  (error: any, _req: Request, res: Response, next: any) => {
    if (error) {
      if (error.code === "LIMIT_FILE_SIZE") {
        return res.status(413).json({
          success: false,
          error: "File too large. Maximum size is 50MB.",
        });
      }
      if (error.message === "Unsupported file type") {
        return res.status(415).json({
          success: false,
          error:
            "Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.",
        });
      }
      return res.status(400).json({
        success: false,
        error: error.message,
      });
    }
    next();
  },
  async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: "No file uploaded",
        });
      }

      const userId = req.user!.id;

      // Enforce per-user document limit (max 10)
      const existingDocs = await documentDbService.getUserDocuments(
        userId,
        11,
        0
      );
      if (existingDocs.length >= 10) {
        return res.status(400).json({
          success: false,
          error: "Document limit reached. You can upload up to 10 documents.",
        });
      }
      const file = req.file;

      // Process file and extract text
      let processingResult: { text: string; pageCount?: number };
      try {
        processingResult = await processor.processFile(file);
      } catch (processingError) {
        console.error("File processing error:", processingError);
        return res.status(415).json({
          success: false,
          error: "Unsupported file type or corrupted file",
        });
      }

      // Upload original file to Supabase Storage (simplified path for testing)
      const fileName = `${Date.now()}_${file.originalname}`;
      let storagePath: string;

      try {
        storagePath = await fileUploadService.uploadFile(
          fileName,
          file.buffer,
          file.mimetype
        );
      } catch (storageError) {
        console.error("Storage upload error:", storageError);
        return res.status(500).json({
          success: false,
          error: "Failed to store file",
        });
      }

      // Insert document metadata and content into database
      try {
        const documentData = {
          user_id: userId,
          filename: file.originalname,
          file_type: getFileTypeFromMimetype(file.mimetype),
          file_size: file.size,
          content_text: processingResult.text,
          supabase_storage_path: storagePath,
          is_processed: true,
          processing_error: undefined,
          page_count: processingResult.pageCount,
        };

        const document = await documentDbService.createDocument(documentData);

        return res.status(201).json({
          success: true,
          data: document,
          message: "Document uploaded and processed successfully",
        });
      } catch (dbError) {
        console.error("Database error:", dbError);

        // Cleanup - delete uploaded file if database insert fails
        try {
          await fileUploadService.deleteFile(storagePath);
        } catch (cleanupError) {
          console.error(
            "Failed to cleanup file after database error:",
            cleanupError
          );
        }

        return res.status(500).json({
          success: false,
          error: "Failed to save document metadata",
        });
      }
    } catch (error) {
      console.error("Unexpected error in uploadDocument:", error);
      return res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  },
];

export const getDocuments = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const documents = await documentDbService.getUserDocuments(
      userId,
      limit,
      offset
    );

    res.json({
      success: true,
      data: documents,
    });
  } catch (error) {
    console.error("Get documents error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to retrieve documents",
    });
  }
};

export const getDocument = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const documentId = req.params.id;

    const document = await documentDbService.getDocumentById(
      documentId,
      userId
    );

    if (!document) {
      return res.status(404).json({
        success: false,
        error: "Document not found",
      });
    }

    res.json({
      success: true,
      data: document,
    });
  } catch (error) {
    console.error("Get document error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to retrieve document",
    });
  }
};

export const deleteDocument = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const documentId = req.params.id;

    // Get document to find storage path
    const document = await documentDbService.getDocumentById(
      documentId,
      userId
    );

    if (!document) {
      return res.status(404).json({
        success: false,
        error: "Document not found",
      });
    }

    // Delete from database first
    await documentDbService.deleteDocument(documentId, userId);

    // Delete from storage (don't fail if this fails)
    try {
      await fileUploadService.deleteFile(document.supabase_storage_path);
    } catch (storageError) {
      console.error("Storage deletion error:", storageError);
      // Continue - database deletion succeeded
    }

    res.json({
      success: true,
      message: "Document deleted successfully",
    });
  } catch (error) {
    console.error("Delete document error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to delete document",
    });
  }
};

export const searchDocuments = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const query = req.query.q as string;

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        error: "Search query must be at least 2 characters",
      });
    }

    const documents = await documentDbService.searchDocuments(
      userId,
      query.trim()
    );

    res.json({
      success: true,
      data: documents,
    });
  } catch (error) {
    console.error("Search documents error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to search documents",
    });
  }
};

function getFileTypeFromMimetype(mimetype: string): DocumentFileType {
  const typeMap: Record<string, DocumentFileType> = {
    "application/pdf": "pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      "docx",
    "text/plain": "txt",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation":
      "pptx",
  };

  return typeMap[mimetype] || "txt";
}
