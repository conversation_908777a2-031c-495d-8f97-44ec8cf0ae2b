import { Request, Response } from 'express';
import { flashcardService } from '../services/flashcardService';

export const getFlashcards = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const studySetId = req.params.studySetId;

    const flashcards = await flashcardService.getFlashcardsByStudySet(studySetId, userId);

    res.json({
      success: true,
      data: flashcards
    });
  } catch (error: any) {
    console.error('Get flashcards error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to retrieve flashcards'
    });
  }
};

export const createFlashcard = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const studySetId = req.params.studySetId;
    const flashcardData = req.body;

    // Validate input
    if (!flashcardData.front || !flashcardData.back) {
      return res.status(400).json({
        success: false,
        error: 'Front and back content are required'
      });
    }

    const flashcard = await flashcardService.createFlashcard(studySetId, userId, flashcardData);

    res.status(201).json({
      success: true,
      data: flashcard
    });
  } catch (error: any) {
    console.error('Create flashcard error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create flashcard'
    });
  }
};

export const updateFlashcard = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const flashcardId = req.params.id;
    const updates = req.body;

    // Only allow certain fields to be updated
    const allowedUpdates = ['front', 'back', 'difficulty_level'];
    const filteredUpdates = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .reduce((obj: any, key) => {
        obj[key] = updates[key];
        return obj;
      }, {});

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid updates provided'
      });
    }

    const flashcard = await flashcardService.updateFlashcard(flashcardId, userId, filteredUpdates);

    res.json({
      success: true,
      data: flashcard
    });
  } catch (error: any) {
    console.error('Update flashcard error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update flashcard'
    });
  }
};

export const deleteFlashcard = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const flashcardId = req.params.id;

    await flashcardService.deleteFlashcard(flashcardId, userId);

    res.json({
      success: true,
      message: 'Flashcard deleted successfully'
    });
  } catch (error: any) {
    console.error('Delete flashcard error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to delete flashcard'
    });
  }
};

export const toggleFlashcardFlag = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const flashcardId = req.params.id;

    const flashcard = await flashcardService.toggleFlashcardFlag(flashcardId, userId);

    res.json({
      success: true,
      data: flashcard
    });
  } catch (error: any) {
    console.error('Toggle flashcard flag error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to toggle flashcard flag'
    });
  }
};

export const bulkDeleteFlashcards = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { flashcardIds } = req.body;

    // Validate input
    if (!Array.isArray(flashcardIds) || flashcardIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'flashcardIds must be a non-empty array'
      });
    }

    if (flashcardIds.length > 100) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete more than 100 flashcards at once'
      });
    }

    // Validate all IDs are strings
    if (!flashcardIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
      return res.status(400).json({
        success: false,
        error: 'All flashcard IDs must be valid strings'
      });
    }

    const deletedCount = await flashcardService.bulkDeleteFlashcards(flashcardIds, userId);

    res.json({
      success: true,
      data: {
        deletedCount,
        requestedCount: flashcardIds.length
      }
    });
  } catch (error: any) {
    console.error('Bulk delete flashcards error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to delete flashcards'
    });
  }
};
