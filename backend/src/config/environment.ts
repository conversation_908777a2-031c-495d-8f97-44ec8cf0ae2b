import dotenv from "dotenv";

dotenv.config();

interface EnvironmentConfig {
  NODE_ENV: string;
  PORT: number;
  FRONTEND_URL: string;

  // Supabase
  SUPABASE_URL: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  SUPABASE_ANON_KEY: string;

  // Stripe
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  STRIPE_PUBLISHABLE_KEY: string;

  // OpenRouter AI
  OPENROUTER_API_KEY: string;

  // JWT
  JWT_SECRET: string;
}

function validateEnvironment(): EnvironmentConfig {
  const requiredVars = [
    "SUPABASE_URL",
    "SUPABASE_SERVICE_ROLE_KEY",
    "SUPABASE_ANON_KEY",
    "STRIPE_SECRET_KEY",
    "STRIPE_WEBHOOK_SECRET",
    "STRIPE_PUBLISHABLE_KEY",
    "OPENROUTER_API_KEY",
    "JWT_SECRET",
  ];

  const missing: string[] = [];
  const invalid: string[] = [];

  // Check for missing variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }

  if (missing.length > 0) {
    console.error("❌ Missing required environment variables:");
    missing.forEach((varName) => console.error(`   - ${varName}`));
    throw new Error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
  }

  // Validate specific formats
  const supabaseUrl = process.env.SUPABASE_URL!;
  if (
    !supabaseUrl.startsWith("https://") ||
    !supabaseUrl.includes("supabase.co")
  ) {
    invalid.push("SUPABASE_URL (must be a valid Supabase URL)");
  }

  const stripeSecretKey = process.env.STRIPE_SECRET_KEY!;
  if (!stripeSecretKey.startsWith("sk_")) {
    invalid.push("STRIPE_SECRET_KEY (must start with sk_)");
  }

  const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
  if (!stripeWebhookSecret.startsWith("whsec_")) {
    invalid.push("STRIPE_WEBHOOK_SECRET (must start with whsec_)");
  }

  const stripePublishableKey = process.env.STRIPE_PUBLISHABLE_KEY!;
  if (!stripePublishableKey.startsWith("pk_")) {
    invalid.push("STRIPE_PUBLISHABLE_KEY (must start with pk_)");
  }

  if (invalid.length > 0) {
    console.error("❌ Invalid environment variables:");
    invalid.forEach((error) => console.error(`   - ${error}`));
    throw new Error(`Invalid environment variables: ${invalid.join(", ")}`);
  }

  // Determine environment
  const nodeEnv = process.env.NODE_ENV || "development";
  const port = parseInt(process.env.PORT || "4000", 10);

  let frontendUrl: string;
  if (nodeEnv === "production") {
    // For Replit deployment, allow fallback to localhost:3000 if FRONTEND_URL not set
    frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
  } else {
    frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
  }

  console.log(`✅ Environment validation passed (${nodeEnv})`);

  return {
    NODE_ENV: nodeEnv,
    PORT: port,
    FRONTEND_URL: frontendUrl,
    SUPABASE_URL: supabaseUrl,
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY!,
    SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY!,
    STRIPE_SECRET_KEY: stripeSecretKey,
    STRIPE_WEBHOOK_SECRET: stripeWebhookSecret,
    STRIPE_PUBLISHABLE_KEY: stripePublishableKey,
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY!,
    JWT_SECRET: process.env.JWT_SECRET!,
  };
}

export const env = validateEnvironment();
export type { EnvironmentConfig };
