import { PRICING_TIERS } from "../../../shared/constants";

// Environment-specific configuration for backend
export const getEnvVar = (key: string, fallback: string = ""): string => {
  return process.env[key] || fallback;
};

// Update pricing tiers with actual Stripe price IDs from environment
export const BACKEND_PRICING_TIERS = PRICING_TIERS.map((tier) => {
  if (tier.stripePriceId) {
    switch (tier.id) {
      case "Study Starter":
        return {
          ...tier,
          stripePriceId: getEnvVar(
            "STRIPE_PRICE_ID_STARTER",
            tier.stripePriceId
          ),
        };
      case "Study Pro":
        return {
          ...tier,
          stripePriceId: getEnvVar("STRIPE_PRICE_ID_PRO", tier.stripePriceId),
        };
      case "Study Master":
        return {
          ...tier,
          stripePriceId: getEnvVar(
            "STRIPE_PRICE_ID_MASTER",
            tier.stripePriceId
          ),
        };
      case "Study Elite":
        return {
          ...tier,
          stripePriceId: getEnvVar("STRIPE_PRICE_ID_ELITE", tier.stripePriceId),
        };
      default:
        return tier;
    }
  }
  return tier;
});

export const BACKEND_STRIPE_CONFIG = {
  publishableKey: getEnvVar("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", ""),
  secretKey: getEnvVar("STRIPE_SECRET_KEY", ""),
  webhookSecret: getEnvVar("STRIPE_WEBHOOK_SECRET", ""),
  successUrl:
    getEnvVar("FRONTEND_URL", "http://localhost:3000") +
    "/subscription/success",
  cancelUrl:
    getEnvVar("FRONTEND_URL", "http://localhost:3000") + "/subscription/cancel",
} as const;

export const BACKEND_CONFIG = {
  port: parseInt(getEnvVar("PORT", "3001")),
  frontendUrl: getEnvVar("FRONTEND_URL", "http://localhost:3000"),
  nodeEnv: getEnvVar("NODE_ENV", "development"),
} as const;
