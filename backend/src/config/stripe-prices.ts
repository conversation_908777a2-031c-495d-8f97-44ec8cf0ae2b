interface StripePriceConfig {
  id: string;
  name: string;
  description: string;
  amount: number; // in cents
  currency: string;
  recurring?: {
    interval: "month" | "year";
    interval_count: number;
  };
  metadata: {
    type: "subscription" | "credit_package";
    tier?: string;
    credits?: number;
    package_id?: string;
  };
}

// Subscription Tiers - Business Model v4.0
export const SUBSCRIPTION_PRICES: StripePriceConfig[] = [
  {
    id: "price_study_starter_monthly",
    name: "Study Starter - Monthly",
    description:
      "150 credits per month - Perfect for high school & early college students",
    amount: 2900, // $29.00
    currency: "usd",
    recurring: {
      interval: "month",
      interval_count: 1,
    },
    metadata: {
      type: "subscription",
      tier: "study_starter",
      credits: 150,
    },
  },
  {
    id: "price_study_pro_monthly",
    name: "Study Pro - Monthly",
    description: "350 credits per month - Most popular for serious students",
    amount: 5900, // $59.00
    currency: "usd",
    recurring: {
      interval: "month",
      interval_count: 1,
    },
    metadata: {
      type: "subscription",
      tier: "study_pro",
      credits: 350,
    },
  },
  {
    id: "price_study_master_monthly",
    name: "Study Master - Monthly",
    description: "750 credits per month - For graduate students & researchers",
    amount: 11900, // $119.00
    currency: "usd",
    recurring: {
      interval: "month",
      interval_count: 1,
    },
    metadata: {
      type: "subscription",
      tier: "study_master",
      credits: 750,
    },
  },
  {
    id: "price_study_elite_monthly",
    name: "Study Elite - Monthly",
    description: "1,500 credits per month - For study groups & institutions",
    amount: 23900, // $239.00
    currency: "usd",
    recurring: {
      interval: "month",
      interval_count: 1,
    },
    metadata: {
      type: "subscription",
      tier: "study_elite",
      credits: 1500,
    },
  },
];

// One-Time Credit Packages - Business Model v4.0
export const CREDIT_PACKAGE_PRICES: StripePriceConfig[] = [
  {
    id: "price_starter_pack",
    name: "Starter Pack",
    description: "50 credits - Perfect for testing features",
    amount: 1500, // $15.00
    currency: "usd",
    metadata: {
      type: "credit_package",
      credits: 50,
      package_id: "starter_pack",
    },
  },
  {
    id: "price_boost_pack",
    name: "Boost Pack",
    description: "150 credits - Great for exam preparation",
    amount: 3900, // $39.00
    currency: "usd",
    metadata: {
      type: "credit_package",
      credits: 150,
      package_id: "boost_pack",
    },
  },
  {
    id: "price_power_pack",
    name: "Power Pack",
    description: "400 credits - For heavy users",
    amount: 8900, // $89.00
    currency: "usd",
    metadata: {
      type: "credit_package",
      credits: 400,
      package_id: "power_pack",
    },
  },
  {
    id: "price_mega_pack",
    name: "Mega Pack",
    description: "1,000 credits - Best value for long-term use",
    amount: 19900, // $199.00
    currency: "usd",
    metadata: {
      type: "credit_package",
      credits: 1000,
      package_id: "mega_pack",
    },
  },
];

// Combined configuration for easy access
export const ALL_STRIPE_PRICES = [
  ...SUBSCRIPTION_PRICES,
  ...CREDIT_PACKAGE_PRICES,
];

// Helper functions
export function getSubscriptionPriceByTier(
  tier: string
): StripePriceConfig | undefined {
  return SUBSCRIPTION_PRICES.find((price) => price.metadata.tier === tier);
}

export function getCreditPackagePriceById(
  packageId: string
): StripePriceConfig | undefined {
  return CREDIT_PACKAGE_PRICES.find(
    (price) => price.metadata.package_id === packageId
  );
}

export function getPriceById(priceId: string): StripePriceConfig | undefined {
  return ALL_STRIPE_PRICES.find((price) => price.id === priceId);
}

// Validate that all price IDs are unique
const allPriceIds = ALL_STRIPE_PRICES.map((price) => price.id);
const uniquePriceIds = new Set(allPriceIds);

if (allPriceIds.length !== uniquePriceIds.size) {
  throw new Error("Duplicate price IDs found in Stripe price configuration");
}

// Environment-specific price mapping
export function getEnvironmentPriceId(
  configPriceId: string,
  environment: "development" | "production"
): string {
  if (environment === "production") {
    // In production, use the actual Stripe price IDs from your Stripe dashboard
    const productionPriceMap: Record<string, string> = {
      price_study_starter_monthly: "price_1RgeXe7lNlVY1bMulc5SRF9z", // Replace with actual production price ID
      price_study_pro_monthly: "price_1RgeYe7lNlVY1bMu8K7nF2xD", // Replace with actual production price ID
      price_study_master_monthly: "price_1RgeZe7lNlVY1bMu3M9pL4vB", // Replace with actual production price ID
      price_study_elite_monthly: "price_1Rgfae7lNlVY1bMu7H6mQ8wC", // Replace with actual production price ID
      price_starter_pack: "price_1Rgfbe7lNlVY1bMu2N5jR9tE", // Replace with actual production price ID
      price_boost_pack: "price_1Rgfce7lNlVY1bMu6P8kS0uF", // Replace with actual production price ID
      price_power_pack: "price_1Rgfde7lNlVY1bMu9Q1lT7vG", // Replace with actual production price ID
      price_mega_pack: "price_1Rgfee7lNlVY1bMu4R2mU8wH", // Replace with actual production price ID
    };

    return productionPriceMap[configPriceId] || configPriceId;
  }

  // In development, use test price IDs (these should be created in your Stripe test environment)
  return configPriceId;
}
