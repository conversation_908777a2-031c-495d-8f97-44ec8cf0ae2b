import Stripe from "stripe";
import { supabase } from "./supabaseService";
import { creditService } from "./creditService";
import { BACKEND_PRICING_TIERS } from "../config/constants";
import { SubscriptionTier } from "../../../shared/types";

class StripeService {
  private stripe: Stripe;
  private webhookSecret: string;

  constructor() {
    // Validate required environment variables
    const secretKey = process.env.STRIPE_SECRET_KEY;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!secretKey) {
      throw new Error("STRIPE_SECRET_KEY environment variable is required");
    }

    if (!webhookSecret) {
      throw new Error("STRIPE_WEBHOOK_SECRET environment variable is required");
    }

    this.webhookSecret = webhookSecret;
    this.stripe = new Stripe(secretKey, {
      apiVersion: "2022-11-15",
      typescript: true,
      maxNetworkRetries: 3,
      timeout: 30000, // 30 second timeout
    });

    console.log("✅ Stripe service initialized successfully");
  }

  // Create or retrieve Stripe customer
  async createOrGetCustomer(
    userId: string,
    email: string,
    name?: string
  ): Promise<string> {
    try {
      // Check if user already has a Stripe customer ID
      const { data: profile } = await supabase
        .from("users")
        .select("stripe_customer_id")
        .eq("id", userId)
        .single();

      if (profile?.stripe_customer_id) {
        return profile.stripe_customer_id;
      }

      // Create new Stripe customer
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          user_id: userId,
        },
      });

      // Update user profile with Stripe customer ID
      await supabase
        .from("users")
        .update({ stripe_customer_id: customer.id })
        .eq("id", userId);

      return customer.id;
    } catch (error) {
      console.error("Error creating/getting Stripe customer:", error);
      throw new Error("Failed to create or retrieve customer");
    }
  }

  // Create subscription with new pricing tiers
  async createSubscription(customerId: string, priceId: string) {
    try {
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: "default_incomplete",
        payment_settings: { save_default_payment_method: "on_subscription" },
        expand: ["latest_invoice.payment_intent"],
        metadata: {
          created_via: "chewyai_app",
        },
      });

      return subscription;
    } catch (error) {
      console.error("Error creating subscription:", error);
      throw new Error("Failed to create subscription");
    }
  }

  // Get customer subscriptions
  async getCustomerSubscriptions(customerId: string) {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: "all",
        expand: ["data.default_payment_method"],
      });

      return subscriptions.data;
    } catch (error) {
      console.error("Error getting customer subscriptions:", error);
      throw new Error("Failed to get subscriptions");
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string) {
    try {
      const subscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          cancel_at_period_end: true,
        }
      );

      return subscription;
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw new Error("Failed to cancel subscription");
    }
  }

  // Validate discount code (updated for new business model v4.0)
  private validateDiscountCode(code: string): {
    isValid: boolean;
    discountPercent: number;
    stripeCouponId?: string;
  } {
    // Hidden discount code for business model v4.0
    const discountCodes: Record<
      string,
      { percent: number; stripeCouponId?: string }
    > = {
      BALLS: { percent: 100, stripeCouponId: "xgJwhS5B" }, // 100% off Stripe coupon ($1000 off)
    };

    const upperCode = code.toUpperCase();
    if (upperCode in discountCodes) {
      const discount = discountCodes[upperCode];
      return {
        isValid: true,
        discountPercent: discount.percent,
        stripeCouponId: discount.stripeCouponId,
      };
    }

    return { isValid: false, discountPercent: 0 };
  }

  // Create payment intent for credit purchase
  async createCreditPurchaseIntent(
    customerId: string,
    amount: number,
    credits: number,
    discountCode?: string
  ) {
    try {
      let finalAmount = amount;
      let discountApplied = false;
      let discountPercent = 0;

      // Apply discount code if provided
      if (discountCode) {
        const discount = this.validateDiscountCode(discountCode);
        if (discount.isValid) {
          discountPercent = discount.discountPercent;
          finalAmount = amount * (1 - discountPercent / 100);
          discountApplied = true;
        }
      }

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(finalAmount * 100), // Convert to cents and round
        currency: "usd",
        customer: customerId,
        metadata: {
          type: "credit_purchase",
          credits: credits.toString(),
          original_amount: amount.toString(),
          discount_code: discountCode || "",
          discount_applied: discountApplied.toString(),
          discount_percent: discountPercent.toString(),
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return paymentIntent;
    } catch (error) {
      console.error("Error creating payment intent:", error);
      throw new Error("Failed to create payment intent");
    }
  }

  // Get customer invoices
  async getCustomerInvoices(customerId: string, limit: number = 10) {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit,
        expand: ["data.payment_intent"],
      });

      return invoices.data;
    } catch (error) {
      console.error("Error getting customer invoices:", error);
      throw new Error("Failed to get invoices");
    }
  }

  // Get customer payment methods
  async getCustomerPaymentMethods(customerId: string) {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: "card",
      });

      return paymentMethods.data;
    } catch (error) {
      console.error("Error getting payment methods:", error);
      throw new Error("Failed to get payment methods");
    }
  }

  // Handle webhook events with enhanced subscription management
  async handleWebhook(body: Buffer | string, signature: string) {
    let event: Stripe.Event;

    try {
      // Construct and verify the webhook event
      event = this.stripe.webhooks.constructEvent(
        body,
        signature,
        this.webhookSecret
      );
      console.log(`✅ Webhook verified: ${event.type} (${event.id})`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("❌ Webhook signature verification failed:", errorMessage);
      throw new Error(`Webhook signature verification failed: ${errorMessage}`);
    }

    try {
      switch (event.type) {
        case "checkout.session.completed":
          await this.handleCheckoutSessionCompleted(
            event.data.object as Stripe.Checkout.Session
          );
          break;
        case "payment_intent.succeeded":
          await this.handlePaymentSuccess(
            event.data.object as Stripe.PaymentIntent
          );
          break;
        case "invoice.payment_succeeded":
          await this.handleInvoicePaymentSuccess(
            event.data.object as Stripe.Invoice
          );
          break;
        case "customer.subscription.created":
          await this.handleSubscriptionCreated(
            event.data.object as Stripe.Subscription
          );
          break;
        case "customer.subscription.updated":
          await this.handleSubscriptionUpdate(
            event.data.object as Stripe.Subscription
          );
          break;
        case "customer.subscription.deleted":
          await this.handleSubscriptionCancellation(
            event.data.object as Stripe.Subscription
          );
          break;
        case "invoice.payment_failed":
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        default:
          // Log unhandled event types for monitoring
          console.log(`ℹ️ Unhandled webhook event type: ${event.type}`);
          break;
      }

      console.log(`✅ Webhook processed successfully: ${event.type}`);
      return { received: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(
        `❌ Webhook processing error for ${event.type}:`,
        errorMessage
      );
      throw new Error(`Webhook processing failed: ${errorMessage}`);
    }
  }

  // Handle successful payment (updated for business model v4.0)
  private async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    if (
      paymentIntent.metadata.type === "credit_purchase" ||
      paymentIntent.metadata.type === "credit_package"
    ) {
      const credits = parseInt(paymentIntent.metadata.credits);
      const customerId = paymentIntent.customer as string;

      // Get user ID from customer
      const { data: user } = await supabase
        .from("users")
        .select("id")
        .eq("stripe_customer_id", customerId)
        .single();

      if (user) {
        await creditService.addCredits(
          user.id,
          credits,
          "stripe_purchase",
          paymentIntent.id
        );

        // Log the credit purchase
        await supabase.from("credit_purchases").insert({
          user_id: user.id,
          credits_purchased: credits,
          amount_paid: paymentIntent.amount / 100, // Convert from cents
          stripe_payment_intent_id: paymentIntent.id,
          created_at: new Date().toISOString(),
        });
      }
    }
  }

  // Handle successful checkout session completion (new for business model v4.0)
  private async handleCheckoutSessionCompleted(
    session: Stripe.Checkout.Session
  ) {
    const userId = session.metadata?.user_id;

    if (!userId) {
      console.error("No user_id in checkout session metadata");
      return;
    }

    // Handle credit package purchases
    if (session.metadata?.type === "credit_package") {
      const packageId = session.metadata.package_id;

      // Map package ID to credits
      const packageCredits: Record<string, number> = {
        starter_pack: 50,
        boost_pack: 150,
        power_pack: 400,
        mega_pack: 1000,
      };

      const credits = packageCredits[packageId];
      if (credits) {
        await creditService.addCredits(
          userId,
          credits,
          "stripe_checkout_credit_package",
          session.id
        );

        // Log the credit purchase
        await supabase.from("credit_purchases").insert({
          user_id: userId,
          credits_purchased: credits,
          amount_paid: (session.amount_total || 0) / 100, // Convert from cents
          stripe_payment_intent_id: session.payment_intent as string,
          created_at: new Date().toISOString(),
        });
      }
    }
  }

  // Handle subscription creation
  private async handleSubscriptionCreated(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    const tier = this.getSubscriptionTierFromPrice(
      subscription.items.data[0].price.id
    );

    // Get user ID from customer
    const { data: user } = await supabase
      .from("users")
      .select("id")
      .eq("stripe_customer_id", customerId)
      .single();

    if (user && tier) {
      // Update user subscription
      await supabase
        .from("users")
        .update({
          subscription_tier: tier,
          stripe_subscription_id: subscription.id,
          subscription_expires_at: new Date(
            subscription.current_period_end * 1000
          ).toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      // Add initial subscription credits
      const tierConfig = BACKEND_PRICING_TIERS.find((t) => t.id === tier);
      if (tierConfig && tierConfig.credits > 0) {
        await creditService.resetMonthlyCredits(user.id, tierConfig.credits);
      }
    }
  }

  // Handle successful invoice payment (subscriptions)
  private async handleInvoicePaymentSuccess(invoice: Stripe.Invoice) {
    const customerId = invoice.customer as string;
    const subscriptionId = invoice.subscription as string;

    if (!subscriptionId) return; // Not a subscription invoice

    // Get subscription details
    const subscription = await this.stripe.subscriptions.retrieve(
      subscriptionId
    );
    const tier = this.getSubscriptionTierFromPrice(
      subscription.items.data[0].price.id
    );

    // Get user ID from customer
    const { data: user } = await supabase
      .from("users")
      .select("id")
      .eq("stripe_customer_id", customerId)
      .single();

    if (user && tier) {
      // Reset monthly credits for subscription renewal
      const tierConfig = BACKEND_PRICING_TIERS.find((t) => t.id === tier);
      if (tierConfig) {
        await creditService.resetMonthlyCredits(user.id, tierConfig.credits);
      }

      // Update subscription expiry
      await supabase
        .from("users")
        .update({
          subscription_expires_at: new Date(
            subscription.current_period_end * 1000
          ).toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);
    }
  }

  // Handle subscription updates
  private async handleSubscriptionUpdate(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    const tier = this.getSubscriptionTierFromPrice(
      subscription.items.data[0].price.id
    );

    // Update user subscription status in database
    const updateData: any = {
      stripe_subscription_id: subscription.id,
      subscription_expires_at: new Date(
        subscription.current_period_end * 1000
      ).toISOString(),
      updated_at: new Date().toISOString(),
    };

    if (tier) {
      updateData.subscription_tier = tier;
    }

    await supabase
      .from("users")
      .update(updateData)
      .eq("stripe_customer_id", customerId);
  }

  // Handle subscription cancellation
  private async handleSubscriptionCancellation(
    subscription: Stripe.Subscription
  ) {
    const customerId = subscription.customer as string;

    // Update user to Free tier but keep credits until period end
    await supabase
      .from("users")
      .update({
        subscription_tier: "Free",
        stripe_subscription_id: null,
        // Keep subscription_expires_at to allow access until period end
        updated_at: new Date().toISOString(),
      })
      .eq("stripe_customer_id", customerId);
  }

  // Handle payment failures
  private async handlePaymentFailed(invoice: Stripe.Invoice) {
    const customerId = invoice.customer as string;

    // Get user and send notification (could implement email service here)
    const { data: user } = await supabase
      .from("users")
      .select("id, email")
      .eq("stripe_customer_id", customerId)
      .single();

    if (user) {
      // Log payment failure for monitoring
      // Note: Email notification service should be implemented for production
    }
  }

  // Get subscription tier from Stripe price ID (updated for business model v4.0)
  private getSubscriptionTierFromPrice(
    priceId: string
  ): SubscriptionTier | null {
    const tierMapping: Record<string, SubscriptionTier> = {
      // Updated to use recurring prices for subscriptions
      price_1RgeXe7lNlVY1bMulc5SRF9z: "Study Starter", // $29/month, 150 credits (recurring)
      price_1RgeXe7lNlVY1bMuWT9HTvnr: "Study Pro", // $59/month, 350 credits (recurring)
      price_1RgeXe7lNlVY1bMuPU6N3CYa: "Study Master", // $119/month, 750 credits (recurring)
      price_1RgeXe7lNlVY1bMuzHc8Fxmg: "Study Elite", // $239/month, 1,500 credits (recurring)
      // Legacy one-time prices (kept for backward compatibility)
      price_1Rgdmb7lNlVY1bMuYP0ygGjS: "Study Starter", // $29/month, 150 credits (one-time)
      price_1Rgdmh7lNlVY1bMutZYduM9a: "Study Pro", // $59/month, 350 credits (one-time)
      price_1Rgdml7lNlVY1bMum4EKDi1s: "Study Master", // $119/month, 750 credits (one-time)
      price_1Rgdmp7lNlVY1bMuI2eXa8z8: "Study Elite", // $239/month, 1,500 credits (one-time)
    };

    return tierMapping[priceId] || null;
  }

  // Get Stripe prices for products
  async getPrices() {
    try {
      const prices = await this.stripe.prices.list({
        active: true,
        expand: ["data.product"],
      });

      return prices.data;
    } catch (error) {
      console.error("Error getting prices:", error);
      throw new Error("Failed to get prices");
    }
  }

  // Get subscription tier from plan ID (updated for business model v4.0)
  private getPriceIdFromPlanId(planId: string): string {
    const planMapping: Record<string, string> = {
      // Updated to use recurring prices for subscriptions
      study_starter: "price_1RgeXe7lNlVY1bMulc5SRF9z", // $29/month, 150 credits (recurring)
      study_pro: "price_1RgeXe7lNlVY1bMuWT9HTvnr", // $59/month, 350 credits (recurring)
      study_master: "price_1RgeXe7lNlVY1bMuPU6N3CYa", // $119/month, 750 credits (recurring)
      study_elite: "price_1RgeXe7lNlVY1bMuzHc8Fxmg", // $239/month, 1,500 credits (recurring)
    };

    const priceId = planMapping[planId];
    if (!priceId) {
      throw new Error(`Invalid plan ID: ${planId}`);
    }
    return priceId;
  }

  // Get credit package price ID from package ID (new for business model v4.0)
  private getCreditPackagePriceId(packageId: string): string {
    const packageMapping: Record<string, string> = {
      starter_pack: "price_1Rge2c7lNlVY1bMu8AobMuTT", // $15, 50 credits
      boost_pack: "price_1Rge2m7lNlVY1bMuJywdctDG", // $39, 150 credits
      power_pack: "price_1Rge2w7lNlVY1bMufspAzvdn", // $89, 400 credits
      mega_pack: "price_1Rge357lNlVY1bMuuzBtmOHM", // $199, 1,000 credits
    };

    const priceId = packageMapping[packageId];
    if (!priceId) {
      throw new Error(`Invalid credit package ID: ${packageId}`);
    }
    return priceId;
  }

  // Validate coupon code for frontend use
  async validateCoupon(code: string): Promise<{
    valid: boolean;
    code?: string;
    discount?: number;
    type?: "percent" | "amount";
  }> {
    try {
      // Check if it's one of our predefined coupons first
      const hardcodedDiscount = this.validateDiscountCode(code);
      if (hardcodedDiscount.isValid) {
        return {
          valid: true,
          code: hardcodedDiscount.stripeCouponId || code.toUpperCase(),
          discount: hardcodedDiscount.discountPercent,
          type: "percent",
        };
      }

      // Try to validate with Stripe
      try {
        const coupon = await this.stripe.coupons.retrieve(code);
        if (coupon.valid) {
          return {
            valid: true,
            code: coupon.id,
            discount:
              coupon.percent_off ||
              (coupon.amount_off ? coupon.amount_off / 100 : 0),
            type: coupon.percent_off ? "percent" : "amount",
          };
        }
      } catch (stripeError) {
        // Coupon not found in Stripe, that's okay
      }

      return { valid: false };
    } catch (error) {
      return { valid: false };
    }
  }

  // Create checkout session for subscription with enhanced options
  async createCheckoutSession(options: {
    customerId: string;
    planId: string;
    coupon?: string | null;
    successUrl: string;
    cancelUrl: string;
    userId: string;
  }) {
    try {
      const { customerId, planId, coupon, successUrl, cancelUrl, userId } =
        options;

      // Input validation
      if (!customerId?.trim()) {
        throw new Error("Customer ID is required");
      }
      if (!planId?.trim()) {
        throw new Error("Plan ID is required");
      }
      if (!successUrl?.trim() || !successUrl.startsWith("http")) {
        throw new Error("Valid success URL is required");
      }
      if (!cancelUrl?.trim() || !cancelUrl.startsWith("http")) {
        throw new Error("Valid cancel URL is required");
      }
      if (!userId?.trim()) {
        throw new Error("User ID is required");
      }

      console.log("Creating checkout session with options:", {
        customerId,
        planId,
        coupon,
        successUrl,
        cancelUrl,
        userId,
      });

      // Verify customer exists (but don't fail if there's an issue)
      let customerValid = false;
      try {
        const customer = await this.stripe.customers.retrieve(customerId);
        console.log("Customer verified:", customer.id);
        customerValid = true;
      } catch (customerError) {
        console.error("Customer verification failed:", customerError);
        console.log("Proceeding without customer to debug checkout issue");
      }

      const priceId = this.getPriceIdFromPlanId(planId);
      console.log("Using price ID:", priceId);

      // First, verify the price is recurring for subscription mode
      const price = await this.stripe.prices.retrieve(priceId);
      if (!price.recurring) {
        throw new Error(
          `Price ${priceId} is not configured for recurring billing. Please contact support.`
        );
      }

      console.log("Price verified as recurring:", price.id);

      // Ensure URLs are properly formatted
      const formattedSuccessUrl = successUrl.includes("?")
        ? `${successUrl}&session_id={CHECKOUT_SESSION_ID}`
        : `${successUrl}?session_id={CHECKOUT_SESSION_ID}`;

      const sessionData: Stripe.Checkout.SessionCreateParams = {
        // Only include customer if verification passed
        ...(customerValid && { customer: customerId }),
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: formattedSuccessUrl,
        cancel_url: cancelUrl,
        // Simplify configuration to avoid potential issues
        billing_address_collection: "required",
        automatic_tax: {
          enabled: false,
        },
        // Remove allow_promotion_codes temporarily to simplify
        metadata: {
          created_via: "chewyai_app",
          user_id: userId,
          plan_id: planId,
          customer_id: customerId,
        },
      };

      // Temporarily disable coupon handling to simplify checkout
      if (coupon) {
        console.log(
          "Coupon provided but temporarily disabled for debugging:",
          coupon
        );
      }

      console.log("Creating Stripe checkout session with data:", sessionData);
      console.log(
        "Using Stripe API key type:",
        process.env.STRIPE_SECRET_KEY?.startsWith("sk_live_") ? "LIVE" : "TEST"
      );

      const session = await this.stripe.checkout.sessions.create(sessionData);
      console.log("Checkout session created successfully:", session.id);
      console.log("Session URL:", session.url);

      return session;
    } catch (error) {
      console.error("Error creating enhanced checkout session:", error);
      throw new Error("Failed to create checkout session");
    }
  }

  // Create checkout session for credit package purchase (new for business model v4.0)
  async createCreditPackageCheckoutSession(options: {
    customerId: string;
    packageId: string;
    coupon?: string | null;
    successUrl: string;
    cancelUrl: string;
    userId: string;
  }) {
    try {
      const { customerId, packageId, coupon, successUrl, cancelUrl, userId } =
        options;

      // Input validation
      if (!customerId?.trim()) {
        throw new Error("Customer ID is required");
      }
      if (!packageId?.trim()) {
        throw new Error("Package ID is required");
      }
      if (!successUrl?.trim() || !successUrl.startsWith("http")) {
        throw new Error("Valid success URL is required");
      }
      if (!cancelUrl?.trim() || !cancelUrl.startsWith("http")) {
        throw new Error("Valid cancel URL is required");
      }
      if (!userId?.trim()) {
        throw new Error("User ID is required");
      }

      const priceId = this.getCreditPackagePriceId(packageId);

      const sessionData: Stripe.Checkout.SessionCreateParams = {
        customer: customerId,
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "payment",
        success_url: successUrl,
        cancel_url: cancelUrl,
        allow_promotion_codes: true,
        billing_address_collection: "auto",
        metadata: {
          created_via: "chewyai_app",
          user_id: userId,
          package_id: packageId,
          type: "credit_package",
        },
      };

      // Add coupon if provided and valid
      if (coupon) {
        const couponValidation = await this.validateCoupon(coupon);
        if (couponValidation.valid) {
          sessionData.discounts = [
            {
              coupon: couponValidation.code!,
            },
          ];
        }
      }

      const session = await this.stripe.checkout.sessions.create(sessionData);
      return session;
    } catch (error) {
      console.error("Error creating credit package checkout session:", error);
      throw new Error("Failed to create credit package checkout session");
    }
  }

  // Create checkout session for subscription (legacy method for backward compatibility)
  async createCheckoutSessionLegacy(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      const session = await this.stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: successUrl,
        cancel_url: cancelUrl,
        allow_promotion_codes: true,
        billing_address_collection: "auto",
        metadata: {
          created_via: "chewyai_app",
        },
      });

      return session;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      throw new Error("Failed to create checkout session");
    }
  }

  // Additional Stripe methods for billing management
  async getCustomer(customerId: string) {
    return await this.stripe.customers.retrieve(customerId);
  }

  async getUpcomingInvoice(customerId: string) {
    return await this.stripe.invoices.retrieveUpcoming({
      customer: customerId,
    });
  }

  async getInvoice(invoiceId: string) {
    return await this.stripe.invoices.retrieve(invoiceId);
  }

  async getPaymentMethod(paymentMethodId: string) {
    return await this.stripe.paymentMethods.retrieve(paymentMethodId);
  }

  async setDefaultPaymentMethod(customerId: string, paymentMethodId: string) {
    return await this.stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });
  }

  async attachPaymentMethod(paymentMethodId: string, customerId: string) {
    return await this.stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    });
  }

  async detachPaymentMethod(paymentMethodId: string) {
    return await this.stripe.paymentMethods.detach(paymentMethodId);
  }

  async reactivateSubscription(subscriptionId: string) {
    return await this.stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });
  }

  // Update subscription to new tier
  async updateSubscription(subscriptionId: string, newPriceId: string) {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(
        subscriptionId
      );

      const updatedSubscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          items: [
            {
              id: subscription.items.data[0].id,
              price: newPriceId,
            },
          ],
          proration_behavior: "create_prorations",
        }
      );

      return updatedSubscription;
    } catch (error) {
      console.error("Error updating subscription:", error);
      throw new Error("Failed to update subscription");
    }
  }

  // Create test checkout session without customer (for debugging)
  async createTestCheckoutSession(
    priceId: string,
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: successUrl,
        cancel_url: cancelUrl,
        automatic_tax: {
          enabled: false,
        },
        metadata: {
          created_via: "chewyai_app_test",
        },
      });

      return session;
    } catch (error) {
      console.error("Error creating test checkout session:", error);
      throw new Error("Failed to create test checkout session");
    }
  }

  // Get Stripe account status for diagnostics
  async getAccountStatus() {
    try {
      const account = await this.stripe.accounts.retrieve();

      return {
        id: account.id,
        country: account.country,
        default_currency: account.default_currency,
        details_submitted: account.details_submitted,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        capabilities: account.capabilities,
        requirements: account.requirements,
      };
    } catch (error) {
      console.error("Error retrieving account status:", error);
      throw new Error("Failed to retrieve account status");
    }
  }

  // Create recurring prices for subscription products (one-time setup)
  async createRecurringPrices() {
    try {
      const subscriptionProducts = [
        {
          productId: "prod_SbrVDoTGguDVIg",
          amount: 2900,
          name: "Study Starter",
        }, // $29/month
        { productId: "prod_SbrVN6t5TdSQt0", amount: 5900, name: "Study Pro" }, // $59/month
        {
          productId: "prod_SbrVm7lFTjTDQB",
          amount: 11900,
          name: "Study Master",
        }, // $119/month
        {
          productId: "prod_SbrVkWvwzVHy2g",
          amount: 23900,
          name: "Study Elite",
        }, // $239/month
      ];

      const createdPrices = [];

      for (const product of subscriptionProducts) {
        const price = await this.stripe.prices.create({
          product: product.productId,
          unit_amount: product.amount,
          currency: "usd",
          recurring: {
            interval: "month",
          },
          nickname: `${product.name} Monthly Subscription`,
        });

        createdPrices.push({
          name: product.name,
          priceId: price.id,
          amount: product.amount,
        });
      }

      return createdPrices;
    } catch (error) {
      throw new Error(`Failed to create recurring prices: ${error}`);
    }
  }
}

export const stripeService = new StripeService();
