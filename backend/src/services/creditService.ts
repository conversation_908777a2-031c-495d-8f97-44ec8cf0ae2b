import { supabase } from "./supabaseService";
import {
  CreditTransaction,
  AIOperationCost,
  SubscriptionTier,
} from "../../../shared/types";
import { SUBSCRIPTION_LIMITS, CREDIT_COSTS } from "../../../shared/constants";
import { BACKEND_PRICING_TIERS } from "../config/constants";

export class CreditService {
  // Get current AI operation cost (updated for new pricing)
  async getOperationCost(operationType: string): Promise<number> {
    // Use new constants-based pricing
    switch (operationType) {
      case "flashcard_generation":
        return CREDIT_COSTS.FLASHCARD_GENERATION;
      case "quiz_generation":
        return CREDIT_COSTS.QUIZ_GENERATION;
      case "flex_generation":
        return CREDIT_COSTS.FLEX_GENERATION;
      case "document_processing":
        return CREDIT_COSTS.DOCUMENT_PROCESSING;
      default:
        // Fallback to database for legacy operations
        const { data, error } = await supabase
          .from("ai_operation_costs")
          .select("credits_required")
          .eq("operation_type", operationType)
          .eq("is_active", true)
          .single();

        if (error || !data) {
          throw new Error(`Invalid operation type: ${operationType}`);
        }

        return data.credits_required;
    }
  }

  // Get operations per credit for an operation type
  async getOperationsPerCredit(operationType: string): Promise<number> {
    // Use new constants-based pricing
    switch (operationType) {
      case "flashcard_generation":
        return 5; // 5 flashcards per credit
      case "quiz_generation":
        return 5; // 5 quiz questions per credit
      case "flex_generation":
        return 5; // 5 flex items per credit
      case "document_processing":
        return 1; // Free document processing
      default:
        // Fallback to database for legacy operations
        const { data, error } = await supabase
          .from("ai_operation_costs")
          .select("operations_per_credit")
          .eq("operation_type", operationType)
          .eq("is_active", true)
          .single();

        if (error || !data) {
          throw new Error(`Invalid operation type: ${operationType}`);
        }

        return data.operations_per_credit;
    }
  }

  // Calculate effective credit cost for a given number of operations
  // This accounts for the fact that credits are only deducted when the counter reaches operations_per_credit
  async getEffectiveCreditCost(
    operationType: string,
    operationCount: number = 1
  ): Promise<number> {
    const operationsPerCredit = await this.getOperationsPerCredit(
      operationType
    );

    // Calculate how many full credits would be consumed
    const fullCredits = Math.floor(operationCount / operationsPerCredit);

    // For validation purposes, if there are remaining operations, we need at least 1 more credit
    const remainingOperations = operationCount % operationsPerCredit;
    const additionalCredit = remainingOperations > 0 ? 1 : 0;

    return fullCredits + additionalCredit;
  }

  // Get all active operation costs
  async getAllOperationCosts(): Promise<AIOperationCost[]> {
    // Return new constants-based costs
    return [
      {
        operation_type: "flashcard_generation",
        credits_required: CREDIT_COSTS.FLASHCARD_GENERATION,
        operations_per_credit: 5,
        is_active: true,
      },
      {
        operation_type: "quiz_generation",
        credits_required: CREDIT_COSTS.QUIZ_GENERATION,
        operations_per_credit: 5,
        is_active: true,
      },
      {
        operation_type: "flex_generation",
        credits_required: CREDIT_COSTS.FLEX_GENERATION,
        operations_per_credit: 5,
        is_active: true,
      },
      {
        operation_type: "document_processing",
        credits_required: CREDIT_COSTS.DOCUMENT_PROCESSING,
        operations_per_credit: 1,
        is_active: true,
      },
    ];
  }

  // Check if user is on freemium tier and within limits
  async checkFreemiumLimits(
    userId: string,
    operationType: string
  ): Promise<{
    isFreemium: boolean;
    withinLimits: boolean;
    currentUsage: number;
    monthlyLimit: number;
  }> {
    // Get user's subscription tier
    const { data: user } = await supabase
      .from("users")
      .select("subscription_tier")
      .eq("id", userId)
      .single();

    if (!user || user.subscription_tier !== "Free") {
      return {
        isFreemium: false,
        withinLimits: true,
        currentUsage: 0,
        monthlyLimit: 0,
      };
    }

    // Get current month's usage
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { data: transactions } = await supabase
      .from("credit_transactions")
      .select("credits_used")
      .eq("user_id", userId)
      .eq("operation_type", operationType)
      .gte("created_at", startOfMonth.toISOString());

    const currentUsage =
      transactions?.reduce((sum, t) => sum + t.credits_used, 0) || 0;
    const monthlyLimit = SUBSCRIPTION_LIMITS.Free.creditsPerMonth;

    return {
      isFreemium: true,
      withinLimits: currentUsage < monthlyLimit,
      currentUsage,
      monthlyLimit,
    };
  }

  // Reset monthly credits for subscription users
  async resetMonthlyCredits(
    userId: string,
    newCreditAmount: number
  ): Promise<void> {
    try {
      // Update user's credit balance
      const { error } = await supabase
        .from("users")
        .update({
          credits_remaining: newCreditAmount,
          updated_at: new Date().toISOString(),
        })
        .eq("id", userId);

      if (error) {
        throw new Error(`Failed to reset monthly credits: ${error.message}`);
      }

      // Log the credit reset transaction
      await supabase.from("credit_transactions").insert({
        user_id: userId,
        credits_used: -newCreditAmount, // Negative for addition
        operation_type: "monthly_reset",
        description: `Monthly credit reset: ${newCreditAmount} credits`,
        metadata: { reset_date: new Date().toISOString() },
        created_at: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Error resetting monthly credits:", error);
      throw error;
    }
  }

  // Check if user needs monthly credit reset (for cron jobs)
  async checkAndResetMonthlyCredits(): Promise<void> {
    try {
      // Get all users with active subscriptions
      const { data: users } = await supabase
        .from("users")
        .select("id, subscription_tier, subscription_expires_at")
        .neq("subscription_tier", "Free")
        .not("subscription_expires_at", "is", null);

      if (!users) return;

      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      for (const user of users) {
        // Check if subscription is still active
        const expiresAt = new Date(user.subscription_expires_at);
        if (expiresAt < now) continue;

        // Check if user has already been reset this month
        const { data: recentReset } = await supabase
          .from("credit_transactions")
          .select("id")
          .eq("user_id", user.id)
          .eq("operation_type", "monthly_reset")
          .gte("created_at", startOfMonth.toISOString())
          .limit(1);

        if (recentReset && recentReset.length > 0) continue;

        // Reset credits for this user
        const tierConfig = BACKEND_PRICING_TIERS.find(
          (t) => t.id === user.subscription_tier
        );
        if (tierConfig) {
          await this.resetMonthlyCredits(user.id, tierConfig.credits);
        }
      }
    } catch (error) {
      console.error("Error in monthly credit reset job:", error);
    }
  }

  // Enhanced credit deduction with freemium support
  async deductCredits(
    userId: string,
    operationType: string,
    metadata?: any,
    studySetId?: string
  ): Promise<{ success: boolean; remainingCredits: number; message: string }> {
    // Check freemium limits first
    const freemiumCheck = await this.checkFreemiumLimits(userId, operationType);
    if (freemiumCheck.isFreemium && !freemiumCheck.withinLimits) {
      return {
        success: false,
        remainingCredits: await this.getUserCredits(userId),
        message: "Monthly credit limit reached. Please upgrade to continue.",
      };
    }

    // Use the existing use_credits function which automatically determines cost
    const { data, error } = await supabase.rpc("use_credits", {
      p_user_id: userId,
      p_operation_type: operationType,
      p_description: `AI generation: ${operationType}`,
      p_study_set_id: studySetId || null,
      p_metadata: metadata || {},
      p_ip_address: null,
      p_user_agent: null,
    });

    if (error) {
      throw new CreditError("Credit deduction failed", error);
    }

    // The use_credits function returns a boolean indicating success
    // We need to get the remaining credits separately if the operation succeeded
    if (data === true) {
      const remainingCredits = await this.getUserCredits(userId);
      return {
        success: true,
        remainingCredits,
        message: "Credits deducted successfully",
      };
    } else {
      return {
        success: false,
        remainingCredits: await this.getUserCredits(userId),
        message: "Insufficient credits",
      };
    }
  }

  // Add credits for purchases/subscriptions
  async addCredits(
    userId: string,
    creditsToAdd: number,
    source: string,
    referenceId?: string
  ): Promise<{ success: boolean; newBalance: number; message: string }> {
    const { data, error } = await supabase.rpc("add_credits", {
      p_user_id: userId,
      p_credits_to_add: creditsToAdd,
      p_operation_type: source,
      p_description: `Credit addition: ${source}${
        referenceId ? ` (${referenceId})` : ""
      }`,
      p_metadata: referenceId ? { reference_id: referenceId } : {},
      p_ip_address: null,
      p_user_agent: null,
    });

    if (error) {
      throw new CreditError("Credit addition failed", error);
    }

    // The add_credits function returns a boolean indicating success
    // We need to get the new balance separately if the operation succeeded
    if (data === true) {
      const newBalance = await this.getUserCredits(userId);
      return {
        success: true,
        newBalance,
        message: "Credits added successfully",
      };
    } else {
      return {
        success: false,
        newBalance: await this.getUserCredits(userId),
        message: "Failed to add credits",
      };
    }
  }

  // Get user's current credit balance
  async getUserCredits(userId: string): Promise<number> {
    const { data, error } = await supabase
      .from("users")
      .select("credits_remaining")
      .eq("id", userId)
      .single();

    if (error || !data) {
      throw new Error("User not found");
    }

    return data.credits_remaining;
  }

  // Get user's subscription info for credit management
  async getUserSubscriptionInfo(userId: string): Promise<{
    tier: SubscriptionTier;
    creditsRemaining: number;
    monthlyAllowance: number;
    nextResetDate: string;
  }> {
    const { data: user, error } = await supabase
      .from("users")
      .select("subscription_tier, credits_remaining, subscription_expires_at")
      .eq("id", userId)
      .single();

    if (error || !user) {
      throw new Error("User not found");
    }

    const tierConfig = BACKEND_PRICING_TIERS.find(
      (t) => t.id === user.subscription_tier
    );
    const monthlyAllowance = tierConfig?.credits || 0;

    // Calculate next reset date (1st of next month)
    const nextReset = new Date();
    nextReset.setMonth(nextReset.getMonth() + 1);
    nextReset.setDate(1);
    nextReset.setHours(0, 0, 0, 0);

    return {
      tier: user.subscription_tier,
      creditsRemaining: user.credits_remaining,
      monthlyAllowance,
      nextResetDate: nextReset.toISOString(),
    };
  }

  // Validate credit operation before execution
  async validateCreditOperation(
    userId: string,
    operationType: string,
    operationCount: number = 1
  ): Promise<boolean> {
    const [userCredits, requiredCredits] = await Promise.all([
      this.getUserCredits(userId),
      this.getEffectiveCreditCost(operationType, operationCount),
    ]);

    return userCredits >= requiredCredits;
  }

  // Get user's credit transaction history
  async getCreditHistory(
    userId: string,
    limit = 50,
    offset = 0
  ): Promise<{ transactions: CreditTransaction[]; total: number }> {
    // Get transactions
    const { data: transactions, error: transError } = await supabase
      .from("credit_transactions")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (transError) {
      throw new Error(`Failed to get credit history: ${transError.message}`);
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from("credit_transactions")
      .select("*", { count: "exact", head: true })
      .eq("user_id", userId);

    if (countError) {
      throw new Error(
        `Failed to get credit history count: ${countError.message}`
      );
    }

    return {
      transactions: transactions || [],
      total: count || 0,
    };
  }

  // Get credit usage statistics
  async getCreditStats(
    userId: string,
    days = 30
  ): Promise<{
    totalUsed: number;
    totalAdded: number;
    operationBreakdown: {
      operation_type: string;
      credits_used: number;
      count: number;
    }[];
  }> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from("credit_transactions")
      .select("credits_used, operation_type")
      .eq("user_id", userId)
      .gte("created_at", startDate.toISOString());

    if (error) {
      throw new Error(`Failed to get credit stats: ${error.message}`);
    }

    const transactions = data || [];

    const totalUsed = transactions
      .filter((t) => t.credits_used > 0)
      .reduce((sum, t) => sum + t.credits_used, 0);

    const totalAdded = Math.abs(
      transactions
        .filter((t) => t.credits_used < 0)
        .reduce((sum, t) => sum + t.credits_used, 0)
    );

    // Group by operation type
    const operationMap = new Map<
      string,
      { credits_used: number; count: number }
    >();

    transactions
      .filter((t) => t.credits_used > 0)
      .forEach((t) => {
        const existing = operationMap.get(t.operation_type) || {
          credits_used: 0,
          count: 0,
        };
        operationMap.set(t.operation_type, {
          credits_used: existing.credits_used + t.credits_used,
          count: existing.count + 1,
        });
      });

    const operationBreakdown = Array.from(operationMap.entries()).map(
      ([operation_type, stats]) => ({
        operation_type,
        ...stats,
      })
    );

    return {
      totalUsed,
      totalAdded,
      operationBreakdown,
    };
  }

  // Check if user has sufficient credits for operation
  async checkSufficientCredits(
    userId: string,
    operationType: string,
    operationCount: number = 1
  ): Promise<{
    sufficient: boolean;
    currentCredits: number;
    requiredCredits: number;
    shortfall: number;
  }> {
    const [currentCredits, requiredCredits] = await Promise.all([
      this.getUserCredits(userId),
      this.getEffectiveCreditCost(operationType, operationCount),
    ]);

    const sufficient = currentCredits >= requiredCredits;
    const shortfall = sufficient ? 0 : requiredCredits - currentCredits;

    return {
      sufficient,
      currentCredits,
      requiredCredits,
      shortfall,
    };
  }
}

// Custom error class for credit operations
export class CreditError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = "CreditError";
  }
}

export const creditService = new CreditService();
