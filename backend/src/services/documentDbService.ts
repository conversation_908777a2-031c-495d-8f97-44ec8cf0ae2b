import { supabase } from "./supabaseService";
import {
  DocumentMetadata,
  DocumentWithContent,
  DocumentFileType,
} from "../../../shared/types";

export class DocumentDbService {
  async createDocument(documentData: {
    user_id: string;
    filename: string;
    file_type: DocumentFileType;
    file_size: number;
    content_text: string;
    supabase_storage_path: string;
    is_processed: boolean;
    processing_error?: string;
    page_count?: number;
  }): Promise<DocumentMetadata> {
    const { data, error } = await supabase
      .from("documents")
      .insert(documentData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create document: ${error.message}`);
    }

    return data;
  }

  async getUserDocuments(
    userId: string,
    limit = 50,
    offset = 0
  ): Promise<DocumentMetadata[]> {
    const { data, error } = await supabase
      .from("documents")
      .select(
        "id, user_id, filename, file_type, file_size, supabase_storage_path, uploaded_at, is_processed, processing_error, page_count"
      )
      .eq("user_id", userId)
      .order("uploaded_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Failed to get documents: ${error.message}`);
    }

    return (data || []) as DocumentMetadata[];
  }

  async getDocumentById(
    documentId: string,
    userId: string
  ): Promise<DocumentWithContent | null> {
    const { data, error } = await supabase
      .from("documents")
      .select("*")
      .eq("id", documentId)
      .eq("user_id", userId)
      .single();

    if (error) {
      if (error.code === "PGRST116") return null; // Not found
      throw new Error(`Failed to get document: ${error.message}`);
    }

    return data;
  }

  async getDocumentsByIds(
    documentIds: string[],
    userId: string
  ): Promise<DocumentWithContent[]> {
    const { data, error } = await supabase
      .from("documents")
      .select("*")
      .in("id", documentIds)
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Failed to get documents: ${error.message}`);
    }

    return data || [];
  }

  async updateDocument(
    documentId: string,
    userId: string,
    updates: Partial<DocumentMetadata>
  ): Promise<DocumentMetadata> {
    const { data, error } = await supabase
      .from("documents")
      .update(updates)
      .eq("id", documentId)
      .eq("user_id", userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update document: ${error.message}`);
    }

    return data;
  }

  async deleteDocument(documentId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from("documents")
      .delete()
      .eq("id", documentId)
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Failed to delete document: ${error.message}`);
    }
  }

  async searchDocuments(
    userId: string,
    query: string,
    limit = 20
  ): Promise<DocumentMetadata[]> {
    const { data, error } = await supabase
      .from("documents")
      .select(
        "id, user_id, filename, file_type, file_size, supabase_storage_path, uploaded_at, is_processed, processing_error, page_count"
      )
      .eq("user_id", userId)
      .or(`filename.ilike.%${query}%,content_text.ilike.%${query}%`)
      .order("uploaded_at", { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to search documents: ${error.message}`);
    }

    return (data || []) as DocumentMetadata[];
  }
}

export const documentDbService = new DocumentDbService();
