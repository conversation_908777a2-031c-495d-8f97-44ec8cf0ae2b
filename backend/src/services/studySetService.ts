import { supabase } from './supabaseService';
import { StudySet, Flashcard, QuizQuestion, StudySetType } from '../../../shared/types';

export class StudySetService {
  async createStudySet(data: {
    user_id: string;
    name: string;
    type: StudySetType;
    is_ai_generated: boolean;
    source_documents?: { id: string; filename: string }[];
    custom_prompt?: string;
  }): Promise<StudySet> {
    const { data: studySet, error } = await supabase
      .from('study_sets')
      .insert({
        user_id: data.user_id,
        name: data.name,
        type: data.type,
        is_ai_generated: data.is_ai_generated,
        source_documents: data.source_documents || null,
        custom_prompt: data.custom_prompt || null,
        total_items: 0
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create study set: ${error.message}`);
    }

    return studySet;
  }

  async addFlashcardsToSet(studySetId: string, flashcards: Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'created_at' | 'updated_at'>[]): Promise<void> {
    const flashcardInserts = flashcards.map(card => ({
      study_set_id: studySetId,
      front: card.front,
      back: card.back,
      difficulty_level: card.difficulty_level,
      is_ai_generated: card.is_ai_generated,
      times_reviewed: card.times_reviewed || 0,
      last_reviewed_at: card.last_reviewed_at,
      is_flagged: false
    }));

    const { error } = await supabase
      .from('flashcards')
      .insert(flashcardInserts);

    if (error) {
      throw new Error(`Failed to add flashcards: ${error.message}`);
    }
  }

  async addFlashcardToSet(studySetId: string, flashcard: Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'created_at' | 'updated_at'>): Promise<Flashcard> {
    const { data, error } = await supabase
      .from('flashcards')
      .insert({
        study_set_id: studySetId,
        front: flashcard.front,
        back: flashcard.back,
        difficulty_level: flashcard.difficulty_level,
        is_ai_generated: flashcard.is_ai_generated,
        times_reviewed: flashcard.times_reviewed || 0,
        last_reviewed_at: flashcard.last_reviewed_at,
        is_flagged: false
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to add flashcard: ${error.message}`);
    }

    return data;
  }

  async addQuizQuestionsToSet(studySetId: string, questions: Omit<QuizQuestion, 'id' | 'study_set_id' | 'created_at' | 'updated_at'>[]): Promise<void> {
    const questionInserts = questions.map(question => ({
      study_set_id: studySetId,
      question_text: question.question_text,
      question_type: question.question_type,
      options: question.options,
      correct_answers: question.correct_answers,
      explanation: question.explanation,
      difficulty_level: question.difficulty_level,
      is_ai_generated: question.is_ai_generated,
      times_attempted: question.times_attempted || 0,
      times_correct: question.times_correct || 0
    }));

    const { error } = await supabase
      .from('quiz_questions')
      .insert(questionInserts);

    if (error) {
      throw new Error(`Failed to add quiz questions: ${error.message}`);
    }
  }

  async getUserStudySets(userId: string, limit = 50, offset = 0): Promise<StudySet[]> {
    const { data, error } = await supabase
      .from('study_sets')
      .select(`
        *,
        flashcard_count:flashcards(count),
        quiz_question_count:quiz_questions(count)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Failed to get study sets: ${error.message}`);
    }

    // Transform the data to include proper count fields
    const studySets = (data || []).map(studySet => ({
      ...studySet,
      flashcard_count: Array.isArray(studySet.flashcard_count) ? studySet.flashcard_count[0]?.count || 0 : 0,
      quiz_question_count: Array.isArray(studySet.quiz_question_count) ? studySet.quiz_question_count[0]?.count || 0 : 0
    }));

    return studySets;
  }

  async getStudySetById(studySetId: string, userId: string): Promise<StudySet | null> {
    const { data, error } = await supabase
      .from('study_sets')
      .select('*')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw new Error(`Failed to get study set: ${error.message}`);
    }

    return data;
  }

  async deleteStudySet(studySetId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('study_sets')
      .delete()
      .eq('id', studySetId)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to delete study set: ${error.message}`);
    }
  }

  async updateStudySet(studySetId: string, userId: string, updates: Partial<StudySet>): Promise<StudySet> {
    const { data, error } = await supabase
      .from('study_sets')
      .update(updates)
      .eq('id', studySetId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update study set: ${error.message}`);
    }

    return data;
  }

  async getFlashcardsByStudySet(studySetId: string, userId: string): Promise<any[]> {
    // First verify the study set belongs to the user
    const studySet = await this.getStudySetById(studySetId, userId);
    if (!studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .select('*')
      .eq('study_set_id', studySetId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to get flashcards: ${error.message}`);
    }

    return data || [];
  }

  async getQuizQuestionsByStudySet(studySetId: string, userId: string): Promise<any[]> {
    // First verify the study set belongs to the user
    const studySet = await this.getStudySetById(studySetId, userId);
    if (!studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('quiz_questions')
      .select('*')
      .eq('study_set_id', studySetId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to get quiz questions: ${error.message}`);
    }

    return data || [];
  }
}

export const studySetService = new StudySetService();
