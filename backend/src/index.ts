import express from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";
import path from "path";
import { env } from "./config/environment";
import supabase from "./config/supabase";
import authRoutes from "./routes/auth";
import documentRoutes from "./routes/documents";
import creditRoutes from "./routes/credits";
import aiRoutes from "./routes/ai";
import studySetRoutes from "./routes/studySets";
import flashcardRoutes from "./routes/flashcards";
import quizQuestionRoutes from "./routes/quizQuestions";
import studySessionRoutes from "./routes/studySessions";
import userSettingsRoutes from "./routes/userSettings";
import userPreferencesRoutes from "./routes/userPreferences";
import stripeRoutes from "./routes/stripe";
import subscriptionRoutes from "./routes/subscription";
import billingRoutes from "./routes/billing";

const app = express();
const PORT = env.PORT;

// Trust proxy for rate limiting when behind a proxy (like React dev server)
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'"],
      },
    },
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: "Too many requests from this IP, please try again later.",
});
app.use("/api", limiter);

const allowedOrigins = [
  env.FRONTEND_URL,
  "http://localhost:3000",
  "https://localhost:3000",
  /^https:\/\/.*\.repl\.co$/,
  /^https:\/\/.*\.replit\.dev$/,
  /^https:\/\/.*\.replit\.app$/,
  /^https:\/\/.*\.worf\.replit\.dev$/
];

app.use(
  cors({
    origin: allowedOrigins,
    credentials: true,
  })
);

// Body parsing middleware
app.use(compression());

// JSON parsing for all routes EXCEPT webhook
app.use((req, res, next) => {
  if (req.path === "/api/stripe/webhook") {
    next();
  } else {
    express.json({ limit: "10mb" })(req, res, next);
  }
});
app.use((req, res, next) => {
  if (req.path === "/api/stripe/webhook") {
    next();
  } else {
    express.urlencoded({ extended: true, limit: "10mb" })(req, res, next);
  }
});

// Stripe webhook endpoint MUST have raw body for signature verification
app.post(
  "/api/stripe/webhook",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    try {
      const { stripeService } = await import("./services/stripeService");

      // Validate webhook signature
      const signature = req.headers["stripe-signature"];
      if (!signature) {
        console.error("Missing Stripe signature header");
        return res.status(400).json({ error: "Missing signature" });
      }

      const signatureString = Array.isArray(signature) ? signature[0] : signature;
      const result = await stripeService.handleWebhook(req.body, signatureString);
      res.json(result);
    } catch (error) {
      console.error("Webhook error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Webhook processing failed";
      res.status(400).json({ error: errorMessage });
    }
  }
);

// All other Stripe routes (with JSON parsing already applied)
app.use("/api/stripe", stripeRoutes);

// Health check endpoint
app.get("/api/health", async (_req, res) => {
  try {
    // Test database connection
    const { data, error } = await supabase
      .from("ai_operation_costs")
      .select("count")
      .limit(1);

    const dbStatus = error ? "error" : "connected";
    const dbRecords = data ? data.length : 0;

    res.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      environment: env.NODE_ENV,
      database: dbStatus,
      dbRecords,
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      environment: env.NODE_ENV,
      database: "error",
      error: "Database connection failed",
    });
  }
});

// API routes (Stripe routes already registered above before JSON parsing)
app.use("/api/auth", authRoutes);
app.use("/api/documents", documentRoutes);
app.use("/api/credits", creditRoutes);
app.use("/api/ai", aiRoutes);
app.use("/api/study-sets", studySetRoutes);
app.use("/api/flashcards", flashcardRoutes);
app.use("/api/quiz-questions", quizQuestionRoutes);
app.use("/api/study-sessions", studySessionRoutes);
app.use("/api/user/settings", userSettingsRoutes);
app.use("/api/user/preferences", userPreferencesRoutes);
app.use("/api/subscription", subscriptionRoutes);
app.use("/api/billing", billingRoutes);

app.get("/api", (_req, res) => {
  res.json({
    message: "ChewyAI API Server",
    version: "1.0.0",
    endpoints: {
      health: "/api/health",
      auth: "/api/auth",
      documents: "/api/documents",
      credits: "/api/credits",
      ai: "/api/ai",
    },
  });
});

// Serve static files from frontend build (production)
if (env.NODE_ENV === "production") {
  app.use(express.static(path.join(__dirname, "../public")));

  // Catch all handler: send back React's index.html file for SPA routing
  app.get("*", (_req, res) => {
    res.sendFile(path.join(__dirname, "../public/index.html"));
  });
}

// Error handling middleware
app.use(
  (
    err: any,
    _req: express.Request,
    res: express.Response,
    _next: express.NextFunction
  ) => {
    console.error("Error:", err);

    const status = err.status || 500;
    const message =
      env.NODE_ENV === "production" ? "Internal server error" : err.message;

    res.status(status).json({
      success: false,
      error: message,
      ...(env.NODE_ENV === "development" && { stack: err.stack }),
    });
  }
);

// 404 handler for API routes
app.use("/api/*", (req, res) => {
  res.status(404).json({
    success: false,
    error: "API endpoint not found",
    path: req.path,
  });
});

app.listen(PORT, () => {
  console.log(`🚀 ChewyAI Backend Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
});
