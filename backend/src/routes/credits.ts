import { Router } from "express";
import { authenticateToken } from "../middleware/auth";
import {
  getCreditBalance,
  getCreditHistory,
  getCreditStats,
  getOperationCosts,
  checkCreditSufficiency,
  purchaseCredits,
} from "../controllers/creditController";

const router = Router();

// Public pricing information (no auth required)
router.get("/pricing", getOperationCosts);

// All other credit routes require authentication
router.use(authenticateToken);

// Credit information routes
router.get("/balance", getCreditBalance);
router.get("/history", getCreditHistory);
router.get("/stats", getCreditStats);
router.get("/check/:operationType", checkCreditSufficiency);

// Credit purchase route
router.post("/purchase", purchaseCredits);

export default router;
