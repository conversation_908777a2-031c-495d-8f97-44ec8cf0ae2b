import express from "express";
import { stripeService } from "../services/stripeService";
import { authenticateToken } from "../middleware/auth";
import { supabase } from "../services/supabaseService";

const router = express.Router();

// Create or get Stripe customer
router.post("/customer", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { email, name } = req.body;

    if (!userId || !email) {
      return res.status(400).json({ error: "User ID and email are required" });
    }

    const customerId = await stripeService.createOrGetCustomer(
      userId,
      email,
      name
    );

    res.json({ customerId });
  } catch (error) {
    console.error("Error creating/getting customer:", error);
    res.status(500).json({ error: "Failed to create or get customer" });
  }
});

// Create subscription
router.post("/subscription", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { priceId, email, name } = req.body;

    if (!userId || !priceId || !email) {
      return res
        .status(400)
        .json({ error: "User ID, price ID, and email are required" });
    }

    const customerId = await stripeService.createOrGetCustomer(
      userId,
      email,
      name
    );
    const subscription = await stripeService.createSubscription(
      customerId,
      priceId
    );

    res.json({ subscription });
  } catch (error) {
    console.error("Error creating subscription:", error);
    res.status(500).json({ error: "Failed to create subscription" });
  }
});

// Get user subscriptions
router.get("/subscriptions", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({ error: "User ID is required" });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from("users")
      .select("stripe_customer_id")
      .eq("id", userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.json({ subscriptions: [] });
    }

    const subscriptions = await stripeService.getCustomerSubscriptions(
      profile.stripe_customer_id
    );

    res.json({ subscriptions });
  } catch (error) {
    console.error("Error getting subscriptions:", error);
    res.status(500).json({ error: "Failed to get subscriptions" });
  }
});

// Cancel subscription
router.post("/subscription/:id/cancel", authenticateToken, async (req, res) => {
  try {
    const { id: subscriptionId } = req.params;
    const userId = req.user?.id;

    if (!userId || !subscriptionId) {
      return res
        .status(400)
        .json({ error: "User ID and subscription ID are required" });
    }

    // Verify subscription belongs to user
    const { data: profile } = await supabase
      .from("users")
      .select("stripe_customer_id")
      .eq("id", userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.status(404).json({ error: "Customer not found" });
    }

    const subscriptions = await stripeService.getCustomerSubscriptions(
      profile.stripe_customer_id
    );
    const subscription = subscriptions.find((sub) => sub.id === subscriptionId);

    if (!subscription) {
      return res.status(404).json({ error: "Subscription not found" });
    }

    const canceledSubscription = await stripeService.cancelSubscription(
      subscriptionId
    );

    res.json({ subscription: canceledSubscription });
  } catch (error) {
    console.error("Error canceling subscription:", error);
    res.status(500).json({ error: "Failed to cancel subscription" });
  }
});

// Legacy credit purchase route - DEPRECATED
// Use /create-credit-package-checkout instead for new implementations

// Get customer invoices
router.get("/invoices", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!userId) {
      return res.status(400).json({ error: "User ID is required" });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from("users")
      .select("stripe_customer_id")
      .eq("id", userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.json({ invoices: [] });
    }

    const invoices = await stripeService.getCustomerInvoices(
      profile.stripe_customer_id,
      limit
    );

    res.json({ invoices });
  } catch (error) {
    console.error("Error getting invoices:", error);
    res.status(500).json({ error: "Failed to get invoices" });
  }
});

// Get customer payment methods
router.get("/payment-methods", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({ error: "User ID is required" });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from("users")
      .select("stripe_customer_id")
      .eq("id", userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.json({ paymentMethods: [] });
    }

    const paymentMethods = await stripeService.getCustomerPaymentMethods(
      profile.stripe_customer_id
    );

    res.json({ paymentMethods });
  } catch (error) {
    console.error("Error getting payment methods:", error);
    res.status(500).json({ error: "Failed to get payment methods" });
  }
});

// Create checkout session for subscription
router.post("/create-checkout-session", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { planId, coupon, successUrl, cancelUrl } = req.body;

    if (!userId || !planId || !successUrl || !cancelUrl) {
      return res.status(400).json({
        success: false,
        error: "User ID, plan ID, success URL, and cancel URL are required",
      });
    }

    // Get user profile for email
    const { data: profile } = await supabase
      .from("users")
      .select("email, name")
      .eq("id", userId)
      .single();

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: "User profile not found",
      });
    }

    const customerId = await stripeService.createOrGetCustomer(
      userId,
      profile.email,
      profile.name
    );

    const session = await stripeService.createCheckoutSession({
      customerId,
      planId,
      coupon,
      successUrl,
      cancelUrl,
      userId,
    });

    res.json({
      success: true,
      data: { sessionId: session.id },
    });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    res.status(500).json({
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create checkout session",
    });
  }
});

// Create checkout session for credit package purchase (new for business model v4.0)
router.post(
  "/create-credit-package-checkout",
  authenticateToken,
  async (req, res) => {
    try {
      const userId = req.user?.id;
      const { packageId, coupon, successUrl, cancelUrl } = req.body;

      if (!userId || !packageId || !successUrl || !cancelUrl) {
        return res.status(400).json({
          success: false,
          error:
            "User ID, package ID, success URL, and cancel URL are required",
        });
      }

      // Get user profile for email
      const { data: profile } = await supabase
        .from("users")
        .select("email, name")
        .eq("id", userId)
        .single();

      if (!profile) {
        return res.status(404).json({
          success: false,
          error: "User profile not found",
        });
      }

      const customerId = await stripeService.createOrGetCustomer(
        userId,
        profile.email,
        profile.name
      );

      const session = await stripeService.createCreditPackageCheckoutSession({
        customerId,
        packageId,
        coupon,
        successUrl,
        cancelUrl,
        userId,
      });

      res.json({
        success: true,
        data: { sessionId: session.id },
      });
    } catch (error) {
      console.error("Error creating credit package checkout session:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create credit package checkout session",
      });
    }
  }
);

// Validate coupon code
router.post("/validate-coupon", authenticateToken, async (req, res) => {
  try {
    const { coupon } = req.body;

    if (!coupon || typeof coupon !== "string") {
      return res.status(400).json({
        success: false,
        error: "Coupon code is required",
      });
    }

    const couponData = await stripeService.validateCoupon(
      coupon.trim().toUpperCase()
    );

    res.json({
      success: true,
      data: couponData,
    });
  } catch (error) {
    console.error("Error validating coupon:", error);
    res.status(500).json({
      success: false,
      error: "Failed to validate coupon",
      data: { valid: false },
    });
  }
});

// Get Stripe prices
router.get("/prices", async (_req, res) => {
  try {
    const prices = await stripeService.getPrices();

    res.json({ prices });
  } catch (error) {
    console.error("Error getting prices:", error);
    res.status(500).json({ error: "Failed to get prices" });
  }
});

// Create recurring prices (one-time setup)
router.post("/setup-recurring-prices", async (_req, res) => {
  try {
    console.log("Creating recurring prices...");
    const createdPrices = await stripeService.createRecurringPrices();
    console.log("Recurring prices created:", createdPrices);

    res.json({
      success: true,
      message: "Recurring prices created successfully",
      prices: createdPrices,
    });
  } catch (error) {
    console.error("Error creating recurring prices:", error);
    res.status(500).json({
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create recurring prices",
    });
  }
});

// Test endpoint to check if server is running
router.get("/test", async (_req, res) => {
  res.json({
    message: "Stripe routes working",
    timestamp: new Date().toISOString(),
  });
});

// Test minimal checkout session creation
router.post("/test-checkout", async (_req, res) => {
  try {
    console.log("Creating test checkout session...");
    const session = await stripeService.createTestCheckoutSession(
      "price_1RgeXe7lNlVY1bMulc5SRF9z", // Study Starter recurring price
      "http://localhost:3000/success",
      "http://localhost:3000/cancel"
    );

    console.log("Test checkout session created:", session.id);
    res.json({
      success: true,
      sessionId: session.id,
      url: session.url,
    });
  } catch (error) {
    console.error("Test checkout error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Diagnostic endpoint to check Stripe account status
router.get("/account-status", async (_req, res) => {
  try {
    // Check account details using the public method
    const account = await stripeService.getAccountStatus();

    // Add additional diagnostic information
    const diagnostics = {
      account,
      keyType: process.env.STRIPE_SECRET_KEY?.startsWith("sk_live_")
        ? "LIVE"
        : "TEST",
      keyPrefix: process.env.STRIPE_SECRET_KEY?.substring(0, 12) + "...",
      timestamp: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: diagnostics,
    });
  } catch (error) {
    console.error("Account status error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Webhook endpoint moved to main index.ts to avoid JSON parsing conflicts
// router.post(
//   "/webhook",
//   express.raw({ type: "application/json" }),
//   async (req, res) => {
//     try {
//       const signature = req.headers["stripe-signature"] as string;

//       if (!signature) {
//         return res
//           .status(400)
//           .json({ error: "Missing stripe-signature header" });
//       }

//       await stripeService.handleWebhook(req.body, signature);

//       res.json({ received: true });
//     } catch (error) {
//       console.error("Webhook error:", error);
//       res.status(400).json({ error: "Webhook signature verification failed" });
//     }
//   }
// );

export default router;
