import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { getUserPreferences, updateUserPreferences } from '../controllers/userSettingsController';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// GET /api/user/preferences - Get user preferences
router.get('/', getUserPreferences);

// PUT /api/user/preferences - Update user preferences
router.put('/', updateUserPreferences);

export default router;
