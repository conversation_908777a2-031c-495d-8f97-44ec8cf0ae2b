import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  uploadDocument,
  getDocuments,
  getDocument,
  deleteDocument,
  searchDocuments
} from '../controllers/documentController';

const router = Router();

// All document routes require authentication
router.use(authenticateToken);

// Document CRUD operations
router.post('/upload', uploadDocument);
router.get('/', getDocuments);
router.get('/search', searchDocuments);
router.get('/:id', getDocument);
router.delete('/:id', deleteDocument);

export default router;
