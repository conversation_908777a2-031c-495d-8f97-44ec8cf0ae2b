import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getQuizQuestions,
  createQuizQuestion,
  updateQuizQuestion,
  deleteQuizQuestion
} from '../controllers/quizQuestionController';

const router = Router();

// All quiz question routes require authentication
router.use(authenticateToken);

// Quiz question CRUD operations
router.get('/study-set/:studySetId', getQuizQuestions);
router.post('/study-set/:studySetId', createQuizQuestion);
router.put('/:id', updateQuizQuestion);
router.delete('/:id', deleteQuizQuestion);

export default router;
