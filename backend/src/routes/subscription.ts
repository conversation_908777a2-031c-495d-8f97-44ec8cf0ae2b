import express from "express";
import { authenticateToken } from "../middleware/auth";
import { stripeService } from "../services/stripeService";
import { supabase } from "../services/supabaseService";

const router = express.Router();

// Get current subscription data
router.get("/", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: "User ID is required",
      });
    }

    // Get user's Stripe customer ID and current subscription info
    const { data: profile } = await supabase
      .from("users")
      .select("stripe_customer_id, subscription_status, subscription_id")
      .eq("id", userId)
      .single();

    if (!profile?.stripe_customer_id) {
      // User has no Stripe customer - return free plan
      return res.json({
        success: true,
        data: {
          currentPlan: {
            id: "free",
            name: "Free",
            price: 0,
            interval: "month",
          },
          status: "active",
          nextBillingDate: null,
          cancelAtPeriodEnd: false,
        },
      });
    }

    // Get subscription details from Stripe
    const subscriptions = await stripeService.getCustomerSubscriptions(
      profile.stripe_customer_id
    );
    const activeSubscription = subscriptions.find(
      (sub) => sub.status === "active" || sub.status === "trialing"
    );

    if (!activeSubscription) {
      // No active subscription - return free plan
      return res.json({
        success: true,
        data: {
          currentPlan: {
            id: "free",
            name: "Study Starter",
            price: 0,
            interval: "month",
          },
          status: "active",
          nextBillingDate: null,
          cancelAtPeriodEnd: false,
        },
      });
    }

    // Map Stripe subscription to our format (Business Model v4.0)
    const planMapping: Record<string, any> = {
      price_1RgeXe7lNlVY1bMulc5SRF9z: {
        id: "study_starter",
        name: "Study Starter",
        price: 29,
        interval: "month",
      },
      price_1RgeXe7lNlVY1bMuWT9HTvnr: {
        id: "study_pro",
        name: "Study Pro",
        price: 59,
        interval: "month",
      },
      price_1RgeXe7lNlVY1bMuPU6N3CYa: {
        id: "study_master",
        name: "Study Master",
        price: 119,
        interval: "month",
      },
      price_1RgeXe7lNlVY1bMuzHc8Fxmg: {
        id: "study_elite",
        name: "Study Elite",
        price: 239,
        interval: "month",
      },
      // Legacy mappings for backward compatibility
      pro_monthly: {
        id: "pro_monthly",
        name: "Scholar Pro",
        price: 9.99,
        interval: "month",
      },
      pro_yearly: {
        id: "pro_yearly",
        name: "Academic Year Pass",
        price: 99.99,
        interval: "year",
      },
    };

    const priceId = activeSubscription.items.data[0]?.price?.id;
    const currentPlan = planMapping[priceId || ""] || {
      id: "study_starter",
      name: "Study Starter",
      price: 29,
      interval: "month",
    };

    res.json({
      success: true,
      data: {
        currentPlan,
        status: activeSubscription.status,
        nextBillingDate: new Date(
          activeSubscription.current_period_end * 1000
        ).toISOString(),
        cancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
      },
    });
  } catch (error) {
    console.error("Error fetching subscription data:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch subscription data",
    });
  }
});

// Change subscription plan
router.post("/change", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { planId } = req.body;

    if (!userId || !planId) {
      return res.status(400).json({
        success: false,
        error: "User ID and plan ID are required",
      });
    }

    // Get user's email for Stripe customer creation
    const { data: profile } = await supabase
      .from("users")
      .select("email, name, stripe_customer_id")
      .eq("id", userId)
      .single();

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: "User not found",
      });
    }

    // Map plan IDs to Stripe price IDs (Business Model v4.0)
    const priceMapping: Record<string, string> = {
      study_starter:
        process.env.STRIPE_PRICE_ID_STARTER || "price_1RgeXe7lNlVY1bMulc5SRF9z",
      study_pro:
        process.env.STRIPE_PRICE_ID_PRO || "price_1RgeXe7lNlVY1bMuWT9HTvnr",
      study_master:
        process.env.STRIPE_PRICE_ID_MASTER || "price_1RgeXe7lNlVY1bMuPU6N3CYa",
      study_elite:
        process.env.STRIPE_PRICE_ID_ELITE || "price_1RgeXe7lNlVY1bMuzHc8Fxmg",
      // Legacy mappings for backward compatibility
      pro_monthly:
        process.env.STRIPE_PRICE_ID_PRO || "price_1RgeXe7lNlVY1bMuWT9HTvnr",
      pro_yearly: process.env.STRIPE_PRICE_PRO_YEARLY || "price_pro_yearly",
    };

    const priceId = priceMapping[planId];
    if (!priceId) {
      return res.status(400).json({
        success: false,
        error: "Invalid plan ID",
      });
    }

    // Create or get customer
    const customerId = await stripeService.createOrGetCustomer(
      userId,
      profile.email,
      profile.name
    );

    // Create subscription
    const subscription = await stripeService.createSubscription(
      customerId,
      priceId
    );

    res.json({
      success: true,
      data: { subscription },
    });
  } catch (error) {
    console.error("Error changing subscription:", error);
    res.status(500).json({
      success: false,
      error: "Failed to change subscription plan",
    });
  }
});

// Cancel subscription
router.post("/cancel", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: "User ID is required",
      });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from("users")
      .select("stripe_customer_id")
      .eq("id", userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.status(404).json({
        success: false,
        error: "No subscription found",
      });
    }

    // Get active subscriptions
    const subscriptions = await stripeService.getCustomerSubscriptions(
      profile.stripe_customer_id
    );
    const activeSubscription = subscriptions.find(
      (sub) => sub.status === "active" || sub.status === "trialing"
    );

    if (!activeSubscription) {
      return res.status(404).json({
        success: false,
        error: "No active subscription found",
      });
    }

    // Cancel subscription at period end
    const canceledSubscription = await stripeService.cancelSubscription(
      activeSubscription.id
    );

    res.json({
      success: true,
      data: { subscription: canceledSubscription },
    });
  } catch (error) {
    console.error("Error canceling subscription:", error);
    res.status(500).json({
      success: false,
      error: "Failed to cancel subscription",
    });
  }
});

// Reactivate subscription
router.post("/reactivate", authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: "User ID is required",
      });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from("users")
      .select("stripe_customer_id")
      .eq("id", userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.status(404).json({
        success: false,
        error: "No subscription found",
      });
    }

    // Get subscriptions
    const subscriptions = await stripeService.getCustomerSubscriptions(
      profile.stripe_customer_id
    );
    const subscription = subscriptions.find((sub) => sub.cancel_at_period_end);

    if (!subscription) {
      return res.status(404).json({
        success: false,
        error: "No subscription to reactivate",
      });
    }

    // Reactivate subscription
    const reactivatedSubscription = await stripeService.reactivateSubscription(
      subscription.id
    );

    res.json({
      success: true,
      data: { subscription: reactivatedSubscription },
    });
  } catch (error) {
    console.error("Error reactivating subscription:", error);
    res.status(500).json({
      success: false,
      error: "Failed to reactivate subscription",
    });
  }
});

export default router;
