{"version": 3, "file": "static/js/689.1d226b38.chunk.js", "mappings": "wLAwCO,MAAMA,GAAiBC,E,QAAAA,IAAoB,CAACC,EAAKC,KAAG,CACzDC,QAAS,EACTC,aAAc,GACdC,eAAgB,GAChBC,MAAO,KACPC,WAAW,EACXC,MAAO,KAEPC,aAAcC,UACZT,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,uBAAwB,CACnDC,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAK7B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,kCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAGP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,2BAF9BP,EAAI,CAAEE,QAASkB,EAAKA,KAAKG,QAASjB,WAAW,GAIjD,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,GAGFmB,kBAAmBhB,iBAAmC,IAA5BiB,EAAKC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAAMH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAC7C3B,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,8BAADG,OACIY,EAAK,YAAAZ,OAAWgB,GAC9C,CACElB,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAM/B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,kCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAOP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,gCAN9BP,EAAI,CACFG,aACa,IAAX2B,EAAeV,EAAKA,KAAO,IAAInB,IAAME,gBAAiBiB,EAAKA,MAC7Dd,WAAW,GAKjB,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,CACF,EAEAyB,oBAAqBtB,UACnBT,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,uBAAwB,CACnDC,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAK7B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,mCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAGP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,mCAF9BP,EAAI,CAAEI,eAAgBgB,EAAKA,KAAMd,WAAW,GAIhD,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,GAGF0B,WAAYvB,iBAAsB,IAAfwB,EAAIN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACxB3B,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,2BAADG,OAA4BmB,GAAQ,CAC9DrB,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAK7B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,gCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAGP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,yBAF9BP,EAAI,CAAEK,MAAOe,EAAKA,KAAMd,WAAW,GAIvC,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,CACF,EAEA4B,gBAAiBzB,UAQfT,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,wBAAyB,CACpDwB,OAAQ,OACRvB,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,gBAG3BoB,KAAMC,KAAKC,UAAUC,KAGvB,IAAK7B,EAASQ,GACZ,MAAM,IAAIC,MAAM,sCAGlB,MAAMC,QAAaV,EAASW,OAC5B,GAAID,EAAKE,QAEP,OADAtB,EAAI,CAAEM,WAAW,IACV,CACLgB,SAAS,EACTkB,aAAcpB,EAAKA,KAAKoB,aACxBC,gBAAiBrB,EAAKA,KAAKqB,iBAG7B,MAAM,IAAItB,MAAMC,EAAKb,OAAS,6BAElC,CAAE,MAAOA,GACP,MAAMmC,EACJnC,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAK3C,OAJAxB,EAAI,CACFO,MAAOmC,EACPpC,WAAW,IAEN,CAAEgB,SAAS,EAAOf,MAAOmC,EAClC,GAGFC,WAAYA,IAAM3C,EAAI,CAAEO,MAAO,OAE/BqC,qBAAsBnC,UACpB,UACQoC,QAAQC,IAAI,CAAC7C,IAAMO,eAAgBP,IAAMwB,qBACjD,CAAE,MAAOlB,GACPwC,QAAQxC,MAAM,yCAA0CA,EAC1D,M,kCCvNG,MAAMyC,EAA8CC,IAIpD,IAJqD,QAC1D/C,EAAO,SACPgD,EAAQ,UACR5C,GACD2C,EACC,MAAME,EAAgBC,IACpB,OAAQA,EAAKC,eACX,IAAK,MACH,MAAO,kBACT,IAAK,QACH,MAAO,gBACT,QACE,MAAO,kBAsBPC,EAPoBpD,IACpBA,GAAW,IAAY,CAAEqD,MAAO,iBAAkBC,OAAQ,aAC1DtD,GAAW,GAAW,CAAEqD,MAAO,kBAAmBC,OAAQ,QAC1DtD,GAAW,GAAW,CAAEqD,MAAO,kBAAmBC,OAAQ,OACvD,CAAED,MAAO,eAAgBC,OAAQ,YAGpBC,CAAiBvD,GAEvC,OACEwD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBT,UAAU,mHAAkHC,SAAA,EAE5HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,mCAAkCC,UAC/CS,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,gCAE1BD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,oBACjDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,uCAIzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAS,+BAAA7C,OAAiCqC,EAAaD,IAAYU,SAAA,CAxC3DR,KACnB,OAAQA,EAAKC,eACX,IAAK,MACH,OAAOgB,EAAAA,EAAAA,KAACE,EAAAA,IAAU,CAACZ,UAAU,YAC/B,IAAK,QACH,OAAOU,EAAAA,EAAAA,KAACG,EAAAA,IAAY,CAACb,UAAU,YACjC,QACE,OAAOU,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,cAkC1Bc,CAAYvB,IACbQ,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEV,EAAS,kBAI5CQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BC,SAAA,EACvCS,EAAAA,EAAAA,KAAA,OAAAT,SACGtD,GACC+D,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,qCAGjBD,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEW,MAAO,IAClBR,QAAS,CAAEQ,MAAO,GAClBP,WAAY,CAAEC,SAAU,GAAKO,MAAO,IAAMf,SAAA,EAE1CS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,gCAA+BC,SAAE1D,EAAQ0E,oBACzDP,EAAAA,EAAAA,KAAA,QAAMV,UAAU,6BAA4BC,SAAC,kBAKnDF,EAAAA,EAAAA,MAAA,OAAKC,UAAS,+BAAA7C,OAAiCwC,EAAcC,MAAK,SAAQK,SAAA,EACxES,EAAAA,EAAAA,KAAA,OAAKV,UAAS,wBAAA7C,OAA0BwC,EAAcC,MAAMsB,QAAQ,QAAS,WAC7ER,EAAAA,EAAAA,KAAA,QAAMV,UAAU,sBAAqBC,SAAEN,EAAcE,eAIxDtD,EAAU,KACTwD,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,GACpBE,QAAS,CAAEF,QAAS,GACpBG,WAAY,CAAEQ,MAAO,IACrBhB,UAAU,6DAA4DC,SAAA,EAEtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAACS,EAAAA,IAAO,CAACnB,UAAU,0BACnBU,EAAAA,EAAAA,KAAA,QAAMV,UAAU,mCAAkCC,SAAC,4BAErDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,4BAA2BC,SAAC,2EAQ/CF,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKO,MAAO,IACpChB,UAAU,sEAAqEC,SAAA,EAE/ES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,iBAEtDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAC,eACxCS,EAAAA,EAAAA,KAAA,QAAMV,UAAS,eAAA7C,OAAiBqC,EAAaD,IAAYU,SAAEV,QAG7DQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAC,YACxCS,EAAAA,EAAAA,KAAA,QAAMV,UAAS,eAAA7C,OAAiBwC,EAAcC,OAAQK,SAAEN,EAAcE,aAGxEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAC,uBACxCS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,yBAAwBC,SAAE1D,OAGhB,SAA3BgD,EAASG,gBACRgB,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,sEChItCmB,EAA8C9B,IAIpD,IAJqD,aAC1D9C,EAAY,UACZG,EAAS,WACT0E,GACD/B,EACC,MAAOgC,EAAQC,IAAaC,EAAAA,EAAAA,UAAuC,QAC5DC,EAAQC,IAAaF,EAAAA,EAAAA,UAA4B,QAUlDG,EAAuBC,GACpBA,EAAc,EAAI,eAAiB,iBAWtCC,EAAuBrF,EAAa8E,OAAOQ,GAChC,SAAXR,EAA0BQ,EAAYC,aAAe,EAC1C,cAAXT,GAA+BQ,EAAYC,aAAe,GAI1DC,EAAqB,IAAIH,GAAsBI,KAAK,CAACC,EAAGC,IAC7C,SAAXV,EACK,IAAIW,KAAKD,EAAEE,YAAYC,UAAY,IAAIF,KAAKF,EAAEG,YAAYC,UAE1DC,KAAKC,IAAIL,EAAEJ,cAAgBQ,KAAKC,IAAIN,EAAEH,eAyBjD,OACEhC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAElFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,yBACjDF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CACjC4B,EAAqB5D,OAAO,OAAKzB,EAAayB,OAAO,uBAI1D8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAE1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,UACE0C,MAAOnB,EACPoB,SAAWC,GAAMpB,EAAUoB,EAAEC,OAAOH,OACpCzC,UAAU,4KAA2KC,SAAA,EAErLS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,MAAKxC,SAAC,sBACpBS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,OAAMxC,SAAC,kBACrBS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,YAAWxC,SAAC,0BAE5BS,EAAAA,EAAAA,KAACmC,EAAAA,IAAa,CAAC7C,UAAU,sGAI3BD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,UACE0C,MAAOhB,EACPiB,SAAWC,GAAMjB,EAAUiB,EAAEC,OAAOH,OACpCzC,UAAU,4KAA2KC,SAAA,EAErLS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,OAAMxC,SAAC,kBACrBS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,SAAQxC,SAAC,uBAEzBS,EAAAA,EAAAA,KAACmC,EAAAA,IAAa,CAAC7C,UAAU,sGAI3BD,EAAAA,EAAAA,MAAC+C,EAAAA,EAAM,CACLC,QA9DiBC,KACzB,MAAMC,EAAa,CACjB,CAAC,OAAQ,OAAQ,UAAW,YAAa,eAAeC,KAAK,QAC1DlB,EAAmBmB,IAAIrB,GAAe,CACvC,IAAIM,KAAKN,EAAYO,YAAYe,qBACjCtB,EAAYC,aAAe,EAAI,OAAS,YACxCQ,KAAKC,IAAIV,EAAYC,cACrBD,EAAYuB,eAAe,IAADlG,OACtB2E,EAAYwB,YAAW,MAC3BJ,KAAK,OACPA,KAAK,MAEDK,EAAO,IAAIC,KAAK,CAACP,GAAa,CAAEQ,KAAM,aACtCC,EAAMC,OAAOC,IAAIC,gBAAgBN,GACjCrB,EAAI4B,SAASC,cAAc,KACjC7B,EAAE8B,KAAON,EACTxB,EAAE+B,SAAQ,kBAAA9G,QAAqB,IAAIiF,MAAO8B,cAAcC,MAAM,KAAK,GAAE,QACrEjC,EAAEkC,QACFT,OAAOC,IAAIS,gBAAgBX,IA6CnBY,QAAQ,YACRC,KAAK,KACLC,SAAkC,IAAxBhI,EAAayB,OAAagC,SAAA,EAEpCS,EAAAA,EAAAA,KAAC+D,EAAAA,IAAU,CAACzE,UAAU,iBAAiB,mBAO7CU,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SACvBtD,GAAqC,IAAxBH,EAAayB,QACzByC,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SACvB,IAAIyE,MAAM,IAAIvB,IAAI,CAACwB,EAAGC,KACrBlE,EAAAA,EAAAA,KAAA,OAAaV,UAAU,gBAAeC,UACpCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sCACfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCACfU,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sCAEjBU,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uCARX4E,MAckB,IAA9B5C,EAAmB/D,QACrB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCS,EAAAA,EAAAA,KAACS,EAAAA,IAAO,CAACnB,UAAU,0CACnBU,EAAAA,EAAAA,KAAA,MAAIV,UAAU,yCAAwCC,SAAC,2BACvDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,gBAAeC,SACd,QAAXqB,EACG,gDAA+C,MAAAnE,OACzCmE,EAAM,6BAKpBU,EAAmBmB,IAAI,CAACrB,EAAa+C,KACnC,MAAM,KAAEC,EAAI,KAAEC,GA/HJC,KAClB,MAAMF,EAAO,IAAI1C,KAAK4C,GACtB,MAAO,CACLF,KAAMA,EAAK1B,qBACX2B,KAAMD,EAAKG,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,cA2HhCC,CAAWtD,EAAYO,YACxCgD,EAAWvD,EAAYC,aAAe,EAE5C,OACErB,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKO,MAAe,IAAR6D,GACpC7E,UAAU,qHAAoHC,UAE9HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAAA,OAAKV,UAAS,oBAAA7C,OAAsBkI,EAAW,kBAAoB,iBAAkBpF,UAxJ3E2B,EAyJYE,EAAYC,aAxJ3CH,EAAc,GACnBlB,EAAAA,EAAAA,KAAC4E,EAAAA,IAAO,CAACtF,UAAU,0BAEnBU,EAAAA,EAAAA,KAAC6E,EAAAA,IAAM,CAACvF,UAAU,+BAwJJD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,yBAAwBC,SACnC6B,EAAYuB,eAAenC,QAAQ,KAAM,KAAKA,QAAQ,QAASsE,GAAKA,EAAEC,kBAEzE/E,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAE6B,EAAYwB,eAClDvD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAE6E,KACzCpE,EAAAA,EAAAA,KAAA,QAAMV,UAAU,gBAAeC,SAAC,YAChCS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAE8E,cAK/ChF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,iBAAA7C,OAAmBwE,EAAoBG,EAAYC,eAAgB9B,SAAA,CAC/EoF,EAAW,IAAM,IAAK9C,KAAKC,IAAIV,EAAYC,cAAc,cAE3DD,EAAY4D,eACXhF,EAAAA,EAAAA,KAAA,KAAGV,UAAU,6BAA4BC,SAAC,qBA9B3C6B,EAAY6D,IAhJH/D,UAyLvBpF,EAAayB,OAAS,GAAKzB,EAAayB,OAAS,KAAO,IACvDyC,EAAAA,EAAAA,KAAA,OAAKV,UAAU,mBAAkBC,UAC/BS,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CACLC,QAAS1B,EACTiD,QAAQ,YACR3H,UAAWA,EACX6H,SAAU7H,EAAUsD,SACrB,iC,cCtMJ,MAAM2F,EAAgDtG,IAGtD,IAHuD,eAC5DuG,EAAc,SACdtG,GACDD,EACC,MAAOwG,EAAiBC,IAAsBvE,EAAAA,EAAAA,UAAwB,OAC/DwE,EAAcC,IAAmBzE,EAAAA,EAAAA,WAAS,IAC1C5E,EAAOsJ,IAAY1E,EAAAA,EAAAA,UAAwB,OAC3C7D,EAASwI,IAAc3E,EAAAA,EAAAA,UAAwB,OAC/C4E,EAAcC,IAAmB7E,EAAAA,EAAAA,UAAiB,KAClD8E,EAAmBC,IAAwB/E,EAAAA,EAAAA,WAAS,IACpDgF,EAAWC,IAAgBjF,EAAAA,EAAAA,UAChC,kBAEI,KAAEkF,IAASC,EAAAA,EAAAA,KAyHX7F,EAAe8F,IACnB,OAAQA,GACN,IAAK,gBACH,OAAOlG,EAAAA,EAAAA,KAACmG,EAAAA,IAAa,CAAC7G,UAAU,YAClC,IAAK,YACH,OAAOU,EAAAA,EAAAA,KAACoG,EAAAA,IAAM,CAAC9G,UAAU,YAC3B,IAAK,eACH,OAAOU,EAAAA,EAAAA,KAACqG,EAAAA,IAAe,CAAC/G,UAAU,YACpC,IAAK,cACH,OAAOU,EAAAA,EAAAA,KAACE,EAAAA,IAAU,CAACZ,UAAU,YAC/B,QACE,OAAOU,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,cAQrC,OACED,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvBrD,IACC8D,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wDAAuDC,UACpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,uBACVgH,QAAQ,YACRC,KAAK,eAAchH,UAEnBS,EAAAA,EAAAA,KAAA,QACEwG,SAAS,UACTC,EAAE,0NACFC,SAAS,iBAIfrH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,WACjDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,4BAA2BC,SAAErD,QAE9C8D,EAAAA,EAAAA,KAAA,OAAKV,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,UACEgD,QAASA,IAAMmD,EAAS,MACxBlG,UAAU,+KAA8KC,SAAA,EAExLS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,UAASC,SAAC,aAC1BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,UACVgH,QAAQ,YACRC,KAAK,eAAchH,UAEnBS,EAAAA,EAAAA,KAAA,QACEwG,SAAS,UACTC,EAAE,qMACFC,SAAS,wBAUtBzJ,IACC+C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,4DAA2DC,UACxEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,yBACVgH,QAAQ,YACRC,KAAK,eAAchH,UAEnBS,EAAAA,EAAAA,KAAA,QACEwG,SAAS,UACTC,EAAE,wIACFC,SAAS,iBAIfrH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,qCAAoCC,SAAC,aACnDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,8BAA6BC,SAAEtC,QAEhD+C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,UACEgD,QAASA,IAAMoD,EAAW,MAC1BnG,UAAU,yLAAwLC,SAAA,EAElMS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,UAASC,SAAC,aAC1BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,UACVgH,QAAQ,YACRC,KAAK,eAAchH,UAEnBS,EAAAA,EAAAA,KAAA,QACEwG,SAAS,UACTC,EAAE,qMACFC,SAAS,yBAUvB1G,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sEAAqEC,UAClFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mCAAkCC,SAAA,CAAC,iBAChCV,MAEjBmB,EAAAA,EAAAA,KAAA,KAAGV,UAAU,gBAAeC,SAAC,+BAE/BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,sCAAqCC,SAClD4F,KAEHnF,EAAAA,EAAAA,KAAA,QAAMV,UAAU,qBAAoBC,SAAC,qBAM3CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnES,EAAAA,EAAAA,KAAA,UACEqC,QAASA,IAAM0D,EAAa,iBAC5BzG,UAAS,qEAAA7C,OACO,kBAAdqJ,EACI,4BACA,kCACHvG,SACJ,mBAGDS,EAAAA,EAAAA,KAAA,UACEqC,QAASA,IAAM0D,EAAa,WAC5BzG,UAAS,qEAAA7C,OACO,YAAdqJ,EACI,4BACA,kCACHvG,SACJ,wBAMY,YAAduG,IACCzG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2EAA0EC,SAAA,EACvFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,iCAAgCC,SAAC,2BAG/CS,EAAAA,EAAAA,KAAA,UACEqC,QAASA,IAAMwD,GAAsBD,GACrCtG,UAAU,oEAAmEC,SAE5EqG,EAAoB,OAAS,kBAIjCA,IACCvG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BS,EAAAA,EAAAA,KAAA,SACE+C,KAAK,OACLhB,MAAO2D,EACP1D,SAAWC,GAAM0D,EAAgB1D,EAAEC,OAAOH,MAAMgD,eAChD4B,YAAY,sBACZrH,UAAU,4LACVsH,UAAW,MAEb5G,EAAAA,EAAAA,KAAA,UACEqC,QAASA,KACPsD,EAAgB,IAChBE,GAAqB,IAEvBvG,UAAU,6DAA4DC,SACvE,gBASM,kBAAduG,IACCzG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,4BAGtDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,qBAAoBC,SAAC,0EAIlCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uDAAsDC,SAClEsH,EAAAA,GAAcjG,OAAQ7B,GAAqB,SAAZA,EAAKkG,IAAexC,IAClD,CAAC1D,EAAmBoF,KAClB,MAAM2C,EAAa1B,IAAoBrG,EAAKkG,GACtC8B,EAAmBzB,GAAgBwB,EACnCE,GA9LGd,EA8LuBnH,EAAKkG,GA7L1CpG,IAAaqH,GADCA,MAgMT,OACE7G,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKO,MAAe,GAAR6D,GACpC7E,UAAS,iIAAA7C,OAGPsC,EAAKkI,UACD,gDACA,sDAAqD,0BAAAxK,OAEzDqK,EAAa,6BAA+B,GAAE,0BAAArK,OAC9CuK,EAAY,2BAA6B,GAAE,wBAC7CzH,SAAA,CAGCR,EAAKkI,YACJjH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sDAAqDC,UAClES,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uEAAsEC,SAAC,mBAOzFyH,IACChH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,0BAAyBC,UACtCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,qEAAoEC,SAAC,oBAMxFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAE1BS,EAAAA,EAAAA,KAAA,OACEV,UAAS,mCAAA7C,OACPsC,EAAKkI,UACD,qCACA,wCACH1H,SAEFa,EAAYrB,EAAKkG,OAIpBjF,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAClDR,EAAKmI,QAIR7H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,gCAA+BC,SAC5CR,EAAK7B,WAER8C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,mBAGvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,CAAC,IACrB,EAAfR,EAAK7B,QAAY,6BAKvBmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gCAA+BC,SAAA,CAAC,IAC5CR,EAAKoI,UAETnH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,YACvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CAAC,KAClCR,EAAKoI,MAAQpI,EAAK7B,SAASkK,QAAQ,GAAG,qBAK7CpH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,iBAAgBC,SAC5BR,EAAKsI,SAAS5E,IACb,CAAC6E,EAAiBC,KAChBlI,EAAAA,EAAAA,MAAA,OAEEC,UAAU,0CAAyCC,SAAA,EAEnDS,EAAAA,EAAAA,KAACwH,EAAAA,IAAO,CAAClI,UAAU,+CACnBU,EAAAA,EAAAA,KAAA,QAAAT,SAAO+H,MAJFC,OAWbvH,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CACLC,QAASA,IAnaIjG,WAEjC,GADqByK,EAAAA,GAAcY,KAAM1I,GAASA,EAAKkG,KAAOiB,GAM9D,GAAS,OAAJF,QAAI,IAAJA,GAAAA,EAAM0B,MAAX,CAKAnC,GAAgB,GAChBF,EAAmBa,GACnBV,EAAS,MACTC,EAAW,MAEX,IAEE,MAAMkC,EACJjL,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,cAEzB,IAAKgL,EACH,MAAM,IAAI7K,MAAM,4CAGlB,MAAMT,QAAiBC,MAAM,sCAAuC,CAClEwB,OAAQ,OACRvB,QAAS,CACPC,cAAc,UAADC,OAAYkL,GACzB,eAAgB,oBAElB5J,KAAMC,KAAKC,UAAU,CACnB2J,OAAQ1B,EAAOlH,cAAcwB,QAAQ,OAAQ,KAC7CqH,OAAQnC,GAAgB,KACxBoC,WAAW,GAADrL,OAAKwG,OAAO8E,SAASC,OAAM,2CACrCC,UAAU,GAADxL,OAAKwG,OAAO8E,SAASC,OAAM,8BAIlCE,QAAe7L,EAASW,OAG9B,GAAwB,MAApBX,EAAS8C,OAIX,MAFAzC,aAAayL,WAAW,cACxBvL,eAAeuL,WAAW,cACpB,IAAIrL,MAAM,kDAGlB,IAAKoL,EAAOjL,QACV,MAAM,IAAIH,MAAMoL,EAAOhM,OAAS,qCAIlC+G,OAAO8E,SAASzE,KAAI,qCAAA7G,OAAwCyL,EAAOnL,KAAKqL,UAC1E,CAAE,MAAOlM,GACPwC,QAAQxC,MAAM,sBAAuBA,GACrCsJ,EAAS,4DACX,CAAC,QACCD,GAAgB,GAChBF,EAAmB,KACrB,CArDA,MAFEG,EAAS,kEALTA,EAAS,4DAgawB6C,CAA2BtJ,EAAKkG,IAC/CrB,QAAS7E,EAAKkI,UAAY,UAAY,YACtC3H,UAAU,SACVrD,UAAW8K,EACXjD,SAAUwB,KAAqB,OAAJU,QAAI,IAAJA,GAAAA,EAAM0B,QAASV,EAAUzH,SAEnDwH,EACG,gBACAC,EACA,eACK,OAAJhB,QAAI,IAAJA,GAAAA,EAAM0B,MACS,gBAAAjL,OACAsC,EAAKmI,MADrB,wBAtGHnI,EAAKkG,WAmHT,YAAda,IACCzG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,8BAGtDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,qBAAoBC,SAAC,qEAIlCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uDAAsDC,SAClE+I,EAAAA,EAAgB7F,IAAK8F,IACpBvI,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CAETH,UAAU,gHACVkJ,WAAY,CAAEnI,MAAO,MACrBoI,SAAU,CAAEpI,MAAO,KAAOd,UAE1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uFAAsFC,UACnGS,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,gCAE1BU,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAClDgJ,EAAIrB,QAEP7H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gCAA+BC,SAAA,CAAC,IAC3CgJ,EAAIpB,UAER9H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CACnCgJ,EAAIrL,QAAQ,eAEfmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BC,SAAA,CAAC,IACtCgJ,EAAIG,eAAetB,QAAQ,GAAG,iBAGpCpH,EAAAA,EAAAA,KAAA,KAAGV,UAAU,6BAA4BC,SACtCgJ,EAAI3F,eAEP5C,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CACLC,QAASA,IAhaSjG,WAElC,GADwBkM,EAAAA,EAAgBb,KAAMc,GAAQA,EAAItD,KAAO0D,GAMjE,GAAS,OAAJ3C,QAAI,IAAJA,GAAAA,EAAM0B,MAAX,CAKAnC,GAAgB,GAChBF,EAAmBsD,GACnBnD,EAAS,MACTC,EAAW,MAEX,IAEE,MAAMkC,EAAQjL,aAAaC,QAAQ,cAC7BN,QAAiBC,MACrB,6CACA,CACEwB,OAAQ,OACRvB,QAAS,CACPC,cAAc,UAADC,OAAYkL,GACzB,eAAgB,oBAElB5J,KAAMC,KAAKC,UAAU,CACnB0K,YACAd,OAAQnC,GAAgB,KACxBoC,WAAW,GAADrL,OAAKwG,OAAO8E,SAASC,OAAM,sCACrCC,UAAU,GAADxL,OAAKwG,OAAO8E,SAASC,OAAM,8BAKpCE,QAAe7L,EAASW,OAC9B,IAAKkL,EAAOjL,QACV,MAAM,IAAIH,MAAMoL,EAAOhM,OAAS,qCAIlC+G,OAAO8E,SAASzE,KAAI,qCAAA7G,OAAwCyL,EAAOnL,KAAKqL,UAC1E,CAAE,MAAOlM,GACPwC,QAAQxC,MAAM,iCAAkCA,GAChDsJ,EAAS,8DACX,CAAC,QACCD,GAAgB,GAChBF,EAAmB,KACrB,CAxCA,MAFEG,EAAS,kEALTA,EAAS,yDA6ZoBoD,CAA4BL,EAAItD,IAC/CnB,SAAUwB,KAAqB,OAAJU,QAAI,IAAJA,GAAAA,EAAM0B,OACjC9D,QAAQ,UACRC,KAAK,KACLvE,UAAU,SAAQC,SAEjB+F,GAAgBF,IAAoBmD,EAAItD,GACrC,gBACK,OAAJe,QAAI,IAAJA,GAAAA,EAAM0B,MACS,OAAAjL,OACT8L,EAAIrB,MADX,uBApCHqB,EAAItD,WA+CnB5F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SACpC,kBAAduG,EACG,wBACA,gCAENzG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,8BAA6BC,SAAC,mBAC5CS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,uJAMvCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,8BAA6BC,SAC1B,kBAAduG,EACG,iBACA,kBAEN9F,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SACnB,kBAAduG,EACG,wGACA,oHChiBL+C,EAAoDjK,IAG1D,IAH2D,MAChE5C,EAAK,eACLD,GACD6C,EACC,MAAMkK,GAAYC,EAAAA,EAAAA,SAAQ,KAExB,MAAMC,EAAYhF,MAAMiF,KAAK,CAAE1L,OAAQ,GAAK,CAAC0G,EAAGC,KAC9C,MAAME,EAAO,IAAI1C,KAEjB,OADA0C,EAAK8E,QAAQ9E,EAAK+E,WAAa,EAAIjF,IAC5B,CACLE,KAAMA,EAAK1B,mBAAmB,QAAS,CAAE0G,QAAS,UAClDC,SAAUjF,EAAKZ,cAAcC,MAAM,KAAK,GACxCvG,QAAS,KAcb,OATS,OAALlB,QAAK,IAALA,GAAAA,EAAOsN,YAActF,MAAMuF,QAAQvN,EAAMsN,aAC3CtN,EAAMsN,WAAWE,QAAQC,IACvB,MAAMC,EAAWV,EAAUW,UAAUC,GAAOA,EAAIP,WAAaI,EAAMrF,OACjD,IAAdsF,IACFV,EAAUU,GAAUxM,QAAUuM,EAAMvM,WAKnC8L,GACN,CAAM,OAALhN,QAAK,IAALA,OAAK,EAALA,EAAOsN,aAELO,EAAWhI,KAAKiI,OAAOhB,EAAUrG,IAAIgE,GAAKA,EAAEvJ,SAAU,GACtD6M,EAAmBjB,EAAUkB,OAAO,CAACC,EAAKL,IAAQK,EAAML,EAAI1M,QAAS,GACrEgN,EAAoBH,EAAmB,EAGvCI,EAAYrB,EAAUsB,MAAM,EAAG,GAAGJ,OAAO,CAACC,EAAKL,IAAQK,EAAML,EAAI1M,QAAS,GAAK,EAC/EmN,EAAavB,EAAUsB,MAAM,GAAGJ,OAAO,CAACC,EAAKL,IAAQK,EAAML,EAAI1M,QAAS,GAAK,EAC7EoN,EAAQD,EAAaF,EAAY,KAAOE,EAAaF,EAAY,OAAS,SAC1EI,EAAkBJ,EAAY,EAAItI,KAAKC,KAAMuI,EAAaF,GAAaA,EAAa,KAAO,EAEjG,OACE9K,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,kBACjDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,oBAGvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,CAC/B,OAAV+K,GACCtK,EAAAA,EAAAA,KAACG,EAAAA,IAAY,CAACb,UAAU,2BACZ,SAAVgL,GACFtK,EAAAA,EAAAA,KAACwK,EAAAA,IAAc,CAAClL,UAAU,yBACxB,KAEO,WAAVgL,IACCjL,EAAAA,EAAAA,MAAA,QAAMC,UAAS,uBAAA7C,OACH,OAAV6N,EAAiB,iBAAmB,gBACnC/K,SAAA,CACAgL,EAAgBnD,QAAQ,GAAG,cAOpCpH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,OAAMC,UACnBS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gDAA+CC,SAC3DuJ,EAAUrG,IAAI,CAACmH,EAAKzF,KACnB,MAAMsG,EAASZ,EAAW,EAAKD,EAAI1M,QAAU2M,EAAY,IAAM,EAE/D,OACExK,EAAAA,EAAAA,MAAA,OAAoBC,UAAU,oCAAmCC,SAAA,EAC/DS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,kCAAiCC,UAC9CS,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAE+K,OAAQ,GACnB5K,QAAS,CAAE4K,OAAO,GAADhO,OAAKgO,EAAM,MAC5B3K,WAAY,CAAEC,SAAU,GAAKO,MAAe,GAAR6D,GACpC7E,UAAU,8FACVoL,MAAO,CAAEC,UAAWF,EAAS,EAAI,MAAQ,OAAQlL,UAGjDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,qHAAoHC,UACjIF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6GAA4GC,SAAA,CACxHqK,EAAI1M,QAAQ,qBAMrB8C,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAEqK,EAAIxF,SAlBrCwF,EAAIxF,aA0BtB/E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gCAA+BC,SAAEwK,KAChD/J,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,0BAI3CS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gCAA+BC,SAAE2K,EAAkB9C,QAAQ,MAC1EpH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,2BAM7CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,sCAAqCC,SAAC,sBACpDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SAClB,OAALvD,QAAK,IAALA,GAAAA,EAAO4O,iBAAmBC,OAAOC,QAAQ9O,EAAM4O,kBAAkBR,MAAM,EAAG,GAAG3H,IAAIsI,IAA2B,IAAzBC,EAAW9N,GAAQ6N,EACrG,MAAME,EAA8B,OAAdlP,QAAc,IAAdA,OAAc,EAAdA,EAAgB0L,KAAKyD,GAAQA,EAAKvI,iBAAmBqI,GACrEG,EAAcF,EAAgB/N,EAAU+N,EAAcG,sBAAwB,EAEpF,OACE/L,EAAAA,EAAAA,MAAA,OAAqBC,UAAU,4CAA2CC,SAAA,EACxES,EAAAA,EAAAA,KAAA,QAAMV,UAAU,2BAA0BC,SACvCyL,EAAUxK,QAAQ,KAAM,QAE3BnB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,aAAYC,SAAA,CACzB4L,EAAY,oBALPH,MAUZhL,EAAAA,EAAAA,KAAA,OAAKV,UAAU,yCAAwCC,SAAC,qCC5H9D8L,EAA2B,CAC/B,CACEpG,GAAI,WACJqG,MAAO,WACPC,KAAMC,EAAAA,IACN5I,YAAa,oCAEf,CACEqC,GAAI,UACJqG,MAAO,sBACPC,KAAM9K,EAAAA,IACNmC,YAAa,mCAEf,CACEqC,GAAI,WACJqG,MAAO,cACPC,KAAME,EAAAA,IACN7I,YAAa,gCAIX8I,EAA2BC,IAC/B,OAAQA,GACN,IAAK,kBACH,MAAO,iBACT,IAAK,uBACH,MAAO,aACT,IAAK,qBACH,MAAO,mBACT,QACE,OACEA,EAAcnL,QAAQ,KAAM,KAAKA,QAAQ,aAAc,IAAIoL,OAAS,MAK/DC,EAAwBA,KACnC,MAAO/F,EAAWC,IAAgBjF,EAAAA,EAAAA,UAAS,aACrC,KAAEkF,IAASC,EAAAA,EAAAA,MACX,QACJpK,EAAO,aACPC,EAAY,eACZC,EAAc,MACdC,EAAK,UACLC,EAAS,MACTC,EAAK,aACLC,EAAY,kBACZiB,EAAiB,oBACjBM,EAAmB,WACnBC,EAAU,WACVW,GACE7C,KAEJqQ,EAAAA,EAAAA,WAAU,KAER3P,IACAiB,IACAM,IACAC,KACC,CAACxB,EAAciB,EAAmBM,EAAqBC,IAE1D,MAUMoO,EAAoBA,KACxB1M,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBS,EAAAA,EAAAA,KAACrB,EAAa,CACZ9C,QAASA,EACTgD,UAAc,OAAJmH,QAAI,IAAJA,OAAI,EAAJA,EAAMgG,oBAAqB,OACrC/P,UAAWA,IAIZD,IACCqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDS,EAAAA,EAAAA,KAAC6I,EAAgB,CAAC7M,MAAOA,EAAOD,eAAgBA,KAEhDsD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,qBAGtDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SAClB,OAALvD,QAAK,IAALA,GAAAA,EAAO4O,iBACNC,OAAOC,QAAQ9O,EAAM4O,kBAAkBnI,IACrC7D,IAAA,IAAEoM,EAAW9N,GAAQ0B,EAAA,OACnBS,EAAAA,EAAAA,MAAA,OAEEC,UAAU,oCAAmCC,SAAA,EAE7CS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,2BAA0BC,SACvCyL,EAAUxK,QAAQ,KAAM,QAE3BnB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,yBAAwBC,SAAA,CACrCrC,EAAQ,gBAPN8N,MAaXhL,EAAAA,EAAAA,KAAA,OAAKV,UAAU,iCAAgCC,SAAC,qCAU1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,kBACtDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uDAAsDC,UACpD,OAAdxD,QAAc,IAAdA,OAAc,EAAdA,EAAgBwB,QAAS,EACxBxB,EAAe0G,IAAKyI,IAClB7L,EAAAA,EAAAA,MAAA,OAEEC,UAAU,uEAAsEC,SAAA,EAEhFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,yCAAwCC,SACnD2L,EAAKvI,eAAenC,QAAQ,KAAM,QAErCnB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,8BACxBD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,iCAAgCC,SAAA,CAAC,cACnC2L,EAAKE,sBAAuB,IACvCM,EAAwBR,EAAKvI,wBAV7BuI,EAAKvI,kBAgBd3C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,+CAA8CC,SAAC,6CAqCxE,OACES,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,qCAAoCC,SAAC,aACnDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,gBAAeC,SAAC,0CAG/BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,8BAA6BC,UAC1CF,EAAAA,EAAAA,MAAC+C,EAAAA,EAAM,CACLC,QA/HUjG,UACpBkC,UACME,QAAQC,IAAI,CAChBtC,IACAiB,IACAM,IACAC,OA0HQiG,QAAQ,YACRE,SAAU7H,EAAUsD,SAAA,EAEpBS,EAAAA,EAAAA,KAACiM,EAAAA,IAAS,CACR3M,UAAS,gBAAA7C,OAAkBR,EAAY,eAAiB,MACxD,kBAOPC,IACCmD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAACkM,EAAAA,IAAmB,CAAC5M,UAAU,0BAC/BU,EAAAA,EAAAA,KAAA,QAAMV,UAAU,2BAA0BC,SAAC,cAE7CS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,oBAAmBC,SAAErD,KAClC8D,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CACLC,QAAS/D,EACTsF,QAAQ,YACRC,KAAK,KACLvE,UAAU,OAAMC,SACjB,gBAMLF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sEAAqEC,UAClFS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SACvB8L,EAAW5I,IAAK0J,IACf,MAAMC,EAAOD,EAAIZ,KACXc,EAAWvG,IAAcqG,EAAIlH,GAEnC,OACE5F,EAAAA,EAAAA,MAAA,UAEEgD,QAASA,IAAM0D,EAAaoG,EAAIlH,IAChC3F,UAAS,6KAAA7C,OAIL4P,EACI,kEACA,8DAA6D,4BAEnE9M,SAAA,EAEFS,EAAAA,EAAAA,KAACoM,EAAI,CAAC9M,UAAU,aAChBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,oBAAmBC,SAAE4M,EAAIb,SACzCtL,EAAAA,EAAAA,KAAA,QAAMV,UAAU,uCAAsCC,SACnD4M,EAAIvJ,mBAhBJuJ,EAAIlH,aA2BrBjF,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAG2M,EAAG,IAC1BzM,QAAS,CAAEF,QAAS,EAAG2M,EAAG,GAC1BxM,WAAY,CAAEC,SAAU,IAAMR,SAnGpBgN,MACpB,OAAQzG,GACN,IAAK,WAML,QACE,OAAOiG,IALT,IAAK,UACH,OAnBJ/L,EAAAA,EAAAA,KAACU,EAAa,CACZ5E,aAAcA,EACdG,UAAWA,EACX0E,WAAYA,IAAMvD,EAAkB,GAAItB,EAAayB,UAiBrD,IAAK,WACH,OAbJyC,EAAAA,EAAAA,KAACkF,EAAc,CACbC,eAAgBtJ,EAChBgD,UAAc,OAAJmH,QAAI,IAAJA,OAAI,EAAJA,EAAMgG,oBAAqB,oBAyG5BO,IALIzG,a,2CCtRZ,MAAMe,EAA+B,CAC1C,CACE5B,GAAI,OACJiC,KAAM,OACNC,MAAO,EACPjK,QAAS,GACTmK,SAAU,CACR,uBACA,sBACA,8BACA,oBACA,mBAEFmF,mBAAehP,GAEjB,CACEyH,GAAI,gBACJiC,KAAM,gBACNC,MAAO,GACPjK,QAAS,IACTmK,SAAU,CACR,wBACA,yBACA,+BACA,gBACA,qBACA,wBAEFmF,cAAe,kCAEjB,CACEvH,GAAI,YACJiC,KAAM,YACNC,MAAO,GACPjK,QAAS,IACTmK,SAAU,CACR,wBACA,wBACA,+BACA,mBACA,qBACA,oBACA,0BAEFJ,WAAW,EACXuF,cAAe,kCAEjB,CACEvH,GAAI,eACJiC,KAAM,eACNC,MAAO,IACPjK,QAAS,IACTmK,SAAU,CACR,wBACA,wBACA,+BACA,mBACA,qBACA,oBACA,qBACA,cAEFmF,cAAe,kCAEjB,CACEvH,GAAI,cACJiC,KAAM,cACNC,MAAO,IACPjK,QAAS,KACTmK,SAAU,CACR,0BACA,wBACA,4BACA,oBACA,qBACA,oBACA,qBACA,aACA,uBAEFmF,cAAe,mCAuONlE,EAAmC,CAC9C,CACErD,GAAI,eACJiC,KAAM,eACNhK,QAAS,GACTiK,MAAO,GACPuB,eAAgB,GAChB9F,YAAa,+BACb4J,cAAe,kCAEjB,CACEvH,GAAI,aACJiC,KAAM,aACNhK,QAAS,IACTiK,MAAO,GACPuB,eAAgB,IAChB9F,YAAa,6BACb4J,cAAe,kCAEjB,CACEvH,GAAI,aACJiC,KAAM,aACNhK,QAAS,IACTiK,MAAO,GACPuB,eAAgB,IAChB9F,YAAa,kBACb4J,cAAe,kCAEjB,CACEvH,GAAI,YACJiC,KAAM,YACNhK,QAAS,IACTiK,MAAO,IACPuB,eAAgB,GAChB9F,YAAa,kCACb4J,cAAe,kC", "sources": ["stores/creditStore.ts", "components/credits/CreditBalance.tsx", "components/credits/CreditHistory.tsx", "components/credits/CreditPurchase.tsx", "components/credits/CreditUsageChart.tsx", "pages/CreditsPage.tsx", "shared/constants.ts"], "sourcesContent": ["import { create } from \"zustand\";\nimport { CreditTransaction, AIOperationCost } from \"../shared/types\";\n\ninterface CreditStats {\n  totalUsed: number;\n  totalPurchased: number;\n  usageByOperation: Record<string, number>;\n  dailyUsage: Array<{ date: string; credits: number }>;\n}\n\ninterface CreditState {\n  balance: number;\n  transactions: CreditTransaction[];\n  operationCosts: AIOperationCost[];\n  stats: CreditStats | null;\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  fetchBalance: () => Promise<void>;\n  fetchTransactions: (limit?: number, offset?: number) => Promise<void>;\n  fetchOperationCosts: () => Promise<void>;\n  fetchStats: (days?: number) => Promise<void>;\n  purchaseCredits: (packageData: {\n    amount: number;\n    credits: number;\n    price: number;\n    email: string;\n    name?: string;\n    discountCode?: string;\n  }) => Promise<{\n    success: boolean;\n    error?: string;\n    clientSecret?: string;\n    paymentIntentId?: string;\n  }>;\n  refreshAfterPurchase: () => Promise<void>;\n  clearError: () => void;\n}\n\nexport const useCreditStore = create<CreditState>((set, get) => ({\n  balance: 0,\n  transactions: [],\n  operationCosts: [],\n  stats: null,\n  isLoading: false,\n  error: null,\n\n  fetchBalance: async () => {\n    set({ isLoading: true, error: null });\n    try {\n      const response = await fetch(\"/api/credits/balance\", {\n        headers: {\n          Authorization: `Bearer ${\n            localStorage.getItem(\"auth_token\") ||\n            sessionStorage.getItem(\"auth_token\")\n          }`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch credit balance\");\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        set({ balance: data.data.credits, isLoading: false });\n      } else {\n        throw new Error(data.error || \"Failed to fetch balance\");\n      }\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : \"Unknown error\",\n        isLoading: false,\n      });\n    }\n  },\n\n  fetchTransactions: async (limit = 50, offset = 0) => {\n    set({ isLoading: true, error: null });\n    try {\n      const response = await fetch(\n        `/api/credits/history?limit=${limit}&offset=${offset}`,\n        {\n          headers: {\n            Authorization: `Bearer ${\n              localStorage.getItem(\"auth_token\") ||\n              sessionStorage.getItem(\"auth_token\")\n            }`,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch credit history\");\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        set({\n          transactions:\n            offset === 0 ? data.data : [...get().transactions, ...data.data],\n          isLoading: false,\n        });\n      } else {\n        throw new Error(data.error || \"Failed to fetch transactions\");\n      }\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : \"Unknown error\",\n        isLoading: false,\n      });\n    }\n  },\n\n  fetchOperationCosts: async () => {\n    set({ isLoading: true, error: null });\n    try {\n      const response = await fetch(\"/api/credits/pricing\", {\n        headers: {\n          Authorization: `Bearer ${\n            localStorage.getItem(\"auth_token\") ||\n            sessionStorage.getItem(\"auth_token\")\n          }`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch operation costs\");\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        set({ operationCosts: data.data, isLoading: false });\n      } else {\n        throw new Error(data.error || \"Failed to fetch operation costs\");\n      }\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : \"Unknown error\",\n        isLoading: false,\n      });\n    }\n  },\n\n  fetchStats: async (days = 30) => {\n    set({ isLoading: true, error: null });\n    try {\n      const response = await fetch(`/api/credits/stats?days=${days}`, {\n        headers: {\n          Authorization: `Bearer ${\n            localStorage.getItem(\"auth_token\") ||\n            sessionStorage.getItem(\"auth_token\")\n          }`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch credit stats\");\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        set({ stats: data.data, isLoading: false });\n      } else {\n        throw new Error(data.error || \"Failed to fetch stats\");\n      }\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : \"Unknown error\",\n        isLoading: false,\n      });\n    }\n  },\n\n  purchaseCredits: async (packageData: {\n    amount: number;\n    credits: number;\n    price: number;\n    email: string;\n    name?: string;\n    discountCode?: string;\n  }) => {\n    set({ isLoading: true, error: null });\n    try {\n      const response = await fetch(\"/api/credits/purchase\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${\n            localStorage.getItem(\"auth_token\") ||\n            sessionStorage.getItem(\"auth_token\")\n          }`,\n        },\n        body: JSON.stringify(packageData),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to initiate credit purchase\");\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        set({ isLoading: false });\n        return {\n          success: true,\n          clientSecret: data.data.clientSecret,\n          paymentIntentId: data.data.paymentIntentId,\n        };\n      } else {\n        throw new Error(data.error || \"Failed to purchase credits\");\n      }\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : \"Unknown error\";\n      set({\n        error: errorMessage,\n        isLoading: false,\n      });\n      return { success: false, error: errorMessage };\n    }\n  },\n\n  clearError: () => set({ error: null }),\n\n  refreshAfterPurchase: async () => {\n    try {\n      await Promise.all([get().fetchBalance(), get().fetchTransactions()]);\n    } catch (error) {\n      console.error(\"Failed to refresh data after purchase:\", error);\n    }\n  },\n}));\n", "import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { \r\n  HiCreditCard, \r\n  HiTrendingUp, \r\n  HiClock,\r\n  HiSparkles\r\n} from 'react-icons/hi';\r\n\r\ninterface CreditBalanceProps {\r\n  balance: number;\r\n  userTier: string;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport const CreditBalance: React.FC<CreditBalanceProps> = ({\r\n  balance,\r\n  userTier,\r\n  isLoading\r\n}) => {\r\n  const getTierColor = (tier: string) => {\r\n    switch (tier.toLowerCase()) {\r\n      case 'pro':\r\n        return 'text-purple-400';\r\n      case 'basic':\r\n        return 'text-blue-400';\r\n      default:\r\n        return 'text-gray-400';\r\n    }\r\n  };\r\n\r\n  const getTierIcon = (tier: string) => {\r\n    switch (tier.toLowerCase()) {\r\n      case 'pro':\r\n        return <HiSparkles className=\"w-5 h-5\" />;\r\n      case 'basic':\r\n        return <HiTrendingUp className=\"w-5 h-5\" />;\r\n      default:\r\n        return <HiCreditCard className=\"w-5 h-5\" />;\r\n    }\r\n  };\r\n\r\n  const getBalanceStatus = (balance: number) => {\r\n    if (balance >= 100) return { color: 'text-green-400', status: 'Excellent' };\r\n    if (balance >= 50) return { color: 'text-yellow-400', status: 'Good' };\r\n    if (balance >= 10) return { color: 'text-orange-400', status: 'Low' };\r\n    return { color: 'text-red-400', status: 'Critical' };\r\n  };\r\n\r\n  const balanceStatus = getBalanceStatus(balance);\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n      {/* Main Balance Card */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n        className=\"md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30\"\r\n      >\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"p-3 bg-primary-500/20 rounded-lg\">\r\n              <HiCreditCard className=\"w-6 h-6 text-primary-400\" />\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-white\">Credit Balance</h3>\r\n              <p className=\"text-gray-400 text-sm\">Available for AI generation</p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className={`flex items-center space-x-2 ${getTierColor(userTier)}`}>\r\n            {getTierIcon(userTier)}\r\n            <span className=\"font-medium\">{userTier} Plan</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-end space-x-4\">\r\n          <div>\r\n            {isLoading ? (\r\n              <div className=\"animate-pulse\">\r\n                <div className=\"h-12 w-32 bg-gray-600 rounded\"></div>\r\n              </div>\r\n            ) : (\r\n              <motion.div\r\n                initial={{ scale: 0.8 }}\r\n                animate={{ scale: 1 }}\r\n                transition={{ duration: 0.3, delay: 0.2 }}\r\n              >\r\n                <span className=\"text-4xl font-bold text-white\">{balance.toLocaleString()}</span>\r\n                <span className=\"text-xl text-gray-400 ml-2\">credits</span>\r\n              </motion.div>\r\n            )}\r\n          </div>\r\n          \r\n          <div className={`flex items-center space-x-1 ${balanceStatus.color} mb-2`}>\r\n            <div className={`w-2 h-2 rounded-full ${balanceStatus.color.replace('text-', 'bg-')}`}></div>\r\n            <span className=\"text-sm font-medium\">{balanceStatus.status}</span>\r\n          </div>\r\n        </div>\r\n\r\n        {balance < 10 && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ delay: 0.5 }}\r\n            className=\"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg\"\r\n          >\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiClock className=\"w-4 h-4 text-red-400\" />\r\n              <span className=\"text-red-400 text-sm font-medium\">Low Balance Warning</span>\r\n            </div>\r\n            <p className=\"text-red-300 text-sm mt-1\">\r\n              Consider purchasing more credits to continue using AI features.\r\n            </p>\r\n          </motion.div>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* Quick Stats Card */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5, delay: 0.1 }}\r\n        className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\"\r\n      >\r\n        <h4 className=\"text-lg font-semibold text-white mb-4\">Quick Stats</h4>\r\n        \r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-gray-400 text-sm\">Plan Type</span>\r\n            <span className={`font-medium ${getTierColor(userTier)}`}>{userTier}</span>\r\n          </div>\r\n          \r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-gray-400 text-sm\">Status</span>\r\n            <span className={`font-medium ${balanceStatus.color}`}>{balanceStatus.status}</span>\r\n          </div>\r\n          \r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-gray-400 text-sm\">Credits Available</span>\r\n            <span className=\"text-white font-medium\">{balance}</span>\r\n          </div>\r\n\r\n          {userTier.toLowerCase() === 'free' && (\r\n            <div className=\"pt-3 border-t border-border-secondary\">\r\n              <p className=\"text-gray-400 text-xs\">\r\n                Upgrade to Basic or Pro for more credits and features\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  Hi<PERSON>lock,\r\n  HiPlus,\r\n  HiMinus,\r\n  HiChevronDown,\r\n  HiDownload\r\n} from 'react-icons/hi';\r\nimport { CreditTransaction } from '../../../../shared/types';\r\nimport { Button } from '../common/Button';\r\n\r\ninterface CreditHistoryProps {\r\n  transactions: CreditTransaction[];\r\n  isLoading: boolean;\r\n  onLoadMore: () => void;\r\n}\r\n\r\nexport const CreditHistory: React.FC<CreditHistoryProps> = ({\r\n  transactions,\r\n  isLoading,\r\n  onLoadMore\r\n}) => {\r\n  const [filter, setFilter] = useState<'all' | 'used' | 'purchased'>('all');\r\n  const [sortBy, setSortBy] = useState<'date' | 'amount'>('date');\r\n\r\n  const getTransactionIcon = (creditsUsed: number) => {\r\n    return creditsUsed > 0 ? (\r\n      <HiMinus className=\"w-4 h-4 text-red-400\" />\r\n    ) : (\r\n      <HiPlus className=\"w-4 h-4 text-green-400\" />\r\n    );\r\n  };\r\n\r\n  const getTransactionColor = (creditsUsed: number) => {\r\n    return creditsUsed > 0 ? 'text-red-400' : 'text-green-400';\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    return {\r\n      date: date.toLocaleDateString(),\r\n      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\r\n    };\r\n  };\r\n\r\n  const filteredTransactions = transactions.filter(transaction => {\r\n    if (filter === 'used') return transaction.credits_used > 0;\r\n    if (filter === 'purchased') return transaction.credits_used < 0;\r\n    return true;\r\n  });\r\n\r\n  const sortedTransactions = [...filteredTransactions].sort((a, b) => {\r\n    if (sortBy === 'date') {\r\n      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n    } else {\r\n      return Math.abs(b.credits_used) - Math.abs(a.credits_used);\r\n    }\r\n  });\r\n\r\n  const exportTransactions = () => {\r\n    const csvContent = [\r\n      ['Date', 'Type', 'Credits', 'Operation', 'Description'].join(','),\r\n      ...sortedTransactions.map(transaction => [\r\n        new Date(transaction.created_at).toLocaleDateString(),\r\n        transaction.credits_used > 0 ? 'Used' : 'Purchased',\r\n        Math.abs(transaction.credits_used),\r\n        transaction.operation_type,\r\n        `\"${transaction.description}\"`\r\n      ].join(','))\r\n    ].join('\\n');\r\n\r\n    const blob = new Blob([csvContent], { type: 'text/csv' });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = `credit-history-${new Date().toISOString().split('T')[0]}.csv`;\r\n    a.click();\r\n    window.URL.revokeObjectURL(url);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold text-white\">Transaction History</h3>\r\n          <p className=\"text-gray-400 text-sm\">\r\n            {filteredTransactions.length} of {transactions.length} transactions\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* Filter Dropdown */}\r\n          <div className=\"relative\">\r\n            <select\r\n              value={filter}\r\n              onChange={(e) => setFilter(e.target.value as 'all' | 'used' | 'purchased')}\r\n              className=\"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500\"\r\n            >\r\n              <option value=\"all\">All Transactions</option>\r\n              <option value=\"used\">Credits Used</option>\r\n              <option value=\"purchased\">Credits Purchased</option>\r\n            </select>\r\n            <HiChevronDown className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\" />\r\n          </div>\r\n\r\n          {/* Sort Dropdown */}\r\n          <div className=\"relative\">\r\n            <select\r\n              value={sortBy}\r\n              onChange={(e) => setSortBy(e.target.value as 'date' | 'amount')}\r\n              className=\"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500\"\r\n            >\r\n              <option value=\"date\">Sort by Date</option>\r\n              <option value=\"amount\">Sort by Amount</option>\r\n            </select>\r\n            <HiChevronDown className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\" />\r\n          </div>\r\n\r\n          {/* Export Button */}\r\n          <Button\r\n            onClick={exportTransactions}\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            disabled={transactions.length === 0}\r\n          >\r\n            <HiDownload className=\"w-4 h-4 mr-2\" />\r\n            Export\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Transaction List */}\r\n      <div className=\"space-y-3\">\r\n        {isLoading && transactions.length === 0 ? (\r\n          <div className=\"space-y-3\">\r\n            {[...Array(5)].map((_, i) => (\r\n              <div key={i} className=\"animate-pulse\">\r\n                <div className=\"bg-background-tertiary rounded-lg p-4\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-8 h-8 bg-gray-600 rounded-full\"></div>\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"h-4 bg-gray-600 rounded w-1/3 mb-2\"></div>\r\n                      <div className=\"h-3 bg-gray-600 rounded w-1/2\"></div>\r\n                    </div>\r\n                    <div className=\"h-6 bg-gray-600 rounded w-16\"></div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : sortedTransactions.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <HiClock className=\"w-12 h-12 text-gray-500 mx-auto mb-4\" />\r\n            <h4 className=\"text-lg font-medium text-gray-400 mb-2\">No Transactions Found</h4>\r\n            <p className=\"text-gray-500\">\r\n              {filter === 'all' \r\n                ? \"You haven't made any credit transactions yet.\"\r\n                : `No ${filter} transactions found.`\r\n              }\r\n            </p>\r\n          </div>\r\n        ) : (\r\n          sortedTransactions.map((transaction, index) => {\r\n            const { date, time } = formatDate(transaction.created_at);\r\n            const isCredit = transaction.credits_used < 0;\r\n            \r\n            return (\r\n              <motion.div\r\n                key={transaction.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.3, delay: index * 0.05 }}\r\n                className=\"bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors\"\r\n              >\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className={`p-2 rounded-full ${isCredit ? 'bg-green-500/20' : 'bg-red-500/20'}`}>\r\n                      {getTransactionIcon(transaction.credits_used)}\r\n                    </div>\r\n                    \r\n                    <div>\r\n                      <h4 className=\"font-medium text-white\">\r\n                        {transaction.operation_type.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                      </h4>\r\n                      <p className=\"text-gray-400 text-sm\">{transaction.description}</p>\r\n                      <div className=\"flex items-center space-x-2 mt-1\">\r\n                        <span className=\"text-gray-500 text-xs\">{date}</span>\r\n                        <span className=\"text-gray-600\">•</span>\r\n                        <span className=\"text-gray-500 text-xs\">{time}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"text-right\">\r\n                    <span className={`font-semibold ${getTransactionColor(transaction.credits_used)}`}>\r\n                      {isCredit ? '+' : '-'}{Math.abs(transaction.credits_used)} credits\r\n                    </span>\r\n                    {transaction.study_set_id && (\r\n                      <p className=\"text-gray-500 text-xs mt-1\">Study Set</p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            );\r\n          })\r\n        )}\r\n      </div>\r\n\r\n      {/* Load More Button */}\r\n      {transactions.length > 0 && transactions.length % 50 === 0 && (\r\n        <div className=\"mt-6 text-center\">\r\n          <Button\r\n            onClick={onLoadMore}\r\n            variant=\"secondary\"\r\n            isLoading={isLoading}\r\n            disabled={isLoading}\r\n          >\r\n            Load More Transactions\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport {\n  HiCreditCard,\n  HiSparkles,\n  HiCheck,\n  HiStar,\n  HiLightningBolt,\n  HiAcademicCap,\n} from \"react-icons/hi\";\nimport { Button } from \"../common/Button\";\nimport useAuthStore from \"../../stores/authStore\";\nimport { PRICING_TIERS, CREDIT_PACKAGES } from \"../../shared/constants\";\nimport type { PricingTier } from \"../../shared/types\";\n\ninterface CreditPurchaseProps {\n  currentBalance: number;\n  userTier: string;\n}\n\nexport const CreditPurchase: React.FC<CreditPurchaseProps> = ({\n  currentBalance,\n  userTier,\n}) => {\n  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [discountCode, setDiscountCode] = useState<string>(\"\");\n  const [showDiscountInput, setShowDiscountInput] = useState(false);\n  const [activeTab, setActiveTab] = useState<\"subscriptions\" | \"credits\">(\n    \"subscriptions\"\n  );\n  const { user } = useAuthStore();\n\n  const handleSubscriptionPurchase = async (tierId: string) => {\n    const selectedTier = PRICING_TIERS.find((tier) => tier.id === tierId);\n    if (!selectedTier) {\n      setError(\"Selected subscription tier not found. Please try again.\");\n      return;\n    }\n\n    if (!user?.email) {\n      setError(\"User email not available. Please log out and log back in.\");\n      return;\n    }\n\n    setIsProcessing(true);\n    setSelectedPackage(tierId);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      // Create Stripe Checkout session for subscription\n      const token =\n        localStorage.getItem(\"auth_token\") ||\n        sessionStorage.getItem(\"auth_token\");\n\n      if (!token) {\n        throw new Error(\"Please log in to purchase a subscription\");\n      }\n\n      const response = await fetch(\"/api/stripe/create-checkout-session\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          planId: tierId.toLowerCase().replace(/\\s+/g, \"_\"),\n          coupon: discountCode || null,\n          successUrl: `${window.location.origin}/credits?success=true&type=subscription`,\n          cancelUrl: `${window.location.origin}/credits?canceled=true`,\n        }),\n      });\n\n      const result = await response.json();\n\n      // Handle authentication errors specifically\n      if (response.status === 401) {\n        // Clear invalid token and redirect to login\n        localStorage.removeItem(\"auth_token\");\n        sessionStorage.removeItem(\"auth_token\");\n        throw new Error(\"Your session has expired. Please log in again.\");\n      }\n\n      if (!result.success) {\n        throw new Error(result.error || \"Failed to create checkout session\");\n      }\n\n      // Redirect to Stripe Checkout\n      window.location.href = `https://checkout.stripe.com/c/pay/${result.data.sessionId}`;\n    } catch (error) {\n      console.error(\"Subscription error:\", error);\n      setError(\"Failed to create subscription checkout. Please try again.\");\n    } finally {\n      setIsProcessing(false);\n      setSelectedPackage(null);\n    }\n  };\n\n  const handleCreditPackagePurchase = async (packageId: string) => {\n    const selectedPackage = CREDIT_PACKAGES.find((pkg) => pkg.id === packageId);\n    if (!selectedPackage) {\n      setError(\"Selected credit package not found. Please try again.\");\n      return;\n    }\n\n    if (!user?.email) {\n      setError(\"User email not available. Please log out and log back in.\");\n      return;\n    }\n\n    setIsProcessing(true);\n    setSelectedPackage(packageId);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      // Create Stripe Checkout session for credit package\n      const token = localStorage.getItem(\"auth_token\");\n      const response = await fetch(\n        \"/api/stripe/create-credit-package-checkout\",\n        {\n          method: \"POST\",\n          headers: {\n            Authorization: `Bearer ${token}`,\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify({\n            packageId,\n            coupon: discountCode || null,\n            successUrl: `${window.location.origin}/credits?success=true&type=credits`,\n            cancelUrl: `${window.location.origin}/credits?canceled=true`,\n          }),\n        }\n      );\n\n      const result = await response.json();\n      if (!result.success) {\n        throw new Error(result.error || \"Failed to create checkout session\");\n      }\n\n      // Redirect to Stripe Checkout\n      window.location.href = `https://checkout.stripe.com/c/pay/${result.data.sessionId}`;\n    } catch (error) {\n      console.error(\"Credit package purchase error:\", error);\n      setError(\"Failed to create credit package checkout. Please try again.\");\n    } finally {\n      setIsProcessing(false);\n      setSelectedPackage(null);\n    }\n  };\n\n  const getTierIcon = (tierId: string) => {\n    switch (tierId) {\n      case \"Study Starter\":\n        return <HiAcademicCap className=\"w-6 h-6\" />;\n      case \"Study Pro\":\n        return <HiStar className=\"w-6 h-6\" />;\n      case \"Study Master\":\n        return <HiLightningBolt className=\"w-6 h-6\" />;\n      case \"Study Elite\":\n        return <HiSparkles className=\"w-6 h-6\" />;\n      default:\n        return <HiCreditCard className=\"w-6 h-6\" />;\n    }\n  };\n\n  const isCurrentTier = (tierId: string) => {\n    return userTier === tierId;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-red-900/20 border border-red-500/50 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg\n                className=\"h-5 w-5 text-red-400\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-400\">Error</h3>\n              <div className=\"mt-1 text-sm text-red-300\">{error}</div>\n            </div>\n            <div className=\"ml-auto pl-3\">\n              <button\n                onClick={() => setError(null)}\n                className=\"inline-flex rounded-md bg-red-900/20 p-1.5 text-red-400 hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-900\"\n              >\n                <span className=\"sr-only\">Dismiss</span>\n                <svg\n                  className=\"h-4 w-4\"\n                  viewBox=\"0 0 20 20\"\n                  fill=\"currentColor\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Success Display */}\n      {success && (\n        <div className=\"bg-green-900/20 border border-green-500/50 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg\n                className=\"h-5 w-5 text-green-400\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-green-400\">Success</h3>\n              <div className=\"mt-1 text-sm text-green-300\">{success}</div>\n            </div>\n            <div className=\"ml-auto pl-3\">\n              <button\n                onClick={() => setSuccess(null)}\n                className=\"inline-flex rounded-md bg-green-900/20 p-1.5 text-green-400 hover:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-green-900\"\n              >\n                <span className=\"sr-only\">Dismiss</span>\n                <svg\n                  className=\"h-4 w-4\"\n                  viewBox=\"0 0 20 20\"\n                  fill=\"currentColor\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Current Balance Display */}\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-white\">\n              Current Plan: {userTier}\n            </h3>\n            <p className=\"text-gray-400\">Your available credits</p>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"text-2xl font-bold text-primary-400\">\n              {currentBalance}\n            </span>\n            <span className=\"text-gray-400 ml-2\">credits</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"flex space-x-1 bg-background-tertiary rounded-lg p-1\">\n        <button\n          onClick={() => setActiveTab(\"subscriptions\")}\n          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n            activeTab === \"subscriptions\"\n              ? \"bg-primary-500 text-white\"\n              : \"text-gray-400 hover:text-white\"\n          }`}\n        >\n          Monthly Plans\n        </button>\n        <button\n          onClick={() => setActiveTab(\"credits\")}\n          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n            activeTab === \"credits\"\n              ? \"bg-primary-500 text-white\"\n              : \"text-gray-400 hover:text-white\"\n          }`}\n        >\n          One-Time Credits\n        </button>\n      </div>\n\n      {/* Discount Code Section */}\n      {activeTab === \"credits\" && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-border-primary mb-6\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h4 className=\"text-md font-medium text-white\">\n              Have a discount code?\n            </h4>\n            <button\n              onClick={() => setShowDiscountInput(!showDiscountInput)}\n              className=\"text-sm text-primary-400 hover:text-primary-300 transition-colors\"\n            >\n              {showDiscountInput ? \"Hide\" : \"Enter Code\"}\n            </button>\n          </div>\n\n          {showDiscountInput && (\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"text\"\n                value={discountCode}\n                onChange={(e) => setDiscountCode(e.target.value.toUpperCase())}\n                placeholder=\"Enter discount code\"\n                className=\"flex-1 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                maxLength={20}\n              />\n              <button\n                onClick={() => {\n                  setDiscountCode(\"\");\n                  setShowDiscountInput(false);\n                }}\n                className=\"px-3 py-2 text-gray-400 hover:text-white transition-colors\"\n              >\n                Clear\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Subscription Plans */}\n      {activeTab === \"subscriptions\" && (\n        <div>\n          <h3 className=\"text-lg font-semibold text-white mb-2\">\n            Choose Your Study Plan\n          </h3>\n          <p className=\"text-gray-400 mb-6\">\n            Monthly subscriptions with automatic credit refills. Cancel anytime.\n          </p>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {PRICING_TIERS.filter((tier) => tier.id !== \"Free\").map(\n              (tier: PricingTier, index: number) => {\n                const isSelected = selectedPackage === tier.id;\n                const isProcessingThis = isProcessing && isSelected;\n                const isCurrent = isCurrentTier(tier.id);\n\n                return (\n                  <motion.div\n                    key={tier.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                    className={`\n                    relative bg-background-secondary rounded-lg p-6 border transition-all duration-200\n                    ${\n                      tier.isPopular\n                        ? \"border-primary-500 ring-2 ring-primary-500/20\"\n                        : \"border-border-primary hover:border-border-secondary\"\n                    }\n                    ${isSelected ? \"ring-2 ring-primary-500/50\" : \"\"}\n                    ${isCurrent ? \"ring-2 ring-green-500/50\" : \"\"}\n                  `}\n                  >\n                    {/* Popular Badge */}\n                    {tier.isPopular && (\n                      <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                        <div className=\"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                          Most Popular\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Current Plan Badge */}\n                    {isCurrent && (\n                      <div className=\"absolute -top-3 right-4\">\n                        <div className=\"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                          Current Plan\n                        </div>\n                      </div>\n                    )}\n\n                    <div className=\"text-center\">\n                      {/* Icon */}\n                      <div\n                        className={`inline-flex p-3 rounded-lg mb-4 ${\n                          tier.isPopular\n                            ? \"bg-primary-500/20 text-primary-400\"\n                            : \"bg-background-tertiary text-gray-400\"\n                        }`}\n                      >\n                        {getTierIcon(tier.id)}\n                      </div>\n\n                      {/* Tier Name */}\n                      <h4 className=\"text-lg font-semibold text-white mb-2\">\n                        {tier.name}\n                      </h4>\n\n                      {/* Credits */}\n                      <div className=\"mb-4\">\n                        <span className=\"text-3xl font-bold text-white\">\n                          {tier.credits}\n                        </span>\n                        <div className=\"text-gray-400 text-sm\">\n                          credits/month\n                        </div>\n                        <div className=\"text-green-400 text-xs\">\n                          ~{tier.credits * 5} flashcards/quizzes\n                        </div>\n                      </div>\n\n                      {/* Price */}\n                      <div className=\"mb-4\">\n                        <span className=\"text-2xl font-bold text-white\">\n                          ${tier.price}\n                        </span>\n                        <div className=\"text-gray-400 text-sm\">/month</div>\n                        <div className=\"text-gray-400 text-xs\">\n                          ${(tier.price / tier.credits).toFixed(3)} per credit\n                        </div>\n                      </div>\n\n                      {/* Features */}\n                      <div className=\"space-y-2 mb-6\">\n                        {tier.features.map(\n                          (feature: string, featureIndex: number) => (\n                            <div\n                              key={featureIndex}\n                              className=\"flex items-center text-sm text-gray-300\"\n                            >\n                              <HiCheck className=\"w-4 h-4 text-green-400 mr-2 flex-shrink-0\" />\n                              <span>{feature}</span>\n                            </div>\n                          )\n                        )}\n                      </div>\n\n                      {/* Subscribe Button */}\n                      <Button\n                        onClick={() => handleSubscriptionPurchase(tier.id)}\n                        variant={tier.isPopular ? \"primary\" : \"secondary\"}\n                        className=\"w-full\"\n                        isLoading={isProcessingThis}\n                        disabled={isProcessing || !user?.email || isCurrent}\n                      >\n                        {isProcessingThis\n                          ? \"Processing...\"\n                          : isCurrent\n                          ? \"Current Plan\"\n                          : !user?.email\n                          ? \"Login Required\"\n                          : `Subscribe to ${tier.name}`}\n                      </Button>\n                    </div>\n                  </motion.div>\n                );\n              }\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* One-Time Credit Packages */}\n      {activeTab === \"credits\" && (\n        <div>\n          <h3 className=\"text-lg font-semibold text-white mb-2\">\n            One-Time Credit Packages\n          </h3>\n          <p className=\"text-gray-400 mb-6\">\n            Purchase credits that never expire. Perfect for occasional use.\n          </p>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {CREDIT_PACKAGES.map((pkg) => (\n              <motion.div\n                key={pkg.id}\n                className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary hover:border-primary-500 transition-colors\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                    <HiCreditCard className=\"w-6 h-6 text-primary-400\" />\n                  </div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">\n                    {pkg.name}\n                  </h4>\n                  <div className=\"mb-4\">\n                    <div className=\"text-3xl font-bold text-white\">\n                      ${pkg.price}\n                    </div>\n                    <div className=\"text-sm text-gray-400\">\n                      {pkg.credits} credits\n                    </div>\n                    <div className=\"text-xs text-primary-400\">\n                      ${pkg.valuePerCredit.toFixed(2)}/credit\n                    </div>\n                  </div>\n                  <p className=\"text-gray-400 text-sm mb-6\">\n                    {pkg.description}\n                  </p>\n                  <Button\n                    onClick={() => handleCreditPackagePurchase(pkg.id)}\n                    disabled={isProcessing || !user?.email}\n                    variant=\"primary\"\n                    size=\"sm\"\n                    className=\"w-full\"\n                  >\n                    {isProcessing && selectedPackage === pkg.id\n                      ? \"Processing...\"\n                      : !user?.email\n                      ? \"Login Required\"\n                      : `Buy ${pkg.name}`}\n                  </Button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Additional Information */}\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n        <h4 className=\"text-lg font-semibold text-white mb-4\">\n          {activeTab === \"subscriptions\"\n            ? \"Subscription Benefits\"\n            : \"One-Time Purchase Benefits\"}\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h5 className=\"font-medium text-white mb-2\">Safe & Secure</h5>\n            <p className=\"text-gray-400 text-sm\">\n              Student-safe payments through Stripe. Your payment info is never\n              stored. Perfect for using your student card or parent's card with\n              permission.\n            </p>\n          </div>\n          <div>\n            <h5 className=\"font-medium text-white mb-2\">\n              {activeTab === \"subscriptions\"\n                ? \"Cancel Anytime\"\n                : \"Never Expire\"}\n            </h5>\n            <p className=\"text-gray-400 text-sm\">\n              {activeTab === \"subscriptions\"\n                ? \"No long-term commitments. Cancel your subscription anytime and keep using credits until they run out.\"\n                : \"One-time credit purchases never expire - perfect for semester planning. Use them at your own pace!\"}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { HiTrendingUp, HiTrendingDown } from 'react-icons/hi';\r\nimport { AIOperationCost } from '../../../../shared/types';\r\n\r\ninterface CreditStats {\r\n  totalUsed: number;\r\n  totalPurchased: number;\r\n  usageByOperation: Record<string, number>;\r\n  dailyUsage: Array<{ date: string; credits: number }>;\r\n}\r\n\r\ninterface CreditUsageChartProps {\r\n  stats: CreditStats;\r\n  operationCosts: AIOperationCost[];\r\n}\r\n\r\nexport const CreditUsageChart: React.FC<CreditUsageChartProps> = ({\r\n  stats,\r\n  operationCosts\r\n}) => {\r\n  const chartData = useMemo(() => {\r\n    // Get last 7 days of usage data\r\n    const last7Days = Array.from({ length: 7 }, (_, i) => {\r\n      const date = new Date();\r\n      date.setDate(date.getDate() - (6 - i));\r\n      return {\r\n        date: date.toLocaleDateString('en-US', { weekday: 'short' }),\r\n        fullDate: date.toISOString().split('T')[0],\r\n        credits: 0\r\n      };\r\n    });\r\n\r\n    // Map actual usage data to the 7-day structure (with defensive programming)\r\n    if (stats?.dailyUsage && Array.isArray(stats.dailyUsage)) {\r\n      stats.dailyUsage.forEach(usage => {\r\n        const dayIndex = last7Days.findIndex(day => day.fullDate === usage.date);\r\n        if (dayIndex !== -1) {\r\n          last7Days[dayIndex].credits = usage.credits;\r\n        }\r\n      });\r\n    }\r\n\r\n    return last7Days;\r\n  }, [stats?.dailyUsage]);\r\n\r\n  const maxUsage = Math.max(...chartData.map(d => d.credits), 1);\r\n  const totalWeeklyUsage = chartData.reduce((sum, day) => sum + day.credits, 0);\r\n  const averageDailyUsage = totalWeeklyUsage / 7;\r\n\r\n  // Calculate trend (comparing first half vs second half of the week)\r\n  const firstHalf = chartData.slice(0, 3).reduce((sum, day) => sum + day.credits, 0) / 3;\r\n  const secondHalf = chartData.slice(4).reduce((sum, day) => sum + day.credits, 0) / 3;\r\n  const trend = secondHalf > firstHalf ? 'up' : secondHalf < firstHalf ? 'down' : 'stable';\r\n  const trendPercentage = firstHalf > 0 ? Math.abs(((secondHalf - firstHalf) / firstHalf) * 100) : 0;\r\n\r\n  return (\r\n    <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold text-white\">Usage Trends</h3>\r\n          <p className=\"text-gray-400 text-sm\">Last 7 days</p>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {trend === 'up' ? (\r\n            <HiTrendingUp className=\"w-5 h-5 text-green-400\" />\r\n          ) : trend === 'down' ? (\r\n            <HiTrendingDown className=\"w-5 h-5 text-red-400\" />\r\n          ) : null}\r\n          \r\n          {trend !== 'stable' && (\r\n            <span className={`text-sm font-medium ${\r\n              trend === 'up' ? 'text-green-400' : 'text-red-400'\r\n            }`}>\r\n              {trendPercentage.toFixed(1)}%\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Chart */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-end justify-between h-32 space-x-2\">\r\n          {chartData.map((day, index) => {\r\n            const height = maxUsage > 0 ? (day.credits / maxUsage) * 100 : 0;\r\n            \r\n            return (\r\n              <div key={day.date} className=\"flex-1 flex flex-col items-center\">\r\n                <div className=\"w-full flex justify-center mb-2\">\r\n                  <motion.div\r\n                    initial={{ height: 0 }}\r\n                    animate={{ height: `${height}%` }}\r\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                    className=\"w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group\"\r\n                    style={{ minHeight: height > 0 ? '4px' : '0px' }}\r\n                  >\r\n                    {/* Tooltip */}\r\n                    <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                      <div className=\"bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap\">\r\n                        {day.credits} credits\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                </div>\r\n                \r\n                <span className=\"text-xs text-gray-400\">{day.date}</span>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Summary */}\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        <div className=\"bg-background-tertiary rounded-lg p-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-white\">{totalWeeklyUsage}</div>\r\n            <div className=\"text-gray-400 text-sm\">Total This Week</div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"bg-background-tertiary rounded-lg p-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-white\">{averageDailyUsage.toFixed(1)}</div>\r\n            <div className=\"text-gray-400 text-sm\">Daily Average</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Usage Efficiency */}\r\n      <div className=\"mt-4 pt-4 border-t border-border-secondary\">\r\n        <h4 className=\"text-sm font-medium text-white mb-3\">Usage Efficiency</h4>\r\n        <div className=\"space-y-2\">\r\n          {stats?.usageByOperation ? Object.entries(stats.usageByOperation).slice(0, 3).map(([operation, credits]) => {\r\n            const operationCost = operationCosts?.find(cost => cost.operation_type === operation);\r\n            const generations = operationCost ? credits * operationCost.operations_per_credit : 0;\r\n\r\n            return (\r\n              <div key={operation} className=\"flex justify-between items-center text-sm\">\r\n                <span className=\"text-gray-300 capitalize\">\r\n                  {operation.replace(/_/g, ' ')}\r\n                </span>\r\n                <span className=\"text-white\">\r\n                  {generations} generations\r\n                </span>\r\n              </div>\r\n            );\r\n          }) : (\r\n            <div className=\"text-gray-400 text-sm text-center py-2\">\r\n              No usage data available\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect, useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport {\n  HiCreditCard,\n  Hi<PERSON><PERSON><PERSON>cyDollar,\n  Hi<PERSON>hartBar,\n  <PERSON><PERSON><PERSON>,\n  HiRefresh,\n  HiExclamationCircle,\n} from \"react-icons/hi\";\nimport { useCreditStore } from \"../stores/creditStore\";\nimport { useAuthStore } from \"../stores/authStore\";\nimport { Button } from \"../components/common/Button\";\nimport { CreditBalance } from \"../components/credits/CreditBalance\";\nimport { CreditHistory } from \"../components/credits/CreditHistory\";\nimport { CreditPurchase } from \"../components/credits/CreditPurchase\";\nimport { CreditUsageChart } from \"../components/credits/CreditUsageChart\";\n\ninterface TabSection {\n  id: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description: string;\n}\n\nconst creditTabs: TabSection[] = [\n  {\n    id: \"overview\",\n    label: \"Overview\",\n    icon: HiChartBar,\n    description: \"Credit balance and usage summary\",\n  },\n  {\n    id: \"history\",\n    label: \"Transaction History\",\n    icon: Hi<PERSON><PERSON>,\n    description: \"Detailed credit transaction log\",\n  },\n  {\n    id: \"purchase\",\n    label: \"Buy Credits\",\n    icon: HiCurrencyDollar,\n    description: \"Purchase additional credits\",\n  },\n];\n\nconst getOperationDisplayName = (operationType: string): string => {\n  switch (operationType) {\n    case \"quiz_generation\":\n      return \"quiz questions\";\n    case \"flashcard_generation\":\n      return \"flashcards\";\n    case \"additional_content\":\n      return \"flex generations\";\n    default:\n      return (\n        operationType.replace(/_/g, \" \").replace(\"generation\", \"\").trim() + \"s\"\n      );\n  }\n};\n\nexport const CreditsPage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState(\"overview\");\n  const { user } = useAuthStore();\n  const {\n    balance,\n    transactions,\n    operationCosts,\n    stats,\n    isLoading,\n    error,\n    fetchBalance,\n    fetchTransactions,\n    fetchOperationCosts,\n    fetchStats,\n    clearError,\n  } = useCreditStore();\n\n  useEffect(() => {\n    // Fetch initial data when component mounts\n    fetchBalance();\n    fetchTransactions();\n    fetchOperationCosts();\n    fetchStats();\n  }, [fetchBalance, fetchTransactions, fetchOperationCosts, fetchStats]);\n\n  const handleRefresh = async () => {\n    clearError();\n    await Promise.all([\n      fetchBalance(),\n      fetchTransactions(),\n      fetchOperationCosts(),\n      fetchStats(),\n    ]);\n  };\n\n  const renderOverviewTab = () => (\n    <div className=\"space-y-6\">\n      {/* Credit Balance Section */}\n      <CreditBalance\n        balance={balance}\n        userTier={user?.subscription_tier || \"Free\"}\n        isLoading={isLoading}\n      />\n\n      {/* Usage Statistics */}\n      {stats && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <CreditUsageChart stats={stats} operationCosts={operationCosts} />\n\n          <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n            <h3 className=\"text-lg font-semibold text-white mb-4\">\n              Usage Breakdown\n            </h3>\n            <div className=\"space-y-3\">\n              {stats?.usageByOperation ? (\n                Object.entries(stats.usageByOperation).map(\n                  ([operation, credits]) => (\n                    <div\n                      key={operation}\n                      className=\"flex justify-between items-center\"\n                    >\n                      <span className=\"text-gray-300 capitalize\">\n                        {operation.replace(/_/g, \" \")}\n                      </span>\n                      <span className=\"text-white font-medium\">\n                        {credits} credits\n                      </span>\n                    </div>\n                  )\n                )\n              ) : (\n                <div className=\"text-gray-400 text-center py-4\">\n                  No usage data available\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Operation Costs */}\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Credit Costs</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {operationCosts?.length > 0 ? (\n            operationCosts.map((cost) => (\n              <div\n                key={cost.operation_type}\n                className=\"bg-background-tertiary rounded-lg p-4 border border-border-secondary\"\n              >\n                <h4 className=\"font-medium text-white capitalize mb-2\">\n                  {cost.operation_type.replace(/_/g, \" \")}\n                </h4>\n                <div className=\"flex items-center space-x-2\">\n                  <HiCreditCard className=\"w-4 h-4 text-primary-400\" />\n                  <span className=\"text-primary-400 font-semibold\">\n                    1 credit = {cost.operations_per_credit}{\" \"}\n                    {getOperationDisplayName(cost.operation_type)}\n                  </span>\n                </div>\n              </div>\n            ))\n          ) : (\n            <div className=\"col-span-full text-gray-400 text-center py-8\">\n              No operation cost data available\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderHistoryTab = () => (\n    <CreditHistory\n      transactions={transactions}\n      isLoading={isLoading}\n      onLoadMore={() => fetchTransactions(50, transactions.length)}\n    />\n  );\n\n  const renderPurchaseTab = () => (\n    <CreditPurchase\n      currentBalance={balance}\n      userTier={user?.subscription_tier || \"Study Starter\"}\n    />\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case \"overview\":\n        return renderOverviewTab();\n      case \"history\":\n        return renderHistoryTab();\n      case \"purchase\":\n        return renderPurchaseTab();\n      default:\n        return renderOverviewTab();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background-primary text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex justify-between items-center mb-8\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-white mb-2\">Credits</h1>\n            <p className=\"text-gray-400\">Manage your AI generation credits</p>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              onClick={handleRefresh}\n              variant=\"secondary\"\n              disabled={isLoading}\n            >\n              <HiRefresh\n                className={`w-4 h-4 mr-2 ${isLoading ? \"animate-spin\" : \"\"}`}\n              />\n              Refresh\n            </Button>\n          </div>\n        </div>\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\n              <span className=\"text-red-400 font-medium\">Error</span>\n            </div>\n            <p className=\"text-red-300 mt-1\">{error}</p>\n            <Button\n              onClick={clearError}\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"mt-2\"\n            >\n              Dismiss\n            </Button>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Tab Navigation */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n              <nav className=\"space-y-2\">\n                {creditTabs.map((tab) => {\n                  const Icon = tab.icon;\n                  const isActive = activeTab === tab.id;\n\n                  return (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\n                        transition-all duration-200\n                        ${\n                          isActive\n                            ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\"\n                            : \"text-gray-300 hover:bg-background-tertiary hover:text-white\"\n                        }\n                      `}\n                    >\n                      <Icon className=\"w-5 h-5\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <span className=\"font-medium block\">{tab.label}</span>\n                        <span className=\"text-xs text-gray-500 block truncate\">\n                          {tab.description}\n                        </span>\n                      </div>\n                    </button>\n                  );\n                })}\n              </nav>\n            </div>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"lg:col-span-3\">\n            <motion.div\n              key={activeTab}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              {renderContent()}\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "// ----------------------------------------\n// Pricing & Subscription Constants\n// ----------------------------------------\n\nimport { PricingTier, SubscriptionTier } from \"./types\";\n\nexport const PRICING_TIERS: PricingTier[] = [\n  {\n    id: \"Free\",\n    name: \"Free\",\n    price: 0,\n    credits: 25,\n    features: [\n      \"25 credits per month\",\n      \"Basic AI generation\",\n      \"Document upload (up to 5MB)\",\n      \"Community support\",\n      \"Basic analytics\",\n    ],\n    stripePriceId: undefined,\n  },\n  {\n    id: \"Study Starter\",\n    name: \"Study Starter\",\n    price: 29,\n    credits: 150,\n    features: [\n      \"150 credits per month\",\n      \"Advanced AI generation\",\n      \"Document upload (up to 10MB)\",\n      \"Email support\",\n      \"Detailed analytics\",\n      \"Export functionality\",\n    ],\n    stripePriceId: \"price_1RgeXe7lNlVY1bMulc5SRF9z\", // Updated to recurring price\n  },\n  {\n    id: \"Study Pro\",\n    name: \"Study Pro\",\n    price: 59,\n    credits: 350,\n    features: [\n      \"350 credits per month\",\n      \"Premium AI generation\",\n      \"Document upload (up to 25MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Collaboration features\",\n    ],\n    isPopular: true,\n    stripePriceId: \"price_1RgeXe7lNlVY1bMuWT9HTvnr\", // Updated to recurring price\n  },\n  {\n    id: \"Study Master\",\n    name: \"Study Master\",\n    price: 119,\n    credits: 750,\n    features: [\n      \"750 credits per month\",\n      \"Premium AI generation\",\n      \"Document upload (up to 50MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Team collaboration\",\n      \"API access\",\n    ],\n    stripePriceId: \"price_1RgeXe7lNlVY1bMuPU6N3CYa\", // Updated to recurring price\n  },\n  {\n    id: \"Study Elite\",\n    name: \"Study Elite\",\n    price: 239,\n    credits: 1500,\n    features: [\n      \"1,500 credits per month\",\n      \"Premium AI generation\",\n      \"Unlimited document upload\",\n      \"Dedicated support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Team collaboration\",\n      \"API access\",\n      \"White-label options\",\n    ],\n    stripePriceId: \"price_1RgeXe7lNlVY1bMuzHc8Fxmg\", // Updated to recurring price\n  },\n];\n\n// ----------------------------------------\n// Credit System Constants\n// ----------------------------------------\n\nexport const CREDIT_COSTS = {\n  FLASHCARD_GENERATION: 1, // 1 credit per 5 flashcards\n  QUIZ_GENERATION: 1, // 1 credit per 5 quiz questions\n  FLEX_GENERATION: 1, // 1 credit per 5 flex items\n  DOCUMENT_PROCESSING: 0, // Free document processing\n} as const;\n\nexport const ITEMS_PER_CREDIT = {\n  FLASHCARDS: 5,\n  QUIZ_QUESTIONS: 5,\n  FLEX_ITEMS: 5,\n} as const;\n\n// ----------------------------------------\n// Subscription Limits\n// ----------------------------------------\n\nexport const SUBSCRIPTION_LIMITS = {\n  Free: {\n    maxDocumentSize: 5 * 1024 * 1024, // 5MB\n    maxDocuments: 10,\n    maxStudySets: 5,\n    creditsPerMonth: 25,\n  },\n  \"Study Starter\": {\n    maxDocumentSize: 10 * 1024 * 1024, // 10MB\n    maxDocuments: 50,\n    maxStudySets: 25,\n    creditsPerMonth: 150,\n  },\n  \"Study Pro\": {\n    maxDocumentSize: 25 * 1024 * 1024, // 25MB\n    maxDocuments: 200,\n    maxStudySets: 100,\n    creditsPerMonth: 350,\n  },\n  \"Study Master\": {\n    maxDocumentSize: 50 * 1024 * 1024, // 50MB\n    maxDocuments: 500,\n    maxStudySets: 250,\n    creditsPerMonth: 750,\n  },\n  \"Study Elite\": {\n    maxDocumentSize: 100 * 1024 * 1024, // 100MB (effectively unlimited)\n    maxDocuments: 1000,\n    maxStudySets: 500,\n    creditsPerMonth: 1500,\n  },\n} as const;\n\n// ----------------------------------------\n// Feature Flags\n// ----------------------------------------\n\nexport const FEATURE_FLAGS = {\n  Free: {\n    advancedAnalytics: false,\n    exportFunctionality: false,\n    prioritySupport: false,\n    customStudySets: false,\n    collaboration: false,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Starter\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: false,\n    customStudySets: false,\n    collaboration: false,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Pro\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Master\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: true,\n    whiteLabelOptions: false,\n  },\n  \"Study Elite\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: true,\n    whiteLabelOptions: true,\n  },\n} as const;\n\n// ----------------------------------------\n// Helper Functions\n// ----------------------------------------\n\nexport const getPricingTier = (\n  tier: SubscriptionTier\n): PricingTier | undefined => {\n  return PRICING_TIERS.find((t) => t.id === tier);\n};\n\nexport const getSubscriptionLimits = (tier: SubscriptionTier) => {\n  return SUBSCRIPTION_LIMITS[tier];\n};\n\nexport const getFeatureFlags = (tier: SubscriptionTier) => {\n  return FEATURE_FLAGS[tier];\n};\n\nexport const hasFeature = (\n  tier: SubscriptionTier,\n  feature: keyof typeof FEATURE_FLAGS.Free\n): boolean => {\n  return FEATURE_FLAGS[tier][feature];\n};\n\nexport const canUploadDocument = (\n  tier: SubscriptionTier,\n  fileSize: number\n): boolean => {\n  const limits = getSubscriptionLimits(tier);\n  return fileSize <= limits.maxDocumentSize;\n};\n\nexport const getRemainingDocuments = (\n  tier: SubscriptionTier,\n  currentCount: number\n): number => {\n  const limits = getSubscriptionLimits(tier);\n  return Math.max(0, limits.maxDocuments - currentCount);\n};\n\nexport const getRemainingStudySets = (\n  tier: SubscriptionTier,\n  currentCount: number\n): number => {\n  const limits = getSubscriptionLimits(tier);\n  return Math.max(0, limits.maxStudySets - currentCount);\n};\n\n// ----------------------------------------\n// Stripe Configuration\n// ----------------------------------------\n\nexport const STRIPE_CONFIG = {\n  publishableKey: \"NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY\",\n  webhookSecret: \"STRIPE_WEBHOOK_SECRET\",\n  successUrl: \"FRONTEND_URL\" + \"/subscription/success\",\n  cancelUrl: \"FRONTEND_URL\" + \"/subscription/cancel\",\n} as const;\n\n// ----------------------------------------\n// API Endpoints\n// ----------------------------------------\n\nexport const API_ENDPOINTS = {\n  // Authentication\n  LOGIN: \"/api/auth/login\",\n  REGISTER: \"/api/auth/register\",\n  LOGOUT: \"/api/auth/logout\",\n  REFRESH: \"/api/auth/refresh\",\n\n  // User Management\n  PROFILE: \"/api/user/profile\",\n  UPDATE_PROFILE: \"/api/user/update\",\n\n  // Subscription Management\n  CREATE_SUBSCRIPTION: \"/api/subscription/create\",\n  CANCEL_SUBSCRIPTION: \"/api/subscription/cancel\",\n  UPDATE_SUBSCRIPTION: \"/api/subscription/update\",\n  GET_SUBSCRIPTION: \"/api/subscription/status\",\n\n  // Payment Processing\n  CREATE_PAYMENT_INTENT: \"/api/payment/create-intent\",\n  CONFIRM_PAYMENT: \"/api/payment/confirm\",\n  WEBHOOK: \"/api/payment/webhook\",\n\n  // Credit Management\n  GET_CREDITS: \"/api/credits/balance\",\n  PURCHASE_CREDITS: \"/api/credits/purchase\",\n  CREDIT_HISTORY: \"/api/credits/history\",\n\n  // Document Management\n  UPLOAD_DOCUMENT: \"/api/documents/upload\",\n  GET_DOCUMENTS: \"/api/documents\",\n  DELETE_DOCUMENT: \"/api/documents/delete\",\n\n  // Study Set Management\n  CREATE_STUDY_SET: \"/api/study-sets/create\",\n  GET_STUDY_SETS: \"/api/study-sets\",\n  DELETE_STUDY_SET: \"/api/study-sets/delete\",\n\n  // AI Generation\n  GENERATE_FLASHCARDS: \"/api/ai/generate-flashcards\",\n  GENERATE_QUIZ: \"/api/ai/generate-quiz\",\n  GENERATE_FLEX: \"/api/ai/generate-flex\",\n} as const;\n\n// ----------------------------------------\n// One-Time Credit Packages\n// ----------------------------------------\n\nexport interface CreditPackage {\n  id: string;\n  name: string;\n  credits: number;\n  price: number;\n  valuePerCredit: number;\n  description: string;\n  stripePriceId?: string;\n}\n\nexport const CREDIT_PACKAGES: CreditPackage[] = [\n  {\n    id: \"starter_pack\",\n    name: \"Starter Pack\",\n    credits: 50,\n    price: 15,\n    valuePerCredit: 0.3,\n    description: \"Perfect for testing features\",\n    stripePriceId: \"price_1Rge2c7lNlVY1bMu8AobMuTT\", // Live price ID\n  },\n  {\n    id: \"boost_pack\",\n    name: \"Boost Pack\",\n    credits: 150,\n    price: 39,\n    valuePerCredit: 0.26,\n    description: \"Great for exam preparation\",\n    stripePriceId: \"price_1Rge2m7lNlVY1bMuJywdctDG\", // Live price ID\n  },\n  {\n    id: \"power_pack\",\n    name: \"Power Pack\",\n    credits: 400,\n    price: 89,\n    valuePerCredit: 0.22,\n    description: \"For heavy users\",\n    stripePriceId: \"price_1Rge2w7lNlVY1bMufspAzvdn\", // Live price ID\n  },\n  {\n    id: \"mega_pack\",\n    name: \"Mega Pack\",\n    credits: 1000,\n    price: 199,\n    valuePerCredit: 0.2,\n    description: \"Maximum value for long-term use\",\n    stripePriceId: \"price_1Rge357lNlVY1bMuuzBtmOHM\", // Live price ID\n  },\n];\n"], "names": ["useCreditStore", "create", "set", "get", "balance", "transactions", "operationCosts", "stats", "isLoading", "error", "fetchBalance", "async", "response", "fetch", "headers", "Authorization", "concat", "localStorage", "getItem", "sessionStorage", "ok", "Error", "data", "json", "success", "credits", "message", "fetchTransactions", "limit", "arguments", "length", "undefined", "offset", "fetchOperationCosts", "fetchStats", "days", "purchaseCredits", "method", "body", "JSON", "stringify", "packageData", "clientSecret", "paymentIntentId", "errorMessage", "clearError", "refreshAfterPurchase", "Promise", "all", "console", "CreditBalance", "_ref", "userTier", "getTierColor", "tier", "toLowerCase", "balanceStatus", "color", "status", "getBalanceStatus", "_jsxs", "className", "children", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "_jsx", "HiCreditCard", "HiSparkles", "HiTrendingUp", "getTierIcon", "scale", "delay", "toLocaleString", "replace", "<PERSON><PERSON><PERSON>", "CreditHistory", "onLoadMore", "filter", "setFilter", "useState", "sortBy", "setSortBy", "getTransactionColor", "creditsUsed", "filteredTransactions", "transaction", "credits_used", "sortedTransactions", "sort", "a", "b", "Date", "created_at", "getTime", "Math", "abs", "value", "onChange", "e", "target", "HiChevronDown", "<PERSON><PERSON>", "onClick", "exportTransactions", "csv<PERSON><PERSON>nt", "join", "map", "toLocaleDateString", "operation_type", "description", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "document", "createElement", "href", "download", "toISOString", "split", "click", "revokeObjectURL", "variant", "size", "disabled", "HiDownload", "Array", "_", "i", "index", "date", "time", "dateString", "toLocaleTimeString", "hour", "minute", "formatDate", "isCredit", "HiMinus", "HiPlus", "l", "toUpperCase", "study_set_id", "id", "CreditPurchase", "currentBalance", "selected<PERSON><PERSON><PERSON>", "setSelectedPackage", "isProcessing", "setIsProcessing", "setError", "setSuccess", "discountCode", "setDiscountCode", "showDiscountInput", "setShowDiscountInput", "activeTab", "setActiveTab", "user", "useAuthStore", "tierId", "HiAcademicCap", "HiStar", "HiLightningBolt", "viewBox", "fill", "fillRule", "d", "clipRule", "placeholder", "max<PERSON><PERSON><PERSON>", "PRICING_TIERS", "isSelected", "isProcessingThis", "isCurrent", "isPopular", "name", "price", "toFixed", "features", "feature", "featureIndex", "<PERSON><PERSON><PERSON><PERSON>", "find", "email", "token", "planId", "coupon", "successUrl", "location", "origin", "cancelUrl", "result", "removeItem", "sessionId", "handleSubscriptionPurchase", "CREDIT_PACKAGES", "pkg", "whileHover", "whileTap", "valuePerCredit", "packageId", "handleCreditPackagePurchase", "CreditUsageChart", "chartData", "useMemo", "last7Days", "from", "setDate", "getDate", "weekday", "fullDate", "dailyUsage", "isArray", "for<PERSON>ach", "usage", "dayIndex", "findIndex", "day", "maxUsage", "max", "totalWeeklyUsage", "reduce", "sum", "averageDailyUsage", "firstHalf", "slice", "secondHalf", "trend", "trendPercentage", "HiTrendingDown", "height", "style", "minHeight", "usageByOperation", "Object", "entries", "_ref2", "operation", "operationCost", "cost", "generations", "operations_per_credit", "creditTabs", "label", "icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getOperationDisplayName", "operationType", "trim", "CreditsPage", "useEffect", "renderOverviewTab", "subscription_tier", "HiRefresh", "HiExclamationCircle", "tab", "Icon", "isActive", "x", "renderContent", "stripePriceId"], "sourceRoot": ""}