# Use Node.js 18 Alpine for smaller image size
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/ ./shared/

# Install dependencies (production only for cost efficiency)
RUN npm ci --only=production --no-audit --no-fund
RUN cd backend && npm ci --only=production --no-audit --no-fund

# Copy source code
COPY backend/src ./backend/src
COPY backend/tsconfig.json ./backend/

# Build the application
RUN cd backend && npm run build

# Remove dev dependencies and source files to reduce image size
RUN rm -rf backend/src backend/tsconfig.json
RUN npm prune --production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:4000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["node", "backend/dist/index.js"]

