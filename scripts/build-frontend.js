#!/usr/bin/env node

const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

async function buildFrontend() {
  log("🏗️  Building ChewyAI Frontend", colors.bright + colors.cyan);
  log("================================", colors.cyan);

  const frontendDir = path.join(__dirname, "../frontend");
  const buildDir = path.join(frontendDir, "build");
  const publicDir = path.join(__dirname, "../backend/public");

  try {
    // Build the React app
    log("\n📦 Building React application...", colors.blue);
    const buildProcess = spawn("npm", ["run", "build"], {
      cwd: frontendDir,
      stdio: "inherit",
      shell: true,
    });

    await new Promise((resolve, reject) => {
      buildProcess.on("close", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Build failed with code ${code}`));
        }
      });
      buildProcess.on("error", reject);
    });

    log("✅ React build completed", colors.green);

    // Copy build files to backend public directory
    log("\n📁 Copying build files to backend/public...", colors.blue);
    
    // Remove existing public directory
    if (fs.existsSync(publicDir)) {
      fs.rmSync(publicDir, { recursive: true, force: true });
    }

    // Copy build directory to public
    fs.cpSync(buildDir, publicDir, { recursive: true });

    log("✅ Build files copied successfully", colors.green);
    log(`\n🎉 Frontend build complete! Files are in: ${publicDir}`, colors.bright + colors.green);

  } catch (error) {
    log(`\n❌ Build failed: ${error.message}`, colors.red);
    process.exit(1);
  }
}

// Run the build if this script is executed directly
if (require.main === module) {
  buildFrontend();
}

module.exports = { buildFrontend };
