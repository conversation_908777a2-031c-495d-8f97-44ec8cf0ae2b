{"name": "chewy-ai", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"setup": "npm install && npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:concurrent": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && HOST=0.0.0.0 PORT=3000 DANGEROUSLY_DISABLE_HOST_CHECK=true npm start", "dev:backend": "cd backend && PORT=4000 npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "node scripts/build-frontend.js", "build:backend": "cd backend && npm run build", "serve": "npm run build && concurrently \"npm run serve:frontend\" \"npm run serve:backend\"", "serve:frontend": "cd frontend && PORT=3000 npm run serve", "serve:backend": "cd backend && PORT=3001 npm start", "production": ""}, "devDependencies": {"concurrently": "^8.2.2", "http-proxy-middleware": "^3.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}