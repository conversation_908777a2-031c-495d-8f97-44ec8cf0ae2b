[nix]
channel = "stable-22_11"

[env]
NODE_ENV = "production"
PORT = "3001"
FRONTEND_URL = "http://localhost:3000"

[gitHubImport]
requiredFiles = [".replit", "replit.nix"]

[languages]

[languages.javascript]
pattern = "**/{*.js,*.jsx,*.ts,*.tsx,*.json}"

[languages.javascript.languageServer]
start = "typescript-language-server --stdio"

[deployment]
build = "npm run build"
run = "npm run serve"
deploymentTarget = "cloudrun"

[[ports]]
localPort = 80
externalPort = 3000

[[ports]]
localPort = 3000
externalPort = 80

[[ports]]
localPort = 3001

[[ports]]
localPort = 3002

[[ports]]
localPort = 4000
externalPort = 3001

[[ports]]
localPort = 5000

[[ports]]
localPort = 24678

[workflows]
runButton = "Development"

[[workflows.workflow]]
name = "Development"
author = 15722197
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"
