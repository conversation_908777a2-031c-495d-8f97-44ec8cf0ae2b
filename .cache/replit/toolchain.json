{"runs": [{"id": "language:nix/run", "name": "Please use the Configuration pane to configure your run command", "fileTypeAttrs": {}, "run": {"command": {"args": ["bash", "-c", "printf \"%s\\n\\n%s\" \"$0\" \"$1\"", "Please use the \u001b[1;93mConfiguration\u001b[0m pane to configure your run command.", "For more info, see: https://docs.replit.com/replit-workspace/configuring-repl#run"]}}}], "languageServers": [{"id": ".replit/languageServer:javascript", "name": "Javascript Language Server", "language": "javascript", "fileTypeAttrs": {"filePattern": "**/{*.js,*.jsx,*.ts,*.tsx,*.json}"}, "config": {"startCommand": {"args": ["sh", "-c", "typescript-language-server --stdio"]}}}, {"id": "module:replit/languageServer:dotreplit-lsp", "name": ".replit LSP", "language": "dotreplit", "fileTypeAttrs": {}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/bz8k1njgmm249fr5krhaq1jsi7jrhx5k-taplo-0.patched/bin/taplo lsp -c /nix/store/2zhz6va20gizdlqmvryab9b7pn6dp0v1-taplo-config.toml stdio"]}}}], "formatters": [{"id": "module:replit/languageServer:dotreplit-lsp", "name": ".replit LSP", "fileTypeAttrs": {}}, {"id": ".replit/languageServer:javascript", "name": "Javascript Language Server", "fileTypeAttrs": {"filePattern": "**/{*.js,*.jsx,*.ts,*.tsx,*.json}"}}]}