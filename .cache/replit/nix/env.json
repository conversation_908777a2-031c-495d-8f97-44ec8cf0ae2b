{"entries": {"replit.nix": {"env": {"AR": "ar", "AS": "as", "CC": "gcc", "CONFIG_SHELL": "/nix/store/dsd5gz46hdbdk2rfdimqddhq6m8m8fqs-bash-5.1-p16/bin/bash", "CXX": "g++", "HOST_PATH": "/nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1/bin:/nix/store/sbksq03iwzjpmcjl9v8r359k2jd90hpb-npm-8.19.3/bin:/nix/store/6w8n8fy21yjssg8b1dqkds8diad3v75g-typescript-4.8.4/bin:/nix/store/w8q2bwi6rdhsa1dz9i8mqznddk5l7p26-typescript-language-server-3.3.2/bin:/nix/store/shgk3hhlw60dcgq6qnxs92fr5rb4pa4r-concurrently-7.5.0/bin:/nix/store/ib2vsijn0haj4rmfc9skiycgq5g4w28y-nodemon-2.0.20/bin:/nix/store/18y8p4mjwjy26npih04hfv48wz9czgk3-attr-2.5.1-bin/bin:/nix/store/q8s1kzhsblr2a1p2g0lh2dkcgca1im3z-libcap-2.66/bin:/nix/store/bvihhgw8bh6cqqlj0d4s8cvmm4mqmzfc-pulseaudio-16.1/bin:/nix/store/a7gvj343m05j2s32xcnwr35v31ynlypr-coreutils-9.1/bin:/nix/store/mydc6f4k2z73xlcz7ilif3v2lcaiqvza-findutils-4.9.0/bin:/nix/store/j9p3g8472iijd50vhdprx0nmk2fqn5gv-diffutils-3.8/bin:/nix/store/89zs7rms6x00xfq4dq6m7mjnhkr8a6r4-gnused-4.8/bin:/nix/store/86bp03jkmsl6f92w0yzg4s59g5mhxwmy-gnugrep-3.7/bin:/nix/store/hwcdqw4jrjnd37wxqgsd47hd0j8bnj09-gawk-5.1.1/bin:/nix/store/cfbhw8r8ags41vwqaz47r583d0p4h4a1-gnutar-1.34/bin:/nix/store/p3m1ndl1lapwrlh698bnb5lvvxh67378-gzip-1.12/bin:/nix/store/a8mhcagrsly7c7mpjrpsnaahk4aax056-bzip2-1.0.8-bin/bin:/nix/store/mblgz65m3zv9x548a3d5m96fj2pbwr09-gnumake-4.3/bin:/nix/store/dsd5gz46hdbdk2rfdimqddhq6m8m8fqs-bash-5.1-p16/bin:/nix/store/v7ljksji50mg3w61dykaa3n3y79n6nil-patch-2.7.6/bin:/nix/store/zlcnmqq14jz5x9439jf937mvayyl63da-xz-5.2.7-bin/bin:/nix/store/y6aj732zm9m87c82fpvf103a1xb22blp-file-5.43/bin", "LD": "ld", "LIBGL_DRIVERS_PATH": "/nix/store/q4hrphx851xvkcnyyvqqyf1yjgmkmiar-mesa-22.2.3-drivers/lib/dri", "LOCALE_ARCHIVE": "/usr/lib/locale/locale-archive", "NIX_BINTOOLS": "/nix/store/1d6ian3r8kdzspw8hacjhl3xkp40g1lj-binutils-wrapper-2.39", "NIX_BINTOOLS_WRAPPER_TARGET_HOST_x86_64_unknown_linux_gnu": "1", "NIX_BUILD_CORES": "4", "NIX_BUILD_TOP": "/tmp", "NIX_CC": "/nix/store/dq0xwmsk1g0i2ayg6pb7y87na2knzylh-gcc-wrapper-11.3.0", "NIX_CC_WRAPPER_TARGET_HOST_x86_64_unknown_linux_gnu": "1", "NIX_CFLAGS_COMPILE": " -frandom-seed=3f7hxnmibx -isystem /nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1/include -isystem /nix/store/k4za15psddq0sa3kvy794034hk0hb0ag-libglvnd-1.5.0-dev/include -isystem /nix/store/66vg52x48y0v0qzf4m1sm7jy087qp00y-pulseaudio-16.1-dev/include -isystem /nix/store/n0r5z0w1j3m0z8980hgg2rjkh617v1md-libcap-2.66-dev/include -isystem /nix/store/a4mgvch1zx61xfsixq3dmh6r81p4dgkl-attr-2.5.1-dev/include -isystem /nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1/include -isystem /nix/store/k4za15psddq0sa3kvy794034hk0hb0ag-libglvnd-1.5.0-dev/include -isystem /nix/store/66vg52x48y0v0qzf4m1sm7jy087qp00y-pulseaudio-16.1-dev/include -isystem /nix/store/n0r5z0w1j3m0z8980hgg2rjkh617v1md-libcap-2.66-dev/include -isystem /nix/store/a4mgvch1zx61xfsixq3dmh6r81p4dgkl-attr-2.5.1-dev/include", "NIX_ENFORCE_NO_NATIVE": "1", "NIX_HARDENING_ENABLE": "fortify stackprotector pic strictoverflow format relro bindnow", "NIX_INDENT_MAKE": "1", "NIX_LDFLAGS": "-rpath /nix/store/3f7hxnmibx86v084wih5kq9igm2y5hmd-nix-shell/lib64 -rpath /nix/store/3f7hxnmibx86v084wih5kq9igm2y5hmd-nix-shell/lib  -L/nix/store/q4hrphx851xvkcnyyvqqyf1yjgmkmiar-mesa-22.2.3-drivers/lib -L/nix/store/cxhib72pd7n3fzl1yh6lrm4q16l42kws-libglvnd-1.5.0/lib -L/nix/store/cr5fmwri3601s7724ayjvckhsg6cz4rv-attr-2.5.1/lib -L/nix/store/jijvvmbza698qa6yjgr1ndgdgyryvq1i-libcap-2.66-lib/lib -L/nix/store/bvihhgw8bh6cqqlj0d4s8cvmm4mqmzfc-pulseaudio-16.1/lib -L/nix/store/q4hrphx851xvkcnyyvqqyf1yjgmkmiar-mesa-22.2.3-drivers/lib -L/nix/store/cxhib72pd7n3fzl1yh6lrm4q16l42kws-libglvnd-1.5.0/lib -L/nix/store/cr5fmwri3601s7724ayjvckhsg6cz4rv-attr-2.5.1/lib -L/nix/store/jijvvmbza698qa6yjgr1ndgdgyryvq1i-libcap-2.66-lib/lib -L/nix/store/bvihhgw8bh6cqqlj0d4s8cvmm4mqmzfc-pulseaudio-16.1/lib", "NIX_STORE": "/nix/store", "NM": "nm", "NODE_PATH": "/nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1/lib/node_modules:/nix/store/sbksq03iwzjpmcjl9v8r359k2jd90hpb-npm-8.19.3/lib/node_modules:/nix/store/6w8n8fy21yjssg8b1dqkds8diad3v75g-typescript-4.8.4/lib/node_modules:/nix/store/w8q2bwi6rdhsa1dz9i8mqznddk5l7p26-typescript-language-server-3.3.2/lib/node_modules:/nix/store/shgk3hhlw60dcgq6qnxs92fr5rb4pa4r-concurrently-7.5.0/lib/node_modules:/nix/store/ib2vsijn0haj4rmfc9skiycgq5g4w28y-nodemon-2.0.20/lib/node_modules", "OBJCOPY": "objcopy", "OBJDUMP": "objdu<PERSON>", "PATH": "/nix/store/bap4d0lpcrhpwmpb8ayjcgkmvfj62lnq-bash-interactive-5.1-p16/bin:/nix/store/pr5n59mb4jzmfx6kanwxly0l07p861fg-patchelf-0.15.0/bin:/nix/store/dq0xwmsk1g0i2ayg6pb7y87na2knzylh-gcc-wrapper-11.3.0/bin:/nix/store/1gf2flfqnpqbr1b4p4qz2f72y42bs56r-gcc-11.3.0/bin:/nix/store/57xv61c5zi8pphjbcwxxjlgc34p61ic9-glibc-2.35-163-bin/bin:/nix/store/a7gvj343m05j2s32xcnwr35v31ynlypr-coreutils-9.1/bin:/nix/store/1d6ian3r8kdzspw8hacjhl3xkp40g1lj-binutils-wrapper-2.39/bin:/nix/store/039g378vc3pc3dvi9dzdlrd0i4q93qwf-binutils-2.39/bin:/nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1/bin:/nix/store/sbksq03iwzjpmcjl9v8r359k2jd90hpb-npm-8.19.3/bin:/nix/store/6w8n8fy21yjssg8b1dqkds8diad3v75g-typescript-4.8.4/bin:/nix/store/w8q2bwi6rdhsa1dz9i8mqznddk5l7p26-typescript-language-server-3.3.2/bin:/nix/store/shgk3hhlw60dcgq6qnxs92fr5rb4pa4r-concurrently-7.5.0/bin:/nix/store/ib2vsijn0haj4rmfc9skiycgq5g4w28y-nodemon-2.0.20/bin:/nix/store/18y8p4mjwjy26npih04hfv48wz9czgk3-attr-2.5.1-bin/bin:/nix/store/q8s1kzhsblr2a1p2g0lh2dkcgca1im3z-libcap-2.66/bin:/nix/store/bvihhgw8bh6cqqlj0d4s8cvmm4mqmzfc-pulseaudio-16.1/bin:/nix/store/a7gvj343m05j2s32xcnwr35v31ynlypr-coreutils-9.1/bin:/nix/store/mydc6f4k2z73xlcz7ilif3v2lcaiqvza-findutils-4.9.0/bin:/nix/store/j9p3g8472iijd50vhdprx0nmk2fqn5gv-diffutils-3.8/bin:/nix/store/89zs7rms6x00xfq4dq6m7mjnhkr8a6r4-gnused-4.8/bin:/nix/store/86bp03jkmsl6f92w0yzg4s59g5mhxwmy-gnugrep-3.7/bin:/nix/store/hwcdqw4jrjnd37wxqgsd47hd0j8bnj09-gawk-5.1.1/bin:/nix/store/cfbhw8r8ags41vwqaz47r583d0p4h4a1-gnutar-1.34/bin:/nix/store/p3m1ndl1lapwrlh698bnb5lvvxh67378-gzip-1.12/bin:/nix/store/a8mhcagrsly7c7mpjrpsnaahk4aax056-bzip2-1.0.8-bin/bin:/nix/store/mblgz65m3zv9x548a3d5m96fj2pbwr09-gnumake-4.3/bin:/nix/store/dsd5gz46hdbdk2rfdimqddhq6m8m8fqs-bash-5.1-p16/bin:/nix/store/v7ljksji50mg3w61dykaa3n3y79n6nil-patch-2.7.6/bin:/nix/store/zlcnmqq14jz5x9439jf937mvayyl63da-xz-5.2.7-bin/bin:/nix/store/y6aj732zm9m87c82fpvf103a1xb22blp-file-5.43/bin", "RANLIB": "ranlib", "READELF": "readelf", "REPLIT_LD_LIBRARY_PATH": "/nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1/lib:/nix/store/sbksq03iwzjpmcjl9v8r359k2jd90hpb-npm-8.19.3/lib:/nix/store/6w8n8fy21yjssg8b1dqkds8diad3v75g-typescript-4.8.4/lib:/nix/store/w8q2bwi6rdhsa1dz9i8mqznddk5l7p26-typescript-language-server-3.3.2/lib:/nix/store/shgk3hhlw60dcgq6qnxs92fr5rb4pa4r-concurrently-7.5.0/lib:/nix/store/ib2vsijn0haj4rmfc9skiycgq5g4w28y-nodemon-2.0.20/lib:/nix/store/q4hrphx851xvkcnyyvqqyf1yjgmkmiar-mesa-22.2.3-drivers/lib:/nix/store/cxhib72pd7n3fzl1yh6lrm4q16l42kws-libglvnd-1.5.0/lib:/nix/store/bvihhgw8bh6cqqlj0d4s8cvmm4mqmzfc-pulseaudio-16.1/lib", "SIZE": "size", "SOURCE_DATE_EPOCH": "315532800", "STRINGS": "strings", "STRIP": "strip", "XDG_DATA_DIRS": "/nix/store/pr5n59mb4jzmfx6kanwxly0l07p861fg-patchelf-0.15.0/share", "_": "/nix/store/a7gvj343m05j2s32xcnwr35v31ynlypr-coreutils-9.1/bin/env", "__EGL_VENDOR_LIBRARY_FILENAMES": "/nix/store/q4hrphx851xvkcnyyvqqyf1yjgmkmiar-mesa-22.2.3-drivers/share/glvnd/egl_vendor.d/50_mesa.json", "__ETC_PROFILE_SOURCED": "1", "buildInputs": "/nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1 /nix/store/sbksq03iwzjpmcjl9v8r359k2jd90hpb-npm-8.19.3 /nix/store/6w8n8fy21yjssg8b1dqkds8diad3v75g-typescript-4.8.4 /nix/store/w8q2bwi6rdhsa1dz9i8mqznddk5l7p26-typescript-language-server-3.3.2 /nix/store/shgk3hhlw60dcgq6qnxs92fr5rb4pa4r-concurrently-7.5.0 /nix/store/ib2vsijn0haj4rmfc9skiycgq5g4w28y-nodemon-2.0.20 /nix/store/q4hrphx851xvkcnyyvqqyf1yjgmkmiar-mesa-22.2.3-drivers /nix/store/k4za15psddq0sa3kvy794034hk0hb0ag-libglvnd-1.5.0-dev /nix/store/66vg52x48y0v0qzf4m1sm7jy087qp00y-pulseaudio-16.1-dev", "buildPhase": "echo \"------------------------------------------------------------\" >>$out\necho \" WARNING: the existence of this path is not guaranteed.\" >>$out\necho \" It is an internal implementation detail for pkgs.mkShell.\"   >>$out\necho \"------------------------------------------------------------\" >>$out\necho >> $out\n# Record all build inputs as runtime dependencies\nexport >> $out\n", "builder": "/nix/store/dsd5gz46hdbdk2rfdimqddhq6m8m8fqs-bash-5.1-p16/bin/bash", "cmakeFlags": "", "configureFlags": "", "depsBuildBuild": "", "depsBuildBuildPropagated": "", "depsBuildTarget": "", "depsBuildTargetPropagated": "", "depsHostHost": "", "depsHostHostPropagated": "", "depsTargetTarget": "", "depsTargetTargetPropagated": "", "doCheck": "", "doInstallCheck": "", "mesonFlags": "", "nativeBuildInputs": "", "out": "/nix/store/3f7hxnmibx86v084wih5kq9igm2y5hmd-nix-shell", "outputs": "out", "patches": "", "phases": "buildPhase", "propagatedBuildInputs": "", "propagatedNativeBuildInputs": "", "shell": "/nix/store/dsd5gz46hdbdk2rfdimqddhq6m8m8fqs-bash-5.1-p16/bin/bash", "shellHook": "", "stdenv": "/nix/store/kmfaajdpyyyg319vfqni5jm9wkxjmf73-stdenv-linux", "strictDeps": "", "system": "x86_64-linux"}, "dependencies": [{"path": "replit.nix", "mod_time": "2025-07-04T04:27:55.061253723Z"}], "closure": ["/nix/store/34xlpp3j3vy7ksn09zh44f1c04w77khf-libunistring-1.0", "/nix/store/5mh5019jigj0k14rdnjam1xwk5avn1id-libidn2-2.3.2", "/nix/store/4nlgxhb09sdr51nc9hdm8az5b08vzkgx-glibc-2.35-163", "/nix/store/026hln0aq1hyshaxsdvhg0kmcm6yf45r-zlib-1.2.13", "/nix/store/mdck89nsfisflwjv6xv8ydj7dj0sj2pn-gcc-11.3.0-lib", "/nix/store/039g378vc3pc3dvi9dzdlrd0i4q93qwf-binutils-2.39", "/nix/store/9ad850yxw6jfvf3l3dbvp8dh2a3g4j71-libgpg-error-1.45", "/nix/store/073j1x6mc31pz9hff2a1nb423kmkl3ii-libassuan-2.5.5", "/nix/store/09gxmx43bc0snqc2hf094hnxpx2bb4hh-libXdmcp-1.1.3", "/nix/store/57xv61c5zi8pphjbcwxxjlgc34p61ic9-glibc-2.35-163-bin", "/nix/store/09gyxf17hs4hkpgnbj1kdz8xk5c3w4mm-getent-glibc-2.35-163", "/nix/store/4mxnw95jcm5a27qk60z7yc0gvxp42b9a-openssl-3.0.7", "/nix/store/75dl24qd9yvigjkndfqjkhlbwq8i2q87-libcbor-0.9.0", "/nix/store/cr5fmwri3601s7724ayjvckhsg6cz4rv-attr-2.5.1", "/nix/store/7q41sbf04qcwv75j5bxis6pfjnmshy44-acl-2.3.1", "/nix/store/w10in9diaqrcqqxi5lg20n3q2jfpk6pq-zstd-1.5.2", "/nix/store/w3sdhqiazzp4iy40wc2g85mv0grg1cx0-xz-5.2.7", "/nix/store/86yrrihrf5ghfvrh3j087zhpc9iajsmh-kmod-30-lib", "/nix/store/9izprbjfnlk2dn9sqp9af9nci6zncx2l-kexec-tools-2.0.25", "/nix/store/lgdq362bvrzc4bg0cb8mj38rsjcmzxsf-gmp-with-cxx-stage4-6.2.1", "/nix/store/a7gvj343m05j2s32xcnwr35v31ynlypr-coreutils-9.1", "/nix/store/acvafmp652dj67f4g3sdx68n2mg1afz4-util-linux-minimal-2.38.1-lib", "/nix/store/dsd5gz46hdbdk2rfdimqddhq6m8m8fqs-bash-5.1-p16", "/nix/store/b09kqza5nw7y2jpvb6q4dz9hixrm0ipk-db-4.8.30", "/nix/store/mhcgwcc7r208pnwv5j26j58iqvl1yvqj-audit-2.8.5", "/nix/store/xf0ssp8s6xjz710q33hspj5dphqhmmc1-libxcrypt-4.4.30", "/nix/store/hk0qw5ynzy1h7w4vn4bg6hwirf2a2y56-linux-pam-1.5.2", "/nix/store/c7klanhckqpsvv4x2izcyzvfb8vazy4s-shadow-4.11.1", "/nix/store/jv63bkng8b3y1zw0qsjrpwz2gsibll82-libcap-ng-0.8.3", "/nix/store/asixagjq1jfkbmgi1600j23v394sr58a-util-linux-minimal-2.38.1-bin", "/nix/store/1i5ah27gxx3a3fyjyydfwwzqq8ni33i8-ncurses-6.3-p20220507", "/nix/store/5gkbi9cfq8hddrfzzjdpvhrw8fsp5xsl-readline-8.1p2", "/nix/store/bap4d0lpcrhpwmpb8ayjcgkmvfj62lnq-bash-interactive-5.1-p16", "/nix/store/c8byvs0rj8vg5cpm5mswcg5dvp7d5ir7-libseccomp-2.5.4-lib", "/nix/store/inhqw7zx59hxa0r9am0kw3ip88hpbbrw-kmod-30", "/nix/store/jijvvmbza698qa6yjgr1ndgdgyryvq1i-libcap-2.66-lib", "/nix/store/ysl6qj5r7nn63b16954dhk7x47r5yq7i-bzip2-1.0.8", "/nix/store/a8mhcagrsly7c7mpjrpsnaahk4aax056-bzip2-1.0.8-bin", "/nix/store/1dgws25664p544znpc6f1nh9xmjf4ykc-pcre-8.45", "/nix/store/86bp03jkmsl6f92w0yzg4s59g5mhxwmy-gnugrep-3.7", "/nix/store/mb0pcxkmrg0f6k0zaywlnvpk9q3j5ans-zstd-1.5.2-bin", "/nix/store/p3m1ndl1lapwrlh698bnb5lvvxh67378-gzip-1.12", "/nix/store/zlcnmqq14jz5x9439jf937mvayyl63da-xz-5.2.7-bin", "/nix/store/qmp028x2h8k1i032w88h2lhbylfpi1wj-kbd-2.5.1", "/nix/store/adnxnv29s3nnrxrv19z5jnqzww0jd1aa-systemd-minimal-251.7", "/nix/store/dfxf2gqizhb1gk1p3qv7jgl9gnlqdaf5-pcsclite-1.9.5", "/nix/store/0g989zr25nvqrxdbxbwpzdfc1r6f7m9z-libfido2-1.12.0", "/nix/store/974czghvkyg72giyf1lcy76f5zl6dgkh-libogg-1.3.5", "/nix/store/0ijjp3ff50ahanz7190syxavgjslbb1r-flac-1.4.2", "/nix/store/46fw6pyn1zs9y0fwfdbgn4363ill17g9-elfutils-0.188", "/nix/store/0snlk51mis3zacmva8lpkd462ck831wh-libbpf-1.0.1", "/nix/store/0y971q54v6jm9ss243xhl4y0gnlsm9c8-zlib-1.2.13-dev", "/nix/store/18y8p4mjwjy26npih04hfv48wz9czgk3-attr-2.5.1-bin", "/nix/store/i38jcxrwa4fxk2b7acxircpi399kyixw-linux-headers-6.0", "/nix/store/4pqv2mwdn88h7xvsm7a5zplrd8sxzvw0-glibc-2.35-163-dev", "/nix/store/a1ad8qiqqb9fpg5a9rhlkm44s02sr61p-expand-response-params", "/nix/store/1d6ian3r8kdzspw8hacjhl3xkp40g1lj-binutils-wrapper-2.39", "/nix/store/1gf2flfqnpqbr1b4p4qz2f72y42bs56r-gcc-11.3.0", "/nix/store/1i5y55x4b4m9qkx5dqbmr1r6bvrqbanw-multiple-outputs.sh", "/nix/store/6z3agp6jqqdrnrdlsgzbdgw56z8azgr9-npth-1.6", "/nix/store/fbd2zvpx1iyisqmq5hn3hdnqs03ycz57-libgcrypt-1.10.1", "/nix/store/1nwdvd4bkac2g3a3sd84qbfi4mf07l3y-gnupg-2.3.7", "/nix/store/1zlrx8g7xs6ar0ggca4h3p6hmapq2p2h-perl-5.36.0", "/nix/store/2217a7zw3rdwx96xn9kcjwlcxkhn9qf8-gdbm-1.23", "/nix/store/cjdm2inq3madwqpnnddi59vv5yd89m52-pcre2-10.40", "/nix/store/ii8xgfqglbvvaiv4isqpy0s7lhqgxrmy-libselinux-3.3", "/nix/store/xghl0l2f6byzqvy06ana2y4isqwa7blw-libffi-3.4.4", "/nix/store/2k366jrbsra97gjfxwvrhvixjfxdach5-glib-2.74.1", "/nix/store/2rgwrp4zwia0ayhhxzw000cnjvr51pxa-lz4-1.9.4", "/nix/store/2w5ssck8y4kfcpj2argg0kmaw32ii275-dconf-0.40.0-lib", "/nix/store/36xp94y7564gc5p6miyddg6xn9bi6pp0-libuv-1.44.2", "/nix/store/3s1pgzk4acxn7rl6l8z2390w70426yb2-lvm2-2.03.16-lib", "/nix/store/4lkjvagv0bqq58qpysfabw66rqrbddgb-expat-2.5.0", "/nix/store/59jmzisg8fkm9c125fw384dqq1np602l-move-docs.sh", "/nix/store/5aspvhbacf0knl5yyf0x3cs25inxh016-readline-6.3p08", "/nix/store/5bi6hkakf48r8j1nhmhmgbqpij5ka597-sbc-1.4", "/nix/store/94gdvbv86cifg62hblk0397n5x7pb03l-libtool-2.4.7-lib", "/nix/store/dj1c4rx8ly8kw6i614plql6vm8595pjq-libasyncns-0.8", "/nix/store/h3h96j064p5kdjyw48j80005y99j5rcg-soxr-0.1.3", "/nix/store/904r2fg24xhnxq7511lqnjy06si0h5j9-libapparmor-3.1.2", "/nix/store/cfbhw8r8ags41vwqaz47r583d0p4h4a1-gnutar-1.34", "/nix/store/ayr7k5an06pnhpgw2a7zdhvnqzb3chsh-json-c-0.16", "/nix/store/g1l4jwmlmab794rb5b3ch3xf0d1168mq-cryptsetup-2.5.0", "/nix/store/cph87856yzakak001w00ck5n5cdajl19-libevent-2.1.12", "/nix/store/ijz81p08bp812q2bvv77lz9qpfzncibd-gmp-with-cxx-6.2.1", "/nix/store/raz5jjjszg123qlmx07dv0w5zjqf2x3y-nettle-3.8.1", "/nix/store/gigp6hvw7vn3576dipp0wlkmpp8szs85-unbound-1.17.0-lib", "/nix/store/vzrbz3lvmiziy3i79rzmk01wfprhwjh0-libtasn1-4.19.0", "/nix/store/h6hww2m19vbc93n6511zvhd810cql278-p11-kit-0.24.1", "/nix/store/nsk5b96rl1imwvwpqnkxmqkh0bk9m2dm-dns-root-data-2019-01-11", "/nix/store/mq9jfv5yzjr626nszc429pkbg6qc22wa-gnutls-3.7.8", "/nix/store/jbvsz76svifygzvvvpi3s6q1zd85b009-libmicrohttpd-0.9.71", "/nix/store/cdrs17s1dbdzv0giqvm2lzvxkwrwl6k2-libpcap-1.10.1", "/nix/store/idfq8lh60m316bs1958bs1lxffcjxbjm-libnfnetlink-1.0.2", "/nix/store/np5midk4wz7xpmwcp4h6ina1jgxwn92h-libmnl-1.0.5", "/nix/store/d24wnh2mgf5v70pb7y1yqhx9vjxzdii7-libnetfilter_conntrack-1.0.9", "/nix/store/iicq4q8n5adnylc3lixlsmfkznx8h3cr-libnftnl-1.2.4", "/nix/store/lq1i7dylhdza29yfpjgvkzfrbpjm4w00-iptables-1.8.8", "/nix/store/9iy1ng7h1l6jdmjk157jra8n4hkrfdj1-brotli-1.0.9-lib", "/nix/store/qz400bwshaqikj5s2qyvh0c9qffgmqik-nghttp2-1.49.0-lib", "/nix/store/816qwr4xy058451rbxr0ccyh1v1akhb6-keyutils-1.6.3-lib", "/nix/store/r7gl900my2fw6k33nxh2r7rzv8nv0s25-libkrb5-1.20", "/nix/store/vqq9s0d6fw6kqf3sr5nrzqbys9rhygqd-libssh2-1.10.0", "/nix/store/rirzp6ijbcwnxlf0b2n286n587r3z9jw-curl-7.86.0", "/nix/store/mbyb9f7k47bisn5y2ga3srda4sasi8yi-tpm2-tss-3.2.0", "/nix/store/hlf86vmyh14scxwann44fy3azvc6njaj-systemd-251.7", "/nix/store/vhw80flmxy6d7xpqs1h56k629xsmmasz-gfortran-11.3.0-lib", "/nix/store/lgx9ccic6miz2wrbjhyw6gzbz12rf0pj-fftw-single-3.3.10", "/nix/store/kdd3flb0zgiy336sja4ic8lmrpdl5xr7-speexdsp-1.2.1", "/nix/store/g6k7s2waqvz6b4bsj4v3ypb9kf12bhd8-alsa-ucm-conf-*******", "/nix/store/rrwf2b4dhpigs3mvc6z3nsnlzm0qccmq-alsa-topology-conf-*******", "/nix/store/l3m7axrj699nx4j024sb0dw0215alsd1-alsa-lib-*******", "/nix/store/k3k6jmzxfjq1s3la01ffpjv2blqd66lk-libvorbis-1.3.7", "/nix/store/kzkg5g476xkdh2xwd27ylmlww4phznmv-libopus-1.3.1", "/nix/store/njswbyisabvgk02vi4v22yd1lwl80m7r-libsndfile-1.1.0", "/nix/store/srqyvqyr1cp5r192xq8vvg2y694w1vyv-webrtc-audio-processing-0.3.1", "/nix/store/8ypkjcki98cwblzc4k0kblxz2zpjqj45-libXau-1.0.9", "/nix/store/xs3v53gpwwpz34aj5h51rz4wxmjfrf08-libxcb-1.14", "/nix/store/w3zzhfl4a7xp0xfflz2gawv02y8ba9z8-libX11-1.8.1", "/nix/store/y1ffp4g3yl0ijwdl8lgh4hhq3wl8frcc-dbus-1.14.4-lib", "/nix/store/bvihhgw8bh6cqqlj0d4s8cvmm4mqmzfc-pulseaudio-16.1", "/nix/store/a4mgvch1zx61xfsixq3dmh6r81p4dgkl-attr-2.5.1-dev", "/nix/store/q8s1kzhsblr2a1p2g0lh2dkcgca1im3z-libcap-2.66", "/nix/store/n0r5z0w1j3m0z8980hgg2rjkh617v1md-libcap-2.66-dev", "/nix/store/66vg52x48y0v0qzf4m1sm7jy087qp00y-pulseaudio-16.1-dev", "/nix/store/r3x96j3kmcs8dv4l02rrjmbhm535jycy-icu4c-72.1", "/nix/store/7xqm5wfm4djpspmdgj36ijnql31id8xg-icu4c-72.1-dev", "/nix/store/fq47cv26nb87hwz2678r6i8ym5b57lwf-openssl-3.0.7-bin", "/nix/store/f95kxwhnr2bazy7nl6wzwjiak02dlp9v-openssl-3.0.7-dev", "/nix/store/i16lgq16av602nfyws3ps8dd9yj36dwh-tzdata-2022f", "/nix/store/jpj9lx0p2h1vs3gkzj8jh350113bsm84-sqlite-3.39.4", "/nix/store/pplp2i09gc4k67a4aayr78a4c04jbqxa-mailcap-2.1.53", "/nix/store/zdba9frlxj2ba8ca095win3nphsiiqhb-python3-3.10.8", "/nix/store/dj805sw07vvpbxx39c8g67x8qddg0ikw-nodejs-18.12.1", "/nix/store/6w8n8fy21yjssg8b1dqkds8diad3v75g-typescript-4.8.4", "/nix/store/7sqa8vdyvc3vwh2bq3q7c2kcx1k83iij-libxshmfence-1.3", "/nix/store/89zs7rms6x00xfq4dq6m7mjnhkr8a6r4-gnused-4.8", "/nix/store/8gx2zdbvdlhayh3g7006dxipx91rggnh-libelf-0.8.13", "/nix/store/h5slhj7gqpqh5q4jb00xdbcwbl8vqqa7-libxml2-2.10.3", "/nix/store/8q73rj23w9dms6mq2nwijxsj2lqcmz0r-llvm-14.0.6-lib", "/nix/store/8zxndz5ag0p6s526c2xyllhk1nrn4c3i-audit-tmpdir.sh", "/nix/store/9krlzvny65gdc8s7kpb6lkx8cd02c25b-default-builder.sh", "/nix/store/fzb7khbic8vpcr3m69v6y8qp6jqspdgw-openssl-1.1.1s", "/nix/store/9zdxd9grnspixp6iaz7h6564ja550i0q-openssl-1.1.1s-bin", "/nix/store/cggynsnj7dyk2sxnrr20jvpwcqdiv42f-libpciaccess-0.16", "/nix/store/hr321ph174h3whvlvbc0lk5w1n1ljkx7-libdrm-2.4.113", "/nix/store/l3y9k2x7cqzcjj9s18z7la9xqsjq6r52-wayland-1.21.0", "/nix/store/bk62iwpqiaipgbim564f7zy6prdwpvri-mesa-22.2.3", "/nix/store/bnj8d7mvbkg3vdb07yz74yhl3g107qq5-patch-shebangs.sh", "/nix/store/c8n9kcdddp9np665xz6ri61b383nxvz8-move-systemd-user-units.sh", "/nix/store/qvvaq9p7lw05r7szvsx3c17ca5x8h982-libXext-1.3.4", "/nix/store/cxhib72pd7n3fzl1yh6lrm4q16l42kws-libglvnd-1.5.0", "/nix/store/ib2vsijn0haj4rmfc9skiycgq5g4w28y-nodemon-2.0.20", "/nix/store/k4za15psddq0sa3kvy794034hk0hb0ag-libglvnd-1.5.0-dev", "/nix/store/cickvswrvann041nqxb0rxilc46svw1n-prune-libtool-files.sh", "/nix/store/dq0xwmsk1g0i2ayg6pb7y87na2knzylh-gcc-wrapper-11.3.0", "/nix/store/fyaryjvghbkpfnsyw97hb3lyb37s1pd6-move-lib64.sh", "/nix/store/g8xg0i02aqwhgxwd2vnp5ax3d6lrkg1v-strip.sh", "/nix/store/hwcdqw4jrjnd37wxqgsd47hd0j8bnj09-gawk-5.1.1", "/nix/store/j9p3g8472iijd50vhdprx0nmk2fqn5gv-diffutils-3.8", "/nix/store/kd4xwxjpjxi71jkm6ka0np72if9rm3y0-move-sbin.sh", "/nix/store/kxw6q8v6isaqjm702d71n2421cxamq68-make-symlinks-relative.sh", "/nix/store/m54bmrhj6fqz8nds5zcj97w9s9bckc9v-compress-man-pages.sh", "/nix/store/mblgz65m3zv9x548a3d5m96fj2pbwr09-gnumake-4.3", "/nix/store/mydc6f4k2z73xlcz7ilif3v2lcaiqvza-findutils-4.9.0", "/nix/store/ngg1cv31c8c7bcm2n8ww4g06nq7s4zhm-set-source-date-epoch-to-latest.sh", "/nix/store/pr5n59mb4jzmfx6kanwxly0l07p861fg-patchelf-0.15.0", "/nix/store/xwl6y60ffijfbhxb754dlxk3pkjgw0d2-ed-1.18", "/nix/store/v7ljksji50mg3w61dykaa3n3y79n6nil-patch-2.7.6", "/nix/store/wlwcf1nw2b21m4gghj70hbg1v7x53ld8-reproducible-builds.sh", "/nix/store/y6aj732zm9m87c82fpvf103a1xb22blp-file-5.43", "/nix/store/kmfaajdpyyyg319vfqni5jm9wkxjmf73-stdenv-linux", "/nix/store/cr7ldrd6xlq4rgqr99yygy43f77zdk1x-libXfixes-6.0.0", "/nix/store/l2p6ikb5wqjcsfnmgcfxmbzi22smr5qa-libXxf86vm-1.1.4", "/nix/store/q4hrphx851xvkcnyyvqqyf1yjgmkmiar-mesa-22.2.3-drivers", "/nix/store/sbksq03iwzjpmcjl9v8r359k2jd90hpb-npm-8.19.3", "/nix/store/shgk3hhlw60dcgq6qnxs92fr5rb4pa4r-concurrently-7.5.0", "/nix/store/p9zk4lm1902mi3r30kg6fjlmqjay5y8q-openssl-1.1.1s-dev", "/nix/store/ii9hqvfq3g2vnsybi1vly9s1j9jcna9k-nodejs-14.21.1", "/nix/store/w8q2bwi6rdhsa1dz9i8mqznddk5l7p26-typescript-language-server-3.3.2", "/nix/store/cg100an17f56j35s94wbz5kgr5w1c5wi-nix-shell"], "channel": "stable-22_11", "channel_nix_path": "/nix/store/k25cf20fhlaijfawgdhrs5w2v7zsm0jm-nixpkgs-stable-22_11-22.11.tar.gz/nixpkgs-stable-22_11", "production": null}}}