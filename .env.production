# ChewyAI Production Environment Configuration
# Optimized for performance and cost efficiency

# Server Configuration
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://chewyai.vercel.app

# Supabase Configuration
SUPABASE_URL=https://jpvbtrzvbpyzgtpvltss.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxMzUzMCwiZXhwIjoyMDY2NDg5NTMwfQ.Kor-OkR26YK5D8TJ4T9EUVUpyjBEf_CeHcOwpOP6Xx4

# Stripe Configuration (Production - REPLACE WITH LIVE KEYS)
STRIPE_SECRET_KEY=sk_live_REPLACE_WITH_LIVE_KEY
STRIPE_WEBHOOK_SECRET=whsec_REPLACE_WITH_LIVE_WEBHOOK_SECRET

# OpenRouter AI Configuration
OPENROUTER_API_KEY=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

# JWT Configuration (CHANGE FOR PRODUCTION)
JWT_SECRET=CHANGE_THIS_IN_PRODUCTION_2025

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,docx,txt,pptx

# Performance & Cost Optimization
ENABLE_CACHING=true
LOG_LEVEL=error
ENABLE_COMPRESSION=true
ENABLE_RATE_LIMITING=true

# Security Settings
CORS_ORIGIN=https://chewyai.vercel.app
SECURE_COOKIES=true
TRUST_PROXY=true

