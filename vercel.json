{"version": 2, "name": "chewyai-frontend", "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/(.*)", "dest": "/frontend/$1"}], "functions": {"frontend/dist/**": {"maxDuration": 5}}, "regions": ["iad1"], "framework": "vite", "buildCommand": "cd frontend && npm run build", "outputDirectory": "frontend/dist", "installCommand": "cd frontend && npm install", "env": {"VITE_SUPABASE_URL": "@supabase_url", "VITE_SUPABASE_ANON_KEY": "@supabase_anon_key", "VITE_API_URL": "@api_url"}, "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=2592000, s-maxage=86400"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}, {"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "$VITE_API_URL/api/$1"}]}