bootstrap:22 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
login:1 Access to fetch at 'https://chewy-ai-backend.replit.app/api/health' from origin 'https://304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
connectionService.ts:61 
            
            
           GET https://chewy-ai-backend.replit.app/api/health net::ERR_FAILED
checkConnection @ connectionService.ts:61
initializeConnectionMonitoring @ connectionService.ts:204
(anonymous) @ App.tsx:73
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24970
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
bootstrap:22 WebSocket connection to 'wss://304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev:3000/ws' failed: 
WebSocketClient @ WebSocketClient.js:13
initSocket @ socket.js:27
../node_modules/webpack-dev-server/client/index.js?protocol=ws%3A&hostname=0.0.0.0&port=3000&pathname=%2Fws&logging=none&overlay=%7B%22errors%22%3Atrue%2C%22warnings%22%3Afalse%7D&reconnect=10&hot=true&live-reload=true @ index.js:320
__webpack_require__ @ bootstrap:22
(anonymous) @ startup:4
(anonymous) @ startup:6
auth.ts:149 Initiating Google OAuth with redirect: https://304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev/auth/callback
login:1 Access to fetch at 'https://chewy-ai-backend.replit.app/api/health' from origin 'https://304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
connectionService.ts:61 
            
            
           GET https://chewy-ai-backend.replit.app/api/health net::ERR_FAILED
checkConnection @ connectionService.ts:61
requestPromise @ connectionService.ts:127
safeFetch @ connectionService.ts:175
signInWithGoogle @ auth.ts:151
signInWithGoogle @ authStore.ts:70
handleGoogleAuth @ LoginForm.tsx:58
callCallback @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
(anonymous) @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
WebSocketClient.js:13 WebSocket connection to 'wss://304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev:3000/ws' failed: 
WebSocketClient @ WebSocketClient.js:13
initSocket @ socket.js:27
(anonymous) @ socket.js:51
WebSocketClient.js:13 WebSocket connection to 'wss://304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev:3000/ws' failed: 
WebSocketClient @ WebSocketClient.js:13
initSocket @ socket.js:27
(anonymous) @ socket.js:51
