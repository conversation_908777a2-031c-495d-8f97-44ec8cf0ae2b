# 🚀 ChewyAI Deployment Guide

## Cost-Optimized Deployment Strategy

This guide provides a **cost-efficient deployment strategy** that keeps monthly costs under **$10-15** while maintaining full functionality.

## 💰 Cost Breakdown

### Monthly Operating Costs (Optimized)
- **Vercel (Frontend)**: $0 (Hobby plan)
- **Railway (Backend)**: $5-15 (with scale-to-zero)
- **Supabase**: $0 (Free tier: 500MB, 2GB bandwidth)
- **Stripe**: $0 (pay per transaction)
- **OpenRouter**: ~$2-5 (based on usage)
- **Total**: **$7-20/month** maximum

### Cost Optimization Features
- ✅ **Scale to zero** when idle (saves ~$10-15/month)
- ✅ **Single region deployment** (reduces latency costs)
- ✅ **Aggressive caching** (reduces API calls)
- ✅ **Resource limits** (512MB RAM, 0.25 vCPU)
- ✅ **Sleep after 5 minutes** of inactivity

## 🏗️ Deployment Architecture

```
Frontend (Vercel)     Backend (Railway)     Database (Supabase)
    ↓                      ↓                      ↓
Static React App    Express.js API Server    PostgreSQL + Auth
Port: 443 (HTTPS)    Port: Dynamic           Managed Service
Cost: $0/month       Cost: $5-15/month       Cost: $0/month
```

## 📋 Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Update Stripe keys to live keys for production
- [ ] Change JWT_SECRET for production
- [ ] Verify all API keys are valid
- [ ] Test locally with production environment

### 2. Repository Setup
- [x] Production branch created
- [x] Staging branch created
- [x] Deployment configurations ready
- [x] Environment files configured

## 🚀 Deployment Steps

### Step 1: Deploy Backend to Railway

1. **Connect Repository**
   ```bash
   # Railway will auto-detect railway.toml configuration
   # Connect your GitHub repository to Railway
   ```

2. **Set Environment Variables**
   - Copy from `.env.production`
   - Set `NODE_ENV=production`
   - Configure domain settings

3. **Deploy**
   - Railway auto-deploys from `production` branch
   - Health check: `/api/health`
   - Expected startup time: 30-60 seconds

### Step 2: Deploy Frontend to Vercel

1. **Connect Repository**
   ```bash
   # Vercel will auto-detect vercel.json configuration
   # Connect your GitHub repository to Vercel
   ```

2. **Set Environment Variables**
   ```env
   VITE_SUPABASE_URL=https://jpvbtrzvbpyzgtpvltss.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   VITE_API_URL=https://your-backend.railway.app
   ```

3. **Deploy**
   - Vercel auto-deploys from `production` branch
   - Build command: `cd frontend && npm run build`
   - Output directory: `frontend/dist`

### Step 3: Configure Custom Domain (Optional)

1. **Purchase Domain** (optional, ~$10-15/year)
2. **Configure DNS**
   - Point domain to Vercel
   - Set up subdomain for API if needed
3. **SSL Certificate** (automatic with Vercel)

## 🔧 Environment-Specific Configurations

### Staging Environment
- **Frontend**: `https://chewyai-staging.vercel.app`
- **Backend**: `https://chewyai-staging.railway.app`
- **Purpose**: Testing and validation
- **Cost**: ~$3-8/month (lower resource limits)

### Production Environment
- **Frontend**: `https://chewyai.vercel.app`
- **Backend**: `https://chewyai-production.railway.app`
- **Purpose**: Live application
- **Cost**: ~$7-20/month (full resources)

## 📊 Monitoring & Cost Control

### Railway Cost Monitoring
```javascript
// Monitor usage with Railway CLI
railway status
railway logs
railway metrics
```

### Vercel Analytics
- Monitor bandwidth usage (100GB free limit)
- Track function invocations
- Monitor build minutes

### Cost Alerts
- Set up Railway spending alerts at $15/month
- Monitor Supabase usage (500MB database limit)
- Track OpenRouter API usage

## 🛡️ Security Considerations

### Production Security
- [ ] Use live Stripe keys
- [ ] Change default JWT secret
- [ ] Enable CORS restrictions
- [ ] Set up rate limiting
- [ ] Configure secure cookies

### Environment Variables
- Never commit `.env` files to Git
- Use platform-specific environment variable systems
- Rotate secrets regularly

## 🔄 CI/CD Workflow

### Git Workflow
```bash
# Feature development
git checkout -b feature/new-feature
# ... make changes ...
git push origin feature/new-feature

# Merge to staging
git checkout staging
git merge feature/new-feature
git push origin staging

# Deploy to staging automatically

# Merge to production
git checkout production
git merge staging
git push origin production

# Deploy to production automatically
```

### Automated Deployments
- **Staging**: Auto-deploy on push to `staging` branch
- **Production**: Auto-deploy on push to `production` branch
- **Rollback**: Use platform-specific rollback features

## 🚨 Troubleshooting

### Common Issues

1. **Backend Won't Start**
   - Check environment variables
   - Verify health check endpoint
   - Check Railway logs

2. **Frontend Build Fails**
   - Verify Node.js version compatibility
   - Check environment variables
   - Review build logs in Vercel

3. **API Connection Issues**
   - Verify CORS settings
   - Check API URL configuration
   - Test health check endpoint

### Cost Overruns
- Enable Railway sleep mode
- Monitor Supabase usage
- Optimize API calls
- Use caching effectively

## 📈 Scaling Strategy

### Growth Phases

**Phase 1: 0-100 users**
- Current setup handles easily
- Cost: $7-15/month

**Phase 2: 100-1,000 users**
- May need Railway Pro plan ($20/month)
- Consider Supabase Pro ($25/month)
- Total: $45-50/month

**Phase 3: 1,000+ users**
- Dedicated infrastructure
- CDN optimization
- Database scaling
- Total: $100-500/month

## 🎯 Next Steps

1. **Deploy to Staging**
   - Test all functionality
   - Validate payment flows
   - Performance testing

2. **Deploy to Production**
   - Update live API keys
   - Configure monitoring
   - Launch marketing

3. **Monitor & Optimize**
   - Track costs daily
   - Optimize performance
   - Scale as needed

---

**Total Setup Time**: 2-3 hours
**Monthly Cost**: $7-20
**Scalability**: 10,000+ users
**Uptime**: 99.9%+ expected

